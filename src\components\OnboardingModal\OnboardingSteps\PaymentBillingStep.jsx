import React, { useState, useContext, useEffect } from "react";
import { ClipLoader } from "react-spinners";
import { GlobalContext, showToast } from "../../../globalContext";
import { getUserDetailsByIdAPI } from "../../../services/userService";

const PaymentBillingStep = ({ stepData, userDetails, onComplete, onSkip }) => {
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Check if user is main member
  const isMainMember = userDetails?.main_user_details?.is_self === true;
  const hasStripe = userDetails?.has_stripe === true;
  const stripeOnboardUrl = userDetails?.account_link?.url;

  // Auto-complete step if <PERSON><PERSON> is already connected
  useEffect(() => {
    console.log("💳 PaymentBillingStep - Auto-detection check:", {
      isMainMember,
      hasStripe,
      payment_billing_complete: stepData.payment_billing_complete,
      userDetails_has_stripe: userDetails?.has_stripe,
      userDetails_keys: userDetails
        ? Object.keys(userDetails)
        : "no userDetails",
    });

    if (isMainMember && hasStripe && !stepData.payment_billing_complete) {
      console.log(
        "💳 PaymentBillingStep - Auto-completing step due to existing Stripe connection"
      );
      // Automatically mark as completed if Stripe is already connected
      const billingData = {
        stripe_setup_completed: true,
        stripe_setup_completed_at: new Date().toISOString(),
      };
      onComplete(billingData);
    }
  }, [
    isMainMember,
    hasStripe,
    stepData.payment_billing_complete,
    onComplete,
    userDetails,
  ]);

  // Manual refresh function to check Stripe status
  const handleRefreshStripeStatus = async () => {
    try {
      setIsRefreshing(true);
      console.log("💳 PaymentBillingStep - Manually refreshing Stripe status");

      const userId = localStorage.getItem("user");
      if (!userId) {
        showToast(globalDispatch, "User session not found", 4000, "error");
        return;
      }

      const result = await getUserDetailsByIdAPI(userId);
      if (result.error) {
        showToast(
          globalDispatch,
          "Failed to refresh Stripe status",
          4000,
          "error"
        );
        return;
      }

      console.log("💳 PaymentBillingStep - Fresh user data:", result.model);

      // Check if Stripe is now connected
      if (
        result.model.has_stripe === true &&
        !stepData.payment_billing_complete
      ) {
        console.log(
          "💳 PaymentBillingStep - Stripe now connected, auto-completing step"
        );
        const billingData = {
          stripe_setup_completed: true,
          stripe_setup_completed_at: new Date().toISOString(),
        };
        onComplete(billingData);
        showToast(
          globalDispatch,
          "Stripe connection verified! Step completed.",
          4000,
          "success"
        );
      } else if (result.model.has_stripe === true) {
        showToast(globalDispatch, "Stripe is already connected!", 3000, "info");
      } else {
        showToast(
          globalDispatch,
          "Stripe setup not yet complete. Please complete the setup first.",
          4000,
          "warning"
        );
      }
    } catch (error) {
      console.error("Error refreshing Stripe status:", error);
      showToast(
        globalDispatch,
        "Failed to refresh Stripe status",
        4000,
        "error"
      );
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleConnectStripe = () => {
    if (stripeOnboardUrl) {
      // Open Stripe Connect onboarding in new tab
      window.open(stripeOnboardUrl, "_blank");
      showToast(
        globalDispatch,
        "Redirecting to Stripe Connect setup...",
        3000,
        "info"
      );
    } else {
      showToast(
        globalDispatch,
        "Stripe onboarding URL not available. Please contact support.",
        4000,
        "error"
      );
    }
  };

  const handleSkipForNow = async () => {
    try {
      setIsLoading(true);

      // Complete step without Stripe setup
      const billingData = {
        stripe_setup_skipped: true,
        stripe_setup_skipped_at: new Date().toISOString(),
      };

      onComplete(billingData);
    } catch (error) {
      console.error("Error skipping billing setup:", error);
      showToast(globalDispatch, "Failed to skip billing setup", 4000, "error");
    } finally {
      setIsLoading(false);
    }
  };

  const handleContinueWithStripe = async () => {
    try {
      setIsLoading(true);

      // Mark as completed with Stripe
      const billingData = {
        stripe_setup_completed: true,
        stripe_setup_completed_at: new Date().toISOString(),
      };

      onComplete(billingData);
    } catch (error) {
      console.error("Error completing billing setup:", error);
      showToast(
        globalDispatch,
        "Failed to complete billing setup",
        4000,
        "error"
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Sub-member view
  if (!isMainMember) {
    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="text-center">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary/20">
            <span className="text-2xl">💳</span>
          </div>
          <h2 className="text-2xl font-bold text-black">
            Payment & Billing Settings
          </h2>
          <p className="mt-2 text-sm text-[#667484]">
            Get paid seamlessly and manage your finances effortlessly.
          </p>
        </div>

        {/* Sub-member Info */}
        <div className="rounded-lg border border-blue-800 bg-blue-900/20 p-6 text-center">
          <div className="mb-4">
            <svg
              className="mx-auto h-12 w-12 text-blue-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <h3 className="mb-2 text-lg font-semibold text-blue-300">
            Team Member Account
          </h3>
          <p className="mb-4 text-blue-200">
            As a team member, your income and billing are managed through your
            main member's account. Payment processing and invoicing will be
            handled by your organization's primary account holder.
          </p>
          <div className="text-sm text-blue-300">
            <p>✓ No separate payment setup required</p>
            <p>✓ Centralized billing management</p>
            <p>✓ Streamlined financial operations</p>
          </div>
        </div>

        {/* Action Button */}
        <div className="flex gap-4">
          <button
            onClick={() => onComplete({})}
            disabled={isLoading}
            className="flex flex-1 items-center justify-center gap-2 rounded-lg bg-primary px-6 py-3 font-medium text-black hover:bg-primary/90 disabled:cursor-not-allowed disabled:opacity-50"
          >
            {isLoading ? (
              <>
                <ClipLoader size={16} color="#ffffff" />
                Processing...
              </>
            ) : (
              "Continue"
            )}
          </button>
        </div>
      </div>
    );
  }

  // Main member view
  return (
    <div className="relative space-y-6">
      {/* Header */}
      <div className="text-center">
        <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
          <svg
            width="57"
            height="56"
            viewBox="0 0 57 56"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <rect x="4.5" y="4" width="48" height="48" rx="24" fill="#E0E6FC" />
            <rect
              x="4.5"
              y="4"
              width="48"
              height="48"
              rx="24"
              stroke="#F5F7FF"
              stroke-width="8"
            />
            <g clip-path="url(#clip0_3161_2796)">
              <path
                d="M37.5 20.5C37.8978 20.5 38.2794 20.658 38.5607 20.9393C38.842 21.2206 39 21.6022 39 22V34C39 34.3978 38.842 34.7794 38.5607 35.0607C38.2794 35.342 37.8978 35.5 37.5 35.5H19.5C19.1022 35.5 18.7206 35.342 18.4393 35.0607C18.158 34.7794 18 34.3978 18 34V22C18 21.6022 18.158 21.2206 18.4393 20.9393C18.7206 20.658 19.1022 20.5 19.5 20.5H37.5ZM19.5 19C18.7044 19 17.9413 19.3161 17.3787 19.8787C16.8161 20.4413 16.5 21.2044 16.5 22V34C16.5 34.7956 16.8161 35.5587 17.3787 36.1213C17.9413 36.6839 18.7044 37 19.5 37H37.5C38.2956 37 39.0587 36.6839 39.6213 36.1213C40.1839 35.5587 40.5 34.7956 40.5 34V22C40.5 21.2044 40.1839 20.4413 39.6213 19.8787C39.0587 19.3161 38.2956 19 37.5 19H19.5Z"
                fill="#3C50E0"
              />
              <path
                d="M19.5 24.25C19.5 24.0511 19.579 23.8603 19.7197 23.7197C19.8603 23.579 20.0511 23.5 20.25 23.5H23.25C23.4489 23.5 23.6397 23.579 23.7803 23.7197C23.921 23.8603 24 24.0511 24 24.25V25.75C24 25.9489 23.921 26.1397 23.7803 26.2803C23.6397 26.421 23.4489 26.5 23.25 26.5H20.25C20.0511 26.5 19.8603 26.421 19.7197 26.2803C19.579 26.1397 19.5 25.9489 19.5 25.75V24.25ZM19.5 28.75C19.5 28.5511 19.579 28.3603 19.7197 28.2197C19.8603 28.079 20.0511 28 20.25 28H27.75C27.9489 28 28.1397 28.079 28.2803 28.2197C28.421 28.3603 28.5 28.5511 28.5 28.75C28.5 28.9489 28.421 29.1397 28.2803 29.2803C28.1397 29.421 27.9489 29.5 27.75 29.5H20.25C20.0511 29.5 19.8603 29.421 19.7197 29.2803C19.579 29.1397 19.5 28.9489 19.5 28.75ZM19.5 31.75C19.5 31.5511 19.579 31.3603 19.7197 31.2197C19.8603 31.079 20.0511 31 20.25 31H21.75C21.9489 31 22.1397 31.079 22.2803 31.2197C22.421 31.3603 22.5 31.5511 22.5 31.75C22.5 31.9489 22.421 32.1397 22.2803 32.2803C22.1397 32.421 21.9489 32.5 21.75 32.5H20.25C20.0511 32.5 19.8603 32.421 19.7197 32.2803C19.579 32.1397 19.5 31.9489 19.5 31.75ZM24 31.75C24 31.5511 24.079 31.3603 24.2197 31.2197C24.3603 31.079 24.5511 31 24.75 31H26.25C26.4489 31 26.6397 31.079 26.7803 31.2197C26.921 31.3603 27 31.5511 27 31.75C27 31.9489 26.921 32.1397 26.7803 32.2803C26.6397 32.421 26.4489 32.5 26.25 32.5H24.75C24.5511 32.5 24.3603 32.421 24.2197 32.2803C24.079 32.1397 24 31.9489 24 31.75ZM28.5 31.75C28.5 31.5511 28.579 31.3603 28.7197 31.2197C28.8603 31.079 29.0511 31 29.25 31H30.75C30.9489 31 31.1397 31.079 31.2803 31.2197C31.421 31.3603 31.5 31.5511 31.5 31.75C31.5 31.9489 31.421 32.1397 31.2803 32.2803C31.1397 32.421 30.9489 32.5 30.75 32.5H29.25C29.0511 32.5 28.8603 32.421 28.7197 32.2803C28.579 32.1397 28.5 31.9489 28.5 31.75ZM33 31.75C33 31.5511 33.079 31.3603 33.2197 31.2197C33.3603 31.079 33.5511 31 33.75 31H35.25C35.4489 31 35.6397 31.079 35.7803 31.2197C35.921 31.3603 36 31.5511 36 31.75C36 31.9489 35.921 32.1397 35.7803 32.2803C35.6397 32.421 35.4489 32.5 35.25 32.5H33.75C33.5511 32.5 33.3603 32.421 33.2197 32.2803C33.079 32.1397 33 31.9489 33 31.75Z"
                fill="#3C50E0"
              />
            </g>
            <defs>
              <clipPath id="clip0_3161_2796">
                <rect
                  width="24"
                  height="24"
                  fill="white"
                  transform="translate(16.5 16)"
                />
              </clipPath>
            </defs>
          </svg>
        </div>
        <h2 className="text-2xl font-bold text-black">
          Secure Your Financials
        </h2>
        <p className="mt-2 text-sm text-[#667484]">
          Connect Stripe for seamless payments & billing.
        </p>
      </div>

      {/* Main Content */}
      <div className="space-y-6 text-center">
        <div>
          <h3 className="mb-4 text-center text-lg font-semibold text-black">
            Connect Your Financials with Stripe Direct
          </h3>
          <p className="mb-4 text-center text-sm text-[#667484]">
            To ensure you get paid directly for your services and to unlock all
            our billing and invoicing features, you'll need to connect your
            account with Stripe Connect.
          </p>
          <p className="mb-4 text-center text-sm text-[#667484]">
            Stripe is a secure, industry-leading payment processor. This
            integration will allow you to:
          </p>
          <ul className="mb-6 space-y-2 text-center text-sm text-[#667484]">
            <li>• Receive payments directly from clients.</li>
            <li>• Manage invoices and track your earnings.</li>
            <li>• Process secure transactions.</li>
          </ul>
          <p className="text-center text-sm text-[#667484]">
            Click 'Continue' to securely set up your Stripe Connect Direct
            account. You will be briefly redirected to Stripe to complete a few
            quick steps.
          </p>
        </div>

        {/* Status Display */}
        {hasStripe && (
          <div className="rounded-lg border border-green-800 bg-green-900/10 p-4">
            <div className="flex items-center gap-3">
              <svg
                className="h-5 w-5 text-green-900"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
              <span className="font-medium text-green-900">
                Stripe Connect Already Set Up
              </span>
            </div>
            <p className="mt-2 text-sm text-green-900">
              Your Stripe account is connected and ready for payment processing.
            </p>
          </div>
        )}
      </div>

      {/* Refresh Status Button - for users returning from Stripe */}
      {!hasStripe && (
        <div className="mt-6 text-center">
          <p className="mb-3 text-sm text-[#667484]">
            Already completed Stripe setup?
          </p>
          <button
            onClick={handleRefreshStripeStatus}
            disabled={isRefreshing || isLoading}
            className="flex items-center justify-center gap-2 rounded-lg border border-primary px-4 py-2 text-sm font-medium text-primary hover:bg-primary/10 disabled:cursor-not-allowed disabled:opacity-50"
          >
            {isRefreshing ? (
              <>
                <ClipLoader size={14} color="#3C50E0" />
                Checking...
              </>
            ) : (
              <>
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M4 12a8 8 0 0 1 8-8V2.5"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M12 4a8 8 0 0 1 8 8"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                Check Status
              </>
            )}
          </button>
        </div>
      )}

      {/* Action Buttons - positioned at bottom with justify-end */}
      <div className="mt-20 flex justify-center gap-4 pt-6">
        <button
          onClick={onSkip || handleSkipForNow}
          disabled={isLoading || isRefreshing}
          className="flex items-center justify-center gap-2 rounded-lg border border-gray-600 px-6 py-3 font-medium text-[#667484] hover:bg-gray-800 disabled:cursor-not-allowed disabled:opacity-50"
        >
          Skip for Now
        </button>
        <button
          onClick={hasStripe ? handleContinueWithStripe : handleConnectStripe}
          disabled={isLoading || isRefreshing}
          className="flex items-center justify-center gap-2 rounded-lg bg-primary px-6 py-3 font-medium text-white hover:bg-primary/90 disabled:cursor-not-allowed disabled:opacity-50"
        >
          {isLoading ? (
            <>
              <ClipLoader size={16} color="#ffffff" />
              Processing...
            </>
          ) : hasStripe ? (
            "Continue"
          ) : (
            "Connect to Stripe"
          )}
        </button>
      </div>
    </div>
  );
};

export default PaymentBillingStep;
