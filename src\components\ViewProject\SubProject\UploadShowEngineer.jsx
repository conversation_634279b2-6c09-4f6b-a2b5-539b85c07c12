import React from "react";
import UploadedDemo from "Components/PublicWorkOrder/WriterWorkOrder/UploadedDemo";
import UploadedMaster from "./UploadedMaster";

const UploadShowEngineer = ({
  subProject,
  lyricsVal,
  setDeleteFileId,
  setLyricsVal,
  submitLyrics,
}) => {
  const [activeTab, setActiveTab] = React.useState("masters");

  const tabs = [
    {
      id: "masters",
      label: "Master Files",
    },
    {
      id: "lyrics",
      label: "Lyrics",
    },
  ];

  return (
    <div className="p-4 w-full rounded border shadow-default border-strokedark bg-boxdark">
      {/* Tabs Navigation */}
      <div className="flex gap-2 mb-4 border-b border-stroke">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            className={`px-4 py-2 text-sm font-semibold ${
              activeTab === tab.id
                ? "border-b-2 border-white text-primary"
                : "text-bodydark hover:text-white"
            }`}
            onClick={() => setActiveTab(tab.id)}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="mt-4">
        {/* Masters Tab */}
        {activeTab === "masters" && (
          <div className="custom-overflow h-[220px] overflow-y-auto rounded border border-form-strokedark bg-form-input p-4">
            {subProject.masters && subProject.masters.length > 0 ? (
              <div className="space-y-2">
                <UploadedMaster
                  uploadedFiles={subProject.masters}
                  showUploadBtn={false}
                  setDeleteFileId={setDeleteFileId}
                  isRow={true}
                />
              </div>
            ) : (
              <div className="flex justify-center items-center py-4">
                <span className="text-sm text-bodydark2">
                  No master files found.
                </span>
              </div>
            )}
          </div>
        )}

        {/* Lyrics Tab */}
        {activeTab === "lyrics" && (
          <div className="custom-overflow h-[220px] overflow-y-auto rounded border border-form-strokedark bg-form-input p-4">
            {lyricsVal ? (
              <div className="flex flex-col gap-3">
                <textarea
                  className="w-full rounded border border-form-strokedark bg-form-input px-4 py-2.5 text-sm text-white focus:border-primary focus:outline-none"
                  placeholder="Lyrics"
                  value={lyricsVal}
                  onChange={(e) => setLyricsVal(e.target.value)}
                  rows={6}
                />
                <button
                  type="button"
                  className="px-4 py-2 w-max text-sm font-medium text-white rounded-sm bg-primary hover:bg-opacity-90"
                  onClick={submitLyrics}
                >
                  Save
                </button>
              </div>
            ) : (
              <div className="flex justify-center items-center py-4">
                <span className="text-sm text-bodydark2">No lyrics found.</span>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default UploadShowEngineer;
