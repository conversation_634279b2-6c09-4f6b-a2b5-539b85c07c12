import MkdSDK from "../utils/MkdSDK";

let sdk = new MkdSDK();

export const CreateTrackAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/cycle_count`;
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const getAllTracksAPI = async (payload) => {
  let data = {
    filter: payload,
  };
  console.log(data);
  try {
    const uri = "/v3/api/custom/equality_record/cycle_count/retrieve-all";
    const res = await sdk.callRawAPI(uri, data, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const getAllTracksClientAPI = async (id) => {
  try {
    const uri = `/v3/api/custom/equality_record/cycle_count/client/${id}`;
    const res = await sdk.callRawAPI(uri, [], "GET");
    return res;
  } catch (error) {
    return error;
  }
};

export const deleteTrackAPI = async (id) => {
  try {
    const uri = `/v3/api/custom/equality_record/cycle_count/${id}`;
    const res = await sdk.callRawAPI(uri, [], "DELETE");
    return res;
  } catch (error) {
    return error;
  }
};
