import React, { useState } from "react";
import { useParams } from "react-router-dom";
import { GlobalContext, showToast } from "Src/globalContext";
import { editTeamDetailsAPI } from "Src/services/clientProjectDetailsService";
import { ClipLoader } from "react-spinners";

const ClientEditMusicDetailsModal = (props) => {
  const { setIsOpen, data, getData } = props;
  const projectId = useParams();

  const [loader, setLoader] = useState(false);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [formValues, setFormValues] = React.useState({
    notes: data?.notes || "",
    song_list: data?.song_list || "",
    // competitions: data.competitions || "",
    colors: data?.colors || "",
    mascot: data?.mascot || "",
    twitter:
      data && data.social_media
        ? JSON.parse(data.social_media)?.twitter || ""
        : "",
    instagram:
      data && data.social_media
        ? JSON.parse(data.social_media)?.instagram || ""
        : "",

    // theme: data?.theme || "",
  });

  const handleChange = (e) => {
    const { name, value } = e.target;

    setFormValues((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };

  const submit = async (e) => {
    e.preventDefault();
    try {
      setLoader(true);
      await editTeamDetailsAPI({
        project_id: projectId.id,

        social_media: JSON.stringify({
          instagram: formValues.instagram,
          twitter: formValues.twitter,
        }),
        song_list: formValues.song_list,
        ...formValues,
      });

      await getData();
      setLoader(false);
      showToast(globalDispatch, "Music Updated");

      setIsOpen(false);
    } catch (error) {
      setIsOpen(false);
      setLoader(false);
      showToast(globalDispatch, "Music Update Failed", "error");
    }
  };

  return (
    <>
      <div className="fixed inset-0 z-50 overflow-y-auto">
        <div
          className="fixed inset-0 h-full w-full bg-black/80"
          onClick={() => setIsOpen(false)}
        ></div>
        <div className="flex min-h-screen items-center px-4 py-8">
          <div className="relative mx-auto w-full max-w-2xl rounded-lg border border-strokedark bg-boxdark shadow-xl">
            <form className="flex flex-col" onSubmit={submit}>
              {/* Header */}
              <div className="flex items-center justify-between border-b border-strokedark p-6">
                <h3 className="text-xl font-semibold text-white">
                  Edit Music Details
                </h3>
                <button
                  type="button"
                  onClick={() => setIsOpen(false)}
                  className="rounded-lg p-2 text-bodydark hover:bg-form-input hover:text-white focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-boxdark"
                >
                  <svg
                    className="h-5 w-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>

              {/* Form Content */}
              <div className="space-y-6 p-6">
                {/* Notes Field */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-white">
                    Notes
                  </label>
                  <textarea
                    name="notes"
                    value={formValues?.notes}
                    onChange={handleChange}
                    rows={4}
                    className="w-full rounded border border-form-strokedark bg-form-input p-3 text-white placeholder:text-bodydark2 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                    placeholder="Enter notes about the routine..."
                  />
                </div>

                {/* Song List Field */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-white">
                    Song List
                  </label>
                  <textarea
                    name="song_list"
                    value={formValues?.song_list}
                    onChange={handleChange}
                    rows={4}
                    className="w-full rounded border border-form-strokedark bg-form-input p-3 text-white placeholder:text-bodydark2 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                    placeholder="Enter song list..."
                  />
                </div>

                {/* Colors Field */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-white">
                    Colors
                  </label>
                  <input
                    type="text"
                    name="colors"
                    value={formValues?.colors}
                    onChange={handleChange}
                    className="w-full rounded border border-form-strokedark bg-form-input p-3 text-white placeholder:text-bodydark2 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                    placeholder="Enter team colors..."
                  />
                </div>

                {/* Mascot Field */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-white">
                    Mascot
                  </label>
                  <input
                    type="text"
                    name="mascot"
                    value={formValues?.mascot}
                    onChange={handleChange}
                    className="w-full rounded border border-form-strokedark bg-form-input p-3 text-white placeholder:text-bodydark2 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                    placeholder="Enter team mascot..."
                  />
                </div>

                {/* Social Media Section */}
                <div className="space-y-4">
                  <h4 className="text-lg font-medium text-white">
                    Social Media
                  </h4>

                  {/* Twitter Field */}
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-white">
                      Twitter
                    </label>
                    <input
                      type="url"
                      name="twitter"
                      value={formValues?.twitter}
                      onChange={handleChange}
                      className="w-full rounded border border-form-strokedark bg-form-input p-3 text-white placeholder:text-bodydark2 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                      placeholder="https://twitter.com/username"
                    />
                  </div>

                  {/* Instagram Field */}
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-white">
                      Instagram
                    </label>
                    <input
                      type="url"
                      name="instagram"
                      value={formValues?.instagram}
                      onChange={handleChange}
                      className="w-full rounded border border-form-strokedark bg-form-input p-3 text-white placeholder:text-bodydark2 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                      placeholder="https://instagram.com/username"
                    />
                  </div>
                </div>
              </div>

              {/* Footer */}
              <div className="flex items-center justify-end gap-4 border-t border-strokedark p-6">
                <button
                  type="button"
                  onClick={() => setIsOpen(false)}
                  className="rounded border border-strokedark bg-transparent px-4 py-2 text-sm font-medium text-bodydark hover:bg-form-input hover:text-white focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-boxdark"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={loader}
                  className="inline-flex items-center justify-center rounded bg-primary px-6 py-2 text-sm font-medium text-white transition-colors hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-boxdark disabled:cursor-not-allowed disabled:opacity-50"
                >
                  {loader ? (
                    <>
                      <ClipLoader size={16} color="white" className="mr-2" />
                      Saving...
                    </>
                  ) : (
                    "Save Changes"
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </>
  );
};

export default ClientEditMusicDetailsModal;
