import{r as c,a as w}from"../vendor-94843817.js";import{j as f}from"../@floating-ui/react-bfff8aa2.js";function S(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function P(...e){return t=>{let n=!1;const o=e.map(r=>{const s=S(r,t);return!n&&typeof s=="function"&&(n=!0),s});if(n)return()=>{for(let r=0;r<o.length;r++){const s=o[r];typeof s=="function"?s():S(e[r],null)}}}}function D(...e){return c.useCallback(P(...e),e)}function W(e,t){const n=c.createContext(t),o=s=>{const{children:i,...u}=s,l=c.useMemo(()=>u,Object.values(u));return f.jsx(n.Provider,{value:l,children:i})};o.displayName=e+"Provider";function r(s){const i=c.useContext(n);if(i)return i;if(t!==void 0)return t;throw new Error(`\`${s}\` must be used within \`${e}\``)}return[o,r]}function M(e,t=[]){let n=[];function o(s,i){const u=c.createContext(i),l=n.length;n=[...n,i];const m=p=>{var y;const{scope:a,children:v,...d}=p,x=((y=a==null?void 0:a[e])==null?void 0:y[l])||u,g=c.useMemo(()=>d,Object.values(d));return f.jsx(x.Provider,{value:g,children:v})};m.displayName=s+"Provider";function C(p,a){var x;const v=((x=a==null?void 0:a[e])==null?void 0:x[l])||u,d=c.useContext(v);if(d)return d;if(i!==void 0)return i;throw new Error(`\`${p}\` must be used within \`${s}\``)}return[m,C]}const r=()=>{const s=n.map(i=>c.createContext(i));return function(u){const l=(u==null?void 0:u[e])||s;return c.useMemo(()=>({[`__scope${e}`]:{...u,[e]:l}}),[u,l])}};return r.scopeName=e,[o,b(r,...t)]}function b(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const o=e.map(r=>({useScope:r(),scopeName:r.scopeName}));return function(s){const i=o.reduce((u,{useScope:l,scopeName:m})=>{const p=l(s)[`__scope${m}`];return{...u,...p}},{});return c.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}var E=c.forwardRef((e,t)=>{const{children:n,...o}=e,r=c.Children.toArray(n),s=r.find(R);if(s){const i=s.props.children,u=r.map(l=>l===s?c.Children.count(i)>1?c.Children.only(null):c.isValidElement(i)?i.props.children:null:l);return f.jsx(h,{...o,ref:t,children:c.isValidElement(i)?c.cloneElement(i,void 0,u):null})}return f.jsx(h,{...o,ref:t,children:n})});E.displayName="Slot";var h=c.forwardRef((e,t)=>{const{children:n,...o}=e;if(c.isValidElement(n)){const r=$(n);return c.cloneElement(n,{...N(o,n.props),ref:t?P(t,r):r})}return c.Children.count(n)>1?c.Children.only(null):null});h.displayName="SlotClone";var j=({children:e})=>f.jsx(f.Fragment,{children:e});function R(e){return c.isValidElement(e)&&e.type===j}function N(e,t){const n={...t};for(const o in t){const r=e[o],s=t[o];/^on[A-Z]/.test(o)?r&&s?n[o]=(...u)=>{s(...u),r(...u)}:r&&(n[o]=r):o==="style"?n[o]={...r,...s}:o==="className"&&(n[o]=[r,s].filter(Boolean).join(" "))}return{...e,...n}}function $(e){var o,r;let t=(o=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(r=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var O=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],A=O.reduce((e,t)=>{const n=c.forwardRef((o,r)=>{const{asChild:s,...i}=o,u=s?E:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),f.jsx(u,{...i,ref:r})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function H(e,t){e&&w.flushSync(()=>e.dispatchEvent(t))}export{A as P,E as S,W as a,M as c,H as d,D as u};
