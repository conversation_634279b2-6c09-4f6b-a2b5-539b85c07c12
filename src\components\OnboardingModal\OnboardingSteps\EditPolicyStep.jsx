import React, { useState, useEffect, useContext } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { ClipLoader } from "react-spinners";

import { GlobalContext, showToast } from "../../../globalContext";
import { uploadS3FilesAPI } from "../../../services/workOrderService";

// Validation schema
const schema = yup.object().shape({
  policy_type: yup
    .string()
    .required("Please select how you want to provide your policy"),
  edit_policy_link: yup.string().when("policy_type", {
    is: "text",
    then: (schema) => schema.required("Edit policy content is required"),
    otherwise: (schema) => schema.notRequired(),
  }),
});

const EditPolicyStep = ({ userDetails, onComplete, onSkip }) => {
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const [isLoading, setIsLoading] = useState(false);
  const [policyFile, setPolicyFile] = useState(null);
  const [uploadedFileName, setUploadedFileName] = useState("");
  const [uploadedFileSize, setUploadedFileSize] = useState("");

  const {
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      policy_type: "pdf",
      edit_policy_link: userDetails?.edit_policy_link || "",
    },
  });

  useEffect(() => {
    // Set initial values if they exist
    if (
      userDetails?.edit_policy_link &&
      userDetails.edit_policy_link.startsWith("http")
    ) {
      // It's a PDF URL - extract filename from URL for display
      const urlParts = userDetails.edit_policy_link.split("/");
      const filename = urlParts[urlParts.length - 1];
      setUploadedFileName(filename || "edit-policy.pdf");
    }
  }, [userDetails]);

  const handleFileUpload = (formData) => {
    const file = formData.get("files");
    if (file) {
      setPolicyFile(file);
      setUploadedFileName(file.name);
      setUploadedFileSize(`${(file.size / 1024).toFixed(0)} KB`);
    }
  };

  const onSubmit = async () => {
    try {
      setIsLoading(true);

      let finalPolicyLink = "";

      if (policyFile) {
        // Upload new PDF file
        try {
          const fileFormData = new FormData();
          fileFormData.append("files", policyFile);
          const uploadResult = await uploadS3FilesAPI(fileFormData);
          if (!uploadResult.error) {
            const attachmentsArr = JSON.parse(uploadResult.attachments);
            finalPolicyLink = attachmentsArr[0];
          } else {
            showToast(
              globalDispatch,
              "Failed to upload policy document",
              4000,
              "error"
            );
            return;
          }
        } catch (error) {
          console.error("Error uploading policy file:", error);
          showToast(
            globalDispatch,
            "Failed to upload policy document",
            4000,
            "error"
          );
          return;
        }
      } else {
        showToast(
          globalDispatch,
          "Please upload a PDF document",
          4000,
          "error"
        );
        return;
      }

      const policyData = {
        edit_policy_link: finalPolicyLink,
      };

      onComplete(policyData);
    } catch (error) {
      console.error("Error saving edit policy:", error);
      showToast(globalDispatch, "Failed to save edit policy", 4000, "error");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="mx-auto max-w-[550px] space-y-6">
      {/* Header */}
      <div className="text-center">
        <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
          <svg
            width="56"
            height="56"
            viewBox="0 0 56 56"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <rect x="4" y="4" width="48" height="48" rx="24" fill="#E0E6FC" />
            <rect
              x="4"
              y="4"
              width="48"
              height="48"
              rx="24"
              stroke="#F5F7FF"
              stroke-width="8"
            />
            <g clip-path="url(#clip0_3161_2954)">
              <path
                d="M24.25 26.5C24.0511 26.5 23.8603 26.579 23.7197 26.7197C23.579 26.8603 23.5 27.0511 23.5 27.25C23.5 27.4489 23.579 27.6397 23.7197 27.7803C23.8603 27.921 24.0511 28 24.25 28H31.75C31.9489 28 32.1397 27.921 32.2803 27.7803C32.421 27.6397 32.5 27.4489 32.5 27.25C32.5 27.0511 32.421 26.8603 32.2803 26.7197C32.1397 26.579 31.9489 26.5 31.75 26.5H24.25ZM23.5 30.25C23.5 30.0511 23.579 29.8603 23.7197 29.7197C23.8603 29.579 24.0511 29.5 24.25 29.5H31.75C31.9489 29.5 32.1397 29.579 32.2803 29.7197C32.421 29.8603 32.5 30.0511 32.5 30.25C32.5 30.4489 32.421 30.6397 32.2803 30.7803C32.1397 30.921 31.9489 31 31.75 31H24.25C24.0511 31 23.8603 30.921 23.7197 30.7803C23.579 30.6397 23.5 30.4489 23.5 30.25ZM23.5 33.25C23.5 33.0511 23.579 32.8603 23.7197 32.7197C23.8603 32.579 24.0511 32.5 24.25 32.5H27.25C27.4489 32.5 27.6397 32.579 27.7803 32.7197C27.921 32.8603 28 33.0511 28 33.25C28 33.4489 27.921 33.6397 27.7803 33.7803C27.6397 33.921 27.4489 34 27.25 34H24.25C24.0511 34 23.8603 33.921 23.7197 33.7803C23.579 33.6397 23.5 33.4489 23.5 33.25Z"
                fill="#3C50E0"
              />
              <path
                d="M30.25 16H22C21.2044 16 20.4413 16.3161 19.8787 16.8787C19.3161 17.4413 19 18.2044 19 19V37C19 37.7956 19.3161 38.5587 19.8787 39.1213C20.4413 39.6839 21.2044 40 22 40H34C34.7956 40 35.5587 39.6839 36.1213 39.1213C36.6839 38.5587 37 37.7956 37 37V22.75L30.25 16ZM30.25 17.5V20.5C30.25 21.0967 30.4871 21.669 30.909 22.091C31.331 22.5129 31.9033 22.75 32.5 22.75H35.5V37C35.5 37.3978 35.342 37.7794 35.0607 38.0607C34.7794 38.342 34.3978 38.5 34 38.5H22C21.6022 38.5 21.2206 38.342 20.9393 38.0607C20.658 37.7794 20.5 37.3978 20.5 37V19C20.5 18.6022 20.658 18.2206 20.9393 17.9393C21.2206 17.658 21.6022 17.5 22 17.5H30.25Z"
                fill="#3C50E0"
              />
            </g>
            <defs>
              <clipPath id="clip0_3161_2954">
                <rect
                  width="24"
                  height="24"
                  fill="white"
                  transform="translate(16 16)"
                />
              </clipPath>
            </defs>
          </svg>
        </div>
        <h2 className="text-2xl font-bold text-[#131E2B]">Edit Policy</h2>
        <p className="mt-2 text-sm text-[#667484]">
          Upload your PDF edit policy here, and it will be accessible to
          clients, setting clear expectations for their music revisions.
        </p>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="w-full">
          {/* PDF Upload Option - Only Option */}
          <div className="space-y-4">
            <div className="relative ml-7 space-y-3">
              <div className="bg-gray-white/80 relative rounded-lg border-2 border-dashed border-gray-300 p-6 text-center">
                <div className="mb-3">
                  <svg
                    className="mx-auto h-8 w-8 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                    />
                  </svg>
                </div>
                <p className="mb-2 text-white">
                  <button
                    type="button"
                    className="font-semibold text-primary hover:text-primary/80"
                  >
                    Click to upload
                  </button>
                  <span className="text-gray-300"> or drag and drop</span>
                </p>
                <p className="text-xs text-gray-500">
                  PDF files recommended (max 5MB)
                </p>
                <input
                  type="file"
                  accept=".pdf"
                  onChange={(e) => {
                    const file = e.target.files[0];
                    if (file) {
                      const formData = new FormData();
                      formData.append("files", file);
                      handleFileUpload(formData);
                    }
                  }}
                  className="absolute inset-0 h-full w-full cursor-pointer opacity-0"
                />
              </div>

              {uploadedFileName && (
                <div className="flex items-center gap-3 rounded-lg border border-green-800 bg-white/80 p-3">
                  <div className="flex-shrink-0">
                    <svg
                      className="h-5 w-5 text-green-800"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="break-words text-sm font-medium text-green-700">
                      {uploadedFileName}
                    </p>
                    {uploadedFileSize && (
                      <p className="text-xs text-green-400">
                        {uploadedFileSize}
                      </p>
                    )}
                  </div>
                  <div className="flex-shrink-0">
                    <svg
                      className="h-5 w-5 text-green-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                  </div>
                </div>
              )}
            </div>
          </div>

          {errors.policy_type && (
            <p className="mt-1 text-sm text-red-400">
              {errors.policy_type.message}
            </p>
          )}
          {errors.edit_policy_link && (
            <p className="mt-1 text-sm text-red-400">
              {errors.edit_policy_link.message}
            </p>
          )}
        </div>

        {/* Action Buttons - centered at bottom */}
        <div className="absolute bottom-[60px] mt-auto flex w-[550px] justify-center gap-4 pt-8">
          <button
            type="button"
            onClick={onSkip}
            disabled={isLoading}
            className="flex h-[44px] w-[200px] items-center justify-center rounded-lg border border-gray-600 px-8 py-3 font-medium text-[#3f4d5d] transition-colors hover:bg-white/80 disabled:cursor-not-allowed disabled:opacity-50"
          >
            Skip for Now
          </button>
          <button
            type="submit"
            disabled={isLoading}
            className=" flex h-[44px] w-[200px] items-center justify-center gap-2 rounded-lg bg-[#3C50E0] px-8 py-3 font-medium text-white transition-colors hover:bg-primary/90 disabled:cursor-not-allowed disabled:opacity-50"
          >
            {isLoading ? (
              <>
                <ClipLoader size={16} color="#ffffff" />
                Saving...
              </>
            ) : (
              "Continue"
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default EditPolicyStep;
