import React from "react";

const UploadProgressBar = ({ progress, isUploading, error }) => {
  if (!isUploading && !error) return null;

  return (
    <div className="mt-1">
      {isUploading && (
        <div className="relative pt-1">
          <div className="mb-2 flex items-center justify-between gap-1">
            <div>
              <span className="inline-block rounded-full bg-white px-2 py-1 text-[10px] font-semibold uppercase text-[#3C50E0]">
                Uploading
              </span>
            </div>
            <div className="text-right">
              <span className="inline-block text-xs font-semibold text-[#3C50E0]">
                {progress}%
              </span>
            </div>
          </div>
          <div className="mb-1 flex h-2 overflow-hidden rounded bg-white text-xs">
            <div
              style={{ width: `${progress}%` }}
              className="flex flex-col justify-center whitespace-nowrap bg-blue-500 text-center text-white shadow-none transition-all duration-500 ease-in-out"
            ></div>
          </div>
        </div>
      )}
      {error && (
        <div className="mt-2 text-sm text-red-500">
          {error.message || "An error occurred during upload."}
        </div>
      )}
    </div>
  );
};

export default UploadProgressBar;
