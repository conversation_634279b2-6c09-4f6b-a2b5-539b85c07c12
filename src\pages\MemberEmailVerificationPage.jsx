import React, { useState, useEffect } from "react";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import { GlobalContext, showToast } from "../globalContext";
import { AuthContext } from "../authContext";
import MkdSDK from "../utils/MkdSDK";
import { ClipLoader } from "react-spinners";
import { isMainMember, hasActiveSubscription } from "../utils/onboardingUtils";

let sdk = new MkdSDK();

const MemberEmailVerificationPage = () => {
  const { dispatch: globalDispatch, state } = React.useContext(GlobalContext);
  const { dispatch: authDispatch } = React.useContext(AuthContext);
  const [verificationStatus, setVerificationStatus] = useState("pending");
  const [isLoading, setIsLoading] = useState(false);
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  useEffect(() => {
    const token = searchParams.get("token");

    if (token) {
      // If token is in URL, verify immediately
      handleEmailVerification(token);
    } else {
      // Check if we have pending verification data
      const pendingUser = localStorage.getItem("pending_verification_user");
      if (!pendingUser) {
        // No pending verification, redirect to login
        navigate("/member/login");
      }
    }
  }, [searchParams, navigate]);

  const handleEmailVerification = async (token) => {
    try {
      setIsLoading(true);
      setVerificationStatus("verifying");

      const result = await sdk.confirmEmailVerification(token);

      if (!result.error) {
        setVerificationStatus("verified");
        showToast(
          globalDispatch,
          "Email verified successfully!",
          3000,
          "success"
        );

        // Clear pending verification data
        localStorage.removeItem("pending_verification_user");
        localStorage.removeItem("registration_token");

        // Store the token from verification response for session persistence
        if (result.token) {
          localStorage.setItem("token", result.token);
          localStorage.setItem("user", result.user_id || result.id);

          // Set auth context if user data is provided
          if (result.user || result.model) {
            authDispatch({
              type: "LOGIN",
              payload: result,
            });
          }
        }

        // Redirect to login after verification
        setTimeout(() => {
          showToast(
            globalDispatch,
            "Email verified! Please login to continue.",
            3000,
            "success"
          );
          navigate("/member/login");
        }, 2000);
      } else {
        throw new Error(result.message || "Email verification failed");
      }
    } catch (error) {
      console.error("Email verification error:", error);
      setVerificationStatus("failed");
      showToast(
        globalDispatch,
        error.message || "Email verification failed. Please try again.",
        4000,
        "error"
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendVerification = async () => {
    try {
      setIsLoading(true);
      const pendingUser = JSON.parse(
        localStorage.getItem("pending_verification_user") || "{}"
      );

      if (!pendingUser.email) {
        throw new Error("No email found for resending verification");
      }

      const result = await sdk.sendVerificationEmail(pendingUser.email);

      if (!result.error) {
        showToast(
          globalDispatch,
          "Verification email sent successfully! Please check your inbox.",
          4000,
          "success"
        );
      } else {
        throw new Error(
          result.message || "Failed to resend verification email"
        );
      }
    } catch (error) {
      console.error("Resend verification error:", error);
      showToast(
        globalDispatch,
        error.message || "Failed to resend verification email",
        4000,
        "error"
      );
    } finally {
      setIsLoading(false);
    }
  };

  const renderContent = () => {
    switch (verificationStatus) {
      case "verifying":
        return (
          <div className="text-center">
            <div className="mb-6">
              <ClipLoader color="#3B82F6" size={50} />
            </div>
            <h2 className="mb-4 text-2xl font-bold text-black dark:text-white">
              Verifying Your Email
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              Please wait while we verify your email address...
            </p>
          </div>
        );

      case "verified":
        return (
          <div className="text-center">
            <div className="mb-6">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/20">
                <svg
                  className="h-8 w-8 text-green-600 dark:text-green-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
            </div>
            <h2 className="mb-4 text-2xl font-bold text-green-600 dark:text-green-400">
              Email Verified Successfully!
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              Your account has been created and verified. Redirecting you to
              complete your setup...
            </p>
          </div>
        );

      case "failed":
        return (
          <div className="text-center">
            <div className="mb-6">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20">
                <svg
                  className="h-8 w-8 text-red-600 dark:text-red-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </div>
            </div>
            <h2 className="mb-4 text-2xl font-bold text-red-600 dark:text-red-400">
              Verification Failed
            </h2>
            <p className="mb-6 text-gray-600 dark:text-gray-400">
              We couldn't verify your email address. The link may be expired or
              invalid.
            </p>
            <button
              onClick={handleResendVerification}
              disabled={isLoading}
              className="mb-4 w-full rounded-lg border border-primary bg-primary px-6 py-3 text-white transition hover:bg-opacity-90 disabled:opacity-50"
            >
              {isLoading ? (
                <ClipLoader size={18} color="#fff" />
              ) : (
                "Resend Verification Email"
              )}
            </button>
          </div>
        );

      default:
        return (
          <div className="text-center">
            <div className="mb-6">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/20">
                <svg
                  className="h-8 w-8 text-[#3C50E0] dark:text-blue-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                  />
                </svg>
              </div>
            </div>
            <h2 className="mb-4 text-2xl font-bold text-black dark:text-white">
              Check Your Email
            </h2>
            <p className="mb-6 text-gray-600 dark:text-gray-400">
              We've sent a verification link to your email address. Please click
              the link to verify your account.
            </p>
            <button
              onClick={handleResendVerification}
              disabled={isLoading}
              className="mb-4 w-full rounded-lg border border-primary bg-transparent px-6 py-3 text-primary transition hover:bg-primary hover:text-white disabled:opacity-50"
            >
              {isLoading ? (
                <ClipLoader size={18} color="#3B82F6" />
              ) : (
                "Resend Verification Email"
              )}
            </button>
          </div>
        );
    }
  };

  return (
    <div className="flex h-screen items-center justify-center bg-gray-50 dark:bg-boxdark-2">
      <div className="w-full max-w-md">
        <div className="rounded-lg bg-white p-8 shadow-lg dark:bg-boxdark">
          <div className="mb-8 text-center">
            <img
              src={`${window.location.origin}/new/cheerEQ-2-Ed2.png`}
              className="mx-auto h-12 w-auto"
              alt="Logo"
            />
          </div>

          {renderContent()}

          <div className="mt-8 text-center">
            <Link
              to="/member/login"
              className="text-sm text-gray-600 hover:text-primary dark:text-gray-400 dark:hover:text-primary"
            >
              Back to Login
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MemberEmailVerificationPage;
