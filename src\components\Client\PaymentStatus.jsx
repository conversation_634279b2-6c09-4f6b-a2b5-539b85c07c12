import React from "react";

function getStatusText(status) {
  let statusText = "";
  let statusColor = "";

  switch (status) {
    case 0:
      statusText = "Unpaid";
      statusColor = "text-red-600";
      break;
    case 1:
      statusText = "Complete";
      statusColor = "text-white";
      break;
    case 3:
      statusText = "Paid in Full";
      statusColor = "text-blue-400";
      break;
    case 4:
      statusText = "Awaiting Edit";
      statusColor = "text-green-600";
      break;
    case 2:
      statusText = "Deposit Paid";
      statusColor = "text-[#3C50E0] ";
      break;
    default:
      statusText = "Unpaid";
      statusColor = "text-red-600"; // Default color for unknown status
  }

  return { text: statusText, color: statusColor };
}

function PaymentStatus({ status }) {
  const { text, color } = getStatusText(status);

  return <span className={`font-medium ${color}`}>{text}</span>;
}

export default PaymentStatus;
