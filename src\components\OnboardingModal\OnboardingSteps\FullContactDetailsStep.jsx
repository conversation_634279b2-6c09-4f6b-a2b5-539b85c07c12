import React, { useState, useEffect, useContext } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { ClipLoader } from "react-spinners";
import { GlobalContext, showToast } from "../../../globalContext";
import {
  updateUserDetailsAPI,
  getUserDetailsByIdAPI,
} from "../../../services/userService";

// Validation schema
const schema = yup.object().shape({
  company_name: yup.string().required("Company name is required"),
  phone: yup.string().required("Phone number is required"),
  address: yup.string().required("Address is required"),
});

const FullContactDetailsStep = ({ userDetails, onComplete, onSkip }) => {
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      company_name: userDetails?.company_name || "",
      phone: userDetails?.phone || "",
      address: userDetails?.company_address || "",
      // apt_house_suite: userDetails?.apt_house_suite || "",
      // city: userDetails?.city || "",
      // state: userDetails?.state || "",
      // zip_code: userDetails?.zip_code || "",
    },
  });

  const onSubmit = async (data) => {
    console.log("jsjsj");
    try {
      setIsLoading(true);

      // Update user details

      // Prepare data for onboarding step completion
      const contactData = {
        company_name: data.company_name,
        phone: data.phone,
        company_address: `${data.address} `,
        // apt_house_suite: data.apt_house_suite,
        // city: data.city,
        // state: data.state,
        // zip_code: data.zip_code,
        // contact_details_complete: true,
      };

      onComplete(contactData);
    } catch (error) {
      console.error("Error saving contact details:", error);
      showToast(
        globalDispatch,
        "Failed to save contact details",
        4000,
        "error"
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="mx-auto max-w-[400px] space-y-6">
      {/* Header */}
      <div className="text-center">
        <svg
          className="mx-auto mb-6 h-16 w-16"
          width="56"
          height="56"
          viewBox="0 0 56 56"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect x="4" y="4" width="48" height="48" rx="24" fill="#E0E6FC" />
          <rect
            x="4"
            y="4"
            width="48"
            height="48"
            rx="24"
            stroke="#F5F7FF"
            stroke-width="8"
          />
          <g clip-path="url(#clip0_3161_3910)">
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M20.65 32.7999C20.7199 32.7068 20.8104 32.6312 20.9146 32.5791C21.0187 32.527 21.1336 32.4999 21.25 32.4999H25C25.1989 32.4999 25.3897 32.5789 25.5303 32.7196C25.671 32.8603 25.75 33.051 25.75 33.2499C25.75 33.4488 25.671 33.6396 25.5303 33.7803C25.3897 33.9209 25.1989 33.9999 25 33.9999H21.625L18.25 38.4999H37.75L34.375 33.9999H31C30.8011 33.9999 30.6103 33.9209 30.4697 33.7803C30.329 33.6396 30.25 33.4488 30.25 33.2499C30.25 33.051 30.329 32.8603 30.4697 32.7196C30.6103 32.5789 30.8011 32.4999 31 32.4999H34.75C34.8664 32.4999 34.9813 32.527 35.0854 32.5791C35.1896 32.6312 35.2801 32.7068 35.35 32.7999L39.85 38.7999C39.9336 38.9114 39.9845 39.0439 39.997 39.1826C40.0095 39.3213 39.9831 39.4608 39.9208 39.5853C39.8585 39.7099 39.7628 39.8147 39.6443 39.8879C39.5258 39.9611 39.3893 39.9999 39.25 39.9999H16.75C16.6107 39.9999 16.4742 39.9611 16.3557 39.8879C16.2372 39.8147 16.1415 39.7099 16.0792 39.5853C16.0169 39.4608 15.9905 39.3213 16.003 39.1826C16.0155 39.0439 16.0664 38.9114 16.15 38.7999L20.65 32.7999Z"
              fill="#3C50E0"
            />
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M28 17.4999C27.4091 17.4999 26.8239 17.6163 26.2779 17.8425C25.732 18.0686 25.2359 18.4001 24.818 18.818C24.4002 19.2358 24.0687 19.7319 23.8425 20.2779C23.6164 20.8238 23.5 21.409 23.5 21.9999C23.5 22.5909 23.6164 23.176 23.8425 23.722C24.0687 24.268 24.4002 24.764 24.818 25.1819C25.2359 25.5998 25.732 25.9312 26.2779 26.1574C26.8239 26.3835 27.4091 26.4999 28 26.4999C29.1935 26.4999 30.3381 26.0258 31.182 25.1819C32.0259 24.338 32.5 23.1934 32.5 21.9999C32.5 20.8065 32.0259 19.6619 31.182 18.818C30.3381 17.974 29.1935 17.4999 28 17.4999ZM22 21.9999C22.0001 20.8453 22.3334 19.7152 22.9598 18.7452C23.5863 17.7753 24.4793 17.0067 25.5318 16.5317C26.5842 16.0567 27.7513 15.8955 28.8931 16.0673C30.0349 16.2392 31.1029 16.7369 31.9688 17.5007C32.8348 18.2645 33.462 19.2619 33.7751 20.3733C34.0883 21.4846 34.0741 22.6628 33.7343 23.7663C33.3945 24.8698 32.7434 25.8519 31.8593 26.5946C30.9752 27.3373 29.8956 27.8091 28.75 27.9534V36.2499C28.75 36.4488 28.671 36.6396 28.5303 36.7803C28.3897 36.9209 28.1989 36.9999 28 36.9999C27.8011 36.9999 27.6103 36.9209 27.4697 36.7803C27.329 36.6396 27.25 36.4488 27.25 36.2499V27.9549C25.7994 27.7722 24.4655 27.0661 23.4988 25.9693C22.5321 24.8725 21.9991 23.462 22 21.9999Z"
              fill="#3C50E0"
            />
          </g>
          <defs>
            <clipPath id="clip0_3161_3910">
              <rect
                width="24"
                height="24"
                fill="white"
                transform="translate(16 16)"
              />
            </clipPath>
          </defs>
        </svg>

        <h2 className="text-xl font-semibold text-[#4A5B70]">
          Full Contact Details
        </h2>
        <p className="mt-1 text-sm text-[#667484]">
          Automate standard project milestones for new projects.
        </p>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Business Information */}
        <div className="space-y-4">
          <div>
            <h3 className="text-base font-semibold text-[#3C50E0]">
              Business Information
            </h3>
            <p className="text-sm text-[#667484]">
              Complete your business contact information for new projects.
            </p>
          </div>

          <div className="space-y-4">
            <div>
              <label className="mb-2 block text-sm font-medium text-[#4A5B70]">
                Company Name
              </label>
              <input
                type="text"
                {...register("company_name")}
                className="w-full rounded border border-[#D5D7DA] bg-white px-3 py-2 text-[#4A5B70] focus:border-blue-500 focus:ring-blue-500"
                placeholder="Enter company name"
              />
              {errors.company_name && (
                <p className="mt-1 text-sm text-red-500">
                  {errors.company_name.message}
                </p>
              )}
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium text-[#4A5B70]">
                Phone
              </label>
              <input
                type="tel"
                {...register("phone")}
                className="w-full rounded border border-[#D5D7DA] bg-white px-3 py-2 text-[#4A5B70] focus:border-blue-500 focus:ring-blue-500"
                placeholder="(*************"
              />
              {errors.phone && (
                <p className="mt-1 text-sm text-red-500">
                  {errors.phone.message}
                </p>
              )}
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium text-[#4A5B70]">
                Address
              </label>
              <input
                type="text"
                {...register("address")}
                className="w-full rounded border border-[#D5D7DA] bg-white px-3 py-2 text-[#4A5B70] focus:border-blue-500 focus:ring-blue-500"
                placeholder="Enter street address"
              />
              {errors.address && (
                <p className="mt-1 text-sm text-red-500">
                  {errors.address.message}
                </p>
              )}
            </div>

            {/* <div>
              <label className="mb-2 block text-sm font-medium text-[#4A5B70]">
                Apt, House, Suite, Unit (Optional)
              </label>
              <input
                type="text"
                {...register("apt_house_suite")}
                className="w-full rounded border border-[#D5D7DA] bg-white px-3 py-2 text-[#4A5B70] focus:border-blue-500 focus:ring-blue-500"
                placeholder="Apt, House, Suite, Unit"
              />
            </div> */}

            {/* <div className="grid grid-cols-3 gap-4">
              <div>
                <label className="mb-2 block text-sm font-medium text-[#4A5B70]">
                  City
                </label>
                <input
                  type="text"
                  {...register("city")}
                  className="w-full rounded border border-[#D5D7DA] bg-white px-3 py-2 text-[#4A5B70] focus:border-blue-500 focus:ring-blue-500"
                  placeholder="Austin"
                />
                {errors.city && (
                  <p className="mt-1 text-sm text-red-500">
                    {errors.city.message}
                  </p>
                )}
              </div>
              <div>
                <label className="mb-2 block text-sm font-medium text-[#4A5B70]">
                  State
                </label>
                <input
                  type="text"
                  {...register("state")}
                  className="w-full rounded border border-[#D5D7DA] bg-white px-3 py-2 text-[#4A5B70] focus:border-blue-500 focus:ring-blue-500"
                  placeholder="TX"
                />
                {errors.state && (
                  <p className="mt-1 text-sm text-red-500">
                    {errors.state.message}
                  </p>
                )}
              </div>
              <div>
                <label className="mb-2 block text-sm font-medium text-[#4A5B70]">
                  ZIP Code
                </label>
                <input
                  type="text"
                  {...register("zip_code")}
                  className="w-full rounded border border-[#D5D7DA] bg-white px-3 py-2 text-[#4A5B70] focus:border-blue-500 focus:ring-blue-500"
                  placeholder="78704"
                />
                {errors.zip_code && (
                  <p className="mt-1 text-sm text-red-500">
                    {errors.zip_code.message}
                  </p>
                )}
              </div>
            </div> */}
          </div>
        </div>

        {/* Action Buttons - centered at bottom */}
        <div className="flex justify-center gap-3 pt-6">
          <button
            type="button"
            onClick={onSkip}
            disabled={isLoading}
            className="flex h-[44px] w-[200px] items-center justify-center rounded-lg border border-[#D5D7DA] bg-white px-6 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50"
          >
            Skip for Now
          </button>
          <button
            type="submit"
            disabled={isLoading}
            className="flex h-[44px] w-[200px] items-center justify-center gap-2 rounded-lg bg-[#3C50E0] px-6 py-2 text-sm font-medium text-white transition-colors hover:bg-[#3C50E0]/80 disabled:cursor-not-allowed disabled:opacity-50"
          >
            {isLoading ? (
              <>
                <ClipLoader size={14} color="#ffffff" />
                Saving...
              </>
            ) : (
              "Continue"
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default FullContactDetailsStep;
