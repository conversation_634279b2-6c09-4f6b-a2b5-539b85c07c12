import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { GlobalContext, showToast } from "Src/globalContext";
import { Elements } from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";
import { CardDetailsModal } from "./MemberSubscriptionPage";
import { getUserDetailsByIdAPI } from "Src/services/userService";
import planPortalImg from "../../../public/figma-icons/plan-portal.png";
import planStudioImg from "../../../public/figma-icons/plan-studio.png";
import planCompleteImg from "../../../public/figma-icons/plan-complete.png";

const stripePromise = loadStripe(
  "pk_test_51QufojCyqqiotS2DrnWrDsIDJBRyZmFN93rnCuWphXRMZuEW77hYvE8iG7CuP6F5iSmKR8ZmEEdzRDc2NcdmUVDr003Qz6h4o5"
);

const SubscriptionDetailsPage = ({
  selectedPlan,
  planDetails,
  onBack,
  onProceed,
  billingInterval,
  projectRange,
}) => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [isModalOpen, setIsModalOpen] = React.useState(false);
  const [isProcessing, setIsProcessing] = React.useState(false);
  const [userDetails, setUserDetails] = useState(null);
  const [isMainMember, setIsMainMember] = useState(true); // Default to true for safety
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();

  const PLAN_STYLES = {
    "The Portal": {
      border: "border-[#2B3EB4]",
      shadow:
        "shadow-[0_0_0_4px_#f5f5f5,0_1px_2px_0px_rgba(10,13,18,0.06),0_1px_3px_0px_rgba(10,13,18,0.1)]",
      icon: planPortalImg,
    },
    "The Studio": {
      border: "border-[#2B3EB4]",
      shadow:
        "shadow-[0_0_0_4px_#f4ebff,0_1px_2px_0px_rgba(10,13,18,0.06),0_1px_3px_0px_rgba(10,13,18,0.1)]",
      icon: planStudioImg,
    },
    "Complete Suite": {
      border: "border-[#2B3EB4]",
      shadow:
        "shadow-[0_0_0_4px_#f4ebff,0_1px_2px_0px_rgba(10,13,18,0.06),0_1px_3px_0px_rgba(10,13,18,0.1)]",
      icon: planCompleteImg,
    },
  };

  // Check if user is a main member on component mount
  useEffect(() => {
    const checkUserMembershipStatus = async () => {
      try {
        setIsLoading(true);
        const userId = localStorage.getItem("user");
        if (userId) {
          const result = await getUserDetailsByIdAPI(Number(userId));
          if (!result.error && result.model) {
            setUserDetails(result.model);

            // Check if user is a main member (not a sub-member)
            const isMain =
              !result.model.main_user_details ||
              result.model.main_user_details.is_self === true;
            setIsMainMember(isMain);

            console.log("User details in subscription details:", result.model);
            console.log("Is main member:", isMain);

            // If user is not a main member, redirect them to dashboard
            if (!isMain) {
              console.log("Sub-member detected, redirecting to dashboard");
              showToast(
                globalDispatch,
                "Access denied. Subscription management is handled by your main account holder.",
                4000,
                "error"
              );
              navigate("/member/dashboard");
              return;
            }
          }
        }
      } catch (error) {
        console.error("Error checking user membership status:", error);
        showToast(
          globalDispatch,
          "Error checking user permissions",
          4000,
          "error"
        );
        navigate("/member/dashboard");
      } finally {
        setIsLoading(false);
      }
    };

    checkUserMembershipStatus();
  }, [globalDispatch, navigate]);

  const handleProceedToPayment = () => {
    setIsModalOpen(true);
  };

  const handleCardSubmit = async (result) => {
    if (result?.error) {
      showToast(globalDispatch, result?.error?.message, 4000, "error");
      return;
    }

    setIsProcessing(true);
    try {
      // Pass the token to the parent component
      await onProceed(result.token.id);
    } catch (error) {
      showToast(globalDispatch, error.message, 4000, "error");
    } finally {
      setIsProcessing(false);
      setIsModalOpen(false);
    }
  };

  // Get the pricing based on the selected plan, project range and billing interval
  const getPricing = () => {
    const planKey = {
      "The Portal": "portal",
      "The Studio": "studio",
      "Complete Suite": "complete",
    }[planDetails.name];

    const projectRanges = {
      "1-50": {
        portal: { monthly: 150, annual: 1500 },
        studio: { monthly: 150, annual: 1500 },
        complete: { monthly: 250, annual: 2500 },
      },
      "51-100": {
        portal: { monthly: 175, annual: 1750 },
        studio: { monthly: 175, annual: 1750 },
        complete: { monthly: 275, annual: 2750 },
      },
      "101-150": {
        portal: { monthly: 200, annual: 2000 },
        studio: { monthly: 200, annual: 2000 },
        complete: { monthly: 300, annual: 3000 },
      },
      "151-200": {
        portal: { monthly: 225, annual: 2250 },
        studio: { monthly: 225, annual: 2250 },
        complete: { monthly: 325, annual: 3250 },
      },
      "201+": {
        portal: { monthly: 250, annual: 2500 },
        studio: { monthly: 250, annual: 2500 },
        complete: { monthly: 350, annual: 3500 },
      },
    };

    return projectRanges[projectRange][planKey][
      billingInterval === "year" ? "annual" : "monthly"
    ];
  };

  const planFeatures = {
    "The Portal": [
      "Project Management",
      "Project Calendar",
      "Client Login Portal",
      "Digital 8-count sheets",
      "Automated Music Licenses",
      "Automated Reminder Emails",
      "Automated Music Surveys",
      "Project Edit Management",
      "8-Count Track Management",
      "Custom Email Domain",
    ],
    "The Studio": [
      "Automated Music Surveys",
      "Project Management",
      "Project Calendar",
      "Project Budget Review",
      "Automated Vocal Orders",
      "Excel Style Order View",
      "Automated Reminder Emails",
      "Company Logo Customization",
      "Custom Email Domain",
    ],
    "Complete Suite": [
      "Everything in The Portal",
      "Everything in The Studio",
      "Priority Support",
      "Dedicated Account Manager",
    ],
  };

  const addOnOptions = {
    "The Portal": [
      "Payment System Add-on ($399.99/month)",
      "Additional Members ($25/month per member)",
    ],
    "The Studio": ["Additional Members ($25/month per member)"],
    "Complete Suite": [
      "Payment System Add-on ($399.99/month)",
      "Additional Members ($25/month per member)",
    ],
  };

  // Show loading state while checking user permissions
  if (isLoading) {
    return (
      <div className="mx-auto max-w-4xl">
        <div className="flex h-64 items-center justify-center">
          <div className="h-16 w-16 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
        </div>
      </div>
    );
  }

  // If user is not a main member, this component will redirect
  // but we can add a fallback just in case
  if (!isMainMember) {
    return (
      <div className="mx-auto max-w-4xl">
        <div className="rounded-lg bg-boxdark p-6">
          <div className="text-center">
            <h2 className="mb-4 text-2xl font-bold text-white">
              Access Denied
            </h2>
            <p className="text-bodydark">
              Subscription management is handled by your main account holder.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto max-w-4xl">
      <div className="mb-8 rounded-lg bg-white p-6">
        <div className="mb-6 flex items-center justify-between">
          <h2 className="font-satoshi text-2xl font-bold text-[#1A1A1A]">
            Subscription Details
          </h2>
          <button
            onClick={onBack}
            className="rounded-lg bg-[#3C50E0] px-4 py-2 font-satoshi text-white hover:bg-opacity-80"
          >
            Back to Plans
          </button>
        </div>
        <div className="mb-6">
          <h3 className="mb-4 font-satoshi text-xl font-bold text-[#1A1A1A]">
            You're About to Purchase:
          </h3>
          <div
            className={`mb-6 rounded-[16px] border ${
              PLAN_STYLES[planDetails.name].border
            } bg-white p-6 ${PLAN_STYLES[planDetails.name].shadow}`}
          >
            <div className="mb-4 flex items-center justify-between">
              <div className="flex items-center gap-3">
                <img
                  src={PLAN_STYLES[planDetails.name].icon}
                  alt="plan"
                  className="h-8 w-8"
                />
                <h4 className="font-satoshi text-[24px] font-bold text-[#6F6C90]">
                  {planDetails.name}
                </h4>
              </div>
              <div>
                <span className="font-satoshi text-2xl font-bold text-[#3C50E0]">
                  ${getPricing()}
                </span>
                <span className="font-satoshi text-[#6F6C90]">
                  /{billingInterval === "year" ? "year" : "month"}
                </span>
              </div>
            </div>
            <div className="mb-4">
              <p className="font-satoshi text-[#6F6C90]">
                Project Range: {projectRange} projects
              </p>
              <p className="font-satoshi text-[#6F6C90]">
                Billing: {billingInterval === "year" ? "Annual" : "Monthly"}
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
          {["The Portal", "The Studio", "Complete Suite"].map((plan, idx) => (
            <div
              key={plan}
              className={`rounded-[16px] border ${PLAN_STYLES[plan].border} flex flex-col gap-4 bg-white p-6 ${PLAN_STYLES[plan].shadow}`}
            >
              <div className="flex items-center gap-3">
                <img
                  src={PLAN_STYLES[plan].icon}
                  alt={plan}
                  className="h-8 w-8"
                />
                <span className="font-satoshi text-[24px] font-bold text-[#6F6C90]">
                  {plan}
                </span>
              </div>
              <span className="mt-2 font-satoshi text-[20px] font-bold text-[#6F6C90]">
                Features:
              </span>
              <ul className="space-y-2">
                {planFeatures[plan].map((feature, i) => (
                  <li
                    key={i}
                    className="flex items-center font-satoshi text-base text-[#6F6C90]"
                  >
                    <svg
                      className="mr-2 h-5 w-5 text-[#3C50E0]"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    {feature}
                  </li>
                ))}
              </ul>
              <button
                className="font-inter mt-4 w-full rounded-[99px] border border-[#3C50E0] bg-black py-2 text-[16px] font-medium text-[#3C50E0] shadow-sm transition hover:bg-[#232323]"
                onClick={() => onProceed(plan)}
              >
                Subscribe Now
              </button>
            </div>
          ))}
        </div>

        <div className="mt-8 rounded-lg border border-strokedark bg-boxdark-2 p-6">
          <h3 className="mb-4 text-xl font-bold text-white">
            Important Information
          </h3>
          <div className="space-y-4 text-bodydark">
            <p>
              <span className="font-semibold text-white">Upgrades:</span> You
              can choose to upgrade to another plan or add additional mix
              packages at a later time through your account settings.
            </p>
            <p>
              <span className="font-semibold text-white">Billing:</span> Your
              subscription will be billed{" "}
              {billingInterval === "year" ? "annually" : "monthly"} and will
              automatically renew until canceled.
            </p>
            <p>
              <span className="font-semibold text-white">Cancellation:</span> If
              you cancel your membership, your files will be deleted 1 month
              after cancellation. You'll receive notifications before this
              happens, giving you time to download any important data.
            </p>
            <p>
              <span className="font-semibold text-white">Non-payment:</span> If
              your account goes into non-payment status, your projects and files
              will be preserved for 2 months, during which you'll receive
              reminder emails. After this period, they may be deleted from our
              servers.
            </p>
          </div>
        </div>

        <div className="mt-8 flex justify-center">
          <button
            onClick={handleProceedToPayment}
            className="rounded-full bg-primary px-8 py-3 font-satoshi font-semibold text-white hover:bg-opacity-90"
          >
            Proceed to Payment
          </button>
        </div>
      </div>

      <Elements stripe={stripePromise}>
        <CardDetailsModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onSubmit={handleCardSubmit}
          isProcessing={isProcessing}
        />
      </Elements>
    </div>
  );
};

export default SubscriptionDetailsPage;
