import{p as S,i as $}from"./fontawesome-svg-core-0d830203.js";import{g as H,R as C}from"../vendor-94843817.js";var W={exports:{}},K="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",q=K,B=q;function E(){}function F(){}F.resetWarningCache=E;var M=function(){function e(a,o,s,y,b,i){if(i!==B){var l=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw l.name="Invariant Violation",l}}e.isRequired=e;function t(){return e}var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:F,resetWarningCache:E};return r.PropTypes=r,r};W.exports=M();var V=W.exports;const n=H(V);function k(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,a)}return r}function f(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?k(Object(r),!0).forEach(function(a){m(e,a,r[a])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):k(Object(r)).forEach(function(a){Object.defineProperty(e,a,Object.getOwnPropertyDescriptor(r,a))})}return e}function O(e){"@babel/helpers - typeof";return O=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},O(e)}function m(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Y(e,t){if(e==null)return{};var r={},a=Object.keys(e),o,s;for(s=0;s<a.length;s++)o=a[s],!(t.indexOf(o)>=0)&&(r[o]=e[o]);return r}function G(e,t){if(e==null)return{};var r=Y(e,t),a,o;if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(o=0;o<s.length;o++)a=s[o],!(t.indexOf(a)>=0)&&Object.prototype.propertyIsEnumerable.call(e,a)&&(r[a]=e[a])}return r}function _(e){return J(e)||Q(e)||X(e)||Z()}function J(e){if(Array.isArray(e))return j(e)}function Q(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function X(e,t){if(e){if(typeof e=="string")return j(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return j(e,t)}}function j(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,a=new Array(t);r<t;r++)a[r]=e[r];return a}function Z(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ee(e){var t,r=e.beat,a=e.fade,o=e.beatFade,s=e.bounce,y=e.shake,b=e.flash,i=e.spin,l=e.spinPulse,u=e.spinReverse,g=e.pulse,x=e.fixedWidth,P=e.inverse,d=e.border,w=e.listItem,c=e.flip,p=e.size,v=e.rotation,I=e.pull,A=(t={"fa-beat":r,"fa-fade":a,"fa-beat-fade":o,"fa-bounce":s,"fa-shake":y,"fa-flash":b,"fa-spin":i,"fa-spin-reverse":u,"fa-spin-pulse":l,"fa-pulse":g,"fa-fw":x,"fa-inverse":P,"fa-border":d,"fa-li":w,"fa-flip":c===!0,"fa-flip-horizontal":c==="horizontal"||c==="both","fa-flip-vertical":c==="vertical"||c==="both"},m(t,"fa-".concat(p),typeof p<"u"&&p!==null),m(t,"fa-rotate-".concat(v),typeof v<"u"&&v!==null&&v!==0),m(t,"fa-pull-".concat(I),typeof I<"u"&&I!==null),m(t,"fa-swap-opacity",e.swapOpacity),t);return Object.keys(A).map(function(h){return A[h]?h:null}).filter(function(h){return h})}function te(e){return e=e-0,e===e}function z(e){return te(e)?e:(e=e.replace(/[\-_\s]+(.)?/g,function(t,r){return r?r.toUpperCase():""}),e.substr(0,1).toLowerCase()+e.substr(1))}var re=["style"];function ne(e){return e.charAt(0).toUpperCase()+e.slice(1)}function ae(e){return e.split(";").map(function(t){return t.trim()}).filter(function(t){return t}).reduce(function(t,r){var a=r.indexOf(":"),o=z(r.slice(0,a)),s=r.slice(a+1).trim();return o.startsWith("webkit")?t[ne(o)]=s:t[o]=s,t},{})}function D(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};if(typeof t=="string")return t;var a=(t.children||[]).map(function(i){return D(e,i)}),o=Object.keys(t.attributes||{}).reduce(function(i,l){var u=t.attributes[l];switch(l){case"class":i.attrs.className=u,delete t.attributes.class;break;case"style":i.attrs.style=ae(u);break;default:l.indexOf("aria-")===0||l.indexOf("data-")===0?i.attrs[l.toLowerCase()]=u:i.attrs[z(l)]=u}return i},{attrs:{}}),s=r.style,y=s===void 0?{}:s,b=G(r,re);return o.attrs.style=f(f({},o.attrs.style),y),e.apply(void 0,[t.tag,f(f({},o.attrs),b)].concat(_(a)))}var L=!1;try{L=!0}catch{}function oe(){if(!L&&console&&typeof console.error=="function"){var e;(e=console).error.apply(e,arguments)}}function R(e){if(e&&O(e)==="object"&&e.prefix&&e.iconName&&e.icon)return e;if(S.icon)return S.icon(e);if(e===null)return null;if(e&&O(e)==="object"&&e.prefix&&e.iconName)return e;if(Array.isArray(e)&&e.length===2)return{prefix:e[0],iconName:e[1]};if(typeof e=="string")return{prefix:"fas",iconName:e}}function T(e,t){return Array.isArray(t)&&t.length>0||!Array.isArray(t)&&t?m({},e,t):{}}var N={border:!1,className:"",mask:null,maskId:null,fixedWidth:!1,inverse:!1,flip:!1,icon:null,listItem:!1,pull:null,pulse:!1,rotation:null,size:null,spin:!1,spinPulse:!1,spinReverse:!1,beat:!1,fade:!1,beatFade:!1,bounce:!1,shake:!1,symbol:!1,title:"",titleId:null,transform:null,swapOpacity:!1},U=C.forwardRef(function(e,t){var r=f(f({},N),e),a=r.icon,o=r.mask,s=r.symbol,y=r.className,b=r.title,i=r.titleId,l=r.maskId,u=R(a),g=T("classes",[].concat(_(ee(r)),_((y||"").split(" ")))),x=T("transform",typeof r.transform=="string"?S.transform(r.transform):r.transform),P=T("mask",R(o)),d=$(u,f(f(f(f({},g),x),P),{},{symbol:s,title:b,titleId:i,maskId:l}));if(!d)return oe("Could not find icon",u),null;var w=d.abstract,c={ref:t};return Object.keys(r).forEach(function(p){N.hasOwnProperty(p)||(c[p]=r[p])}),se(w[0],c)});U.displayName="FontAwesomeIcon";U.propTypes={beat:n.bool,border:n.bool,beatFade:n.bool,bounce:n.bool,className:n.string,fade:n.bool,flash:n.bool,mask:n.oneOfType([n.object,n.array,n.string]),maskId:n.string,fixedWidth:n.bool,inverse:n.bool,flip:n.oneOf([!0,!1,"horizontal","vertical","both"]),icon:n.oneOfType([n.object,n.array,n.string]),listItem:n.bool,pull:n.oneOf(["right","left"]),pulse:n.bool,rotation:n.oneOf([0,90,180,270]),shake:n.bool,size:n.oneOf(["2xs","xs","sm","lg","xl","2xl","1x","2x","3x","4x","5x","6x","7x","8x","9x","10x"]),spin:n.bool,spinPulse:n.bool,spinReverse:n.bool,symbol:n.oneOfType([n.bool,n.string]),title:n.string,titleId:n.string,transform:n.oneOfType([n.string,n.object]),swapOpacity:n.bool};var se=D.bind(null,C.createElement);export{U as F,n as P,V as p};
