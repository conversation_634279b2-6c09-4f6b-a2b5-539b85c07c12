import React from "react";
import {
  GlobalContext,
  setUserDetails,
  setUserDetailsLoading,
} from "../globalContext";
import { getUserDetailsByIdAPI } from "../services/userService";
import { AuthContext } from "Src/authContext";

/**
 * Custom hook for managing user details with caching
 * Provides centralized user data management to avoid repeated API calls
 */
export const useUserDetails = (options = {}) => {
  const {
    forceRefresh = false,
    cacheTimeout = 5 * 60 * 1000, // 5 minutes default cache
    autoFetch = true,
  } = options;

  const globalContext = React.useContext(GlobalContext);
  const [localLoading, setLocalLoading] = React.useState(false);
  const { dispatch } = React.useContext(AuthContext);

  const { state: globalState, dispatch: globalDispatch } = globalContext || {
    state: {
      userDetails: {
        data: null,
        isLoading: false,
        lastFetched: null,
        error: null,
      },
    },
    dispatch: () => {},
  };

  // Safety check for context - return early object if context not available
  const contextAvailable = !!globalContext;

  const userDetails = globalState.userDetails;

  // Check if data is fresh enough
  const isDataFresh = React.useMemo(() => {
    if (!userDetails.data || !userDetails.lastFetched) return false;
    const now = Date.now();
    return now - userDetails.lastFetched < cacheTimeout;
  }, [userDetails.lastFetched, cacheTimeout]);

  // Fetch user details function
  const fetchUserDetails = React.useCallback(
    async (userId = null) => {
      const targetUserId = userId || localStorage.getItem("user");
      if (!targetUserId) return null;

      // Prevent multiple simultaneous calls - check if already loading
      if (userDetails.isLoading) {
        console.log("User details already loading, skipping duplicate call");
        return userDetails.data;
      }

      try {
        setUserDetailsLoading(globalDispatch, true);
        setLocalLoading(true);

        const result = await getUserDetailsByIdAPI(targetUserId);

        if (!result.error && result.model) {
          setUserDetails(globalDispatch, result.model);
          dispatch({
            type: "SET_PROFILE",
            payload: {
              photo: result.model.photo,
              companyName: result.model.company_name,
            },
          });
          return result.model;
        } else {
          setUserDetails(globalDispatch, null, {
            error: result.message || "Failed to fetch user details",
          });
          return null;
        }
      } catch (error) {
        console.error("Error fetching user details:", error);
        setUserDetails(globalDispatch, null, { error: error.message });
        return null;
      } finally {
        setUserDetailsLoading(globalDispatch, false);
        setLocalLoading(false);
      }
    },
    [globalDispatch, userDetails.isLoading, userDetails.data]
  );

  // Get user details with caching logic
  const getUserDetails = React.useCallback(
    async (userId = null) => {
      // If we have fresh data and not forcing refresh, return cached data
      if (!forceRefresh && isDataFresh && userDetails.data) {
        return userDetails.data;
      }

      // Otherwise fetch fresh data
      return await fetchUserDetails(userId);
    },
    [forceRefresh, isDataFresh, userDetails.data, fetchUserDetails]
  );

  // Update user details in cache after successful API update
  const updateUserDetailsCache = React.useCallback(
    (updatedData) => {
      if (userDetails.data) {
        const mergedData = { ...userDetails.data, ...updatedData };
        setUserDetails(globalDispatch, mergedData);
      }
    },
    [userDetails.data, globalDispatch]
  );

  // Create refetch function that forces a fresh fetch
  const refetchUserDetails = React.useCallback(async () => {
    return await fetchUserDetails();
  }, [fetchUserDetails]);

  // Auto-fetch on mount if enabled and no fresh data
  // Remove fetchUserDetails from dependencies to prevent multiple calls
  React.useEffect(() => {
    if (autoFetch && (!isDataFresh || !userDetails.data)) {
      const userId = localStorage.getItem("user");
      if (userId && !userDetails.isLoading) {
        // Only fetch if not already loading to prevent multiple simultaneous calls
        fetchUserDetails(userId);
      }
    }
  }, [autoFetch, isDataFresh, userDetails.data, userDetails.isLoading]);

  // Return safe fallback if context not available
  if (!contextAvailable) {
    console.warn("GlobalContext not available in useUserDetails");
    return {
      userDetails: null,
      isLoading: false,
      error: "Context not available",
      lastFetched: null,
      isDataFresh: false,
      fetchUserDetails: () => Promise.resolve(null),
      getUserDetails: () => Promise.resolve(null),
      updateUserDetailsCache: () => {},
      refetchUserDetails: () => Promise.resolve(null),
      refreshUserDetails: () => Promise.resolve(null),
      clearCache: () => {},
    };
  }

  return {
    // Data
    userDetails: userDetails.data,
    isLoading: userDetails.isLoading || localLoading,
    error: userDetails.error,
    lastFetched: userDetails.lastFetched,
    isDataFresh,

    // Actions
    fetchUserDetails,
    getUserDetails,
    updateUserDetailsCache,
    refetchUserDetails, // Add the refetch function

    // Utilities
    refreshUserDetails: () => fetchUserDetails(),
    clearCache: () => setUserDetails(globalDispatch, null),
  };
};

export default useUserDetails;
