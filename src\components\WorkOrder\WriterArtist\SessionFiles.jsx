import React, { useContext, useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import AudioPlayer from "Components/AudioPlayer";
import FileUpload from "Components/FileUpload/FileUpload";
import ConfirmModal from "Components/Modal/ConfirmModal";
import { Download, MoreVertical } from "lucide-react";
import { GlobalContext, showToast } from "Src/globalContext";

const SessionFiles = ({
  uploadedFiles,
  setDeleteFileId,
  setEmployeeType,
  setFileUploadType,
  setFormData,
  uploadedFilesProgressData = {},
}) => {
  const { dispatch } = useContext(GlobalContext);
  const [activeMenu, setActiveMenu] = useState(null);
  const [localDeleteFileId, setLocalDeleteFileId] = React.useState(null);
  const [showDeleteFileConfirmModal, setShowDeleteFileConfirmModal] =
    React.useState(false);
  const audioFileTypes = ["mp3", "wav", "ogg", "flac", "aac", "wma", "m4a"];

  const handleDownload = async (url, filename) => {
    try {
      showToast(dispatch, "File Downloading", 3000);
      const response = await fetch(url);
      const blob = await response.blob();
      const blobUrl = window.URL.createObjectURL(blob);

      const link = document.createElement("a");
      link.href = blobUrl;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(blobUrl);
    } catch (error) {
      console.error("Download failed:", error);
    }
    setActiveMenu(null); // Close menu after download starts
  };

  // Close menu when clicking outside
  React.useEffect(() => {
    const handleClickOutside = () => setActiveMenu(null);
    document.addEventListener("click", handleClickOutside);
    return () => document.removeEventListener("click", handleClickOutside);
  }, []);

  return (
    <>
      <div className="shadow-default rounded border border-stroke bg-boxdark">
        {/* Header */}
        <div className="border-b border-stroke px-6 py-4">
          <div className="flex items-center gap-3">
            <FontAwesomeIcon
              icon="fa-solid fa-headphones"
              className="text-lg text-primary"
            />
            <h3 className="text-lg font-medium text-white">Session Files</h3>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Files List */}
          {uploadedFiles && uploadedFiles.length > 0 ? (
            <div className="mb-6 space-y-4">
              {uploadedFiles.map((file, index) => {
                const fileName = file.url.split("/").pop();
                const fileExt = file.url.split(".").pop().toLowerCase();
                const isAudio = audioFileTypes.includes(fileExt);

                return (
                  <div
                    key={index}
                    className="rounded border border-stroke bg-boxdark-2 p-4 transition-all hover:border-primary"
                  >
                    <div className="flex items-center justify-between gap-4">
                      <div className="min-w-0 flex-1">
                        <div className="mb-2 flex items-center gap-2">
                          <FontAwesomeIcon
                            icon={
                              isAudio ? "fa-solid fa-music" : "fa-solid fa-file"
                            }
                            className="text-bodydark2"
                          />
                          <a
                            href={file.url}
                            rel="noreferrer"
                            target="_blank"
                            className="truncate text-sm font-medium text-white underline hover:text-primary"
                          >
                            {fileName}
                          </a>
                          <div className="relative">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                setActiveMenu(
                                  activeMenu === index ? null : index
                                );
                              }}
                              className="rounded-full p-1 hover:bg-gray-700"
                            >
                              <MoreVertical className="h-5 w-5 text-primary" />
                            </button>

                            {activeMenu === index && (
                              <div className="absolute right-0 z-50 mt-1 w-48 rounded-md bg-boxdark shadow-lg ring-1 ring-black ring-opacity-5">
                                <div className="py-1">
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleDownload(file.url, fileName);
                                    }}
                                    className="flex w-full items-center px-4 py-2 text-sm text-white hover:bg-gray-700"
                                  >
                                    <Download className="mr-2 h-4 w-4" />
                                    Download
                                  </button>
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      setShowDeleteFileConfirmModal(true);
                                      setLocalDeleteFileId(file.id);
                                    }}
                                    className="flex w-full items-center px-4 py-2 text-sm text-danger hover:bg-gray-700"
                                  >
                                    <FontAwesomeIcon
                                      icon="fa-solid fa-trash"
                                      className="mr-2 h-4 w-4"
                                    />
                                    Delete
                                  </button>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>

                        {isAudio && (
                          <div className="mt-2">
                            <AudioPlayer fileSource={file.url} />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            // Empty State
            <div className="mb-6 flex flex-col items-center justify-center py-8">
              <div className="mb-3 rounded-full bg-boxdark p-3">
                <FontAwesomeIcon
                  icon="fa-solid fa-file-audio"
                  className="h-6 w-6 text-bodydark2"
                />
              </div>
              <p className="text-sm text-bodydark2">
                No session files uploaded yet
              </p>
            </div>
          )}

          {/* Upload Section */}
          <div className="rounded border border-stroke bg-boxdark-2 p-6">
            <div className="flex flex-col items-center">
              <div className="mb-4 text-center">
                <h5 className="mb-1 text-sm font-medium text-white">
                  Upload More Files
                </h5>
                <p className="text-xs text-bodydark2">
                  Drag and drop your files or click to browse
                </p>
              </div>

              <FileUpload
                uploadedFilesProgressData={uploadedFilesProgressData}
                justify="center"
                items="center"
                maxFileSize={2048}
                setFormData={setFormData}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteFileConfirmModal && (
        <ConfirmModal
          confirmText="Are you sure you want to delete this file?"
          setModalClose={() => setShowDeleteFileConfirmModal(false)}
          setFormYes={() => {
            setDeleteFileId(localDeleteFileId);
            setShowDeleteFileConfirmModal(false);
          }}
        />
      )}
    </>
  );
};

export default SessionFiles;
