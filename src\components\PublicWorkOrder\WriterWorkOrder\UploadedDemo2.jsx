import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import AudioPlayer from "Components/AudioPlayer";
import FileUpload from "Components/FileUpload/FileUpload";
import ConfirmModal from "Components/Modal/ConfirmModal";

const audioFileTypes = ["mp3", "wav", "ogg", "flac", "aac", "wma", "m4a"];

const UploadedDemo2 = ({
  canUpload = true,
  uploadedFiles,
  showUploadBtn = true,
  setDeleteFileId,
  setEmployeeType,
  setFileUploadType,
  setFormData,
  uploadedFilesProgressData = {},
}) => {
  const [localDeleteFileId, setLocalDeleteFileId] = React.useState(null);
  const [showDeleteFileConfirmModal, setShowDeleteFileConfirmModal] =
    React.useState(false);

  return (
    <>
      <div
        onClick={() => {
          setEmployeeType("writer");
          setFileUploadType("demo");
        }}
        className="flex flex-col"
      >
        {/* Upload Section */}
        {showUploadBtn && canUpload && uploadedFiles.length < 2 && (
          <div className="p-2 mb-2 w-full rounded border border-stroke bg-boxdark-2">
            <div className="flex flex-col items-center">
              <div className="mb-2 text-center">
                <h5 className="text-sm font-medium text-white">
                  Upload More Files
                </h5>
                <p className="text-xs text-bodydark2">
                  Drag and drop your files or click to browse
                </p>
              </div>

              <FileUpload
                uploadedFilesProgressData={uploadedFilesProgressData}
                justify="center"
                items="center"
                maxFileSize={500}
                setFormData={setFormData}
              />
            </div>
          </div>
        )}

        {/* Files List */}
        <div className="flex flex-wrap gap-2">
          {uploadedFiles?.map((file, index) => {
            const fileName = file.url.split("/").pop();
            const fileExt = file.url.split(".").pop().toLowerCase();
            const isAudio = audioFileTypes.includes(fileExt);

            return (
              <div
                key={index}
                className="w-[30%]  min-w-[230px] rounded border border-stroke bg-boxdark p-2.5 hover:border-primary"
              >
                <div className="flex flex-nowrap gap-2 items-center w-full">
                  <div className="flex flex-col gap-2 items-center">
                    {/* File Icon & Name */}
                    <div className="flex flex-1 gap-2 items-center min-w-0">
                      <FontAwesomeIcon
                        icon={
                          isAudio ? "fa-solid fa-music" : "fa-solid fa-file"
                        }
                        className="text-sm text-bodydark2"
                      />
                      <a
                        style={{ whiteSpace: "break-spaces" }}
                        href={file.url}
                        rel="noreferrer"
                        target="_blank"
                        className="truncate text-[10px] font-medium text-white hover:text-primary"
                      >
                        {fileName}
                      </a>
                    </div>

                    {/* Audio Player - Compact Version */}
                    {isAudio && (
                      <div className="flex-shrink-0 w-48">
                        <AudioPlayer fileSource={file.url} compact={true} />
                      </div>
                    )}

                    {/* Delete Button */}
                  </div>{" "}
                  {canUpload && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setShowDeleteFileConfirmModal(true);
                        setLocalDeleteFileId(file.id);
                      }}
                      className="group ml-2 rounded-full p-1.5 hover:bg-danger/40"
                    >
                      <FontAwesomeIcon
                        icon="fa-solid fa-trash"
                        className="h-3.5 w-3.5 text-danger group-hover:text-danger"
                      />
                    </button>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {canUpload && uploadedFiles && uploadedFiles.length >= 2 && (
          <div className="p-2 mt-3 text-center rounded border border-danger bg-boxdark">
            <span className="text-xs font-medium text-danger">
              Demo upload limit reached
            </span>
          </div>
        )}
      </div>

      {showDeleteFileConfirmModal && (
        <ConfirmModal
          confirmText="Are you sure you want to delete this file?"
          setModalClose={() => setShowDeleteFileConfirmModal(false)}
          setFormYes={() => {
            setDeleteFileId(localDeleteFileId);
            setShowDeleteFileConfirmModal(false);
          }}
        />
      )}
    </>
  );
};

export default UploadedDemo2;
