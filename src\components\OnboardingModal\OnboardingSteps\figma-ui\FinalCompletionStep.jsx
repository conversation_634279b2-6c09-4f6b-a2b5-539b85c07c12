import React from "react";

const FinalCompletionStep = ({ onGoToDashboard }) => {
  return (
    <div className="mx-auto flex w-full max-w-[397px] flex-col items-center gap-8 bg-white p-0">
      <div className="flex w-full flex-col items-center" style={{ gap: 8 }}>
        <svg
          width="56"
          height="56"
          viewBox="0 0 56 56"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect x="4" y="4" width="48" height="48" rx="24" fill="#E0E6FC" />
          <rect
            x="4"
            y="4"
            width="48"
            height="48"
            rx="24"
            stroke="#F5F7FF"
            strokeWidth="8"
          />
          <g clipPath="url(#clip0_3161_4192)">
            <path
              d="M27.4855 25.3707C27.6505 24.8757 28.3495 24.8757 28.5145 25.3707L29.482 28.2762C29.6949 28.9149 30.0537 29.4952 30.5299 29.9712C31.0061 30.4471 31.5866 30.8056 32.2255 31.0182L35.1295 31.9857C35.6245 32.1507 35.6245 32.8497 35.1295 33.0147L32.224 33.9822C31.5853 34.1952 31.0049 34.554 30.529 35.0302C30.053 35.5064 29.6946 36.0869 29.482 36.7257L28.5145 39.6297C28.4789 39.7381 28.41 39.8325 28.3176 39.8994C28.2252 39.9663 28.114 40.0023 28 40.0023C27.8859 40.0023 27.7747 39.9663 27.6823 39.8994C27.5899 39.8325 27.521 39.7381 27.4855 39.6297L26.518 36.7242C26.3052 36.0857 25.9466 35.5054 25.4706 35.0295C24.9947 34.5536 24.4145 34.195 23.776 33.9822L20.8705 33.0147C20.7621 32.9791 20.6677 32.9102 20.6008 32.8178C20.5338 32.7254 20.4978 32.6143 20.4978 32.5002C20.4978 32.3861 20.5338 32.275 20.6008 32.1826C20.6677 32.0902 20.7621 32.0213 20.8705 31.9857L23.776 31.0182C24.4145 30.8054 24.9947 30.4468 25.4706 29.9709C25.9466 29.495 26.3052 28.9147 26.518 28.2762L27.4855 25.3707ZM21.691 17.7222C21.7125 17.6572 21.7539 17.6007 21.8094 17.5606C21.8648 17.5206 21.9315 17.499 22 17.499C22.0684 17.499 22.1351 17.5206 22.1905 17.5606C22.246 17.6007 22.2874 17.6572 22.309 17.7222L22.8895 19.4652C23.149 20.2422 23.758 20.8512 24.535 21.1107L26.278 21.6912C26.3429 21.7127 26.3994 21.7541 26.4395 21.8096C26.4796 21.8651 26.5011 21.9318 26.5011 22.0002C26.5011 22.0686 26.4796 22.1353 26.4395 22.1908C26.3994 22.2463 26.3429 22.2877 26.278 22.3092L24.535 22.8897C24.1517 23.0174 23.8035 23.2325 23.5179 23.5182C23.2323 23.8038 23.0171 24.152 22.8895 24.5352L22.309 26.2782C22.2874 26.3432 22.246 26.3997 22.1905 26.4397C22.1351 26.4798 22.0684 26.5014 22 26.5014C21.9315 26.5014 21.8648 26.4798 21.8094 26.4397C21.7539 26.3997 21.7125 26.3432 21.691 26.2782L21.1105 24.5352C20.9828 24.152 20.7676 23.8038 20.482 23.5182C20.1964 23.2325 19.8482 23.0174 19.465 22.8897L17.722 22.3092C17.657 22.2877 17.6005 22.2463 17.5604 22.1908C17.5203 22.1353 17.4988 22.0686 17.4988 22.0002C17.4988 21.9318 17.5203 21.8651 17.5604 21.8096C17.6005 21.7541 17.657 21.7127 17.722 21.6912L19.465 21.1107C19.8482 20.983 20.1964 20.7679 20.482 20.4822C20.7676 20.1966 20.9828 19.8484 21.1105 19.4652L21.691 17.7222ZM32.2945 16.1487C32.3093 16.106 32.337 16.0689 32.3738 16.0427C32.4107 16.0165 32.4548 16.0024 32.5 16.0024C32.5452 16.0024 32.5892 16.0165 32.6261 16.0427C32.6629 16.0689 32.6907 16.106 32.7055 16.1487L33.0925 17.3097C33.265 17.8287 33.6715 18.2352 34.1905 18.4077L35.3515 18.7947C35.3942 18.8095 35.4312 18.8373 35.4574 18.8741C35.4836 18.9109 35.4977 18.955 35.4977 19.0002C35.4977 19.0454 35.4836 19.0895 35.4574 19.1263C35.4312 19.1631 35.3942 19.1909 35.3515 19.2057L34.1905 19.5927C33.9347 19.6778 33.7023 19.8213 33.5117 20.0119C33.3211 20.2025 33.1775 20.4349 33.0925 20.6907L32.7055 21.8517C32.6907 21.8944 32.6629 21.9314 32.6261 21.9577C32.5892 21.9839 32.5452 21.998 32.5 21.998C32.4548 21.998 32.4107 21.9839 32.3738 21.9577C32.337 21.9314 32.3093 21.8944 32.2945 21.8517L31.9075 20.6907C31.8224 20.4349 31.6788 20.2025 31.4882 20.0119C31.2976 19.8213 31.0652 19.6778 30.8095 19.5927L29.65 19.2057C29.6072 19.1909 29.5702 19.1631 29.544 19.1263C29.5178 19.0895 29.5037 19.0454 29.5037 19.0002C29.5037 18.955 29.5178 18.9109 29.544 18.8741C29.5702 18.8373 29.6072 18.8095 29.65 18.7947L30.811 18.4077C31.33 18.2352 31.7365 17.8287 31.909 17.3097L32.2945 16.1502V16.1487Z"
              fill="#3C50E0"
            />
          </g>
          <defs>
            <clipPath id="clip0_3161_4192">
              <rect width="24" height="24" fill="white" transform="translate(16 16)" />
            </clipPath>
          </defs>
        </svg>

        <h2 className="font-inter mb-1 text-center text-[18px] font-semibold leading-[28px] text-[#131E2B]">
          Onboarding Completion
        </h2>
        <h3 className="font-inter mb-2 max-w-[350px] text-center text-[14px] font-normal leading-[20px] text-[#667484]">
          Congratulations! You're All Set.
        </h3>
        <p className="font-inter mb-2 max-w-[350px] text-center text-[14px] font-normal leading-[20px] text-[#4A5B70]">
          You've successfully completed your business setup and unlocked CheerEQ's full power!
        </p>
        <p className="font-inter max-w-[350px] text-center text-[14px] font-normal leading-[20px] text-[#667484]">
          You're now ready to connect with clients, manage projects, and streamline your workflow.
        </p>
      </div>
      <div className="mt-8 flex w-full flex-col gap-4">
        <button
          onClick={onGoToDashboard}
          className="font-inter w-full rounded-[12px] border border-[#3C50E0] bg-[#3C50E0] py-3 text-base font-semibold text-white transition hover:bg-[#3C50E0]/80"
          type="button"
        >
          Go to Dashboard
        </button>
      </div>
    </div>
  );
};

export default FinalCompletionStep;
