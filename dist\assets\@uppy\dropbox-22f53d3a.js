import{t,g as e,P as r}from"./aws-s3-4ad7e04c.js";import{P as n}from"./dashboard-f375f58d.js";import{y as i}from"../@fullcalendar/core-ff88745d.js";import{U as a}from"./core-bcb0cc7b.js";const l={strings:{pluginNameDropbox:"Dropbox"}},p={version:"3.4.0"};class h extends a{constructor(o,s){super(o,s),this.id=this.opts.id||"Dropbox",this.type="acquirer",this.storage=this.opts.storage||t,this.files=[],this.icon=()=>i("svg",{className:"uppy-DashboardTab-iconDropbox","aria-hidden":"true",focusable:"false",width:"32",height:"32",viewBox:"0 0 32 32"},i("path",{d:"M10.5 7.5L5 10.955l5.5 3.454 5.5-3.454 5.5 3.454 5.5-3.454L21.5 7.5 16 10.955zM10.5 21.319L5 17.864l5.5-3.455 5.5 3.455zM16 17.864l5.5-3.455 5.5 3.455-5.5 3.455zM16 25.925l-5.5-3.455 5.5-3.454 5.5 3.454z",fill:"currentcolor",fillRule:"nonzero"})),this.opts.companionAllowedHosts=e(this.opts.companionAllowedHosts,this.opts.companionUrl),this.provider=new r(o,{companionUrl:this.opts.companionUrl,companionHeaders:this.opts.companionHeaders,companionKeysParams:this.opts.companionKeysParams,companionCookiesRule:this.opts.companionCookiesRule,provider:"dropbox",pluginId:this.id,supportsRefreshToken:!0}),this.defaultLocale=l,this.i18nInit(),this.title=this.opts.title||this.i18n("pluginNameDropbox"),this.onFirstRender=this.onFirstRender.bind(this),this.render=this.render.bind(this)}install(){this.view=new n(this,{provider:this.provider,loadAllFiles:!0,virtualList:!0});const{target:o}=this.opts;o&&this.mount(o,this)}uninstall(){this.view.tearDown(),this.unmount()}async onFirstRender(){await Promise.all([this.provider.fetchPreAuthToken(),this.view.getFolder()])}render(o){return this.view.render(o)}}h.VERSION=p.version;export{h as D};
