/**
 * Utility functions for onboarding flow
 */

/**
 * Check if user has completed onboarding
 * @param {Object} userDetails - User details object
 * @returns {boolean} - Whether onboarding is complete
 */
export const isOnboardingComplete = (userDetails) => {
  if (!userDetails?.steps) return false;
  
  try {
    const stepData = JSON.parse(userDetails.steps);
    return stepData.onboarding_complete === true;
  } catch (error) {
    console.error("Error parsing step data:", error);
    return false;
  }
};

/**
 * Check if business profile is complete
 * @param {Object} userDetails - User details object
 * @returns {boolean} - Whether business profile is complete
 */
export const isBusinessProfileComplete = (userDetails) => {
  if (!userDetails?.steps) return false;
  
  try {
    const stepData = JSON.parse(userDetails.steps);
    return stepData.business_profile_complete === true;
  } catch (error) {
    console.error("Error parsing step data:", error);
    return false;
  }
};

/**
 * Get onboarding step data
 * @param {Object} userDetails - User details object
 * @returns {Object} - Step data object
 */
export const getOnboardingStepData = (userDetails) => {
  if (!userDetails?.steps) return {};
  
  try {
    return JSON.parse(userDetails.steps);
  } catch (error) {
    console.error("Error parsing step data:", error);
    return {};
  }
};

/**
 * Check if user is a main member (not a sub-member)
 * @param {Object} userDetails - User details object
 * @returns {boolean} - Whether user is a main member
 */
export const isMainMember = (userDetails) => {
  return !userDetails?.main_user_details || userDetails.main_user_details.is_self;
};

/**
 * Check if user has an active subscription
 * @param {Object} userDetails - User details object
 * @returns {boolean} - Whether user has a subscription
 */
export const hasActiveSubscription = (userDetails) => {
  return Boolean(userDetails?.plan_id);
};

/**
 * Determine the next step in the onboarding flow
 * @param {Object} userDetails - User details object
 * @returns {string} - Next step: 'dashboard', 'subscription', 'onboarding'
 */
export const getNextOnboardingStep = (userDetails) => {
  // Sub-members always go to dashboard
  if (!isMainMember(userDetails)) {
    return 'dashboard';
  }

  // Check if onboarding is complete
  if (isOnboardingComplete(userDetails)) {
    return 'dashboard';
  }

  // Check if user has subscription
  if (!hasActiveSubscription(userDetails)) {
    return 'subscription';
  }

  // Has subscription but onboarding not complete
  return 'onboarding';
};

/**
 * Get completed steps count
 * @param {Object} stepData - Step data object
 * @param {number} totalSteps - Total number of steps
 * @returns {number} - Number of completed steps
 */
export const getCompletedStepsCount = (stepData, totalSteps = 7) => {
  let completedCount = 0;
  for (let i = 1; i <= totalSteps; i++) {
    if (stepData[`step_${i}_complete`]) {
      completedCount++;
    }
  }
  return completedCount;
};

/**
 * Get the current step (first incomplete step)
 * @param {Object} stepData - Step data object
 * @param {number} totalSteps - Total number of steps
 * @returns {number} - Current step number
 */
export const getCurrentStep = (stepData, totalSteps = 7) => {
  for (let i = 1; i <= totalSteps; i++) {
    if (!stepData[`step_${i}_complete`]) {
      return i;
    }
  }
  return totalSteps; // All steps complete
};

/**
 * Validate step completion requirements
 * @param {number} stepId - Step ID
 * @param {Object} data - Step data
 * @returns {Object} - Validation result { isValid, errors }
 */
export const validateStepCompletion = (stepId, data) => {
  const errors = [];

  switch (stepId) {
    case 1: // Business Info
      if (!data.company_name) errors.push("Company name is required");
      if (!data.company_address) errors.push("Company address is required");
      if (!data.office_email) errors.push("Office email is required");
      if (!data.phone) errors.push("Phone number is required");
      break;
    
    case 5: // Project Management
      if (data.management_fee_percentage === undefined) {
        errors.push("Management fee percentage is required");
      }
      if (!data.team_deadline_days) errors.push("Team deadline days is required");
      if (!data.artist_deadline_days) errors.push("Artist deadline days is required");
      if (!data.engineer_deadline_days) errors.push("Engineer deadline days is required");
      break;
    
    // Add validation for other steps as needed
    default:
      break;
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};
