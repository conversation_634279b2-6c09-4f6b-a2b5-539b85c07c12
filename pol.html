<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=1440, initial-scale=1.0" />
    <title>UpdateStack - Pixel Perfect</title>
    <link
      href="https://fonts.googleapis.com/css?family=Inter:400,600,700&display=swap"
      rel="stylesheet"
    />
    <style>
      body {
        background: #1b1917;
        color: #fff4ec;
        font-family: "Inter", Arial, sans-serif;
        margin: 0;
        padding: 0;
        min-width: 1440px;
        min-height: 659px;
      }
      .container {
        width: 1201px;
        margin: 60px auto 40px auto;
        padding: 0 119px;
        display: flex;
        flex-direction: column;
        gap: 16px;
        background: none;
      }
      .header {
        display: flex;
        flex-direction: row;
        gap: 100px;
        align-items: flex-start;
        margin-bottom: 60px;
      }
      .header-left {
        width: 242px;
        display: flex;
        flex-direction: column;
        gap: 36px;
      }
      .header-left .headline {
        color: #cccccc;
        font-size: 16px;
        font-weight: 400;
        line-height: 1.5em;
        margin-bottom: 12px;
      }
      .header-left .logo {
        width: 176px;
        height: 24px;
      }
      .features {
        width: 260px;
        display: flex;
        flex-direction: column;
        gap: 32px;
      }
      .features-title {
        font-family: "IowanOldSt BT", serif;
        font-size: 20px;
        font-weight: 700;
        color: #fff4ec;
        margin-bottom: 20px;
      }
      .features-section {
        margin-bottom: 20px;
      }
      .features-section .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #fff4ec;
        margin-bottom: 12px;
      }
      .features-section ul {
        list-style: none;
        padding: 0;
        margin: 0;
      }
      .features-section li {
        font-size: 16px;
        font-weight: 400;
        color: rgba(255, 244, 236, 0.8);
        margin-bottom: 12px;
      }
      .update-templates {
        width: 173px;
        display: flex;
        flex-direction: column;
        gap: 19px;
        margin-left: 40px;
      }
      .update-templates .templates-title {
        font-family: "IowanOldSt BT", serif;
        font-size: 20px;
        font-weight: 700;
        color: #fff4ec;
        margin-bottom: 12px;
      }
      .update-templates .template {
        font-size: 16px;
        font-weight: 400;
        color: rgba(255, 244, 236, 0.8);
        margin-bottom: 6px;
      }
      .update-templates .template.alt {
        color: rgba(255, 244, 236, 0.48);
      }
      .divider {
        width: 100%;
        height: 1px;
        background: rgba(237, 237, 237, 0.42);
        margin: 40px 0 20px 0;
      }
      .footer {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        margin-top: 40px;
        color: #cccccc;
        font-size: 14px;
        font-weight: 400;
      }
      .footer .socials {
        display: flex;
        flex-direction: row;
        gap: 12px;
      }
      .footer .socials img {
        width: 24px;
        height: 24px;
        filter: brightness(0) invert(1) opacity(0.8);
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <div class="header-left">
          <img
            src="/updatestack_logo-invert.svg"
            alt="UpdateStack Logo"
            class="logo"
          />
          <div class="headline">
            Save time by automating your team and investor updates now.
          </div>
        </div>
        <div class="features">
          <div class="features-title">Features</div>
          <div class="features-section">
            <div class="section-title">Corporate Managers</div>
            <ul>
              <li>Team/Investor Update Automation</li>
              <li>Update Performance Tracking</li>
              <li>Update Engagement Tools</li>
              <li>Investor Fundraising Tools</li>
              <li>Investor Database</li>
            </ul>
          </div>
          <div class="features-section">
            <div class="section-title">Fund Managers</div>
            <ul>
              <li>Portfolio Performance Tracking</li>
              <li>Update Management</li>
              <li>Update Engagement Tools</li>
              <li>Limited Partner Nurturing</li>
            </ul>
          </div>
        </div>
        <div class="update-templates">
          <div class="templates-title">Update Templates</div>
          <div class="template">Investor</div>
          <div class="template">Finance</div>
          <div class="template">Product</div>
          <div class="template alt">Design</div>
          <div class="template alt">Engineering</div>
          <div class="template">Marketing</div>
          <div class="template">Operations</div>
          <div class="template">Team/HR</div>
        </div>
      </div>
      <!-- Update Templates Section Pixel Perfect -->
      <div
        class="update-templates-pixel"
        style="
          display: flex;
          flex-direction: row;
          gap: 21px;
          background: #f2dfce;
          padding: 40px 0;
          justify-content: center;
          align-items: flex-start;
          border-radius: 2px;
        "
      >
        <div
          class="template-card"
          style="
            background: #fff4ec;
            border: 2px solid #1f1d1a;
            border-radius: 2px;
            width: 183px;
            height: 183px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 20px;
          "
        >
          <img
            src="/template_investor.svg"
            alt="Investor"
            style="width: 80px; height: 80px"
          />
          <div
            style="
              font-family: 'IowanOldSt BT', serif;
              font-size: 20px;
              font-weight: 700;
              color: #1f1d1a;
              text-align: center;
            "
          >
            Investor
          </div>
        </div>
        <div
          class="template-card"
          style="
            background: #fff4ec;
            border: 2px solid #1f1d1a;
            border-radius: 2px;
            width: 183px;
            height: 183px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 20px;
          "
        >
          <img
            src="/template_finance.svg"
            alt="Finance"
            style="width: 80px; height: 80px"
          />
          <div
            style="
              font-family: 'IowanOldSt BT', serif;
              font-size: 20px;
              font-weight: 700;
              color: #1f1d1a;
              text-align: center;
            "
          >
            Finance
          </div>
        </div>
        <div
          class="template-card"
          style="
            background: #fff4ec;
            border: 2px solid #1f1d1a;
            border-radius: 2px;
            width: 183px;
            height: 183px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 20px;
          "
        >
          <img
            src="/template_product.svg"
            alt="Product"
            style="width: 80px; height: 80px"
          />
          <div
            style="
              font-family: 'IowanOldSt BT', serif;
              font-size: 20px;
              font-weight: 700;
              color: #1f1d1a;
              text-align: center;
            "
          >
            Product
          </div>
        </div>
        <div
          class="template-card"
          style="
            background: #fff4ec;
            border: 2px solid #1f1d1a;
            border-radius: 2px;
            width: 183px;
            height: 183px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 20px;
          "
        >
          <img
            src="/template_marketing.svg"
            alt="Marketing"
            style="width: 80px; height: 80px"
          />
          <div
            style="
              font-family: 'IowanOldSt BT', serif;
              font-size: 20px;
              font-weight: 700;
              color: #1f1d1a;
              text-align: center;
            "
          >
            Marketing
          </div>
        </div>
        <div
          class="template-card"
          style="
            background: #fff4ec;
            border: 2px solid #1f1d1a;
            border-radius: 2px;
            width: 183px;
            height: 183px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 20px;
          "
        >
          <img
            src="/template_operations.svg"
            alt="Operations"
            style="width: 80px; height: 80px"
          />
          <div
            style="
              font-family: 'IowanOldSt BT', serif;
              font-size: 20px;
              font-weight: 700;
              color: #1f1d1a;
              text-align: center;
            "
          >
            Operations
          </div>
        </div>
        <div
          class="template-card"
          style="
            background: #fff4ec;
            border: 2px solid #1f1d1a;
            border-radius: 2px;
            width: 183px;
            height: 183px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 20px;
          "
        >
          <img
            src="/template_teamhr.svg"
            alt="Team/HR"
            style="width: 80px; height: 80px"
          />
          <div
            style="
              font-family: 'IowanOldSt BT', serif;
              font-size: 20px;
              font-weight: 700;
              color: #1f1d1a;
              text-align: center;
            "
          >
            Team/HR
          </div>
        </div>
      </div>
      <div class="divider"></div>
      <div class="footer">
        <div>© 2025 UpdateStack. All rights reserved.</div>
        <div class="socials">
          <img
            src="https://cdn.jsdelivr.net/gh/simple-icons/simple-icons/icons/slack.svg"
            alt="Slack"
          />
          <img
            src="https://cdn.jsdelivr.net/gh/simple-icons/simple-icons/icons/linkedin.svg"
            alt="LinkedIn"
          />
          <img
            src="https://cdn.jsdelivr.net/gh/simple-icons/simple-icons/icons/x.svg"
            alt="Twitter X"
          />
          <img
            src="https://cdn.jsdelivr.net/gh/simple-icons/simple-icons/icons/discord.svg"
            alt="Discord"
          />
        </div>
      </div>
    </div>
  </body>
</html>
