import { updateUserDetailsAPI } from "../services/userService";
import { setUserDetails } from "../globalContext";

/**
 * Helper function to update user details and sync with global cache
 * This should be used instead of direct updateUserDetailsAPI calls
 * to ensure the global cache stays in sync
 */
export const updateUserDetailsWithCache = async (globalDispatch, payload) => {
  try {
    // Update via API
    const result = await updateUserDetailsAPI(payload);
    
    if (!result.error) {
      // Update the global cache with the new data
      // We merge with existing data since the API might not return all fields
      const currentUserDetails = JSON.parse(localStorage.getItem("userDetails") || "{}");
      const updatedUserDetails = { ...currentUserDetails, ...payload };
      
      // Update global context
      setUserDetails(globalDispatch, updatedUserDetails);
      
      // Also update localStorage for backward compatibility
      localStorage.setItem("userDetails", JSON.stringify(updatedUserDetails));
    }
    
    return result;
  } catch (error) {
    console.error("Error updating user details:", error);
    return { error: true, message: error.message };
  }
};

/**
 * Helper to invalidate user details cache
 * Use this when you want to force a fresh fetch on next access
 */
export const invalidateUserDetailsCache = (globalDispatch) => {
  setUserDetails(globalDispatch, null, { 
    lastFetched: null,
    error: null 
  });
  localStorage.removeItem("userDetails");
};

/**
 * Helper to check if user details cache is fresh
 */
export const isUserDetailsCacheFresh = (userDetails, cacheTimeout = 5 * 60 * 1000) => {
  if (!userDetails.data || !userDetails.lastFetched) return false;
  const now = Date.now();
  return (now - userDetails.lastFetched) < cacheTimeout;
};

/**
 * Migration helper for pages that currently use getUserDetailsByIdAPI
 * This provides a drop-in replacement that uses the cache when possible
 */
export const getUserDetailsWithCache = async (globalDispatch, userId, options = {}) => {
  const { forceRefresh = false, cacheTimeout = 5 * 60 * 1000 } = options;
  
  // Get current cache state from global context
  const currentCache = JSON.parse(localStorage.getItem("userDetails") || "{}");
  
  // Check if cache is fresh and we're not forcing refresh
  if (!forceRefresh && isUserDetailsCacheFresh(currentCache, cacheTimeout)) {
    return { error: false, model: currentCache.data };
  }
  
  // If cache is stale or we're forcing refresh, use the useUserDetails hook logic
  // This is a simplified version - in practice, components should use the hook directly
  try {
    const { getUserDetailsByIdAPI } = await import("../services/userService");
    const result = await getUserDetailsByIdAPI(userId);
    
    if (!result.error && result.model) {
      // Update cache
      setUserDetails(globalDispatch, result.model);
      localStorage.setItem("userDetails", JSON.stringify({
        data: result.model,
        lastFetched: Date.now()
      }));
    }
    
    return result;
  } catch (error) {
    console.error("Error fetching user details:", error);
    return { error: true, message: error.message };
  }
};
