import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import UploadedMaster2 from "./UploadedMaster2";
import SimpleFileUpload from "Components/FileUpload/SimpleFileUpload";
import UploadedDemo2 from "Components/PublicWorkOrder/WriterWorkOrder/UploadedDemo2";
import UploadedAdminInstrumentals2 from "Components/PublicWorkOrder/WriterWorkOrder/UploadedAdminInstrumentals2";

const SubProjectShowEngineer = ({
  // Existing props
  masters,
  // Added props from SubProjectCollapseEngineer
  subProjectId,
  assignedIdeas,
  demos,
  lyricsVal,
  surveySubmitStatus,
  adminWriterInstrumentals,
  handleInstrumentalUploads,
  setDeleteFileId,
  setShowAssignIdeaModal,
  setSelectedSubProjectId,
  handleShowAddIdeaModalOpen,
  setLyricsVal,
  submitLyrics,
  uploadedFilesProgressData = {},
}) => {
  const [activeTab, setActiveTab] = React.useState("instrumentals&ideas");

  const tabs = [
    {
      id: "instrumentals&ideas",
      label: "Survey & Instrumentals",
    },
    {
      id: "demos",
      label: "Demos",
    },
    {
      id: "lyrics",
      label: "Lyrics",
    },

    {
      id: "masters",
      label: "Master Files",
    },
  ];

  return (
    <div className="p-4 w-full rounded border shadow-default border-strokedark bg-boxdark">
      {/* Tabs Navigation */}
      <div className="flex gap-2 mb-4 border-b border-stroke">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            className={`px-4 py-2 text-sm font-semibold ${
              activeTab === tab.id
                ? "border-b-2 border-white text-primary"
                : "text-bodydark hover:text-white"
            }`}
            onClick={() => setActiveTab(tab.id)}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="mt-4">
        {/* Masters Tab */}
        {activeTab === "masters" && (
          <div className="custom-overflow max-h-[220px] overflow-y-auto rounded border border-form-strokedark bg-form-input p-4">
            {masters && masters.length > 0 ? (
              <div className="space-y-2">
                <UploadedMaster2
                  uploadedFiles={masters}
                  showUploadBtn={false}
                  setDeleteFileId={setDeleteFileId}
                  isRow={true}
                />
              </div>
            ) : (
              <div className="flex justify-center items-center py-4">
                <span className="text-sm text-bodydark2">
                  No master files found.
                </span>
              </div>
            )}
          </div>
        )}

        {/* Ideas Tab */}
        {activeTab === "instrumentals&ideas" && (
          <>
            <div className="flex justify-between items-start p-4 rounded border border-form-strokedark bg-form-input">
              <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <div className="flex gap-2 items-center">
                  <FontAwesomeIcon
                    icon="fa-solid fa-circle-check"
                    color={assignedIdeas?.length > 0 ? "#00FF00" : "#cccccc"}
                  />
                  <span className="text-white">
                    {assignedIdeas?.length > 0
                      ? "Ideas assigned"
                      : "Ideas not assigned"}
                  </span>
                </div>

                <div className="flex flex-wrap gap-3 items-center">
                  <button
                    className="inline-flex items-center justify-center rounded-sm bg-primary px-3 py-1.5 text-center text-sm font-medium text-white hover:bg-opacity-90"
                    onClick={() => {
                      setShowAssignIdeaModal(true);
                      setSelectedSubProjectId(subProjectId);
                    }}
                  >
                    Assign New Idea
                  </button>
                  <button
                    className="inline-flex items-center justify-center rounded-sm bg-primary px-3 py-1.5 text-center text-sm font-medium text-white hover:bg-opacity-90"
                    onClick={handleShowAddIdeaModalOpen}
                  >
                    Add New Idea
                  </button>
                </div>
              </div>
              <div className="custom-overflow max-h-[220px] w-1/2 overflow-y-auto rounded border border-form-strokedark bg-form-input p-4">
                <div className="space-y-4">
                  <SimpleFileUpload
                    maxFileSize={15}
                    label={"Instrumental"}
                    uploadedFilesProgressData={uploadedFilesProgressData}
                    setFormData={handleInstrumentalUploads}
                  />
                  {adminWriterInstrumentals?.length > 0 && (
                    <div className="mt-4">
                      <UploadedAdminInstrumentals2
                        uploadedFiles={adminWriterInstrumentals}
                        setDeleteFileId={setDeleteFileId}
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>
          </>
        )}

        {/* Demos Tab */}
        {activeTab === "demos" && (
          <div className="custom-overflow max-h-[220px] overflow-y-auto rounded border border-form-strokedark bg-form-input p-4">
            {demos && demos.length > 0 ? (
              <UploadedDemo2
                uploadedFiles={demos}
                showUploadBtn={false}
                setDeleteFileId={setDeleteFileId}
              />
            ) : (
              <div className="flex justify-center items-center py-4">
                <span className="text-sm text-bodydark2">
                  No demo files found.
                </span>
              </div>
            )}
          </div>
        )}

        {/* Lyrics Tab */}
        {activeTab === "lyrics" && (
          <div className="custom-overflow max-h-[220px] overflow-y-auto rounded border border-form-strokedark bg-form-input p-4">
            {lyricsVal ? (
              <div className="flex flex-col gap-3">
                <textarea
                  className="w-full rounded border border-form-strokedark bg-form-input px-4 py-2.5 text-sm text-white focus:border-primary focus:outline-none"
                  placeholder="Lyrics"
                  value={lyricsVal}
                  onChange={(e) => setLyricsVal(e.target.value)}
                  rows={6}
                />
                <button
                  type="button"
                  className="px-4 py-2 w-max text-sm font-medium text-white rounded-sm bg-primary hover:bg-opacity-90"
                  onClick={submitLyrics}
                >
                  Save
                </button>
              </div>
            ) : (
              <div className="flex justify-center items-center py-4">
                <span className="text-sm text-bodydark2">No lyrics found.</span>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default SubProjectShowEngineer;
