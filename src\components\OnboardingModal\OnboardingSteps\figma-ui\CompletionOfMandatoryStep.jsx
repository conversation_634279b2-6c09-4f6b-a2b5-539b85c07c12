import React from "react";
import { ClipLoader } from "react-spinners";

const CompletionOfMandatoryStep = ({
  isLoading,
  onContinueSetup,
  onGoToDashboard,
}) => {
  return (
    <div className="mx-auto flex w-full max-w-[397px] flex-col items-center gap-8 bg-white p-0">
      <div className="flex w-full flex-col items-center" style={{ gap: 8 }}>
        <svg
          width="57"
          height="56"
          viewBox="0 0 57 56"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect x="4.5" y="4" width="48" height="48" rx="24" fill="#E0E6FC" />
          <rect
            x="4.5"
            y="4"
            width="48"
            height="48"
            rx="24"
            stroke="#F6F7F8"
            stroke-width="8"
          />
          <rect x="12.5" y="12" width="32" height="32" rx="16" fill="#E0E6FC" />
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M35.2953 21.8532L25.7487 31.0666L23.2153 28.3599C22.7487 27.9199 22.0153 27.8932 21.482 28.2666C20.962 28.6532 20.8153 29.3332 21.1353 29.8799L24.1353 34.7599C24.4287 35.2132 24.9353 35.4932 25.5087 35.4932C26.0553 35.4932 26.5753 35.2132 26.8687 34.7599C27.3487 34.1332 36.5087 23.2132 36.5087 23.2132C37.7087 21.9866 36.2553 20.9066 35.2953 21.8399V21.8532Z"
            fill="#5C72E9"
          />
        </svg>

        <h2 className="font-inter mb-1 text-center text-[18px] font-semibold leading-[28px] text-[#131E2B]">
          Instant Kick-Off Complete!
        </h2>
        <h3 className="font-inter mb-2 max-w-[350px] text-center text-[14px] font-normal leading-[20px] text-[#667484]">
          Congratulations! You've set up the essentials for your cheer music
          business.
        </h3>
        <p className="font-inter mb-2 max-w-[350px] text-center text-[14px] font-semibold leading-[20px] text-[#667484]">
          Ready to unlock CheerEQ's full potential?
        </p>
        <p className="font-inter max-w-[350px] text-center text-[14px] font-normal leading-[20px] text-[#4A5B70]">
          Completing the remaining setup steps will supercharge your operations,
          automate workflows, and empower you with advanced features.
        </p>
        <p className="font-inter mb-2 max-w-[350px] text-center text-[14px] font-normal leading-[20px] text-[#667484]">
          You can do this at your convenience, or dive into your dashboard now!
        </p>
      </div>
      <div className="mt-8 flex w-full flex-col gap-4">
        <button
          onClick={onContinueSetup}
          disabled={isLoading}
          className="font-inter w-full rounded-[12px] bg-[#F6F7F8] py-3 text-base font-semibold text-[#3C50E0] transition hover:bg-[#c3d9ef] disabled:opacity-50"
          type="button"
        >
          {isLoading ? (
            <div className="flex items-center justify-center gap-2">
              <ClipLoader size={16} color="#fff" />
              Loading...
            </div>
          ) : (
            "Continue Setup"
          )}
        </button>
        <button
          onClick={onGoToDashboard}
          className="font-inter w-full rounded-[12px] border border-[#3C50E0] bg-[#3C50E0]  py-3 text-base font-semibold  text-white transition hover:bg-[#3C50E0]/80"
          type="button"
        >
          Go to Dashboard
        </button>
      </div>
    </div>
  );
};

export default CompletionOfMandatoryStep;
