import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { ClipLoader } from "react-spinners";

import { uploadS3FilesAPI } from "../../../services/workOrderService";
import { GlobalContext, showToast } from "../../../globalContext";
import {
  getUserDetailsByIdAPI,
  updateUserDetailsAPI,
} from "../../../services/userService";

const schema = yup
  .object({
    company_name: yup.string().required("Company name is required"),
    company_address: yup.string().required("Company address is required"),
    office_email: yup
      .string()
      .email("Invalid email")
      .required("Office email is required"),
    phone: yup.string().required("Phone number is required"),
  })
  .required();

const BusinessInfoStep = ({ stepData, userDetails, onComplete }) => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [isLoading, setIsLoading] = useState(false);
  const [logoUrl, setLogoUrl] = useState("");
  const [licenseLogoUrl, setLicenseLogoUrl] = useState("");
  const [logoFile, setLogoFile] = useState(null);
  const [licenseLogoFile, setLicenseLogoFile] = useState(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      company_name: userDetails?.company_name || "",
      company_address: userDetails?.company_address || "",
      office_email: userDetails?.office_email || "",
      phone: userDetails?.phone || "",
    },
  });

  useEffect(() => {
    // Set initial logo URLs if they exist
    if (userDetails?.company_logo) {
      setLogoUrl(userDetails.company_logo);
    }
    if (userDetails?.license_company_logo) {
      setLicenseLogoUrl(userDetails.license_company_logo);
    }
  }, [userDetails]);

  const onSubmit = async (data) => {
    try {
      setIsLoading(true);

      // First, fetch current user details to get all fields
      const userId = localStorage.getItem("user");
      const currentUserDetails = await getUserDetailsByIdAPI(userId);

      if (currentUserDetails.error) {
        throw new Error("Failed to fetch current user details");
      }

      let finalLogoUrl = logoUrl || currentUserDetails.model?.company_logo;
      let finalLicenseLogoUrl =
        licenseLogoUrl || currentUserDetails.model?.license_company_logo;

      // Upload company logo if a new file was selected
      if (logoFile) {
        try {
          const logoFormData = new FormData();
          logoFormData.append("files", logoFile);
          const logoResult = await uploadS3FilesAPI(logoFormData);
          if (!logoResult.error) {
            const attachmentsArr = JSON.parse(logoResult.attachments);
            finalLogoUrl = attachmentsArr[0];
          } else {
            showToast(
              globalDispatch,
              "Failed to upload company logo",
              4000,
              "error"
            );
            return;
          }
        } catch (error) {
          console.error("Error uploading company logo:", error);
          showToast(
            globalDispatch,
            "Failed to upload company logo",
            4000,
            "error"
          );
          return;
        }
      }

      // Upload license logo if a new file was selected
      if (licenseLogoFile) {
        try {
          const licenseFormData = new FormData();
          licenseFormData.append("files", licenseLogoFile);
          const licenseResult = await uploadS3FilesAPI(licenseFormData);
          if (!licenseResult.error) {
            const attachmentsArr = JSON.parse(licenseResult.attachments);
            finalLicenseLogoUrl = attachmentsArr[0];
          } else {
            showToast(
              globalDispatch,
              "Failed to upload license logo",
              4000,
              "error"
            );
            return;
          }
        } catch (error) {
          console.error("Error uploading license logo:", error);
          showToast(
            globalDispatch,
            "Failed to upload license logo",
            4000,
            "error"
          );
          return;
        }
      }

      // Prepare payload with all user fields (following ListMemberSettingPage pattern)
      const userPayload = {
        id: parseInt(userId),
        // Business info fields
        company_name: data.company_name,
        company_address: data.company_address,
        office_email: data.office_email,
        phone: data.phone,
        company_logo: finalLogoUrl,
        license_company_logo: finalLicenseLogoUrl,
        // Keep all existing user fields
        email: currentUserDetails.model?.email,
        first_name: currentUserDetails.model?.first_name,
        last_name: currentUserDetails.model?.last_name,
        // Keep existing settings if they exist
        deposit_percent: currentUserDetails.model?.deposit_percent,
        contract_agreement: currentUserDetails.model?.contract_agreement,
        survey: currentUserDetails.model?.survey
          ? JSON.stringify(currentUserDetails.model.survey)
          : "",
        routine_submission_date: currentUserDetails.model
          ?.routine_submission_date
          ? JSON.stringify(currentUserDetails.model.routine_submission_date)
          : "",
        estimated_delivery: currentUserDetails.model?.estimated_delivery
          ? JSON.stringify(currentUserDetails.model.estimated_delivery)
          : "",
        edit_policy_link: currentUserDetails.model?.edit_policy_link,
        // Keep existing steps and add business completion
        steps: currentUserDetails.model?.steps,
      };

      // Update user details
      const userResult = await updateUserDetailsAPI(userPayload);
      if (!userResult.error) {
        showToast(
          globalDispatch,
          "Business information saved successfully!",
          4000,
          "success"
        );

        // Prepare data for onboarding step completion
        const businessData = {
          company_name: data.company_name,
          company_address: data.company_address,
          office_email: data.office_email,
          phone: data.phone,
          company_logo: finalLogoUrl,
          license_company_logo: finalLicenseLogoUrl,
          business_identity_complete: true,
        };

        onComplete(businessData);
      } else {
        throw new Error(
          userResult.message || "Failed to save business information"
        );
      }
    } catch (error) {
      console.error("Error saving business info:", error);
      showToast(globalDispatch, "Failed to save business info", 4000, "error");
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogoUpload = (formData) => {
    const file = formData.get("files");
    if (file) {
      setLogoFile(file);
      const tempUrl = URL.createObjectURL(file);
      setLogoUrl(tempUrl);
    }
  };

  const handleLicenseLogoUpload = (formData) => {
    const file = formData.get("files");
    if (file) {
      setLicenseLogoFile(file);
      const tempUrl = URL.createObjectURL(file);
      setLicenseLogoUrl(tempUrl);
    }
  };

  return (
    <div className="mx-auto flex w-full max-w-[397px] flex-col items-center gap-8 rounded-[16px] bg-white p-0 shadow-none">
      <div className="flex w-full flex-col items-center" style={{ gap: 12 }}>
        <svg
          width="57"
          height="56"
          viewBox="0 0 57 56"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect x="4.5" y="4" width="48" height="48" rx="24" fill="#E0E6FC" />
          <rect
            x="4.5"
            y="4"
            width="48"
            height="48"
            rx="24"
            stroke="#F6F7F8"
            stroke-width="8"
          />
          <path
            d="M19.25 37H38.75M20.75 19V37M31.25 19V37M37.25 23.5V37M23.75 22.75H24.5M23.75 25.75H24.5M23.75 28.75H24.5M27.5 22.75H28.25M27.5 25.75H28.25M27.5 28.75H28.25M23.75 37V33.625C23.75 33.0037 24.2537 32.5 24.875 32.5H27.125C27.7463 32.5 28.25 33.0037 28.25 33.625V37M20 19H32M31.25 23.5H38M34.25 27.25H34.2575V27.2575H34.25V27.25ZM34.25 30.25H34.2575V30.2575H34.25V30.25ZM34.25 33.25H34.2575V33.2575H34.25V33.25Z"
            stroke="#3C50E0"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>

        <h2 className="font-inter mb-1 text-center text-[20px] font-bold leading-[28px] text-[#131E2B]">
          Your Business Identity
        </h2>
        <p className="font-inter mb-2 max-w-[350px] text-center text-[14px] font-normal leading-[20px] text-[#667484]">
          Let's make this app feel like home for your cheer music business! Tell
          us your name and add your logo.
        </p>
      </div>
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="mt-8 flex w-full flex-col items-center gap-3"
      >
        <div className="flex w-full flex-col gap-4">
          <label className="font-inter mb-1 text-[14px] font-medium text-[#4A5B70]">
            Business Name
          </label>
          <input
            type="text"
            {...register("company_name")}
            className={`font-inter w-full rounded-lg border ${
              errors.company_name ? "border-red-500" : "border-[#D1D6DE]"
            } bg-white p-3 text-[14px] text-[#414651] outline-none`}
            placeholder="Enter your company name"
          />
          {errors.company_name && (
            <p className="mt-1 text-sm text-red-500">
              {errors.company_name.message}
            </p>
          )}
        </div>
        <div className="flex w-full flex-col gap-4">
          <label className="font-inter mb-1 text-[14px] font-medium text-[#4A5B70]">
            Company Address
          </label>
          <input
            type="text"
            {...register("company_address")}
            className={`font-inter w-full rounded-lg border ${
              errors.company_address ? "border-red-500" : "border-[#D1D6DE]"
            } bg-white p-3 text-[14px] text-[#414651] outline-none`}
            placeholder="Enter your company address"
          />
          {errors.company_address && (
            <p className="mt-1 text-sm text-red-500">
              {errors.company_address.message}
            </p>
          )}
        </div>
        <div className="flex w-full flex-col gap-4">
          <label className="font-inter mb-1 text-[14px] font-medium text-[#4A5B70]">
            Office Email
          </label>
          <input
            type="email"
            {...register("office_email")}
            className={`font-inter w-full rounded-lg border ${
              errors.office_email ? "border-red-500" : "border-[#D1D6DE]"
            } bg-white p-3 text-[14px] text-[#414651] outline-none`}
            placeholder="Enter your office email"
          />
          {errors.office_email && (
            <p className="mt-1 text-sm text-red-500">
              {errors.office_email.message}
            </p>
          )}
        </div>
        <div className="flex w-full flex-col gap-4">
          <label className="font-inter mb-1 text-[14px] font-medium text-[#4A5B70]">
            Phone Number
          </label>
          <input
            type="tel"
            {...register("phone")}
            className={`font-inter w-full rounded-lg border ${
              errors.phone ? "border-red-500" : "border-[#D1D6DE]"
            } bg-white p-3 text-[14px] text-[#414651] outline-none`}
            placeholder="Enter your phone number"
          />
          {errors.phone && (
            <p className="mt-1 text-sm text-red-500">{errors.phone.message}</p>
          )}
        </div>
        <div className="flex w-full flex-col gap-4">
          <label className="font-inter mb-1 text-[14px] font-medium text-[#4A5B70]">
            Company Logo
          </label>
          <div className="flex w-full items-center gap-3">
            <div className="flex h-16 w-16 items-center justify-center rounded-[12px] bg-[#E0E6FC]">
              {logoUrl ? (
                <img
                  src={logoUrl}
                  alt="Company Logo"
                  className="h-12 w-12 object-contain"
                />
              ) : (
                <img
                  src="/uploads/1717617310029-myeq_logo.png"
                  alt="Company Logo"
                  className="h-12 w-12 object-contain"
                />
              )}
            </div>
            <div className="relative">
              <input
                type="file"
                accept="image/*"
                onChange={(e) => {
                  const file = e.target.files[0];
                  if (file) {
                    const formData = new FormData();
                    formData.append("files", file);
                    handleLogoUpload(formData);
                  }
                }}
                className="absolute inset-0 h-full w-full cursor-pointer opacity-0"
              />
              <button
                type="button"
                className="rounded-full bg-[#3C50E0] px-4 py-2 text-sm font-semibold text-white hover:bg-[#2B3EB4]"
              >
                Upload
              </button>
            </div>
          </div>
        </div>
        <div className="flex w-full flex-col gap-4">
          <label className="font-inter mb-1 text-[14px] font-medium text-[#4A5B70]">
            License Logo
          </label>
          <div className="flex w-full items-center gap-3">
            <div className="flex h-16 w-16 items-center justify-center rounded-[12px] bg-[#E0E6FC]">
              {licenseLogoUrl ? (
                <img
                  src={licenseLogoUrl}
                  alt="License Logo"
                  className="h-12 w-12 object-contain"
                />
              ) : (
                <img
                  src="/uploads/1717617310029-myeq_logo.png"
                  alt="License Logo"
                  className="h-12 w-12 object-contain"
                />
              )}
            </div>
            <div className="relative">
              <input
                type="file"
                accept="image/*"
                onChange={(e) => {
                  const file = e.target.files[0];
                  if (file) {
                    const formData = new FormData();
                    formData.append("files", file);
                    handleLicenseLogoUpload(formData);
                  }
                }}
                className="absolute inset-0 h-full w-full cursor-pointer opacity-0"
              />
              <button
                type="button"
                className="rounded-full bg-[#3C50E0] px-4 py-2 text-sm font-semibold text-white hover:bg-[#2B3EB4]"
              >
                Upload
              </button>
            </div>
          </div>
        </div>
        <div className="mt-8 flex w-full gap-3">
          <button
            type="submit"
            disabled={isLoading}
            className="font-inter flex-1 rounded-[8px] bg-[#3C50E0] py-3 text-base font-semibold text-white transition hover:bg-[#2B3EB4] disabled:opacity-50"
          >
            {isLoading ? (
              <div className="flex items-center justify-center gap-2">
                <ClipLoader size={16} color="#fff" />
                Saving...
              </div>
            ) : (
              "Save & Continue"
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default BusinessInfoStep;
