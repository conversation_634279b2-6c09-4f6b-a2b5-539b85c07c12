import{g as Y,d as T,c as nt,r as Tt}from"./vendor-94843817.js";import{s as At}from"./suneditor-a0939aba.js";import{d as Dt}from"./@react-pdf/renderer-8ed2c300.js";var j={},K={};const st={name:"blockquote",display:"command",add:function(t,e){const i=t.context;i.blockquote={targetButton:e,tag:t.util.createElement("BLOCKQUOTE")}},active:function(t){if(!t)this.util.removeClass(this.context.blockquote.targetButton,"active");else if(/blockquote/i.test(t.nodeName))return this.util.addClass(this.context.blockquote.targetButton,"active"),!0;return!1},action:function(){const t=this.util.getParentElement(this.getSelectionNode(),"blockquote");t?this.detachRangeFormatElement(t,null,null,!1,!1):this.applyRangeFormatElement(this.context.blockquote.tag.cloneNode(!1))}},ot={name:"align",display:"submenu",add:function(t,e){const i=t.icons,l=t.context;l.align={targetButton:e,_itemMenu:null,_alignList:null,currentAlign:"",defaultDir:t.options.rtl?"right":"left",icons:{justify:i.align_justify,left:i.align_left,right:i.align_right,center:i.align_center}};let n=this.setSubmenu(t),s=l.align._itemMenu=n.querySelector("ul");s.addEventListener("click",this.pickup.bind(t)),l.align._alignList=s.querySelectorAll("li button"),t.initMenuTarget(this.name,e,n),n=null,s=null},setSubmenu:function(t){const e=t.lang,i=t.icons,l=t.util.createElement("DIV"),n=t.options.alignItems;let s="";for(let o=0,a,r;o<n.length;o++)a=n[o],r=e.toolbar["align"+a.charAt(0).toUpperCase()+a.slice(1)],s+='<li><button type="button" class="se-btn-list se-btn-align" data-value="'+a+'" title="'+r+'" aria-label="'+r+'"><span class="se-list-icon">'+i["align_"+a]+"</span>"+r+"</button></li>";return l.className="se-submenu se-list-layer se-list-align",l.innerHTML='<div class="se-list-inner"><ul class="se-list-basic">'+s+"</ul></div>",l},active:function(t){const e=this.context.align,i=e.targetButton,l=i.firstElementChild;if(!t)this.util.changeElement(l,e.icons[e.defaultDir]),i.removeAttribute("data-focus");else if(this.util.isFormatElement(t)){const n=t.style.textAlign;if(n)return this.util.changeElement(l,e.icons[n]||e.icons[e.defaultDir]),i.setAttribute("data-focus",n),!0}return!1},on:function(){const t=this.context.align,e=t._alignList,i=t.targetButton.getAttribute("data-focus")||t.defaultDir;if(i!==t.currentAlign){for(let l=0,n=e.length;l<n;l++)i===e[l].getAttribute("data-value")?this.util.addClass(e[l],"active"):this.util.removeClass(e[l],"active");t.currentAlign=i}},exchangeDir:function(){const t=this.options.rtl?"right":"left";if(!this.context.align||this.context.align.defaultDir===t)return;this.context.align.defaultDir=t;let e=this.context.align._itemMenu,i=e.querySelector('[data-value="left"]'),l=e.querySelector('[data-value="right"]');if(i&&l){const n=i.parentElement,s=l.parentElement;n.appendChild(l),s.appendChild(i)}},pickup:function(t){t.preventDefault(),t.stopPropagation();let e=t.target,i=null;for(;!i&&!/UL/i.test(e.tagName);)i=e.getAttribute("data-value"),e=e.parentNode;if(!i)return;const l=this.context.align.defaultDir,n=this.getSelectedElements();for(let s=0,o=n.length;s<o;s++)this.util.setStyle(n[s],"textAlign",i===l?"":i);this.effectNode=null,this.submenuOff(),this.focus(),this.history.push(!1)}},at={name:"font",display:"submenu",add:function(t,e){const i=t.context;i.font={targetText:e.querySelector(".txt"),targetTooltip:e.parentNode.querySelector(".se-tooltip-text"),_fontList:null,currentFont:""};let l=this.setSubmenu(t);l.querySelector(".se-list-inner").addEventListener("click",this.pickup.bind(t)),i.font._fontList=l.querySelectorAll("ul li button"),t.initMenuTarget(this.name,e,l),l=null},setSubmenu:function(t){const e=t.lang,i=t.util.createElement("DIV");i.className="se-submenu se-list-layer se-list-font-family";let l,n,s,o,a=t.options.font,r='<div class="se-list-inner"><ul class="se-list-basic"><li><button type="button" class="default_value se-btn-list" title="'+e.toolbar.default+'" aria-label="'+e.toolbar.default+'">('+e.toolbar.default+")</button></li>";for(s=0,o=a.length;s<o;s++)l=a[s],n=l.split(",")[0],r+='<li><button type="button" class="se-btn-list" data-value="'+l+'" data-txt="'+n+'" title="'+n+'" aria-label="'+n+'" style="font-family:'+l+';">'+n+"</button></li>";return r+="</ul></div>",i.innerHTML=r,i},active:function(t){const e=this.context.font.targetText,i=this.context.font.targetTooltip;if(t){if(t.style&&t.style.fontFamily.length>0){const l=t.style.fontFamily.replace(/["']/g,"");return this.util.changeTxt(e,l),this.util.changeTxt(i,this.lang.toolbar.font+" ("+l+")"),!0}}else{const l=this.hasFocus?this.wwComputedStyle.fontFamily:this.lang.toolbar.font;this.util.changeTxt(e,l),this.util.changeTxt(i,this.hasFocus?this.lang.toolbar.font+(l?" ("+l+")":""):l)}return!1},on:function(){const t=this.context.font,e=t._fontList,i=t.targetText.textContent;if(i!==t.currentFont){for(let l=0,n=e.length;l<n;l++)i===(e[l].getAttribute("data-value")||"").replace(/'|"/g,"")?this.util.addClass(e[l],"active"):this.util.removeClass(e[l],"active");t.currentFont=i}},pickup:function(t){if(!/^BUTTON$/i.test(t.target.tagName))return!1;t.preventDefault(),t.stopPropagation();let e=t.target.getAttribute("data-value");if(e){const i=this.util.createElement("SPAN");/[\s\d\W]/.test(e)&&!/^['"].*['"]$/.test(e)&&(e='"'+e+'"'),i.style.fontFamily=e,this.nodeChange(i,["font-family"],null,null)}else this.nodeChange(null,["font-family"],["span"],!0);this.submenuOff()}},rt={name:"fontSize",display:"submenu",add:function(t,e){const i=t.context;i.fontSize={targetText:e.querySelector(".txt"),_sizeList:null,currentSize:""};let l=this.setSubmenu(t),n=l.querySelector("ul");n.addEventListener("click",this.pickup.bind(t)),i.fontSize._sizeList=n.querySelectorAll("li button"),t.initMenuTarget(this.name,e,l),l=null,n=null},setSubmenu:function(t){const e=t.options,i=t.lang,l=t.util.createElement("DIV");l.className="se-submenu se-list-layer se-list-font-size";const n=e.fontSize?e.fontSize:[8,9,10,11,12,14,16,18,20,22,24,26,28,36,48,72];let s='<div class="se-list-inner"><ul class="se-list-basic"><li><button type="button" class="default_value se-btn-list" title="'+i.toolbar.default+'" aria-label="'+i.toolbar.default+'">('+i.toolbar.default+")</button></li>";for(let o=0,a=e.fontSizeUnit,r=n.length,c;o<r;o++)c=n[o],s+='<li><button type="button" class="se-btn-list" data-value="'+c+a+'" title="'+c+a+'" aria-label="'+c+a+'" style="font-size:'+c+a+';">'+c+"</button></li>";return s+="</ul></div>",l.innerHTML=s,l},active:function(t){if(!t)this.util.changeTxt(this.context.fontSize.targetText,this.hasFocus?this._convertFontSize.call(this,this.options.fontSizeUnit,this.wwComputedStyle.fontSize):this.lang.toolbar.fontSize);else if(t.style&&t.style.fontSize.length>0)return this.util.changeTxt(this.context.fontSize.targetText,this._convertFontSize.call(this,this.options.fontSizeUnit,t.style.fontSize)),!0;return!1},on:function(){const t=this.context.fontSize,e=t._sizeList,i=t.targetText.textContent;if(i!==t.currentSize){for(let l=0,n=e.length;l<n;l++)i===e[l].getAttribute("data-value")?this.util.addClass(e[l],"active"):this.util.removeClass(e[l],"active");t.currentSize=i}},pickup:function(t){if(!/^BUTTON$/i.test(t.target.tagName))return!1;t.preventDefault(),t.stopPropagation();const e=t.target.getAttribute("data-value");if(e){const i=this.util.createElement("SPAN");i.style.fontSize=e,this.nodeChange(i,["font-size"],null,null)}else this.nodeChange(null,["font-size"],["span"],!0);this.submenuOff()}},ct={name:"colorPicker",add:function(t){const e=t.context;e.colorPicker={colorListHTML:"",_colorInput:"",_defaultColor:"#000",_styleProperty:"color",_currentColor:"",_colorList:[]},e.colorPicker.colorListHTML=this.createColorList(t,this._makeColorList)},createColorList:function(t,e){const i=t.options,l=t.lang,n=!i.colorList||i.colorList.length===0?["#ff0000","#ff5e00","#ffe400","#abf200","#00d8ff","#0055ff","#6600ff","#ff00dd","#000000","#ffd8d8","#fae0d4","#faf4c0","#e4f7ba","#d4f4fa","#d9e5ff","#e8d9ff","#ffd9fa","#f1f1f1","#ffa7a7","#ffc19e","#faed7d","#cef279","#b2ebf4","#b2ccff","#d1b2ff","#ffb2f5","#bdbdbd","#f15f5f","#f29661","#e5d85c","#bce55c","#5cd1e5","#6699ff","#a366ff","#f261df","#8c8c8c","#980000","#993800","#998a00","#6b9900","#008299","#003399","#3d0099","#990085","#353535","#670000","#662500","#665c00","#476600","#005766","#002266","#290066","#660058","#222222"]:i.colorList;let s=[],o='<div class="se-list-inner">';for(let a=0,r=n.length,c;a<r;a++)c=n[a],c&&(typeof c=="string"&&(s.push(c),a<r-1)||(s.length>0&&(o+='<div class="se-selector-color">'+e(s)+"</div>",s=[]),typeof c=="object"&&(o+='<div class="se-selector-color">'+e(c)+"</div>")));return o+='<form class="se-form-group"><input type="text" maxlength="9" class="_se_color_picker_input se-color-input"/><button type="submit" class="se-btn-primary _se_color_picker_submit" title="'+l.dialogBox.submitButton+'" aria-label="'+l.dialogBox.submitButton+'">'+t.icons.checked+'</button><button type="button" class="se-btn _se_color_picker_remove" title="'+l.toolbar.removeFormat+'" aria-label="'+l.toolbar.removeFormat+'">'+t.icons.erase+"</button></form></div>",o},_makeColorList:function(t){let e="";e+='<ul class="se-color-pallet">';for(let i=0,l=t.length,n;i<l;i++)n=t[i],typeof n=="string"&&(e+='<li><button type="button" data-value="'+n+'" title="'+n+'" aria-label="'+n+'" style="background-color:'+n+';"></button></li>');return e+="</ul>",e},init:function(t,e){const i=this.plugins.colorPicker;let l=e||i.getColorInNode.call(this,t)||this.context.colorPicker._defaultColor;l=i.isHexColor(l)?l:i.rgb2hex(l)||l;const n=this.context.colorPicker._colorList;if(n)for(let s=0,o=n.length;s<o;s++)l.toLowerCase()===n[s].getAttribute("data-value").toLowerCase()?this.util.addClass(n[s],"active"):this.util.removeClass(n[s],"active");i.setInputText.call(this,i.colorName2hex.call(this,l))},setCurrentColor:function(t){this.context.colorPicker._currentColor=t,this.context.colorPicker._colorInput.style.borderColor=t},setInputText:function(t){t=/^#/.test(t)?t:"#"+t,this.context.colorPicker._colorInput.value=t,this.plugins.colorPicker.setCurrentColor.call(this,t)},getColorInNode:function(t){let e="";const i=this.context.colorPicker._styleProperty;for(;t&&!this.util.isWysiwygDiv(t)&&e.length===0;)t.nodeType===1&&t.style[i]&&(e=t.style[i]),t=t.parentNode;return e},isHexColor:function(t){return/^#[0-9a-f]{3}(?:[0-9a-f]{3})?$/i.test(t)},rgb2hex:function(t){const e=t.match(/^rgba?[\s+]?\([\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?/i);return e&&e.length===4?"#"+("0"+parseInt(e[1],10).toString(16)).slice(-2)+("0"+parseInt(e[2],10).toString(16)).slice(-2)+("0"+parseInt(e[3],10).toString(16)).slice(-2):""},colorName2hex:function(t){if(/^#/.test(t))return t;var e=this.util.createElement("div");e.style.display="none",e.style.color=t;var i=this._w.getComputedStyle(this._d.body.appendChild(e)).color.match(/\d+/g).map(function(l){return parseInt(l,10)});return this.util.removeItem(e),i.length>=3?"#"+((1<<24)+(i[0]<<16)+(i[1]<<8)+i[2]).toString(16).substr(1):!1}},ut={name:"fontColor",display:"submenu",add:function(t,e){t.addModule([ct]);const i=t.context;i.fontColor={previewEl:null,colorInput:null,colorList:null};let l=this.setSubmenu(t);i.fontColor.colorInput=l.querySelector("._se_color_picker_input"),i.fontColor.colorInput.addEventListener("keyup",this.onChangeInput.bind(t)),l.querySelector("._se_color_picker_submit").addEventListener("click",this.submit.bind(t)),l.querySelector("._se_color_picker_remove").addEventListener("click",this.remove.bind(t)),l.addEventListener("click",this.pickup.bind(t)),i.fontColor.colorList=l.querySelectorAll("li button"),t.initMenuTarget(this.name,e,l),l=null},setSubmenu:function(t){const e=t.context.colorPicker.colorListHTML,i=t.util.createElement("DIV");return i.className="se-submenu se-list-layer",i.innerHTML=e,i},on:function(){const t=this.context.colorPicker,e=this.context.fontColor;t._colorInput=e.colorInput;const i=this.wwComputedStyle.color;t._defaultColor=i?this.plugins.colorPicker.isHexColor(i)?i:this.plugins.colorPicker.rgb2hex(i):"#333333",t._styleProperty="color",t._colorList=e.colorList,this.plugins.colorPicker.init.call(this,this.getSelectionNode(),null)},onChangeInput:function(t){this.plugins.colorPicker.setCurrentColor.call(this,t.target.value)},submit:function(){this.plugins.fontColor.applyColor.call(this,this.context.colorPicker._currentColor)},pickup:function(t){t.preventDefault(),t.stopPropagation(),this.plugins.fontColor.applyColor.call(this,t.target.getAttribute("data-value"))},remove:function(){this.nodeChange(null,["color"],["span"],!0),this.submenuOff()},applyColor:function(t){if(!t)return;const e=this.util.createElement("SPAN");e.style.color=t,this.nodeChange(e,["color"],null,null),this.submenuOff()}},dt={name:"hiliteColor",display:"submenu",add:function(t,e){t.addModule([ct]);const i=t.context;i.hiliteColor={previewEl:null,colorInput:null,colorList:null};let l=this.setSubmenu(t);i.hiliteColor.colorInput=l.querySelector("._se_color_picker_input"),i.hiliteColor.colorInput.addEventListener("keyup",this.onChangeInput.bind(t)),l.querySelector("._se_color_picker_submit").addEventListener("click",this.submit.bind(t)),l.querySelector("._se_color_picker_remove").addEventListener("click",this.remove.bind(t)),l.addEventListener("click",this.pickup.bind(t)),i.hiliteColor.colorList=l.querySelectorAll("li button"),t.initMenuTarget(this.name,e,l),l=null},setSubmenu:function(t){const e=t.context.colorPicker.colorListHTML,i=t.util.createElement("DIV");return i.className="se-submenu se-list-layer",i.innerHTML=e,i},on:function(){const t=this.context.colorPicker,e=this.context.hiliteColor;t._colorInput=e.colorInput;const i=this.wwComputedStyle.backgroundColor;t._defaultColor=i?this.plugins.colorPicker.isHexColor(i)?i:this.plugins.colorPicker.rgb2hex(i):"#ffffff",t._styleProperty="backgroundColor",t._colorList=e.colorList,this.plugins.colorPicker.init.call(this,this.getSelectionNode(),null)},onChangeInput:function(t){this.plugins.colorPicker.setCurrentColor.call(this,t.target.value)},submit:function(){this.plugins.hiliteColor.applyColor.call(this,this.context.colorPicker._currentColor)},pickup:function(t){t.preventDefault(),t.stopPropagation(),this.plugins.hiliteColor.applyColor.call(this,t.target.getAttribute("data-value"))},remove:function(){this.nodeChange(null,["background-color"],["span"],!0),this.submenuOff()},applyColor:function(t){if(!t)return;const e=this.util.createElement("SPAN");e.style.backgroundColor=t,this.nodeChange(e,["background-color"],null,null),this.submenuOff()}},ht={name:"horizontalRule",display:"submenu",add:function(t,e){t.context.horizontalRule={currentHR:null};let i=this.setSubmenu(t);i.querySelector("ul").addEventListener("click",this.horizontalRulePick.bind(t)),t.initMenuTarget(this.name,e,i),i=null},setSubmenu:function(t){const e=t.lang,i=t.util.createElement("DIV"),l=t.options.hrItems||[{name:e.toolbar.hr_solid,class:"__se__solid"},{name:e.toolbar.hr_dashed,class:"__se__dashed"},{name:e.toolbar.hr_dotted,class:"__se__dotted"}];let n="";for(let s=0,o=l.length;s<o;s++)n+='<li><button type="button" class="se-btn-list btn_line" data-command="horizontalRule" data-value="'+l[s].class+'" title="'+l[s].name+'" aria-label="'+l[s].name+'"><hr'+(l[s].class?' class="'+l[s].class+'"':"")+(l[s].style?' style="'+l[s].style+'"':"")+"/></button></li>";return i.className="se-submenu se-list-layer se-list-line",i.innerHTML='<div class="se-list-inner"><ul class="se-list-basic">'+n+"</ul></div>",i},active:function(t){if(!t)this.util.hasClass(this.context.horizontalRule.currentHR,"on")&&this.controllersOff();else if(/HR/i.test(t.nodeName))return this.context.horizontalRule.currentHR=t,this.util.hasClass(t,"on")||(this.util.addClass(t,"on"),this.controllersOn("hr",this.util.removeClass.bind(this.util,t,"on"))),!0;return!1},appendHr:function(t){return this.focus(),this.insertComponent(t.cloneNode(!1),!1,!0,!1)},horizontalRulePick:function(t){t.preventDefault(),t.stopPropagation();let e=t.target,i=e.getAttribute("data-command");for(;!i&&!/UL/i.test(e.tagName);)e=e.parentNode,i=e.getAttribute("data-command");if(!i)return;const l=this.plugins.horizontalRule.appendHr.call(this,e.firstElementChild);l&&(this.setRange(l,0,l,0),this.submenuOff())}},ft={name:"list",display:"submenu",add:function(t,e){const i=t.context;i.list={targetButton:e,_list:null,currentList:"",icons:{bullets:t.icons.list_bullets,number:t.icons.list_number}};let l=this.setSubmenu(t),n=l.querySelector("ul");n.addEventListener("click",this.pickup.bind(t)),i.list._list=n.querySelectorAll("li button"),t.initMenuTarget(this.name,e,l),l=null,n=null},setSubmenu:function(t){const e=t.lang,i=t.util.createElement("DIV");return i.className="se-submenu se-list-layer",i.innerHTML='<div class="se-list-inner"><ul class="se-list-basic"><li><button type="button" class="se-btn-list se-tooltip" data-command="OL" title="'+e.toolbar.orderList+'" aria-label="'+e.toolbar.orderList+'">'+t.icons.list_number+'</button></li><li><button type="button" class="se-btn-list se-tooltip" data-command="UL" title="'+e.toolbar.unorderList+'" aria-label="'+e.toolbar.unorderList+'">'+t.icons.list_bullets+"</button></li></ul></div>",i},active:function(t){const e=this.context.list.targetButton,i=e.firstElementChild,l=this.util;if(l.isList(t)){const n=t.nodeName;return e.setAttribute("data-focus",n),l.addClass(e,"active"),/UL/i.test(n)?l.changeElement(i,this.context.list.icons.bullets):l.changeElement(i,this.context.list.icons.number),!0}else e.removeAttribute("data-focus"),l.changeElement(i,this.context.list.icons.number),l.removeClass(e,"active");return!1},on:function(){const t=this.context.list,e=t._list,i=t.targetButton.getAttribute("data-focus")||"";if(i!==t.currentList){for(let l=0,n=e.length;l<n;l++)i===e[l].getAttribute("data-command")?this.util.addClass(e[l],"active"):this.util.removeClass(e[l],"active");t.currentList=i}},editList:function(t,e,i){let l=this.getRange(),n=e||this.getSelectedElementsAndComponents(!1);if(n.length===0&&(e||(l=this.getRange_addLine(l,null),n=this.getSelectedElementsAndComponents(!1),n.length===0)))return;const s=this.util;s.sortByDepth(n,!0);let o=n[0],a=n[n.length-1],r=(s.isListCell(o)||s.isComponent(o))&&!o.previousElementSibling?o.parentNode.previousElementSibling:o.previousElementSibling,c=(s.isListCell(a)||s.isComponent(a))&&!a.nextElementSibling?a.parentNode.nextElementSibling:a.nextElementSibling;const u=l.collapsed,f={sc:l.startContainer,so:l.startContainer===l.endContainer&&s.onlyZeroWidthSpace(l.startContainer)&&l.startOffset===0&&l.endOffset===1?l.endOffset:l.startOffset,ec:l.endContainer,eo:l.endOffset};let p=null,m=!0;for(let d=0,_=n.length;d<_;d++)if(!s.isList(s.getRangeFormatElement(n[d],(function(g){return this.getRangeFormatElement(g)&&g!==n[d]}).bind(s)))){m=!1;break}if(m&&(!r||o.tagName!==r.tagName||t!==r.tagName.toUpperCase())&&(!c||a.tagName!==c.tagName||t!==c.tagName.toUpperCase())){if(i){for(let v=0,x=n.length;v<x;v++)for(let y=v-1;y>=0;y--)if(n[y].contains(n[v])){n.splice(v,1),v--,x--;break}}const d=s.getRangeFormatElement(o),_=d&&d.tagName===t;let g,h;const b=(function(v){return!this.isComponent(v)}).bind(s);_||(h=s.createElement(t));for(let v=0,x=n.length,y,w;v<x;v++)w=s.getRangeFormatElement(n[v],b),!(!w||!s.isList(w))&&(y?y!==w?(i&&s.isListCell(w.parentNode)?this.plugins.list._detachNested.call(this,g.f):p=this.detachRangeFormatElement(g.f[0].parentNode,g.f,h,!1,!0),w=n[v].parentNode,_||(h=s.createElement(t)),y=w,g={r:y,f:[s.getParentElement(n[v],"LI")]}):g.f.push(s.getParentElement(n[v],"LI")):(y=w,g={r:y,f:[s.getParentElement(n[v],"LI")]}),v===x-1&&(i&&s.isListCell(w.parentNode)?this.plugins.list._detachNested.call(this,g.f):p=this.detachRangeFormatElement(g.f[0].parentNode,g.f,h,!1,!0)))}else{const d=r&&r.parentNode,_=c&&c.parentNode;r=d&&!s.isWysiwygDiv(d)&&d.nodeName===t?d:r,c=_&&!s.isWysiwygDiv(_)&&_.nodeName===t?_:c;const g=r&&r.tagName===t,h=c&&c.tagName===t;let b=g?r:s.createElement(t),v=null,x=null,y=null;const w=(function(C){return!this.isComponent(C)&&!this.isList(C)}).bind(s);for(let C=0,B=n.length,z,L,N,E,S,M,k,O,I;C<B;C++){if(L=n[C],L.childNodes.length===0&&!s._isIgnoreNodeChange(L)){s.removeItem(L);continue}if(E=n[C+1],S=L.parentNode,M=E?E.parentNode:null,N=s.isListCell(L),I=s.isRangeFormatElement(S)?S:null,k=N&&!s.isWysiwygDiv(S)?S.parentNode:S,O=N&&!s.isWysiwygDiv(S)?!E||s.isListCell(k)?S:S.nextSibling:L.nextSibling,z=s.createElement("LI"),s.copyFormatAttributes(z,L),C===0&&f.sc===L&&(f.sc=z),C===B-1&&f.ec===L&&(f.ec=z),s.isComponent(L)){const A=/^HR$/i.test(L.nodeName);A||(z.innerHTML="<br>"),z.innerHTML+=L.outerHTML,A&&(z.innerHTML+="<br>")}else{const A=L.childNodes;for(;A[0];)z.appendChild(A[0])}b.appendChild(z),(!E||k!==M||s.isRangeFormatElement(O))&&(v||(v=b),(!g||!E||k!==M)&&!(E&&s.isList(M)&&M===S)&&b.parentNode!==k&&k.insertBefore(b,O)),s.removeItem(L),g&&x===null&&(x=b.children.length-1),E&&(s.getRangeFormatElement(M,w)!==s.getRangeFormatElement(S,w)||s.isList(M)&&s.isList(S)&&s.getElementDepth(M)!==s.getElementDepth(S))&&(b=s.createElement(t)),I&&I.children.length===0&&s.removeItem(I)}x&&(v=v.children[x]),h&&(y=b.children.length-1,b.innerHTML+=c.innerHTML,b.children[y],s.removeItem(c))}return this.effectNode=null,u?p:f},_detachNested:function(t){const e=t[0],i=t[t.length-1],l=i.nextElementSibling,n=e.parentNode,s=n.parentNode.nextElementSibling,o=n.parentNode.parentNode;for(let r=0,c=t.length;r<c;r++)o.insertBefore(t[r],s);if(l&&n.children.length>0){const r=n.cloneNode(!1),c=n.childNodes,u=this.util.getPositionIndex(l);for(;c[u];)r.appendChild(c[u]);i.appendChild(r)}n.children.length===0&&this.util.removeItem(n),this.util.mergeSameTags(o);const a=this.util.getEdgeChildNodes(e,i);return{cc:e.parentNode,sc:a.sc,ec:a.ec}},editInsideList:function(t,e){e=e||this.getSelectedElements().filter((function(o){return this.isListCell(o)}).bind(this.util));const i=e.length;if(i===0||!t&&!this.util.isListCell(e[0].previousElementSibling)&&!this.util.isListCell(e[i-1].nextElementSibling))return{sc:e[0],so:0,ec:e[i-1],eo:1};let l=e[0].parentNode,n=e[i-1],s=null;if(t){if(l!==n.parentNode&&this.util.isList(n.parentNode.parentNode)&&n.nextElementSibling)for(n=n.nextElementSibling;n;)e.push(n),n=n.nextElementSibling;s=this.plugins.list.editList.call(this,l.nodeName.toUpperCase(),e,!0)}else{let o=this.util.createElement(l.nodeName),a=e[0].previousElementSibling,r=n.nextElementSibling;const c={s:null,e:null,sl:l,el:l};for(let p=0,m=i,d;p<m;p++)d=e[p],d.parentNode!==l&&(this.plugins.list._insiedList.call(this,l,o,a,r,c),l=d.parentNode,o=this.util.createElement(l.nodeName)),a=d.previousElementSibling,r=d.nextElementSibling,o.appendChild(d);this.plugins.list._insiedList.call(this,l,o,a,r,c);const u=this.util.getNodeFromPath(c.s,c.sl),f=this.util.getNodeFromPath(c.e,c.el);s={sc:u,so:0,ec:f,eo:f.textContent.length}}return s},_insiedList:function(t,e,i,l,n){let s=!1;if(i&&e.tagName===i.tagName){const o=e.children;for(;o[0];)i.appendChild(o[0]);e=i,s=!0}if(l&&e.tagName===l.tagName){const o=l.children;for(;o[0];)e.appendChild(o[0]);const a=l.nextElementSibling;l.parentNode.removeChild(l),l=a}if(!s){this.util.isListCell(i)&&(t=i,l=null),t.insertBefore(e,l),n.s||(n.s=this.util.getNodePath(e.firstElementChild.firstChild,t,null),n.sl=t);const o=t.contains(n.sl)?this.util.getNodePath(n.sl,t):null;n.e=this.util.getNodePath(e.lastElementChild.firstChild,t,null),n.el=t,this.util.mergeSameTags(t,[n.s,n.e,o],!1),this.util.mergeNestedTags(t),o&&(n.sl=this.util.getNodeFromPath(o,t))}return e},pickup:function(t){t.preventDefault(),t.stopPropagation();let e=t.target,i="";for(;!i&&!/^UL$/i.test(e.tagName);)i=e.getAttribute("data-command"),e=e.parentNode;if(!i)return;const l=this.plugins.list.editList.call(this,i,null,!1);l&&this.setRange(l.sc,l.so,l.ec,l.eo),this.submenuOff(),this.history.push(!1)}},pt={name:"table",display:"submenu",add:function(t,e){const i=t.context;let l=i.table={_element:null,_tdElement:null,_trElement:null,_trElements:null,_tableXY:[],_maxWidth:!0,_fixedColumn:!1,_rtl:t.options.rtl,cellControllerTop:t.options.tableCellControllerPosition==="top",resizeText:null,headerButton:null,mergeButton:null,splitButton:null,splitMenu:null,maxText:t.lang.controller.maxSize,minText:t.lang.controller.minSize,_physical_cellCnt:0,_logical_cellCnt:0,_rowCnt:0,_rowIndex:0,_physical_cellIndex:0,_logical_cellIndex:0,_current_colSpan:0,_current_rowSpan:0,icons:{expansion:t.icons.expansion,reduction:t.icons.reduction}},n=this.setSubmenu(t),s=n.querySelector(".se-controller-table-picker");l.tableHighlight=n.querySelector(".se-table-size-highlighted"),l.tableUnHighlight=n.querySelector(".se-table-size-unhighlighted"),l.tableDisplay=n.querySelector(".se-table-size-display"),t.options.rtl&&(l.tableHighlight.style.left=10*18-13+"px");let o=this.setController_table(t);l.tableController=o,l.resizeButton=o.querySelector("._se_table_resize"),l.resizeText=o.querySelector("._se_table_resize > span > span"),l.columnFixedButton=o.querySelector("._se_table_fixed_column"),l.headerButton=o.querySelector("._se_table_header");let a=this.setController_tableEditor(t,l.cellControllerTop);l.resizeDiv=a,l.splitMenu=a.querySelector(".se-btn-group-sub"),l.mergeButton=a.querySelector("._se_table_merge_button"),l.splitButton=a.querySelector("._se_table_split_button"),l.insertRowAboveButton=a.querySelector("._se_table_insert_row_a"),l.insertRowBelowButton=a.querySelector("._se_table_insert_row_b"),s.addEventListener("mousemove",this.onMouseMove_tablePicker.bind(t,l)),s.addEventListener("click",this.appendTable.bind(t)),a.addEventListener("click",this.onClick_tableController.bind(t)),o.addEventListener("click",this.onClick_tableController.bind(t)),t.initMenuTarget(this.name,e,n),i.element.relative.appendChild(a),i.element.relative.appendChild(o),n=null,s=null,a=null,o=null,l=null},setSubmenu:function(t){const e=t.util.createElement("DIV");return e.className="se-submenu se-selector-table",e.innerHTML='<div class="se-table-size"><div class="se-table-size-picker se-controller-table-picker"></div><div class="se-table-size-highlighted"></div><div class="se-table-size-unhighlighted"></div></div><div class="se-table-size-display">1 x 1</div>',e},setController_table:function(t){const e=t.lang,i=t.icons,l=t.util.createElement("DIV");return l.className="se-controller se-controller-table",l.innerHTML='<div><div class="se-btn-group"><button type="button" data-command="resize" class="se-btn se-tooltip _se_table_resize">'+i.expansion+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+e.controller.maxSize+'</span></span></button><button type="button" data-command="layout" class="se-btn se-tooltip _se_table_fixed_column">'+i.fixed_column_width+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+e.controller.fixedColumnWidth+'</span></span></button><button type="button" data-command="header" class="se-btn se-tooltip _se_table_header">'+i.table_header+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+e.controller.tableHeader+'</span></span></button><button type="button" data-command="remove" class="se-btn se-tooltip">'+i.delete+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+e.controller.remove+"</span></span></button></div></div>",l},setController_tableEditor:function(t,e){const i=t.lang,l=t.icons,n=t.util.createElement("DIV");return n.className="se-controller se-controller-table-cell",n.innerHTML=(e?"":'<div class="se-arrow se-arrow-up"></div>')+'<div class="se-btn-group"><button type="button" data-command="insert" data-value="row" data-option="up" class="se-btn se-tooltip _se_table_insert_row_a">'+l.insert_row_above+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+i.controller.insertRowAbove+'</span></span></button><button type="button" data-command="insert" data-value="row" data-option="down" class="se-btn se-tooltip _se_table_insert_row_b">'+l.insert_row_below+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+i.controller.insertRowBelow+'</span></span></button><button type="button" data-command="delete" data-value="row" class="se-btn se-tooltip">'+l.delete_row+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+i.controller.deleteRow+'</span></span></button><button type="button" data-command="merge" class="_se_table_merge_button se-btn se-tooltip" disabled>'+l.merge_cell+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+i.controller.mergeCells+'</span></span></button></div><div class="se-btn-group" style="padding-top: 0;"><button type="button" data-command="insert" data-value="cell" data-option="left" class="se-btn se-tooltip">'+l.insert_column_left+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+i.controller.insertColumnBefore+'</span></span></button><button type="button" data-command="insert" data-value="cell" data-option="right" class="se-btn se-tooltip">'+l.insert_column_right+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+i.controller.insertColumnAfter+'</span></span></button><button type="button" data-command="delete" data-value="cell" class="se-btn se-tooltip">'+l.delete_column+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+i.controller.deleteColumn+'</span></span></button><button type="button" data-command="onsplit" class="_se_table_split_button se-btn se-tooltip">'+l.split_cell+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+i.controller.splitCells+'</span></span></button><div class="se-btn-group-sub sun-editor-common se-list-layer se-table-split"><div class="se-list-inner"><ul class="se-list-basic"><li class="se-btn-list" data-command="split" data-value="vertical" style="line-height:32px;" title="'+i.controller.VerticalSplit+'" aria-label="'+i.controller.VerticalSplit+'">'+i.controller.VerticalSplit+'</li><li class="se-btn-list" data-command="split" data-value="horizontal" style="line-height:32px;" title="'+i.controller.HorizontalSplit+'" aria-label="'+i.controller.HorizontalSplit+'">'+i.controller.HorizontalSplit+"</li></ul></div></div></div>",n},appendTable:function(){const t=this.util.createElement("TABLE"),e=this.plugins.table.createCells,i=this.context.table._tableXY[0];let l=this.context.table._tableXY[1],n="<tbody>";for(;l>0;)n+="<tr>"+e.call(this,"td",i)+"</tr>",--l;if(n+="</tbody>",t.innerHTML=n,this.insertComponent(t,!1,!0,!1)){const o=t.querySelector("td div");this.setRange(o,0,o,0),this.plugins.table.reset_table_picker.call(this)}},createCells:function(t,e,i){if(t=t.toLowerCase(),i){const l=this.util.createElement(t);return l.innerHTML="<div><br></div>",l}else{let l="";for(;e>0;)l+="<"+t+"><div><br></div></"+t+">",e--;return l}},onMouseMove_tablePicker:function(t,e){e.stopPropagation();let i=this._w.Math.ceil(e.offsetX/18),l=this._w.Math.ceil(e.offsetY/18);i=i<1?1:i,l=l<1?1:l,t._rtl&&(t.tableHighlight.style.left=i*18-13+"px",i=11-i),t.tableHighlight.style.width=i+"em",t.tableHighlight.style.height=l+"em",this.util.changeTxt(t.tableDisplay,i+" x "+l),t._tableXY=[i,l]},reset_table_picker:function(){if(!this.context.table.tableHighlight)return;const t=this.context.table.tableHighlight.style,e=this.context.table.tableUnHighlight.style;t.width="1em",t.height="1em",e.width="10em",e.height="10em",this.util.changeTxt(this.context.table.tableDisplay,"1 x 1"),this.submenuOff()},init:function(){const t=this.context.table,e=this.plugins.table;if(e._removeEvents.call(this),e._selectedTable){const i=e._selectedTable.querySelectorAll(".se-table-selected-cell");for(let l=0,n=i.length;l<n;l++)this.util.removeClass(i[l],"se-table-selected-cell")}e._toggleEditor.call(this,!0),t._element=null,t._tdElement=null,t._trElement=null,t._trElements=null,t._tableXY=[],t._maxWidth=!0,t._fixedColumn=!1,t._physical_cellCnt=0,t._logical_cellCnt=0,t._rowCnt=0,t._rowIndex=0,t._physical_cellIndex=0,t._logical_cellIndex=0,t._current_colSpan=0,t._current_rowSpan=0,e._shift=!1,e._selectedCells=null,e._selectedTable=null,e._ref=null,e._fixedCell=null,e._selectedCell=null,e._fixedCellName=null},call_controller_tableEdit:function(t){const e=this.plugins.table,i=this.context.table;if(!this.getSelection().isCollapsed&&!e._selectedCell){this.controllersOff(),this.util.removeClass(t,"se-table-selected-cell");return}const l=i._element||this.plugins.table._selectedTable||this.util.getParentElement(t,"TABLE");i._maxWidth=this.util.hasClass(l,"se-table-size-100")||l.style.width==="100%"||!l.style.width&&!this.util.hasClass(l,"se-table-size-auto"),i._fixedColumn=this.util.hasClass(l,"se-table-layout-fixed")||l.style.tableLayout==="fixed",e.setTableStyle.call(this,i._maxWidth?"width|column":"width"),e.setPositionControllerTop.call(this,l),e.setPositionControllerDiv.call(this,t,e._shift),e._shift||this.controllersOn(i.resizeDiv,i.tableController,e.init.bind(this),t,"table")},setPositionControllerTop:function(t){this.setControllerPosition(this.context.table.tableController,t,"top",{left:0,top:0})},setPositionControllerDiv:function(t,e){const i=this.context.table,l=i.resizeDiv;this.plugins.table.setCellInfo.call(this,t,e),i.cellControllerTop?this.setControllerPosition(l,i._element,"top",{left:i.tableController.offsetWidth,top:0}):this.setControllerPosition(l,t,"bottom",{left:0,top:0})},setCellInfo:function(t,e){const i=this.context.table,l=i._element=this.plugins.table._selectedTable||this.util.getParentElement(t,"TABLE");if(/THEAD/i.test(l.firstElementChild.nodeName)?this.util.addClass(i.headerButton,"active"):this.util.removeClass(i.headerButton,"active"),e||i._physical_cellCnt===0){i._tdElement!==t&&(i._tdElement=t,i._trElement=t.parentNode);const n=i._trElements=l.rows,s=t.cellIndex;let o=0;for(let u=0,f=n[0].cells,p=n[0].cells.length;u<p;u++)o+=f[u].colSpan;const a=i._rowIndex=i._trElement.rowIndex;i._rowCnt=n.length,i._physical_cellCnt=i._trElement.cells.length,i._logical_cellCnt=o,i._physical_cellIndex=s,i._current_colSpan=i._tdElement.colSpan-1,i._current_rowSpan-i._trElement.cells[s].rowSpan-1;let r=[],c=[];for(let u=0,f,p;u<=a;u++){f=n[u].cells,p=0;for(let m=0,d=f.length,_,g,h,b;m<d;m++){if(_=f[m],g=_.colSpan-1,h=_.rowSpan-1,b=m+p,c.length>0)for(let v=0,x;v<c.length;v++)x=c[v],!(x.row>u)&&(b>=x.index?(p+=x.cs,b+=x.cs,x.rs-=1,x.row=u+1,x.rs<1&&(c.splice(v,1),v--)):m===d-1&&(x.rs-=1,x.row=u+1,x.rs<1&&(c.splice(v,1),v--)));if(u===a&&m===s){i._logical_cellIndex=b;break}h>0&&r.push({index:b,cs:g+1,rs:h,row:-1}),p+=g}c=c.concat(r).sort(function(m,d){return m.index-d.index}),r=[]}r=null,c=null}},editTable:function(t,e){const i=this.plugins.table,l=this.context.table,n=l._element,s=t==="row";if(s){const o=l._trElement.parentNode;if(/^THEAD$/i.test(o.nodeName)){if(e==="up")return;if(!o.nextElementSibling||!/^TBODY$/i.test(o.nextElementSibling.nodeName)){n.innerHTML+="<tbody><tr>"+i.createCells.call(this,"td",l._logical_cellCnt,!1)+"</tr></tbody>";return}}}if(i._ref){const o=l._tdElement,a=i._selectedCells;if(s)if(e)i.setCellInfo.call(this,e==="up"?a[0]:a[a.length-1],!0),i.editRow.call(this,e,o);else{let r=a[0].parentNode;const c=[a[0]];for(let u=1,f=a.length,p;u<f;u++)p=a[u],r!==p.parentNode&&(c.push(p),r=p.parentNode);for(let u=0,f=c.length;u<f;u++)i.setCellInfo.call(this,c[u],!0),i.editRow.call(this,e)}else{const r=a[0].parentNode;if(e){let c=null;for(let u=0,f=a.length-1;u<f;u++)if(r!==a[u+1].parentNode){c=a[u];break}i.setCellInfo.call(this,e==="left"?a[0]:c||a[0],!0),i.editCell.call(this,e,o)}else{const c=[a[0]];for(let u=1,f=a.length,p;u<f&&(p=a[u],r===p.parentNode);u++)c.push(p);for(let u=0,f=c.length;u<f;u++)i.setCellInfo.call(this,c[u],!0),i.editCell.call(this,e)}}e||i.init.call(this)}else i[s?"editRow":"editCell"].call(this,e);if(!e){const o=n.children;for(let a=0;a<o.length;a++)o[a].children.length===0&&(this.util.removeItem(o[a]),a--);n.children.length===0&&this.util.removeItem(n)}},editRow:function(t,e){const i=this.context.table,l=!t,n=t==="up",s=i._rowIndex,o=l||n?s:s+i._current_rowSpan+1,a=l?-1:1,r=i._trElements;let c=i._logical_cellCnt;for(let u=0,f=s+(l?-1:0),p;u<=f;u++){if(p=r[u].cells,p.length===0)return;for(let m=0,d=p.length,_,g;m<d;m++)_=p[m].rowSpan,g=p[m].colSpan,!(_<2&&g<2)&&_+u>o&&o>u&&(p[m].rowSpan=_+a,c-=g)}if(l){const u=r[s+1];if(u){const f=[];let p=r[s].cells,m=0;for(let d=0,_=p.length,g,h;d<_;d++)g=p[d],h=d+m,m+=g.colSpan-1,g.rowSpan>1&&(g.rowSpan-=1,f.push({cell:g.cloneNode(!1),index:h}));if(f.length>0){let d=f.shift();p=u.cells,m=0;for(let _=0,g=p.length,h,b;_<g&&(h=p[_],b=_+m,m+=h.colSpan-1,!(b>=d.index&&(_--,m--,m+=d.cell.colSpan-1,u.insertBefore(d.cell,h),d=f.shift(),!d)));_++);if(d){u.appendChild(d.cell);for(let _=0,g=f.length;_<g;_++)u.appendChild(f[_].cell)}}}i._element.deleteRow(o)}else{const u=i._element.insertRow(o);u.innerHTML=this.plugins.table.createCells.call(this,"td",c,!1)}l?this.controllersOff():this.plugins.table.setPositionControllerDiv.call(this,e||i._tdElement,!0)},editCell:function(t,e){const i=this.context.table,l=this.util,n=!t,s=t==="left",o=i._current_colSpan,a=n||s?i._logical_cellIndex:i._logical_cellIndex+o+1,r=i._trElements;let c=[],u=[],f=0;const p=[],m=[];for(let d=0,_=i._rowCnt,g,h,b,v,x,y;d<_;d++){g=r[d],h=a,x=!1,b=g.cells,y=0;for(let w=0,C,B=b.length,z,L,N;w<B&&(C=b[w],!!C);w++)if(z=C.rowSpan-1,L=C.colSpan-1,n){if(N=w+y,u.length>0){const E=!b[w+1];for(let S=0,M;S<u.length;S++)M=u[S],!(M.row>d)&&(N>=M.index?(y+=M.cs,N=w+y,M.rs-=1,M.row=d+1,M.rs<1&&(u.splice(S,1),S--)):E&&(M.rs-=1,M.row=d+1,M.rs<1&&(u.splice(S,1),S--)))}z>0&&c.push({rs:z,cs:L+1,index:N,row:-1}),N>=h&&N+L<=h+o?p.push(C):N<=h+o&&N+L>=h?C.colSpan-=l.getOverlapRangeAtIndex(a,a+o,N,N+L):z>0&&(N<h||N+L>h+o)&&m.push({cell:C,i:d,rs:d+z}),y+=L}else{if(w>=h)break;if(L>0){if(f<1&&L+w>=h){C.colSpan+=1,h=null,f=z+1;break}h-=L}if(!x){for(let E=0,S;E<u.length;E++)S=u[E],h-=S.cs,S.rs-=1,S.rs<1&&(u.splice(E,1),E--);x=!0}}if(u=u.concat(c).sort(function(w,C){return w.index-C.index}),c=[],!n){if(f>0){f-=1;continue}h!==null&&b.length>0&&(v=this.plugins.table.createCells.call(this,b[0].nodeName,0,!0),v=g.insertBefore(v,b[h]))}}if(n){let d,_;for(let g=0,h=p.length,b;g<h;g++)b=p[g].parentNode,l.removeItem(p[g]),b.cells.length===0&&(d||(d=l.getArrayIndex(r,b)),_=l.getArrayIndex(r,b),l.removeItem(b));for(let g=0,h=m.length,b;g<h;g++)b=m[g],b.cell.rowSpan=l.getOverlapRangeAtIndex(d,_,b.i,b.rs);this.controllersOff()}else this.plugins.table.setPositionControllerDiv.call(this,e||i._tdElement,!0)},_closeSplitMenu:null,openSplitMenu:function(){this.util.addClass(this.context.table.splitButton,"on"),this.context.table.splitMenu.style.display="inline-table",this.plugins.table._closeSplitMenu=(function(){this.util.removeClass(this.context.table.splitButton,"on"),this.context.table.splitMenu.style.display="none",this.removeDocEvent("click",this.plugins.table._closeSplitMenu),this.plugins.table._closeSplitMenu=null}).bind(this),this.addDocEvent("click",this.plugins.table._closeSplitMenu)},splitCells:function(t){const e=this.util,i=t==="vertical",l=this.context.table,n=l._tdElement,s=l._trElements,o=l._trElement,a=l._logical_cellIndex,r=l._rowIndex,c=this.plugins.table.createCells.call(this,n.nodeName,0,!0);if(i){const u=n.colSpan;if(c.rowSpan=n.rowSpan,u>1)c.colSpan=this._w.Math.floor(u/2),n.colSpan=u-c.colSpan,o.insertBefore(c,n.nextElementSibling);else{let f=[],p=[];for(let m=0,d=l._rowCnt,_,g;m<d;m++){_=s[m].cells,g=0;for(let h=0,b=_.length,v,x,y,w;h<b;h++){if(v=_[h],x=v.colSpan-1,y=v.rowSpan-1,w=h+g,p.length>0)for(let C=0,B;C<p.length;C++)B=p[C],!(B.row>m)&&(w>=B.index?(g+=B.cs,w+=B.cs,B.rs-=1,B.row=m+1,B.rs<1&&(p.splice(C,1),C--)):h===b-1&&(B.rs-=1,B.row=m+1,B.rs<1&&(p.splice(C,1),C--)));if(w<=a&&y>0&&f.push({index:w,cs:x+1,rs:y,row:-1}),v!==n&&w<=a&&w+x>=a+u-1){v.colSpan+=1;break}if(w>a)break;g+=x}p=p.concat(f).sort(function(h,b){return h.index-b.index}),f=[]}o.insertBefore(c,n.nextElementSibling)}}else{const u=n.rowSpan;if(c.colSpan=n.colSpan,u>1){c.rowSpan=this._w.Math.floor(u/2);const f=u-c.rowSpan,p=[],m=e.getArrayIndex(s,o)+f;for(let h=0,b,v;h<m;h++){b=s[h].cells,v=0;for(let x=0,y=b.length,w,C,B;x<y&&(B=x+v,!(B>=a));x++)w=b[x],C=w.rowSpan-1,C>0&&C+h>=m&&B<a&&p.push({index:B,cs:w.colSpan}),v+=w.colSpan-1}const d=s[m],_=d.cells;let g=p.shift();for(let h=0,b=_.length,v=0,x,y,w,C;h<b;h++){if(w=h+v,x=_[h],y=x.colSpan-1,C=w+y+1,g&&C>=g.index&&(v+=g.cs,C+=g.cs,g=p.shift()),C>=a||h===b-1){d.insertBefore(c,x.nextElementSibling);break}v+=y}n.rowSpan=f}else{c.rowSpan=n.rowSpan;const f=e.createElement("TR");f.appendChild(c);for(let d=0,_;d<r;d++){if(_=s[d].cells,_.length===0)return;for(let g=0,h=_.length;g<h;g++)d+_[g].rowSpan-1>=r&&(_[g].rowSpan+=1)}const p=l._physical_cellIndex,m=o.cells;for(let d=0,_=m.length;d<_;d++)d!==p&&(m[d].rowSpan+=1);o.parentNode.insertBefore(f,o.nextElementSibling)}}this.focusEdge(n),this.plugins.table.setPositionControllerDiv.call(this,n,!0)},mergeCells:function(){const t=this.plugins.table,e=this.context.table,i=this.util,l=t._ref,n=t._selectedCells,s=n[0];let o=null,a=null,r=l.ce-l.cs+1,c=l.re-l.rs+1,u="",f=null;for(let p=1,m=n.length,d,_;p<m;p++){d=n[p],f!==d.parentNode&&(f=d.parentNode),_=d.children;for(let g=0,h=_.length;g<h;g++)i.isFormatElement(_[g])&&i.onlyZeroWidthSpace(_[g].textContent)&&i.removeItem(_[g]);u+=d.innerHTML,i.removeItem(d),f.cells.length===0&&(o?a=f:o=f,c-=1)}if(o){const p=e._trElements,m=i.getArrayIndex(p,o),d=i.getArrayIndex(p,a||o),_=[];for(let g=0,h;g<=d;g++){if(h=p[g].cells,h.length===0){_.push(p[g]);continue}for(let b=0,v=h.length,x,y;b<v;b++)x=h[b],y=x.rowSpan-1,y>0&&g+y>=m&&(x.rowSpan-=i.getOverlapRangeAtIndex(m,d,g,g+y))}for(let g=0,h=_.length;g<h;g++)i.removeItem(_[g])}s.innerHTML+=u,s.colSpan=r,s.rowSpan=c,this.controllersOff(),t.setActiveButton.call(this,!0,!1),t.call_controller_tableEdit.call(this,s),i.addClass(s,"se-table-selected-cell"),this.focusEdge(s)},toggleHeader:function(){const t=this.util,e=this.context.table.headerButton,i=t.hasClass(e,"active"),l=this.context.table._element;if(i)t.removeItem(l.querySelector("thead"));else{const n=t.createElement("THEAD");n.innerHTML="<tr>"+this.plugins.table.createCells.call(this,"th",this.context.table._logical_cellCnt,!1)+"</tr>",l.insertBefore(n,l.firstElementChild)}t.toggleClass(e,"active"),/TH/i.test(this.context.table._tdElement.nodeName)?this.controllersOff():this.plugins.table.setPositionControllerDiv.call(this,this.context.table._tdElement,!1)},setTableStyle:function(t){const e=this.context.table,i=e._element;let l,n,s,o;t.indexOf("width")>-1&&(l=e.resizeButton.firstElementChild,n=e.resizeText,e._maxWidth?(s=e.icons.reduction,o=e.minText,e.columnFixedButton.style.display="block",this.util.removeClass(i,"se-table-size-auto"),this.util.addClass(i,"se-table-size-100")):(s=e.icons.expansion,o=e.maxText,e.columnFixedButton.style.display="none",this.util.removeClass(i,"se-table-size-100"),this.util.addClass(i,"se-table-size-auto")),this.util.changeElement(l,s),this.util.changeTxt(n,o)),t.indexOf("column")>-1&&(e._fixedColumn?(this.util.removeClass(i,"se-table-layout-auto"),this.util.addClass(i,"se-table-layout-fixed"),this.util.addClass(e.columnFixedButton,"active")):(this.util.removeClass(i,"se-table-layout-fixed"),this.util.addClass(i,"se-table-layout-auto"),this.util.removeClass(e.columnFixedButton,"active")))},setActiveButton:function(t,e){const i=this.context.table;/^TH$/i.test(t.nodeName)?(i.insertRowAboveButton.setAttribute("disabled",!0),i.insertRowBelowButton.setAttribute("disabled",!0)):(i.insertRowAboveButton.removeAttribute("disabled"),i.insertRowBelowButton.removeAttribute("disabled")),!e||t===e?(i.splitButton.removeAttribute("disabled"),i.mergeButton.setAttribute("disabled",!0)):(i.splitButton.setAttribute("disabled",!0),i.mergeButton.removeAttribute("disabled"))},_bindOnSelect:null,_bindOffSelect:null,_bindOffShift:null,_selectedCells:null,_shift:!1,_fixedCell:null,_fixedCellName:null,_selectedCell:null,_selectedTable:null,_ref:null,_toggleEditor:function(t){this.context.element.wysiwyg.setAttribute("contenteditable",t),t?this.util.removeClass(this.context.element.wysiwyg,"se-disabled"):this.util.addClass(this.context.element.wysiwyg,"se-disabled")},_offCellMultiSelect:function(t){t.stopPropagation();const e=this.plugins.table;e._shift?e._initBind&&(this._wd.removeEventListener("touchmove",e._initBind),e._initBind=null):(e._removeEvents.call(this),e._toggleEditor.call(this,!0)),!(!e._fixedCell||!e._selectedTable)&&(e.setActiveButton.call(this,e._fixedCell,e._selectedCell),e.call_controller_tableEdit.call(this,e._selectedCell||e._fixedCell),e._selectedCells=e._selectedTable.querySelectorAll(".se-table-selected-cell"),e._selectedCell&&e._fixedCell&&this.focusEdge(e._selectedCell),e._shift||(e._fixedCell=null,e._selectedCell=null,e._fixedCellName=null))},_onCellMultiSelect:function(t){this._antiBlur=!0;const e=this.plugins.table,i=this.util.getParentElement(t.target,this.util.isCell);if(e._shift)i===e._fixedCell?e._toggleEditor.call(this,!0):e._toggleEditor.call(this,!1);else if(!e._ref){if(i===e._fixedCell)return;e._toggleEditor.call(this,!1)}!i||i===e._selectedCell||e._fixedCellName!==i.nodeName||e._selectedTable!==this.util.getParentElement(i,"TABLE")||(e._selectedCell=i,e._setMultiCells.call(this,e._fixedCell,i))},_setMultiCells:function(t,e){const i=this.plugins.table,l=i._selectedTable.rows,n=this.util,s=i._selectedTable.querySelectorAll(".se-table-selected-cell");for(let u=0,f=s.length;u<f;u++)n.removeClass(s[u],"se-table-selected-cell");if(t===e&&(n.addClass(t,"se-table-selected-cell"),!i._shift))return;let o=!0,a=[],r=[];const c=i._ref={_i:0,cs:null,ce:null,rs:null,re:null};for(let u=0,f=l.length,p,m;u<f;u++){p=l[u].cells,m=0;for(let d=0,_=p.length,g,h,b,v;d<_;d++){if(g=p[d],b=g.colSpan-1,v=g.rowSpan-1,h=d+m,a.length>0)for(let x=0,y;x<a.length;x++)y=a[x],!(y.row>u)&&(h>=y.index?(m+=y.cs,h+=y.cs,y.rs-=1,y.row=u+1,y.rs<1&&(a.splice(x,1),x--)):d===_-1&&(y.rs-=1,y.row=u+1,y.rs<1&&(a.splice(x,1),x--)));if(o){if((g===t||g===e)&&(c.cs=c.cs!==null&&c.cs<h?c.cs:h,c.ce=c.ce!==null&&c.ce>h+b?c.ce:h+b,c.rs=c.rs!==null&&c.rs<u?c.rs:u,c.re=c.re!==null&&c.re>u+v?c.re:u+v,c._i+=1),c._i===2){o=!1,a=[],r=[],u=-1;break}}else if(n.getOverlapRangeAtIndex(c.cs,c.ce,h,h+b)&&n.getOverlapRangeAtIndex(c.rs,c.re,u,u+v)){const x=c.cs<h?c.cs:h,y=c.ce>h+b?c.ce:h+b,w=c.rs<u?c.rs:u,C=c.re>u+v?c.re:u+v;if(c.cs!==x||c.ce!==y||c.rs!==w||c.re!==C){c.cs=x,c.ce=y,c.rs=w,c.re=C,u=-1,a=[],r=[];break}n.addClass(g,"se-table-selected-cell")}v>0&&r.push({index:h,cs:b+1,rs:v,row:-1}),m+=g.colSpan-1}a=a.concat(r).sort(function(d,_){return d.index-_.index}),r=[]}},_removeEvents:function(){const t=this.plugins.table;t._initBind&&(this._wd.removeEventListener("touchmove",t._initBind),t._initBind=null),t._bindOnSelect&&(this._wd.removeEventListener("mousedown",t._bindOnSelect),this._wd.removeEventListener("mousemove",t._bindOnSelect),t._bindOnSelect=null),t._bindOffSelect&&(this._wd.removeEventListener("mouseup",t._bindOffSelect),t._bindOffSelect=null),t._bindOffShift&&(this._wd.removeEventListener("keyup",t._bindOffShift),t._bindOffShift=null)},_initBind:null,onTableCellMultiSelect:function(t,e){const i=this.plugins.table;i._removeEvents.call(this),this.controllersOff(),i._shift=e,i._fixedCell=t,i._fixedCellName=t.nodeName,i._selectedTable=this.util.getParentElement(t,"TABLE");const l=i._selectedTable.querySelectorAll(".se-table-selected-cell");for(let n=0,s=l.length;n<s;n++)this.util.removeClass(l[n],"se-table-selected-cell");this.util.addClass(t,"se-table-selected-cell"),i._bindOnSelect=i._onCellMultiSelect.bind(this),i._bindOffSelect=i._offCellMultiSelect.bind(this),e?(i._bindOffShift=(function(){this.controllersOn(this.context.table.resizeDiv,this.context.table.tableController,this.plugins.table.init.bind(this),t,"table"),i._ref||this.controllersOff()}).bind(this),this._wd.addEventListener("keyup",i._bindOffShift,!1),this._wd.addEventListener("mousedown",i._bindOnSelect,!1)):this._wd.addEventListener("mousemove",i._bindOnSelect,!1),this._wd.addEventListener("mouseup",i._bindOffSelect,!1),i._initBind=i.init.bind(this),this._wd.addEventListener("touchmove",i._initBind,!1)},onClick_tableController:function(t){t.stopPropagation();const e=t.target.getAttribute("data-command")?t.target:t.target.parentNode;if(e.getAttribute("disabled"))return;const i=e.getAttribute("data-command"),l=e.getAttribute("data-value"),n=e.getAttribute("data-option"),s=this.plugins.table;if(typeof s._closeSplitMenu=="function"&&(s._closeSplitMenu(),i==="onsplit")||!i)return;t.preventDefault();const o=this.context.table;switch(i){case"insert":case"delete":s.editTable.call(this,l,n);break;case"header":s.toggleHeader.call(this);break;case"onsplit":s.openSplitMenu.call(this);break;case"split":s.splitCells.call(this,l);break;case"merge":s.mergeCells.call(this);break;case"resize":o._maxWidth=!o._maxWidth,s.setTableStyle.call(this,"width"),s.setPositionControllerTop.call(this,o._element),s.setPositionControllerDiv.call(this,o._tdElement,s._shift);break;case"layout":o._fixedColumn=!o._fixedColumn,s.setTableStyle.call(this,"column"),s.setPositionControllerTop.call(this,o._element),s.setPositionControllerDiv.call(this,o._tdElement,s._shift);break;case"remove":const a=o._element.parentNode;this.util.removeItem(o._element),this.controllersOff(),a!==this.context.element.wysiwyg&&this.util.removeItemAllParents(a,function(r){return r.childNodes.length===0},null),this.focus()}this.history.push(!1)}},gt={name:"formatBlock",display:"submenu",add:function(t,e){const i=t.context;i.formatBlock={targetText:e.querySelector(".txt"),targetTooltip:e.parentNode.querySelector(".se-tooltip-text"),_formatList:null,currentFormat:""};let l=this.setSubmenu(t);l.querySelector("ul").addEventListener("click",this.pickUp.bind(t)),i.formatBlock._formatList=l.querySelectorAll("li button"),t.initMenuTarget(this.name,e,l),l=null},setSubmenu:function(t){const e=t.options,i=t.lang.toolbar,l=t.util.createElement("DIV");l.className="se-submenu se-list-layer se-list-format";const n=["p","div","blockquote","pre","h1","h2","h3","h4","h5","h6"],s=!e.formats||e.formats.length===0?n:e.formats;let o='<div class="se-list-inner"><ul class="se-list-basic">';for(let a=0,r=s.length,c,u,f,p,m,d,_;a<r;a++)c=s[a],typeof c=="string"&&n.indexOf(c)>-1?(u=c.toLowerCase(),f=u==="blockquote"?"range":u==="pre"?"free":"replace",m=/^h/.test(u)?u.match(/\d+/)[0]:"",p=i["tag_"+(m?"h":u)]+m,_="",d=""):(u=c.tag.toLowerCase(),f=c.command,p=c.name||u,_=c.class,d=_?' class="'+_+'"':""),o+='<li><button type="button" class="se-btn-list" data-command="'+f+'" data-value="'+u+'" data-class="'+_+'" title="'+p+'" aria-label="'+p+'"><'+u+d+">"+p+"</"+u+"></button></li>";return o+="</ul></div>",l.innerHTML=o,l},active:function(t){let e=this.lang.toolbar.formats;const i=this.context.formatBlock.targetText;if(!t)this.util.changeTxt(i,e);else if(this.util.isFormatElement(t)){const n=this.context.formatBlock._formatList,s=t.nodeName.toLowerCase(),o=(t.className.match(/(\s|^)__se__format__[^\s]+/)||[""])[0].trim();for(let a=0,r=n.length,c;a<r;a++)if(c=n[a],s===c.getAttribute("data-value")&&o===c.getAttribute("data-class")){e=c.title;break}return this.util.changeTxt(i,e),i.setAttribute("data-value",s),i.setAttribute("data-class",o),!0}return!1},on:function(){const t=this.context.formatBlock,e=t._formatList,i=t.targetText,l=(i.getAttribute("data-value")||"")+(i.getAttribute("data-class")||"");if(l!==t.currentFormat){for(let n=0,s=e.length,o;n<s;n++)o=e[n],l===o.getAttribute("data-value")+o.getAttribute("data-class")?this.util.addClass(o,"active"):this.util.removeClass(o,"active");t.currentFormat=l}},pickUp:function(t){t.preventDefault(),t.stopPropagation();let e=t.target,i=null,l=null,n=null,s="";for(;!i&&!/UL/i.test(e.tagName);){if(i=e.getAttribute("data-command"),l=e.getAttribute("data-value"),s=e.getAttribute("data-class"),i){n=e.firstChild;break}e=e.parentNode}if(i){if(i==="range"){const o=n.cloneNode(!1);this.applyRangeFormatElement(o)}else{let o=this.getRange(),a=this.getSelectedElementsAndComponents(!1);if(a.length===0&&(o=this.getRange_addLine(o,null),a=this.getSelectedElementsAndComponents(!1),a.length===0))return;const r=o.startOffset,c=o.endOffset,u=this.util;let f=a[0],p=a[a.length-1];const m=u.getNodePath(o.startContainer,f,null,null),d=u.getNodePath(o.endContainer,p,null,null),_=this.detachList(a,!1);_.sc&&(f=_.sc),_.ec&&(p=_.ec),this.setRange(u.getNodeFromPath(m,f),r,u.getNodeFromPath(d,p),c);const g=this.getSelectedElementsAndComponents(!1);if(i==="free"){const h=g.length-1;let b=g[h].parentNode,v=n.cloneNode(!1);const x=v;for(let y=h,w,C,B,z,L,N,E=!0;y>=0;y--)if(w=g[y],w!==(g[y+1]?g[y+1].parentNode:null)){if(N=u.isComponent(w),C=N?"":w.innerHTML.replace(/(?!>)\s+(?=<)|\n/g," "),B=u.getParentElement(w,function(S){return S.parentNode===b}),(b!==w.parentNode||N)&&(u.isFormatElement(b)?(b.parentNode.insertBefore(v,b.nextSibling),b=b.parentNode):(b.insertBefore(v,B?B.nextSibling:null),b=w.parentNode),z=v.nextSibling,z&&v.nodeName===z.nodeName&&u.isSameAttributes(v,z)&&(v.innerHTML+="<BR>"+z.innerHTML,u.removeItem(z)),v=n.cloneNode(!1),E=!0),L=v.innerHTML,v.innerHTML=(E||!C||!L||/<br>$/i.test(C)?C:C+"<BR>")+L,y===0){b.insertBefore(v,w),z=w.nextSibling,z&&v.nodeName===z.nodeName&&u.isSameAttributes(v,z)&&(v.innerHTML+="<BR>"+z.innerHTML,u.removeItem(z));const S=v.previousSibling;S&&v.nodeName===S.nodeName&&u.isSameAttributes(v,S)&&(S.innerHTML+="<BR>"+v.innerHTML,u.removeItem(v))}N||u.removeItem(w),C&&(E=!1)}this.setRange(x,0,x,0)}else{for(let h=0,b=g.length,v,x;h<b;h++)v=g[h],(v.nodeName.toLowerCase()!==l.toLowerCase()||(v.className.match(/(\s|^)__se__format__[^\s]+/)||[""])[0].trim()!==s)&&!u.isComponent(v)&&(x=n.cloneNode(!1),u.copyFormatAttributes(x,v),x.innerHTML=v.innerHTML,v.parentNode.replaceChild(x,v)),h===0&&(f=x||v),h===b-1&&(p=x||v),x=null;this.setRange(u.getNodeFromPath(m,f),r,u.getNodeFromPath(d,p),c)}this.history.push(!1)}this.submenuOff()}}},mt={name:"lineHeight",display:"submenu",add:function(t,e){const i=t.context;i.lineHeight={_sizeList:null,currentSize:-1};let l=this.setSubmenu(t),n=l.querySelector("ul");n.addEventListener("click",this.pickup.bind(t)),i.lineHeight._sizeList=n.querySelectorAll("li button"),t.initMenuTarget(this.name,e,l),l=null,n=null},setSubmenu:function(t){const e=t.options,i=t.lang,l=t.util.createElement("DIV");l.className="se-submenu se-list-layer";const n=e.lineHeights?e.lineHeights:[{text:"1",value:1},{text:"1.15",value:1.15},{text:"1.5",value:1.5},{text:"2",value:2}];let s='<div class="se-list-inner"><ul class="se-list-basic"><li><button type="button" class="default_value se-btn-list" title="'+i.toolbar.default+'" aria-label="'+i.toolbar.default+'">('+i.toolbar.default+")</button></li>";for(let o=0,a=n.length,r;o<a;o++)r=n[o],s+='<li><button type="button" class="se-btn-list" data-value="'+r.value+'" title="'+r.text+'" aria-label="'+r.text+'">'+r.text+"</button></li>";return s+="</ul></div>",l.innerHTML=s,l},on:function(){const t=this.context.lineHeight,e=t._sizeList,i=this.util.getFormatElement(this.getSelectionNode()),l=i?i.style.lineHeight+"":"";if(l!==t.currentSize){for(let n=0,s=e.length;n<s;n++)l===e[n].getAttribute("data-value")?this.util.addClass(e[n],"active"):this.util.removeClass(e[n],"active");t.currentSize=l}},pickup:function(t){if(!/^BUTTON$/i.test(t.target.tagName))return!1;t.preventDefault(),t.stopPropagation();const e=t.target.getAttribute("data-value")||"",i=this.getSelectedElements();for(let l=0,n=i.length;l<n;l++)i[l].style.lineHeight=e;this.submenuOff(),this.history.push(!1)}},_t={name:"template",display:"submenu",add:function(t,e){const i=t.context;i.template={selectedIndex:-1};let l=this.setSubmenu(t);l.querySelector("ul").addEventListener("click",this.pickup.bind(t)),t.initMenuTarget(this.name,e,l),l=null},setSubmenu:function(t){const e=t.options.templates;if(!e||e.length===0)throw Error('[SUNEDITOR.plugins.template.fail] To use the "template" plugin, please define the "templates" option.');const i=t.util.createElement("DIV");i.className="se-list-layer";let l='<div class="se-submenu se-list-inner"><ul class="se-list-basic">';for(let n=0,s=e.length,o;n<s;n++)o=e[n],l+='<li><button type="button" class="se-btn-list" data-value="'+n+'" title="'+o.name+'" aria-label="'+o.name+'">'+o.name+"</button></li>";return l+="</ul></div>",i.innerHTML=l,i},pickup:function(t){if(!/^BUTTON$/i.test(t.target.tagName))return!1;t.preventDefault(),t.stopPropagation(),this.context.template.selectedIndex=t.target.getAttribute("data-value")*1;const e=this.options.templates[this.context.template.selectedIndex];if(e.html)this.setContents(e.html);else throw this.submenuOff(),Error('[SUNEDITOR.template.fail] cause : "templates[i].html not found"');this.submenuOff()}},bt={name:"paragraphStyle",display:"submenu",add:function(t,e){const i=t.context;i.paragraphStyle={_classList:null};let l=this.setSubmenu(t);l.querySelector("ul").addEventListener("click",this.pickUp.bind(t)),i.paragraphStyle._classList=l.querySelectorAll("li button"),t.initMenuTarget(this.name,e,l),l=null},setSubmenu:function(t){const e=t.options,i=t.util.createElement("DIV");i.className="se-submenu se-list-layer se-list-format";const l=t.lang.menu,n={spaced:{name:l.spaced,class:"__se__p-spaced",_class:""},bordered:{name:l.bordered,class:"__se__p-bordered",_class:""},neon:{name:l.neon,class:"__se__p-neon",_class:""}},s=!e.paragraphStyles||e.paragraphStyles.length===0?["spaced","bordered","neon"]:e.paragraphStyles;let o='<div class="se-list-inner"><ul class="se-list-basic">';for(let a=0,r=s.length,c,u,f,p;a<r;a++){if(c=s[a],typeof c=="string"){const m=n[c.toLowerCase()];if(!m)continue;c=m}u=c.name,f=c.class?' class="'+c.class+'"':"",p=c._class,o+='<li><button type="button" class="se-btn-list'+(p?" "+p:"")+'" data-value="'+c.class+'" title="'+u+'" aria-label="'+u+'"><div'+f+">"+u+"</div></button></li>"}return o+="</ul></div>",i.innerHTML=o,i},on:function(){const e=this.context.paragraphStyle._classList,i=this.util.getFormatElement(this.getSelectionNode());for(let l=0,n=e.length;l<n;l++)this.util.hasClass(i,e[l].getAttribute("data-value"))?this.util.addClass(e[l],"active"):this.util.removeClass(e[l],"active")},pickUp:function(t){t.preventDefault(),t.stopPropagation();let e=t.target,i=null;for(;!/^UL$/i.test(e.tagName)&&(i=e.getAttribute("data-value"),!i);)e=e.parentNode;if(!i)return;let l=this.getSelectedElements();if(l.length===0&&(this.getRange_addLine(this.getRange(),null),l=this.getSelectedElements(),l.length===0))return;const n=this.util.hasClass(e,"active")?this.util.removeClass.bind(this.util):this.util.addClass.bind(this.util);for(let s=0,o=l.length;s<o;s++)n(l[s],i);this.submenuOff(),this.history.push(!1)}},vt={name:"textStyle",display:"submenu",add:function(t,e){const i=t.context;i.textStyle={_styleList:null};let l=this.setSubmenu(t),n=l.querySelector("ul");n.addEventListener("click",this.pickup.bind(t)),i.textStyle._styleList=l.querySelectorAll("li button"),t.initMenuTarget(this.name,e,l),l=null,n=null},setSubmenu:function(t){const e=t.options,i=t.util.createElement("DIV");i.className="se-submenu se-list-layer se-list-format";const l={code:{name:t.lang.menu.code,class:"__se__t-code",tag:"code"},translucent:{name:t.lang.menu.translucent,style:"opacity: 0.5;",tag:"span"},shadow:{name:t.lang.menu.shadow,class:"__se__t-shadow",tag:"span"}},n=e.textStyles?e.textStyles:t._w.Object.keys(l);let s='<div class="se-list-inner"><ul class="se-list-basic">';for(let o=0,a=n.length,r,c,u,f,p,m,d;o<a;o++){if(r=n[o],f="",m="",p=[],typeof r=="string"){const _=l[r.toLowerCase()];if(!_)continue;r=_}u=r.name,c=r.tag||"span",d=r._class,r.style&&(f+=' style="'+r.style+'"',m+=r.style.replace(/:[^;]+(;|$)\s*/g,","),p.push("style")),r.class&&(f+=' class="'+r.class+'"',m+="."+r.class.trim().replace(/\s+/g,",."),p.push("class")),m=m.replace(/,$/,""),s+='<li><button type="button" class="se-btn-list'+(d?" "+d:"")+'" data-command="'+c+'" data-value="'+m+'" title="'+u+'" aria-label="'+u+'"><'+c+f+">"+u+"</"+c+"></button></li>"}return s+="</ul></div>",i.innerHTML=s,i},on:function(){const t=this.util,i=this.context.textStyle._styleList,l=this.getSelectionNode();for(let n=0,s=i.length,o,a,r;n<s;n++){o=i[n],a=o.getAttribute("data-value").split(",");for(let c=0,u,f;c<a.length;c++){for(u=l,r=!1;u&&!t.isFormatElement(u)&&!t.isComponent(u);){if(u.nodeName.toLowerCase()===o.getAttribute("data-command").toLowerCase()&&(f=a[c],/^\./.test(f)?t.hasClass(u,f.replace(/^\./,"")):u.style[f])){r=!0;break}u=u.parentNode}if(!r)break}r?t.addClass(o,"active"):t.removeClass(o,"active")}},pickup:function(t){t.preventDefault(),t.stopPropagation();let e=t.target,i=null,l=null;for(;!i&&!/UL/i.test(e.tagName);){if(i=e.getAttribute("data-command"),i){l=e.firstChild;break}e=e.parentNode}if(!i)return;const n=l.style.cssText.replace(/:.+(;|$)/g,",").split(",");n.pop();const s=l.classList;for(let r=0,c=s.length;r<c;r++)n.push("."+s[r]);const o=this.util.hasClass(e,"active")?null:l.cloneNode(!1),a=o?null:[l.nodeName];this.nodeChange(o,n,a,!0),this.submenuOff()}};var yt={exports:{}};(function(t){(function(e,i){t.exports=e.document?i(e,!0):function(l){if(!l.document)throw new Error("SUNEDITOR_MODULES a window with a document");return i(l)}})(typeof window<"u"?window:T,function(e,i){const l={name:"dialog",add:function(n){const s=n.context;s.dialog={kind:"",updateModal:!1,_closeSignal:!1};let o=n.util.createElement("DIV");o.className="se-dialog sun-editor-common";let a=n.util.createElement("DIV");a.className="se-dialog-back",a.style.display="none";let r=n.util.createElement("DIV");r.className="se-dialog-inner",r.style.display="none",o.appendChild(a),o.appendChild(r),s.dialog.modalArea=o,s.dialog.back=a,s.dialog.modal=r,s.dialog.modal.addEventListener("mousedown",this._onMouseDown_dialog.bind(n)),s.dialog.modal.addEventListener("click",this._onClick_dialog.bind(n)),s.element.relative.appendChild(o),o=null,a=null,r=null},_onMouseDown_dialog:function(n){/se-dialog-inner/.test(n.target.className)?this.context.dialog._closeSignal=!0:this.context.dialog._closeSignal=!1},_onClick_dialog:function(n){(/close/.test(n.target.getAttribute("data-command"))||this.context.dialog._closeSignal)&&this.plugins.dialog.close.call(this)},open:function(n,s){if(this.modalForm)return!1;this.plugins.dialog._bindClose&&(this._d.removeEventListener("keydown",this.plugins.dialog._bindClose),this.plugins.dialog._bindClose=null),this.plugins.dialog._bindClose=(function(a){/27/.test(a.keyCode)&&this.plugins.dialog.close.call(this)}).bind(this),this._d.addEventListener("keydown",this.plugins.dialog._bindClose),this.context.dialog.updateModal=s,this.options.popupDisplay==="full"?this.context.dialog.modalArea.style.position="fixed":this.context.dialog.modalArea.style.position="absolute",this.context.dialog.kind=n,this.modalForm=this.context[n].modal;const o=this.context[n].focusElement;typeof this.plugins[n].on=="function"&&this.plugins[n].on.call(this,s),this.context.dialog.modalArea.style.display="block",this.context.dialog.back.style.display="block",this.context.dialog.modal.style.display="block",this.modalForm.style.display="block",o&&o.focus()},_bindClose:null,close:function(){this.plugins.dialog._bindClose&&(this._d.removeEventListener("keydown",this.plugins.dialog._bindClose),this.plugins.dialog._bindClose=null);const n=this.context.dialog.kind;this.modalForm.style.display="none",this.context.dialog.back.style.display="none",this.context.dialog.modalArea.style.display="none",this.context.dialog.updateModal=!1,typeof this.plugins[n].init=="function"&&this.plugins[n].init.call(this),this.context.dialog.kind="",this.modalForm=null,this.focus()}};return typeof i>"u"&&(e.SUNEDITOR_MODULES||Object.defineProperty(e,"SUNEDITOR_MODULES",{enumerable:!0,writable:!1,configurable:!1,value:{}}),Object.defineProperty(e.SUNEDITOR_MODULES,"dialog",{enumerable:!0,writable:!1,configurable:!1,value:l})),l})})(yt);var Rt=yt.exports;const X=Y(Rt),Ut={name:"selectMenu",add:function(t){t.context.selectMenu={caller:{},callerContext:null}},setForm:function(){return'<div class="se-select-list"></div>'},createList:function(t,e,i){t.form.innerHTML="<ul>"+i+"</ul>",t.items=e,t.menus=t.form.querySelectorAll("li")},initEvent:function(t,e){const i=e.querySelector(".se-select-list"),l=this.context.selectMenu.caller[t]={form:i,items:[],menus:[],index:-1,item:null,clickMethod:null,callerName:t};i.addEventListener("mousedown",this.plugins.selectMenu.onMousedown_list),i.addEventListener("mousemove",this.plugins.selectMenu.onMouseMove_list.bind(this,l)),i.addEventListener("click",this.plugins.selectMenu.onClick_list.bind(this,l))},onMousedown_list:function(t){t.preventDefault(),t.stopPropagation()},onMouseMove_list:function(t,e){this.util.addClass(t.form,"__se_select-menu-mouse-move");const i=e.target.getAttribute("data-index");i&&(t.index=i*1)},onClick_list:function(t,e){const i=e.target.getAttribute("data-index");i&&t.clickMethod.call(this,t.items[i])},moveItem:function(t,e){this.util.removeClass(t.form,"__se_select-menu-mouse-move"),e=t.index+e;const i=t.menus,l=i.length,n=t.index=e>=l?0:e<0?l-1:e;for(let s=0;s<l;s++)s===n?this.util.addClass(i[s],"active"):this.util.removeClass(i[s],"active");t.item=t.items[n]},getItem:function(t,e){return e=!e||e<0?t.index:e,t.items[e]},on:function(t,e){const i=this.context.selectMenu.caller[t];this.context.selectMenu.callerContext=i,i.clickMethod=e,i.callerName=t},open:function(t,e){const i=t.form;i.style.visibility="hidden",i.style.display="block",e(i),i.style.visibility=""},close:function(t){t.form.style.display="none",t.items=[],t.menus=[],t.index=-1,t.item=null},init:function(t){t&&(t.items=[],t.menus=[],t.index=-1,t.item=null,t.callerName="",this.context.selectMenu.callerContext=null)}},xt={name:"anchor",add:function(t){t.addModule([Ut]),t.context.anchor={caller:{},forms:this.setDialogForm(t),host:(t._w.location.origin+t._w.location.pathname).replace(/\/$/,""),callerContext:null}},setDialogForm:function(t){const e=t.lang,i=t.options.linkRel,l=(t.options.linkRelDefault.default||"").split(" "),n=t.icons,s=t.util.createElement("DIV");let o='<div class="se-dialog-body"><div class="se-dialog-form"><label>'+e.dialogBox.linkBox.url+'</label><div class="se-dialog-form-files"><input class="se-input-form se-input-url" type="text" placeholder="'+(t.options.protocol||"")+'" /><button type="button" class="se-btn se-dialog-files-edge-button _se_bookmark_button" title="'+e.dialogBox.linkBox.bookmark+'" aria-label="'+e.dialogBox.linkBox.bookmark+'">'+n.bookmark+"</button>"+t.plugins.selectMenu.setForm()+'</div><div class="se-anchor-preview-form"><span class="se-svg se-anchor-preview-icon _se_anchor_bookmark_icon">'+n.bookmark+'</span><span class="se-svg se-anchor-preview-icon _se_anchor_download_icon">'+n.download+'</span><pre class="se-link-preview"></pre></div></div><div class="se-dialog-form"><label>'+e.dialogBox.linkBox.text+'</label><input class="se-input-form _se_anchor_text" type="text" /></div><div class="se-dialog-form-footer"><label><input type="checkbox" class="se-dialog-btn-check _se_anchor_check" />&nbsp;'+e.dialogBox.linkBox.newWindowCheck+'</label><label><input type="checkbox" class="se-dialog-btn-check _se_anchor_download" />&nbsp;'+e.dialogBox.linkBox.downloadLinkCheck+"</label>";if(i.length>0){o+='<div class="se-anchor-rel"><button type="button" class="se-btn se-btn-select se-anchor-rel-btn">&lt;rel&gt;</button><div class="se-anchor-rel-wrapper"><pre class="se-link-preview se-anchor-rel-preview"></pre></div><div class="se-list-layer"><div class="se-list-inner"><ul class="se-list-basic se-list-checked">';for(let a=0,r=i.length,c;a<r;a++)c=i[a],o+='<li><button type="button" class="se-btn-list'+(l.indexOf(c)>-1?" se-checked":"")+'" data-command="'+c+'" title="'+c+'" aria-label="'+c+'"><span class="se-svg">'+n.checked+"</span>"+c+"</button></li>";o+="</ul></div></div></div>"}return o+="</div></div>",s.innerHTML=o,s},initEvent:function(t,e){const i=this.plugins.anchor,l=this.context.anchor.caller[t]={modal:e,urlInput:null,linkDefaultRel:this.options.linkRelDefault,defaultRel:this.options.linkRelDefault.default||"",currentRel:[],linkAnchor:null,linkValue:"",_change:!1,callerName:t};typeof l.linkDefaultRel.default=="string"&&(l.linkDefaultRel.default=l.linkDefaultRel.default.trim()),typeof l.linkDefaultRel.check_new_window=="string"&&(l.linkDefaultRel.check_new_window=l.linkDefaultRel.check_new_window.trim()),typeof l.linkDefaultRel.check_bookmark=="string"&&(l.linkDefaultRel.check_bookmark=l.linkDefaultRel.check_bookmark.trim()),l.urlInput=e.querySelector(".se-input-url"),l.anchorText=e.querySelector("._se_anchor_text"),l.newWindowCheck=e.querySelector("._se_anchor_check"),l.downloadCheck=e.querySelector("._se_anchor_download"),l.download=e.querySelector("._se_anchor_download_icon"),l.preview=e.querySelector(".se-link-preview"),l.bookmark=e.querySelector("._se_anchor_bookmark_icon"),l.bookmarkButton=e.querySelector("._se_bookmark_button"),this.plugins.selectMenu.initEvent.call(this,t,e);const n=this.context.selectMenu.caller[t];this.options.linkRel.length>0&&(l.relButton=e.querySelector(".se-anchor-rel-btn"),l.relList=e.querySelector(".se-list-layer"),l.relPreview=e.querySelector(".se-anchor-rel-preview"),l.relButton.addEventListener("click",i.onClick_relButton.bind(this,l)),l.relList.addEventListener("click",i.onClick_relList.bind(this,l))),l.newWindowCheck.addEventListener("change",i.onChange_newWindowCheck.bind(this,l)),l.downloadCheck.addEventListener("change",i.onChange_downloadCheck.bind(this,l)),l.anchorText.addEventListener("input",i.onChangeAnchorText.bind(this,l)),l.urlInput.addEventListener("input",i.onChangeUrlInput.bind(this,l)),l.urlInput.addEventListener("keydown",i.onKeyDownUrlInput.bind(this,n)),l.urlInput.addEventListener("focus",i.onFocusUrlInput.bind(this,l,n)),l.urlInput.addEventListener("blur",i.onBlurUrlInput.bind(this,n)),l.bookmarkButton.addEventListener("click",i.onClick_bookmarkButton.bind(this,l))},on:function(t,e){const i=this.plugins.anchor;if(!e)i.init.call(this,t),t.anchorText.value=this.getSelection().toString().trim(),t.newWindowCheck.checked=this.options.linkTargetNewWindow;else if(t.linkAnchor){this.context.dialog.updateModal=!0;const l=t.linkAnchor.getAttribute("href");t.linkValue=t.preview.textContent=t.urlInput.value=i.selfPathBookmark.call(this,l)?l.substr(l.lastIndexOf("#")):l,t.anchorText.value=t.linkAnchor.textContent,t.newWindowCheck.checked=!!/_blank/i.test(t.linkAnchor.target),t.downloadCheck.checked=t.linkAnchor.download}this.context.anchor.callerContext=t,i.setRel.call(this,t,e&&t.linkAnchor?t.linkAnchor.rel:t.defaultRel),i.setLinkPreview.call(this,t,t.linkValue),this.plugins.selectMenu.on.call(this,t.callerName,this.plugins.anchor.setHeaderBookmark)},selfPathBookmark:function(t){const e=this._w.location.href.replace(/\/$/,"");return t.indexOf("#")===0||t.indexOf(e)===0&&t.indexOf("#")===(e.indexOf("#")===-1?e.length:e.substr(0,e.indexOf("#")).length)},_closeRelMenu:null,toggleRelList:function(t,e){if(!e)this.plugins.anchor._closeRelMenu&&this.plugins.anchor._closeRelMenu();else{const i=t.relButton,l=t.relList;this.util.addClass(i,"active"),l.style.visibility="hidden",l.style.display="block",this.options.rtl?l.style.left=i.offsetLeft-l.offsetWidth-1+"px":l.style.left=i.offsetLeft+i.offsetWidth+1+"px",l.style.top=i.offsetTop+i.offsetHeight/2-l.offsetHeight/2+"px",l.style.visibility="",this.plugins.anchor._closeRelMenu=(function(n,s,o){o&&(n.relButton.contains(o.target)||n.relList.contains(o.target))||(this.util.removeClass(s,"active"),n.relList.style.display="none",this.modalForm.removeEventListener("click",this.plugins.anchor._closeRelMenu),this.plugins.anchor._closeRelMenu=null)}).bind(this,t,i),this.modalForm.addEventListener("click",this.plugins.anchor._closeRelMenu)}},onClick_relButton:function(t,e){this.plugins.anchor.toggleRelList.call(this,t,!this.util.hasClass(e.target,"active"))},onClick_relList:function(t,e){const i=e.target,l=i.getAttribute("data-command");if(!l)return;const n=t.currentRel,s=this.util.toggleClass(i,"se-checked"),o=n.indexOf(l);s?o===-1&&n.push(l):o>-1&&n.splice(o,1),t.relPreview.title=t.relPreview.textContent=n.join(" ")},setRel:function(t,e){const i=t.relList,l=t.currentRel=e?e.split(" "):[];if(!i)return;const n=i.querySelectorAll("button");for(let s=0,o=n.length,a;s<o;s++)a=n[s].getAttribute("data-command"),l.indexOf(a)>-1?this.util.addClass(n[s],"se-checked"):this.util.removeClass(n[s],"se-checked");t.relPreview.title=t.relPreview.textContent=l.join(" ")},createHeaderList:function(t,e,i){const l=this.util.getListChildren(this.context.element.wysiwyg,function(a){return/h[1-6]/i.test(a.nodeName)});if(l.length===0)return;const n=new this._w.RegExp("^"+i.replace(/^#/,""),"i"),s=[];let o="";for(let a=0,r=l.length,c;a<r;a++)c=l[a],n.test(c.textContent)&&(s.push(c),o+='<li class="se-select-item" data-index="'+a+'">'+c.textContent+"</li>");s.length===0?this.plugins.selectMenu.close.call(this,e):(this.plugins.selectMenu.createList(e,s,o),this.plugins.selectMenu.open.call(this,e,this.plugins.anchor._setMenuListPosition.bind(this,t)))},_setMenuListPosition:function(t,e){e.style.top=t.urlInput.offsetHeight+1+"px"},onKeyDownUrlInput:function(t,e){switch(e.keyCode){case 38:e.preventDefault(),e.stopPropagation(),this.plugins.selectMenu.moveItem.call(this,t,-1);break;case 40:e.preventDefault(),e.stopPropagation(),this.plugins.selectMenu.moveItem.call(this,t,1);break;case 13:t.index>-1&&(e.preventDefault(),e.stopPropagation(),this.plugins.anchor.setHeaderBookmark.call(this,this.plugins.selectMenu.getItem(t,null)));break}},setHeaderBookmark:function(t){const e=this.context.anchor.callerContext,i=t.id||"h_"+this._w.Math.random().toString().replace(/.+\./,"");t.id=i,e.urlInput.value="#"+i,(!e.anchorText.value.trim()||!e._change)&&(e.anchorText.value=t.textContent),this.plugins.anchor.setLinkPreview.call(this,e,e.urlInput.value),this.plugins.selectMenu.close.call(this,this.context.selectMenu.callerContext),this.context.anchor.callerContext.urlInput.focus()},onChangeAnchorText:function(t,e){t._change=!!e.target.value.trim()},onChangeUrlInput:function(t,e){const i=e.target.value.trim();this.plugins.anchor.setLinkPreview.call(this,t,i),this.plugins.anchor.selfPathBookmark.call(this,i)?this.plugins.anchor.createHeaderList.call(this,t,this.context.selectMenu.callerContext,i):this.plugins.selectMenu.close.call(this,this.context.selectMenu.callerContext)},onFocusUrlInput:function(t,e){const i=t.urlInput.value;this.plugins.anchor.selfPathBookmark.call(this,i)&&this.plugins.anchor.createHeaderList.call(this,t,e,i)},onBlurUrlInput:function(t){this.plugins.selectMenu.close.call(this,t)},setLinkPreview:function(t,e){const i=t.preview,l=this.options.linkProtocol,n=this.options.linkNoPrefix,s=/^(mailto\:|tel\:|sms\:|https*\:\/\/|#)/.test(e)||e.indexOf(l)===0,o=l?this._w.RegExp("^"+this.util.escapeStringRegexp(e.substr(0,l.length))).test(l):!1;e=t.linkValue=i.textContent=e?n?e:l&&!s&&!o?l+e:s?e:/^www\./.test(e)?"http://"+e:this.context.anchor.host+(/^\//.test(e)?"":"/")+e:"",this.plugins.anchor.selfPathBookmark.call(this,e)?(t.bookmark.style.display="block",this.util.addClass(t.bookmarkButton,"active")):(t.bookmark.style.display="none",this.util.removeClass(t.bookmarkButton,"active")),!this.plugins.anchor.selfPathBookmark.call(this,e)&&t.downloadCheck.checked?t.download.style.display="block":t.download.style.display="none"},setCtx:function(t,e){t&&(e.linkAnchor=t,e.linkValue=t.href,e.currentRel=t.rel.split(" "))},updateAnchor:function(t,e,i,l,n){!this.plugins.anchor.selfPathBookmark.call(this,e)&&l.downloadCheck.checked?t.setAttribute("download",i||e):t.removeAttribute("download"),l.newWindowCheck.checked?t.target="_blank":t.removeAttribute("target");const s=l.currentRel.join(" ");s?t.rel=s:t.removeAttribute("rel"),t.href=e,n?t.children.length===0&&(t.textContent=""):t.textContent=i},createAnchor:function(t,e){if(t.linkValue.length===0)return null;const i=t.linkValue,l=t.anchorText,n=l.value.length===0?i:l.value,s=t.linkAnchor||this.util.createElement("A");return this.plugins.anchor.updateAnchor.call(this,s,i,n,t,e),t.linkValue=t.preview.textContent=t.urlInput.value=t.anchorText.value="",s},onClick_bookmarkButton:function(t){let e=t.urlInput.value;this.plugins.anchor.selfPathBookmark.call(this,e)?(e=e.substr(1),t.bookmark.style.display="none",this.util.removeClass(t.bookmarkButton,"active"),this.plugins.selectMenu.close.call(this,this.context.selectMenu.callerContext)):(e="#"+e,t.bookmark.style.display="block",this.util.addClass(t.bookmarkButton,"active"),t.downloadCheck.checked=!1,t.download.style.display="none",this.plugins.anchor.createHeaderList.call(this,t,this.context.selectMenu.callerContext,e)),t.urlInput.value=e,this.plugins.anchor.setLinkPreview.call(this,t,e),t.urlInput.focus()},onChange_newWindowCheck:function(t,e){typeof t.linkDefaultRel.check_new_window=="string"&&(e.target.checked?this.plugins.anchor.setRel.call(this,t,this.plugins.anchor._relMerge.call(this,t,t.linkDefaultRel.check_new_window)):this.plugins.anchor.setRel.call(this,t,this.plugins.anchor._relDelete.call(this,t,t.linkDefaultRel.check_new_window)))},onChange_downloadCheck:function(t,e){e.target.checked?(t.download.style.display="block",t.bookmark.style.display="none",this.util.removeClass(t.bookmarkButton,"active"),t.linkValue=t.preview.textContent=t.urlInput.value=t.urlInput.value.replace(/^\#+/,""),typeof t.linkDefaultRel.check_bookmark=="string"&&this.plugins.anchor.setRel.call(this,t,this.plugins.anchor._relMerge.call(this,t,t.linkDefaultRel.check_bookmark))):(t.download.style.display="none",typeof t.linkDefaultRel.check_bookmark=="string"&&this.plugins.anchor.setRel.call(this,t,this.plugins.anchor._relDelete.call(this,t,t.linkDefaultRel.check_bookmark)))},_relMerge:function(t,e){const i=t.currentRel;if(!e)return i.join(" ");if(/^only\:/.test(e))return e=e.replace(/^only\:/,"").trim(),t.currentRel=e.split(" "),e;const l=e.split(" ");for(let n=0,s=l.length,o;n<s;n++)o=i.indexOf(l[n]),o===-1&&i.push(l[n]);return i.join(" ")},_relDelete:function(t,e){if(!e)return t.currentRel.join(" ");/^only\:/.test(e)&&(e=e.replace(/^only\:/,"").trim());const i=t.currentRel.join(" ").replace(this._w.RegExp(e+"\\s*"),"");return t.currentRel=i.split(" "),i},init:function(t){t.linkAnchor=null,t.linkValue=t.preview.textContent=t.urlInput.value="",t.anchorText.value="",t.newWindowCheck.checked=!1,t.downloadCheck.checked=!1,t._change=!1,this.plugins.anchor.setRel.call(this,t,t.defaultRel),t.relList&&this.plugins.anchor.toggleRelList.call(this,t,!1),this.context.anchor.callerContext=null,this.plugins.selectMenu.init.call(this,this.context.selectMenu.callerContext)}},wt={name:"link",display:"dialog",add:function(t){t.addModule([X,xt]);const e=t.context,i=e.link={focusElement:null,_linkAnchor:null,anchorCtx:null};let l=this.setDialog(t);i.modal=l;let n=this.setController_LinkButton(t);i.linkController=n,l.querySelector("form").addEventListener("submit",this.submit.bind(t)),n.addEventListener("click",this.onClick_linkController.bind(t)),e.dialog.modal.appendChild(l),e.element.relative.appendChild(n),t.plugins.anchor.initEvent.call(t,"link",l),i.focusElement=e.anchor.caller.link.urlInput,l=null,n=null},setDialog:function(t){const e=t.lang,i=t.util.createElement("DIV"),l=t.icons;i.className="se-dialog-content",i.style.display="none";let n='<form><div class="se-dialog-header"><button type="button" data-command="close" class="se-btn se-dialog-close" title="'+e.dialogBox.close+'" aria-label="'+e.dialogBox.close+'">'+l.cancel+'</button><span class="se-modal-title">'+e.dialogBox.linkBox.title+"</span></div>"+t.context.anchor.forms.innerHTML+'<div class="se-dialog-footer"><button type="submit" class="se-btn-primary" title="'+e.dialogBox.submitButton+'" aria-label="'+e.dialogBox.submitButton+'"><span>'+e.dialogBox.submitButton+"</span></button></div></form>";return i.innerHTML=n,i},setController_LinkButton:function(t){const e=t.lang,i=t.icons,l=t.util.createElement("DIV");return l.className="se-controller se-controller-link",l.innerHTML='<div class="se-arrow se-arrow-up"></div><div class="link-content"><span><a target="_blank" href=""></a>&nbsp;</span><div class="se-btn-group"><button type="button" data-command="update" tabindex="-1" class="se-btn se-tooltip">'+i.edit+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+e.controller.edit+'</span></span></button><button type="button" data-command="unlink" tabindex="-1" class="se-btn se-tooltip">'+i.unlink+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+e.controller.unlink+'</span></span></button><button type="button" data-command="delete" tabindex="-1" class="se-btn se-tooltip">'+i.delete+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+e.controller.remove+"</span></span></button></div></div>",l},open:function(){this.plugins.dialog.open.call(this,"link",this.currentControllerName==="link")},submit:function(t){this.showLoading(),t.preventDefault(),t.stopPropagation();try{const e=this.plugins.anchor.createAnchor.call(this,this.context.anchor.caller.link,!1);if(e===null)return;if(this.context.dialog.updateModal){const i=this.context.link._linkAnchor.childNodes[0];this.setRange(i,0,i,i.textContent.length)}else{const i=this.getSelectedElements();if(i.length>1){const l=this.util.createElement(i[0].nodeName);if(l.appendChild(e),!this.insertNode(l,null,!0))return}else if(!this.insertNode(e,null,!0))return;this.setRange(e.childNodes[0],0,e.childNodes[0],e.textContent.length)}}finally{this.plugins.dialog.close.call(this),this.closeLoading(),this.history.push(!1)}return!1},active:function(t){if(!t)this.controllerArray.indexOf(this.context.link.linkController)>-1&&this.controllersOff();else if(this.util.isAnchor(t)&&t.getAttribute("data-image-link")===null)return this.controllerArray.indexOf(this.context.link.linkController)<0&&this.plugins.link.call_controller.call(this,t),!0;return!1},on:function(t){this.plugins.anchor.on.call(this,this.context.anchor.caller.link,t)},call_controller:function(t){this.editLink=this.context.link._linkAnchor=this.context.anchor.caller.link.linkAnchor=t;const e=this.context.link.linkController,i=e.querySelector("a");i.href=t.href,i.title=t.textContent,i.textContent=t.textContent,this.util.addClass(t,"on"),this.setControllerPosition(e,t,"bottom",{left:0,top:0}),this.controllersOn(e,t,"link",this.util.removeClass.bind(this.util,this.context.link._linkAnchor,"on"))},onClick_linkController:function(t){t.stopPropagation();const e=t.target.getAttribute("data-command")||t.target.parentNode.getAttribute("data-command");if(e){if(t.preventDefault(),/update/.test(e))this.plugins.dialog.open.call(this,"link",!0);else if(/unlink/.test(e)){const i=this.util.getChildElement(this.context.link._linkAnchor,function(n){return n.childNodes.length===0||n.nodeType===3},!1),l=this.util.getChildElement(this.context.link._linkAnchor,function(n){return n.childNodes.length===0||n.nodeType===3},!0);this.setRange(i,0,l,l.textContent.length),this.nodeChange(null,null,["A"],!1)}else this.util.removeItem(this.context.link._linkAnchor),this.context.anchor.caller.link.linkAnchor=null,this.focus(),this.history.push(!1);this.controllersOff()}},init:function(){this.context.link.linkController.style.display="none",this.plugins.anchor.init.call(this,this.context.anchor.caller.link)}};var Ct={exports:{}};(function(t){(function(e,i){t.exports=e.document?i(e,!0):function(l){if(!l.document)throw new Error("SUNEDITOR_MODULES a window with a document");return i(l)}})(typeof window<"u"?window:T,function(e,i){const l={name:"component",set_container:function(n,s){const o=this.util.createElement("DIV");return o.className="se-component "+s,o.appendChild(n),o},set_cover:function(n){const s=this.util.createElement("FIGURE");return s.appendChild(n),s},create_caption:function(){const n=this.util.createElement("FIGCAPTION");return n.innerHTML="<div>"+this.lang.dialogBox.caption+"</div>",n}};return typeof i>"u"&&(e.SUNEDITOR_MODULES||Object.defineProperty(e,"SUNEDITOR_MODULES",{enumerable:!0,writable:!1,configurable:!1,value:{}}),Object.defineProperty(e.SUNEDITOR_MODULES,"component",{enumerable:!0,writable:!1,configurable:!1,value:l})),l})})(Ct);var Ot=Ct.exports;const Q=Y(Ot);var St={exports:{}};(function(t){(function(e,i){t.exports=e.document?i(e,!0):function(l){if(!l.document)throw new Error("SUNEDITOR_MODULES a window with a document");return i(l)}})(typeof window<"u"?window:T,function(e,i){const l={name:"resizing",add:function(n){const s=n.icons,o=n.context;o.resizing={_resizeClientX:0,_resizeClientY:0,_resize_plugin:"",_resize_w:0,_resize_h:0,_origin_w:0,_origin_h:0,_rotateVertical:!1,_resize_direction:"",_move_path:null,_isChange:!1,alignIcons:{basic:s.align_justify,left:s.align_left,right:s.align_right,center:s.align_center}};let a=this.setController_resize(n);o.resizing.resizeContainer=a,o.resizing.resizeDiv=a.querySelector(".se-modal-resize"),o.resizing.resizeDot=a.querySelector(".se-resize-dot"),o.resizing.resizeDisplay=a.querySelector(".se-resize-display");let r=this.setController_button(n);o.resizing.resizeButton=r;let c=o.resizing.resizeHandles=o.resizing.resizeDot.querySelectorAll("span");o.resizing.resizeButtonGroup=r.querySelector("._se_resizing_btn_group"),o.resizing.rotationButtons=r.querySelectorAll("._se_resizing_btn_group ._se_rotation"),o.resizing.percentageButtons=r.querySelectorAll("._se_resizing_btn_group ._se_percentage"),o.resizing.alignMenu=r.querySelector(".se-resizing-align-list"),o.resizing.alignMenuList=o.resizing.alignMenu.querySelectorAll("button"),o.resizing.alignButton=r.querySelector("._se_resizing_align_button"),o.resizing.autoSizeButton=r.querySelector("._se_resizing_btn_group ._se_auto_size"),o.resizing.captionButton=r.querySelector("._se_resizing_caption_button"),a.addEventListener("mousedown",function(u){u.preventDefault()}),c[0].addEventListener("mousedown",this.onMouseDown_resize_handle.bind(n)),c[1].addEventListener("mousedown",this.onMouseDown_resize_handle.bind(n)),c[2].addEventListener("mousedown",this.onMouseDown_resize_handle.bind(n)),c[3].addEventListener("mousedown",this.onMouseDown_resize_handle.bind(n)),c[4].addEventListener("mousedown",this.onMouseDown_resize_handle.bind(n)),c[5].addEventListener("mousedown",this.onMouseDown_resize_handle.bind(n)),c[6].addEventListener("mousedown",this.onMouseDown_resize_handle.bind(n)),c[7].addEventListener("mousedown",this.onMouseDown_resize_handle.bind(n)),r.addEventListener("click",this.onClick_resizeButton.bind(n)),o.element.relative.appendChild(a),o.element.relative.appendChild(r),a=null,r=null,c=null},setController_resize:function(n){const s=n.util.createElement("DIV");return s.className="se-controller se-resizing-container",s.style.display="none",s.innerHTML='<div class="se-modal-resize"></div><div class="se-resize-dot"><span class="tl"></span><span class="tr"></span><span class="bl"></span><span class="br"></span><span class="lw"></span><span class="th"></span><span class="rw"></span><span class="bh"></span><div class="se-resize-display"></div></div>',s},setController_button:function(n){const s=n.lang,o=n.icons,a=n.util.createElement("DIV");return a.className="se-controller se-controller-resizing",a.innerHTML='<div class="se-arrow se-arrow-up"></div><div class="se-btn-group _se_resizing_btn_group"><button type="button" data-command="percent" data-value="1" class="se-tooltip _se_percentage"><span>100%</span><span class="se-tooltip-inner"><span class="se-tooltip-text">'+s.controller.resize100+'</span></span></button><button type="button" data-command="percent" data-value="0.75" class="se-tooltip _se_percentage"><span>75%</span><span class="se-tooltip-inner"><span class="se-tooltip-text">'+s.controller.resize75+'</span></span></button><button type="button" data-command="percent" data-value="0.5" class="se-tooltip _se_percentage"><span>50%</span><span class="se-tooltip-inner"><span class="se-tooltip-text">'+s.controller.resize50+'</span></span></button><button type="button" data-command="auto" class="se-btn se-tooltip _se_auto_size">'+o.auto_size+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+s.controller.autoSize+'</span></span></button><button type="button" data-command="rotate" data-value="-90" class="se-btn se-tooltip _se_rotation">'+o.rotate_left+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+s.controller.rotateLeft+'</span></span></button><button type="button" data-command="rotate" data-value="90" class="se-btn se-tooltip _se_rotation">'+o.rotate_right+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+s.controller.rotateRight+'</span></span></button></div><div class="se-btn-group" style="padding-top: 0;"><button type="button" data-command="mirror" data-value="h" class="se-btn se-tooltip">'+o.mirror_horizontal+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+s.controller.mirrorHorizontal+'</span></span></button><button type="button" data-command="mirror" data-value="v" class="se-btn se-tooltip">'+o.mirror_vertical+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+s.controller.mirrorVertical+'</span></span></button><button type="button" data-command="onalign" class="se-btn se-tooltip _se_resizing_align_button">'+o.align_justify+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+s.toolbar.align+'</span></span></button><div class="se-btn-group-sub sun-editor-common se-list-layer se-resizing-align-list"><div class="se-list-inner"><ul class="se-list-basic"><li><button type="button" class="se-btn-list se-tooltip" data-command="align" data-value="basic">'+o.align_justify+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+s.dialogBox.basic+'</span></span></button></li><li><button type="button" class="se-btn-list se-tooltip" data-command="align" data-value="left">'+o.align_left+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+s.dialogBox.left+'</span></span></button></li><li><button type="button" class="se-btn-list se-tooltip" data-command="align" data-value="center">'+o.align_center+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+s.dialogBox.center+'</span></span></button></li><li><button type="button" class="se-btn-list se-tooltip" data-command="align" data-value="right">'+o.align_right+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+s.dialogBox.right+'</span></span></button></li></ul></div></div><button type="button" data-command="caption" class="se-btn se-tooltip _se_resizing_caption_button">'+o.caption+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+s.dialogBox.caption+'</span></span></button><button type="button" data-command="revert" class="se-btn se-tooltip">'+o.revert+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+s.dialogBox.revertButton+'</span></span></button><button type="button" data-command="update" class="se-btn se-tooltip">'+o.modify+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+s.controller.edit+'</span></span></button><button type="button" data-command="delete" class="se-btn se-tooltip">'+o.delete+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+s.controller.remove+"</span></span></button></div>",a},_module_getSizeX:function(n,s,o,a){return s||(s=n._element),o||(o=n._cover),a||(a=n._container),s?/%$/.test(s.style.width)?(a&&this.util.getNumber(a.style.width,2)||100)+"%":s.style.width:""},_module_getSizeY:function(n,s,o,a){return s||(s=n._element),o||(o=n._cover),a||(a=n._container),!a||!o?s&&s.style.height||"":this.util.getNumber(o.style.paddingBottom,0)>0&&!this.context.resizing._rotateVertical?o.style.height:!/%$/.test(s.style.height)||!/%$/.test(s.style.width)?s.style.height:(a&&this.util.getNumber(a.style.height,2)||100)+"%"},_module_setModifyInputSize:function(n,s){const o=n._onlyPercentage&&this.context.resizing._rotateVertical;n.proportion.checked=n._proportionChecked=n._element.getAttribute("data-proportion")!=="false";let a=o?"":this.plugins.resizing._module_getSizeX.call(this,n);if(a===n._defaultSizeX&&(a=""),n._onlyPercentage&&(a=this.util.getNumber(a,2)),n.inputX.value=a,s.setInputSize.call(this,"x"),!n._onlyPercentage){let r=o?"":this.plugins.resizing._module_getSizeY.call(this,n);r===n._defaultSizeY&&(r=""),n._onlyPercentage&&(r=this.util.getNumber(r,2)),n.inputY.value=r}n.inputX.disabled=!!o,n.inputY.disabled=!!o,n.proportion.disabled=!!o,s.setRatio.call(this)},_module_setInputSize:function(n,s){if(n._onlyPercentage){s==="x"&&n.inputX.value>100&&(n.inputX.value=100);return}if(n.proportion.checked&&n._ratio&&/\d/.test(n.inputX.value)&&/\d/.test(n.inputY.value)){const o=n.inputX.value.replace(/\d+|\./g,"")||n.sizeUnit,a=n.inputY.value.replace(/\d+|\./g,"")||n.sizeUnit;if(o!==a)return;const r=o==="%"?2:0;s==="x"?n.inputY.value=this.util.getNumber(n._ratioY*this.util.getNumber(n.inputX.value,r),r)+a:n.inputX.value=this.util.getNumber(n._ratioX*this.util.getNumber(n.inputY.value,r),r)+o}},_module_setRatio:function(n){const s=n.inputX.value,o=n.inputY.value;if(n.proportion.checked&&/\d+/.test(s)&&/\d+/.test(o)){const a=s.replace(/\d+|\./g,"")||n.sizeUnit,r=o.replace(/\d+|\./g,"")||n.sizeUnit;if(a!==r)n._ratio=!1;else if(!n._ratio){const c=this.util.getNumber(s,0),u=this.util.getNumber(o,0);n._ratio=!0,n._ratioX=c/u,n._ratioY=u/c}}else n._ratio=!1},_module_sizeRevert:function(n){n._onlyPercentage?n.inputX.value=n._origin_w>100?100:n._origin_w:(n.inputX.value=n._origin_w,n.inputY.value=n._origin_h)},_module_saveCurrentSize:function(n){const s=this.plugins.resizing._module_getSizeX.call(this,n),o=this.plugins.resizing._module_getSizeY.call(this,n);n._element.setAttribute("width",s.replace("px","")),n._element.setAttribute("height",o.replace("px","")),n._element.setAttribute("data-size",s+","+o),n._videoRatio&&(n._videoRatio=o)},call_controller_resize:function(n,s){const o=this.context.resizing,a=this.context[s];o._resize_plugin=s;const r=o.resizeContainer,c=o.resizeDiv,u=this.util.getOffset(n,this.context.element.wysiwygFrame),f=o._rotateVertical=/^(90|270)$/.test(Math.abs(n.getAttribute("data-rotate")).toString()),p=f?n.offsetHeight:n.offsetWidth,m=f?n.offsetWidth:n.offsetHeight,d=u.top,_=u.left-this.context.element.wysiwygFrame.scrollLeft;r.style.top=d+"px",r.style.left=_+"px",r.style.width=p+"px",r.style.height=m+"px",c.style.top="0px",c.style.left="0px",c.style.width=p+"px",c.style.height=m+"px";let g=n.getAttribute("data-align")||"basic";g=g==="none"?"basic":g;const h=this.util.getParentElement(n,this.util.isComponent),b=this.util.getParentElement(n,"FIGURE"),v=this.plugins.resizing._module_getSizeX.call(this,a,n,b,h)||"auto",x=a._onlyPercentage&&s==="image"?"":", "+(this.plugins.resizing._module_getSizeY.call(this,a,n,b,h)||"auto");this.util.changeTxt(o.resizeDisplay,this.lang.dialogBox[g]+" ("+v+x+")"),o.resizeButtonGroup.style.display=a._resizing?"":"none";const y=a._resizing&&!a._resizeDotHide&&!a._onlyPercentage?"flex":"none",w=o.resizeHandles;for(let E=0,S=w.length;E<S;E++)w[E].style.display=y;if(a._resizing){const E=o.rotationButtons;E[0].style.display=E[1].style.display=a._rotation?"":"none"}if(a._alignHide)o.alignButton.style.display="none";else{o.alignButton.style.display="";const E=o.alignMenuList;this.util.changeElement(o.alignButton.firstElementChild,o.alignIcons[g]);for(let S=0,M=E.length;S<M;S++)E[S].getAttribute("data-value")===g?this.util.addClass(E[S],"on"):this.util.removeClass(E[S],"on")}const C=o.percentageButtons,B=/%$/.test(n.style.width)&&/%$/.test(h.style.width)?this.util.getNumber(h.style.width,0)/100+"":"";for(let E=0,S=C.length;E<S;E++)C[E].getAttribute("data-value")===B?this.util.addClass(C[E],"active"):this.util.removeClass(C[E],"active");a._captionShow?(o.captionButton.style.display="",this.util.getChildElement(n.parentNode,"figcaption")?(this.util.addClass(o.captionButton,"active"),a._captionChecked=!0):(this.util.removeClass(o.captionButton,"active"),a._captionChecked=!1)):o.captionButton.style.display="none",r.style.display="block";const z={left:0,top:50};this.options.iframe&&(z.left-=this.context.element.wysiwygFrame.parentElement.offsetLeft,z.top-=this.context.element.wysiwygFrame.parentElement.offsetTop),this.setControllerPosition(o.resizeButton,r,"bottom",z);const L=function(){this.util.setDisabledButtons.call(this.util,!1,this.resizingDisabledButtons),this.history._resetCachingButton()};this.controllersOn(r,o.resizeButton,L.bind(this),n,s),this.util.setDisabledButtons(!0,this.resizingDisabledButtons),o._resize_w=p,o._resize_h=m;const N=(n.getAttribute("origin-size")||"").split(",");return o._origin_w=N[0]||n.naturalWidth,o._origin_h=N[1]||n.naturalHeight,{w:p,h:m,t:d,l:_}},_closeAlignMenu:null,openAlignMenu:function(){const n=this.context.resizing.alignButton;this.util.addClass(n,"on"),this.context.resizing.alignMenu.style.top=n.offsetTop+n.offsetHeight+"px",this.context.resizing.alignMenu.style.left=n.offsetLeft-n.offsetWidth/2+"px",this.context.resizing.alignMenu.style.display="block",this.plugins.resizing._closeAlignMenu=(function(){this.util.removeClass(this.context.resizing.alignButton,"on"),this.context.resizing.alignMenu.style.display="none",this.removeDocEvent("click",this.plugins.resizing._closeAlignMenu),this.plugins.resizing._closeAlignMenu=null}).bind(this),this.addDocEvent("click",this.plugins.resizing._closeAlignMenu)},onClick_resizeButton:function(n){n.stopPropagation();const s=n.target,o=s.getAttribute("data-command")||s.parentNode.getAttribute("data-command");if(!o)return;const a=s.getAttribute("data-value")||s.parentNode.getAttribute("data-value"),r=this.context.resizing._resize_plugin,c=this.context[r],u=c._element,f=this.plugins[r];if(n.preventDefault(),!(typeof this.plugins.resizing._closeAlignMenu=="function"&&(this.plugins.resizing._closeAlignMenu(),o==="onalign"))){switch(o){case"auto":this.plugins.resizing.resetTransform.call(this,u),f.setAutoSize.call(this),this.selectComponent(u,r);break;case"percent":let p=this.plugins.resizing._module_getSizeY.call(this,c);if(this.context.resizing._rotateVertical){const y=u.getAttribute("data-percentage");y&&(p=y.split(",")[1])}this.plugins.resizing.resetTransform.call(this,u),f.setPercentSize.call(this,a*100,this.util.getNumber(p,0)===null||!/%$/.test(p)?"":p),this.selectComponent(u,r);break;case"mirror":const m=u.getAttribute("data-rotate")||"0";let d=u.getAttribute("data-rotateX")||"",_=u.getAttribute("data-rotateY")||"";a==="h"&&!this.context.resizing._rotateVertical||a==="v"&&this.context.resizing._rotateVertical?_=_?"":"180":d=d?"":"180",u.setAttribute("data-rotateX",d),u.setAttribute("data-rotateY",_),this.plugins.resizing._setTransForm(u,m,d,_);break;case"rotate":const g=this.context.resizing,h=u.getAttribute("data-rotate")*1+a*1,b=this._w.Math.abs(h)>=360?0:h;u.setAttribute("data-rotate",b),g._rotateVertical=/^(90|270)$/.test(this._w.Math.abs(b).toString()),this.plugins.resizing.setTransformSize.call(this,u,null,null),this.selectComponent(u,r);break;case"onalign":this.plugins.resizing.openAlignMenu.call(this);return;case"align":const v=a==="basic"?"none":a;f.setAlign.call(this,v,null,null,null),this.selectComponent(u,r);break;case"caption":const x=!c._captionChecked;if(f.openModify.call(this,!0),c._captionChecked=c.captionCheckEl.checked=x,f.update_image.call(this,!1,!1,!1),x){const y=this.util.getChildElement(c._caption,function(w){return w.nodeType===3});y?this.setRange(y,0,y,y.textContent.length):c._caption.focus(),this.controllersOff()}else this.selectComponent(u,r),f.openModify.call(this,!0);break;case"revert":f.setOriginSize.call(this),this.selectComponent(u,r);break;case"update":f.openModify.call(this),this.controllersOff();break;case"delete":f.destroy.call(this);break}this.history.push(!1)}},resetTransform:function(n){const s=(n.getAttribute("data-size")||n.getAttribute("data-origin")||"").split(",");this.context.resizing._rotateVertical=!1,n.style.maxWidth="",n.style.transform="",n.style.transformOrigin="",n.setAttribute("data-rotate",""),n.setAttribute("data-rotateX",""),n.setAttribute("data-rotateY",""),this.plugins[this.context.resizing._resize_plugin].setSize.call(this,s[0]?s[0]:"auto",s[1]?s[1]:"",!0)},setTransformSize:function(n,s,o){let a=n.getAttribute("data-percentage");const r=this.context.resizing._rotateVertical,c=n.getAttribute("data-rotate")*1;let u="";if(a&&!r)a=a.split(","),a[0]==="auto"&&a[1]==="auto"?this.plugins[this.context.resizing._resize_plugin].setAutoSize.call(this):this.plugins[this.context.resizing._resize_plugin].setPercentSize.call(this,a[0],a[1]);else{const f=this.util.getParentElement(n,"FIGURE"),p=s||n.offsetWidth,m=o||n.offsetHeight,d=(r?m:p)+"px",_=(r?p:m)+"px";if(this.plugins[this.context.resizing._resize_plugin].cancelPercentAttr.call(this),this.plugins[this.context.resizing._resize_plugin].setSize.call(this,p+"px",m+"px",!0),f.style.width=d,f.style.height=this.context[this.context.resizing._resize_plugin]._caption?"":_,r){let g=p/2+"px "+p/2+"px 0",h=m/2+"px "+m/2+"px 0";u=c===90||c===-270?h:g}}n.style.transformOrigin=u,this.plugins.resizing._setTransForm(n,c.toString(),n.getAttribute("data-rotateX")||"",n.getAttribute("data-rotateY")||""),r?n.style.maxWidth="none":n.style.maxWidth="",this.plugins.resizing.setCaptionPosition.call(this,n)},_setTransForm:function(n,s,o,a){let r=(n.offsetWidth-n.offsetHeight)*(/-/.test(s)?1:-1),c="";if(/[1-9]/.test(s)&&(o||a))switch(c=o?"Y":"X",s){case"90":c=o&&a?"X":a?c:"";break;case"270":r*=-1,c=o&&a?"Y":o?c:"";break;case"-90":c=o&&a?"Y":o?c:"";break;case"-270":r*=-1,c=o&&a?"X":a?c:"";break;default:c=""}s%180===0&&(n.style.maxWidth=""),n.style.transform="rotate("+s+"deg)"+(o?" rotateX("+o+"deg)":"")+(a?" rotateY("+a+"deg)":"")+(c?" translate"+c+"("+r+"px)":"")},setCaptionPosition:function(n){const s=this.util.getChildElement(this.util.getParentElement(n,"FIGURE"),"FIGCAPTION");s&&(s.style.marginTop=(this.context.resizing._rotateVertical?n.offsetWidth-n.offsetHeight:0)+"px")},onMouseDown_resize_handle:function(n){n.stopPropagation(),n.preventDefault();const s=this.context.resizing,o=s._resize_direction=n.target.classList[0];s._resizeClientX=n.clientX,s._resizeClientY=n.clientY,this.context.element.resizeBackground.style.display="block",s.resizeButton.style.display="none",s.resizeDiv.style.float=/l/.test(o)?"right":/r/.test(o)?"left":"none";const a=(function(u){if(u.type==="keydown"&&u.keyCode!==27)return;const f=s._isChange;s._isChange=!1,this.removeDocEvent("mousemove",r),this.removeDocEvent("mouseup",a),this.removeDocEvent("keydown",a),u.type==="keydown"?(this.controllersOff(),this.context.element.resizeBackground.style.display="none",this.plugins[this.context.resizing._resize_plugin].init.call(this)):(this.plugins.resizing.cancel_controller_resize.call(this,o),f&&this.history.push(!1))}).bind(this),r=this.plugins.resizing.resizing_element.bind(this,s,o,this.context[s._resize_plugin]);this.addDocEvent("mousemove",r),this.addDocEvent("mouseup",a),this.addDocEvent("keydown",a)},resizing_element:function(n,s,o,a){const r=a.clientX,c=a.clientY;let u=o._element_w,f=o._element_h;const p=o._element_w+(/r/.test(s)?r-n._resizeClientX:n._resizeClientX-r),m=o._element_h+(/b/.test(s)?c-n._resizeClientY:n._resizeClientY-c),d=o._element_h/o._element_w*p;/t/.test(s)&&(n.resizeDiv.style.top=o._element_h-(/h/.test(s)?m:d)+"px"),/l/.test(s)&&(n.resizeDiv.style.left=o._element_w-p+"px"),/r|l/.test(s)&&(n.resizeDiv.style.width=p+"px",u=p),/^(t|b)[^h]$/.test(s)?(n.resizeDiv.style.height=d+"px",f=d):/^(t|b)h$/.test(s)&&(n.resizeDiv.style.height=m+"px",f=m),n._resize_w=u,n._resize_h=f,this.util.changeTxt(n.resizeDisplay,this._w.Math.round(u)+" x "+this._w.Math.round(f)),n._isChange=!0},cancel_controller_resize:function(n){const s=this.context.resizing._rotateVertical;this.controllersOff(),this.context.element.resizeBackground.style.display="none";let o=this._w.Math.round(s?this.context.resizing._resize_h:this.context.resizing._resize_w),a=this._w.Math.round(s?this.context.resizing._resize_w:this.context.resizing._resize_h);if(!s&&!/%$/.test(o)){const u=this.context.element.wysiwygFrame.clientWidth-32-2;this.util.getNumber(o,0)>u&&(a=this._w.Math.round(a/o*u),o=u)}const r=this.context.resizing._resize_plugin;this.plugins[r].setSize.call(this,o,a,!1,n),s&&this.plugins.resizing.setTransformSize.call(this,this.context[this.context.resizing._resize_plugin]._element,o,a),this.selectComponent(this.context[r]._element,r)}};return typeof i>"u"&&(e.SUNEDITOR_MODULES||Object.defineProperty(e,"SUNEDITOR_MODULES",{enumerable:!0,writable:!1,configurable:!1,value:{}}),Object.defineProperty(e.SUNEDITOR_MODULES,"resizing",{enumerable:!0,writable:!1,configurable:!1,value:l})),l})})(St);var Ft=St.exports;const kt=Y(Ft);var Et={exports:{}};(function(t){(function(e,i){t.exports=e.document?i(e,!0):function(l){if(!l.document)throw new Error("SUNEDITOR_MODULES a window with a document");return i(l)}})(typeof window<"u"?window:T,function(e,i){const l={name:"fileManager",_xmlHttp:null,_checkMediaComponent:function(n){return/IMG/i.test(n)?!/FIGURE/i.test(n.parentElement.nodeName)||!/FIGURE/i.test(n.parentElement.parentElement.nodeName):!0},upload:function(n,s,o,a,r){this.showLoading();const c=this.plugins.fileManager,u=c._xmlHttp=this.util.getXMLHttpRequest();if(u.onreadystatechange=c._callBackUpload.bind(this,u,a,r),u.open("post",n,!0),s!==null&&typeof s=="object"&&this._w.Object.keys(s).length>0)for(let f in s)u.setRequestHeader(f,s[f]);u.send(o)},_callBackUpload:function(n,s,o){if(n.readyState===4)if(n.status===200)try{s(n)}catch(a){throw Error('[SUNEDITOR.fileManager.upload.callBack.fail] cause : "'+a.message+'"')}finally{this.closeLoading()}else{this.closeLoading();const a=n.responseText?JSON.parse(n.responseText):n;if(typeof o!="function"||o("",a,this)){const r="[SUNEDITOR.fileManager.upload.serverException] status: "+n.status+", response: "+(a.errorMessage||n.responseText);throw this.functions.noticeOpen(r),Error(r)}}},checkInfo:function(n,s,o,a,r){let c=[];for(let h=0,b=s.length;h<b;h++)c=c.concat([].slice.call(this.context.element.wysiwyg.querySelectorAll(s[h]+':not([data-se-embed="true"])')));const u=this.plugins.fileManager,f=this.context[n],p=f._infoList,m=u.setInfo.bind(this);if(c.length===p.length)if(this._componentsInfoReset){for(let h=0,b=c.length;h<b;h++)m(n,c[h],o,null,r);return}else{let h=!1;for(let b=0,v=p.length,x;b<v;b++)if(x=p[b],c.filter(function(y){return x.src===y.src&&x.index.toString()===y.getAttribute("data-index")}).length===0){h=!0;break}if(!h)return}const d=r?this.context.resizing._resize_plugin:"";r&&(this.context.resizing._resize_plugin=n);const _=[],g=[];for(let h=0,b=p.length;h<b;h++)g[h]=p[h].index;for(f.__updateTags=c;c.length>0;){const h=c.shift();!this.util.getParentElement(h,this.util.isMediaComponent)||!u._checkMediaComponent(h)?(_.push(f._infoIndex),a(h)):!h.getAttribute("data-index")||g.indexOf(h.getAttribute("data-index")*1)<0?(_.push(f._infoIndex),h.removeAttribute("data-index"),m(n,h,o,null,r)):_.push(h.getAttribute("data-index")*1)}for(let h=0,b;h<p.length;h++)b=p[h].index,!(_.indexOf(b)>-1)&&(p.splice(h,1),typeof o=="function"&&o(null,b,"delete",null,0,this),h--);r&&(this.context.resizing._resize_plugin=d)},setInfo:function(n,s,o,a,r){const c=r?this.context.resizing._resize_plugin:"";r&&(this.context.resizing._resize_plugin=n);const u=this.plugins[n],f=this.context[n],p=f._infoList;let m=s.getAttribute("data-index"),d=null,_="";if(a||(a={name:s.getAttribute("data-file-name")||(typeof s.src=="string"?s.src.split("/").pop():""),size:s.getAttribute("data-file-size")||0}),!m||this._componentsInfoInit)_="create",m=f._infoIndex++,s.setAttribute("data-index",m),s.setAttribute("data-file-name",a.name),s.setAttribute("data-file-size",a.size),d={src:s.src,index:m*1,name:a.name,size:a.size},p.push(d);else{_="update",m*=1;for(let g=0,h=p.length;g<h;g++)if(m===p[g].index){d=p[g];break}d||(m=f._infoIndex++,d={index:m},p.push(d)),d.src=s.src,d.name=s.getAttribute("data-file-name"),d.size=s.getAttribute("data-file-size")*1}if(d.element=s,d.delete=u.destroy.bind(this,s),d.select=(function(g){g.scrollIntoView(!0),this._w.setTimeout(u.select.bind(this,g))}).bind(this,s),r){if(!s.getAttribute("origin-size")&&s.naturalWidth&&s.setAttribute("origin-size",s.naturalWidth+","+s.naturalHeight),!s.getAttribute("data-origin")){const g=this.util.getParentElement(s,this.util.isMediaComponent),h=this.util.getParentElement(s,"FIGURE"),b=this.plugins.resizing._module_getSizeX.call(this,f,s,h,g),v=this.plugins.resizing._module_getSizeY.call(this,f,s,h,g);s.setAttribute("data-origin",b+","+v),s.setAttribute("data-size",b+","+v)}if(!s.style.width){const g=(s.getAttribute("data-size")||s.getAttribute("data-origin")||"").split(",");u.onModifyMode.call(this,s,null),u.applySize.call(this,g[0],g[1])}this.context.resizing._resize_plugin=c}typeof o=="function"&&o(s,m,_,d,--f._uploadFileLength<0?0:f._uploadFileLength,this)},deleteInfo:function(n,s,o){if(s>=0){const a=this.context[n]._infoList;for(let r=0,c=a.length;r<c;r++)if(s===a[r].index){a.splice(r,1),typeof o=="function"&&o(null,s,"delete",null,0,this);return}}},resetInfo:function(n,s){const o=this.context[n];if(typeof s=="function"){const a=o._infoList;for(let r=0,c=a.length;r<c;r++)s(null,a[r].index,"delete",null,0,this)}o._infoList=[],o._infoIndex=0}};return typeof i>"u"&&(e.SUNEDITOR_MODULES||Object.defineProperty(e,"SUNEDITOR_MODULES",{enumerable:!0,writable:!1,configurable:!1,value:{}}),Object.defineProperty(e.SUNEDITOR_MODULES,"fileManager",{enumerable:!0,writable:!1,configurable:!1,value:l})),l})})(Et);var Pt=Et.exports;const J=Y(Pt),zt={name:"image",display:"dialog",add:function(t){t.addModule([X,xt,Q,kt,J]);const e=t.options,i=t.context,l=i.image={_infoList:[],_infoIndex:0,_uploadFileLength:0,focusElement:null,sizeUnit:e._imageSizeUnit,_linkElement:"",_altText:"",_align:"none",_floatClassRegExp:"__se__float\\-[a-z]+",_v_src:{_linkValue:""},svgDefaultSize:"30%",base64RenderIndex:0,_element:null,_cover:null,_container:null,inputX:null,inputY:null,_element_w:1,_element_h:1,_element_l:0,_element_t:0,_defaultSizeX:"auto",_defaultSizeY:"auto",_origin_w:e.imageWidth==="auto"?"":e.imageWidth,_origin_h:e.imageHeight==="auto"?"":e.imageHeight,_proportionChecked:!0,_resizing:e.imageResizing,_resizeDotHide:!e.imageHeightShow,_rotation:e.imageRotation,_alignHide:!e.imageAlignShow,_onlyPercentage:e.imageSizeOnlyPercentage,_ratio:!1,_ratioX:1,_ratioY:1,_captionShow:!0,_captionChecked:!1,_caption:null,captionCheckEl:null};let n=this.setDialog(t);l.modal=n,l.imgInputFile=n.querySelector("._se_image_file"),l.imgUrlFile=n.querySelector("._se_image_url"),l.focusElement=l.imgInputFile||l.imgUrlFile,l.altText=n.querySelector("._se_image_alt"),l.captionCheckEl=n.querySelector("._se_image_check_caption"),l.previewSrc=n.querySelector("._se_tab_content_image .se-link-preview"),n.querySelector(".se-dialog-tabs").addEventListener("click",this.openTab.bind(t)),n.querySelector("form").addEventListener("submit",this.submit.bind(t)),l.imgInputFile&&n.querySelector(".se-file-remove").addEventListener("click",this._removeSelectedFiles.bind(l.imgInputFile,l.imgUrlFile,l.previewSrc)),l.imgUrlFile&&l.imgUrlFile.addEventListener("input",this._onLinkPreview.bind(l.previewSrc,l._v_src,e.linkProtocol)),l.imgInputFile&&l.imgUrlFile&&l.imgInputFile.addEventListener("change",this._fileInputChange.bind(l));const s=n.querySelector(".__se__gallery");s&&s.addEventListener("click",this._openGallery.bind(t)),l.proportion={},l.inputX={},l.inputY={},e.imageResizing&&(l.proportion=n.querySelector("._se_image_check_proportion"),l.inputX=n.querySelector("._se_image_size_x"),l.inputY=n.querySelector("._se_image_size_y"),l.inputX.value=e.imageWidth,l.inputY.value=e.imageHeight,l.inputX.addEventListener("keyup",this.setInputSize.bind(t,"x")),l.inputY.addEventListener("keyup",this.setInputSize.bind(t,"y")),l.inputX.addEventListener("change",this.setRatio.bind(t)),l.inputY.addEventListener("change",this.setRatio.bind(t)),l.proportion.addEventListener("change",this.setRatio.bind(t)),n.querySelector(".se-dialog-btn-revert").addEventListener("click",this.sizeRevert.bind(t))),i.dialog.modal.appendChild(n),t.plugins.anchor.initEvent.call(t,"image",n.querySelector("._se_tab_content_url")),l.anchorCtx=t.context.anchor.caller.image,n=null},setDialog:function(t){const e=t.options,i=t.lang,l=t.util.createElement("DIV");l.className="se-dialog-content se-dialog-image",l.style.display="none";let n='<div class="se-dialog-header"><button type="button" data-command="close" class="se-btn se-dialog-close" class="close" title="'+i.dialogBox.close+'" aria-label="'+i.dialogBox.close+'">'+t.icons.cancel+'</button><span class="se-modal-title">'+i.dialogBox.imageBox.title+'</span></div><div class="se-dialog-tabs"><button type="button" class="_se_tab_link active" data-tab-link="image">'+i.toolbar.image+'</button><button type="button" class="_se_tab_link" data-tab-link="url">'+i.toolbar.link+'</button></div><form method="post" enctype="multipart/form-data"><div class="_se_tab_content _se_tab_content_image"><div class="se-dialog-body"><div style="border-bottom: 1px dashed #ccc;">';if(e.imageFileInput&&(n+='<div class="se-dialog-form"><label>'+i.dialogBox.imageBox.file+'</label><div class="se-dialog-form-files"><input class="se-input-form _se_image_file" type="file" accept="'+e.imageAccept+'"'+(e.imageMultipleFile?' multiple="multiple"':"")+'/><button type="button" class="se-btn se-dialog-files-edge-button se-file-remove" title="'+i.controller.remove+'" aria-label="'+i.controller.remove+'">'+t.icons.cancel+"</button></div></div>"),e.imageUrlInput&&(n+='<div class="se-dialog-form"><label>'+i.dialogBox.imageBox.url+'</label><div class="se-dialog-form-files"><input class="se-input-form se-input-url _se_image_url" type="text" />'+(e.imageGalleryUrl&&t.plugins.imageGallery?'<button type="button" class="se-btn se-dialog-files-edge-button __se__gallery" title="'+i.toolbar.imageGallery+'" aria-label="'+i.toolbar.imageGallery+'">'+t.icons.image_gallery+"</button>":"")+'</div><pre class="se-link-preview"></pre></div>'),n+='</div><div class="se-dialog-form"><label>'+i.dialogBox.imageBox.altText+'</label><input class="se-input-form _se_image_alt" type="text" /></div>',e.imageResizing){const s=e.imageSizeOnlyPercentage,o=s?' style="display: none !important;"':"",a=e.imageHeightShow?"":' style="display: none !important;"';n+='<div class="se-dialog-form">',s||!e.imageHeightShow?n+='<div class="se-dialog-size-text"><label class="size-w">'+i.dialogBox.size+"</label></div>":n+='<div class="se-dialog-size-text"><label class="size-w">'+i.dialogBox.width+'</label><label class="se-dialog-size-x">&nbsp;</label><label class="size-h">'+i.dialogBox.height+"</label></div>",n+='<input class="se-input-control _se_image_size_x" placeholder="auto"'+(s?' type="number" min="1"':'type="text"')+(s?' max="100"':"")+' /><label class="se-dialog-size-x"'+a+">"+(s?"%":"x")+'</label><input type="text" class="se-input-control _se_image_size_y" placeholder="auto"'+o+(s?' max="100"':"")+a+"/><label"+o+a+'><input type="checkbox" class="se-dialog-btn-check _se_image_check_proportion" checked/>&nbsp;'+i.dialogBox.proportion+'</label><button type="button" title="'+i.dialogBox.revertButton+'" aria-label="'+i.dialogBox.revertButton+'" class="se-btn se-dialog-btn-revert" style="float: right;">'+t.icons.revert+"</button></div>"}return n+='<div class="se-dialog-form se-dialog-form-footer"><label><input type="checkbox" class="se-dialog-btn-check _se_image_check_caption" />&nbsp;'+i.dialogBox.caption+'</label></div></div></div><div class="_se_tab_content _se_tab_content_url" style="display: none">'+t.context.anchor.forms.innerHTML+'</div><div class="se-dialog-footer"><div'+(e.imageAlignShow?"":' style="display: none"')+'><label><input type="radio" name="suneditor_image_radio" class="se-dialog-btn-radio" value="none" checked>'+i.dialogBox.basic+'</label><label><input type="radio" name="suneditor_image_radio" class="se-dialog-btn-radio" value="left">'+i.dialogBox.left+'</label><label><input type="radio" name="suneditor_image_radio" class="se-dialog-btn-radio" value="center">'+i.dialogBox.center+'</label><label><input type="radio" name="suneditor_image_radio" class="se-dialog-btn-radio" value="right">'+i.dialogBox.right+'</label></div><button type="submit" class="se-btn-primary" title="'+i.dialogBox.submitButton+'" aria-label="'+i.dialogBox.submitButton+'"><span>'+i.dialogBox.submitButton+"</span></button></div></form>",l.innerHTML=n,l},_fileInputChange:function(){this.imgInputFile.value?(this.imgUrlFile.setAttribute("disabled",!0),this.previewSrc.style.textDecoration="line-through"):(this.imgUrlFile.removeAttribute("disabled"),this.previewSrc.style.textDecoration="")},_removeSelectedFiles:function(t,e){this.value="",t&&(t.removeAttribute("disabled"),e.style.textDecoration="")},_openGallery:function(){this.callPlugin("imageGallery",this.plugins.imageGallery.open.bind(this,this.plugins.image._setUrlInput.bind(this.context.image)),null)},_setUrlInput:function(t){this.altText.value=t.alt,this._v_src._linkValue=this.previewSrc.textContent=this.imgUrlFile.value=t.getAttribute("data-value")||t.src,this.imgUrlFile.focus()},_onLinkPreview:function(t,e,i){const l=i.target.value.trim();t._linkValue=this.textContent=l?e&&l.indexOf("://")===-1&&l.indexOf("#")!==0?e+l:l.indexOf("://")===-1?"/"+l:l:""},fileTags:["img"],select:function(t){this.plugins.image.onModifyMode.call(this,t,this.plugins.resizing.call_controller_resize.call(this,t,"image"))},destroy:function(t){const e=t||this.context.image._element,i=this.util.getParentElement(e,this.util.isMediaComponent)||e,l=e.getAttribute("data-index")*1;if(typeof this.functions.onImageDeleteBefore=="function"&&this.functions.onImageDeleteBefore(e,i,l,this)===!1)return;let n=i.previousElementSibling||i.nextElementSibling;const s=i.parentNode;this.util.removeItem(i),this.plugins.image.init.call(this),this.controllersOff(),s!==this.context.element.wysiwyg&&this.util.removeItemAllParents(s,function(o){return o.childNodes.length===0},null),this.focusEdge(n),this.plugins.fileManager.deleteInfo.call(this,"image",l,this.functions.onImageUpload),this.history.push(!1)},on:function(t){const e=this.context.image;t?e.imgInputFile&&this.options.imageMultipleFile&&e.imgInputFile.removeAttribute("multiple"):(e.inputX.value=e._origin_w=this.options.imageWidth===e._defaultSizeX?"":this.options.imageWidth,e.inputY.value=e._origin_h=this.options.imageHeight===e._defaultSizeY?"":this.options.imageHeight,e.imgInputFile&&this.options.imageMultipleFile&&e.imgInputFile.setAttribute("multiple","multiple")),this.plugins.anchor.on.call(this,e.anchorCtx,t)},open:function(){this.plugins.dialog.open.call(this,"image",this.currentControllerName==="image")},openTab:function(t){const e=this.context.image.modal,i=t==="init"?e.querySelector("._se_tab_link"):t.target;if(!/^BUTTON$/i.test(i.tagName))return!1;const l=i.getAttribute("data-tab-link"),n="_se_tab_content";let s,o,a;for(o=e.getElementsByClassName(n),s=0;s<o.length;s++)o[s].style.display="none";for(a=e.getElementsByClassName("_se_tab_link"),s=0;s<a.length;s++)this.util.removeClass(a[s],"active");return e.querySelector("."+n+"_"+l).style.display="block",this.util.addClass(i,"active"),l==="image"&&this.context.image.focusElement?this.context.image.focusElement.focus():l==="url"&&this.context.anchor.caller.image.urlInput.focus(),!1},submit:function(t){const e=this.context.image,i=this.plugins.image;t.preventDefault(),t.stopPropagation(),e._altText=e.altText.value,e._align=e.modal.querySelector('input[name="suneditor_image_radio"]:checked').value,e._captionChecked=e.captionCheckEl.checked,e._resizing&&(e._proportionChecked=e.proportion.checked);try{this.context.dialog.updateModal&&i.update_image.call(this,!1,!0,!1),e.imgInputFile&&e.imgInputFile.files.length>0?(this.showLoading(),i.submitAction.call(this,this.context.image.imgInputFile.files)):e.imgUrlFile&&e._v_src._linkValue.length>0&&(this.showLoading(),i.onRender_imgUrl.call(this,e._v_src._linkValue))}catch(l){throw this.closeLoading(),Error('[SUNEDITOR.image.submit.fail] cause : "'+l.message+'"')}finally{this.plugins.dialog.close.call(this)}return!1},submitAction:function(t){if(t.length===0)return;let e=0,i=[];for(let a=0,r=t.length;a<r;a++)/image/i.test(t[a].type)&&(i.push(t[a]),e+=t[a].size);const l=this.options.imageUploadSizeLimit;if(l>0){let a=0;const r=this.context.image._infoList;for(let c=0,u=r.length;c<u;c++)a+=r[c].size*1;if(e+a>l){this.closeLoading();const c="[SUNEDITOR.imageUpload.fail] Size of uploadable total images: "+l/1e3+"KB";(typeof this.functions.onImageUploadError!="function"||this.functions.onImageUploadError(c,{limitSize:l,currentSize:a,uploadSize:e},this))&&this.functions.noticeOpen(c);return}}const n=this.context.image;n._uploadFileLength=i.length;const o={anchor:this.plugins.anchor.createAnchor.call(this,n.anchorCtx,!0),inputWidth:n.inputX.value,inputHeight:n.inputY.value,align:n._align,isUpdate:this.context.dialog.updateModal,alt:n._altText,element:n._element};if(typeof this.functions.onImageUploadBefore=="function"){const a=this.functions.onImageUploadBefore(i,o,this,(function(r){r&&this._w.Array.isArray(r.result)?this.plugins.image.register.call(this,o,r):this.plugins.image.upload.call(this,o,r)}).bind(this));if(typeof a>"u")return;if(!a){this.closeLoading();return}this._w.Array.isArray(a)&&a.length>0&&(i=a)}this.plugins.image.upload.call(this,o,i)},error:function(t,e){if(this.closeLoading(),typeof this.functions.onImageUploadError!="function"||this.functions.onImageUploadError(t,e,this))throw this.functions.noticeOpen(t),Error("[SUNEDITOR.plugin.image.error] response: "+t)},upload:function(t,e){if(!e){this.closeLoading();return}if(typeof e=="string"){this.plugins.image.error.call(this,e,null);return}const i=this.options.imageUploadUrl,l=this.context.dialog.updateModal?1:e.length;if(typeof i=="string"&&i.length>0){const n=new FormData;for(let s=0;s<l;s++)n.append("file-"+s,e[s]);this.plugins.fileManager.upload.call(this,i,this.options.imageUploadHeader,n,this.plugins.image.callBack_imgUpload.bind(this,t),this.functions.onImageUploadError)}else this.plugins.image.setup_reader.call(this,e,t.anchor,t.inputWidth,t.inputHeight,t.align,t.alt,l,t.isUpdate)},callBack_imgUpload:function(t,e){if(typeof this.functions.imageUploadHandler=="function")this.functions.imageUploadHandler(e,t,this);else{const i=JSON.parse(e.responseText);i.errorMessage?this.plugins.image.error.call(this,i.errorMessage,i):this.plugins.image.register.call(this,t,i)}},register:function(t,e){const i=e.result;for(let l=0,n=i.length,s;l<n;l++)if(s={name:i[l].name,size:i[l].size},t.isUpdate){this.plugins.image.update_src.call(this,i[l].url,t.element,s);break}else this.plugins.image.create_image.call(this,i[l].url,t.anchor,t.inputWidth,t.inputHeight,t.align,s,t.alt);this.closeLoading()},setup_reader:function(t,e,i,l,n,s,o,a){try{if(o===0){this.closeLoading(),console.warn("[SUNEDITOR.image.base64.fail] cause : No applicable files");return}this.context.image.base64RenderIndex=o;const r=this._w.FileReader,c=[o];this.context.image.inputX.value=i,this.context.image.inputY.value=l;for(let u=0,f,p;u<o;u++)f=new r,p=t[u],f.onload=(function(m,d,_,g,h){c[h]={result:m.result,file:g},--this.context.image.base64RenderIndex===0&&(this.plugins.image.onRender_imgBase64.call(this,d,c,_,e,i,l,n,s),this.closeLoading())}).bind(this,f,a,this.context.image._element,p,u),f.readAsDataURL(p)}catch(r){throw this.closeLoading(),Error('[SUNEDITOR.image.setup_reader.fail] cause : "'+r.message+'"')}},onRender_imgBase64:function(t,e,i,l,n,s,o,a){const r=this.plugins.image.update_src,c=this.plugins.image.create_image;for(let u=0,f=e.length;u<f;u++)t?(this.context.image._element.setAttribute("data-file-name",e[u].file.name),this.context.image._element.setAttribute("data-file-size",e[u].file.size),r.call(this,e[u].result,i,e[u].file)):c.call(this,e[u].result,l,n,s,o,e[u].file,a)},onRender_imgUrl:function(t){if(t||(t=this.context.image._v_src._linkValue),!t)return!1;const e=this.context.image;try{const i={name:t.split("/").pop(),size:0};this.context.dialog.updateModal?this.plugins.image.update_src.call(this,t,e._element,i):this.plugins.image.create_image.call(this,t,this.plugins.anchor.createAnchor.call(this,e.anchorCtx,!0),e.inputX.value,e.inputY.value,e._align,i,e._altText)}catch(i){throw Error('[SUNEDITOR.image.URLRendering.fail] cause : "'+i.message+'"')}finally{this.closeLoading()}},onRender_link:function(t,e){return e?(e.setAttribute("data-image-link","image"),t.setAttribute("data-image-link",e.href),e.appendChild(t),e):t},setInputSize:function(t,e){if(e&&e.keyCode===32){e.preventDefault();return}this.plugins.resizing._module_setInputSize.call(this,this.context.image,t)},setRatio:function(){this.plugins.resizing._module_setRatio.call(this,this.context.image)},checkFileInfo:function(){const t=this.plugins.image,e=this.context.image,i=(function(l){t.onModifyMode.call(this,l,null),t.openModify.call(this,!0),e.inputX.value=e._origin_w,e.inputY.value=e._origin_h;const n=this.util.getFormatElement(l);n&&(e._align=n.style.textAlign||n.style.float),this.util.isAnchor(l.parentNode)&&!e.anchorCtx.linkValue&&(e.anchorCtx.linkValue=" "),t.update_image.call(this,!0,!1,!0),t.init.call(this)}).bind(this);this.plugins.fileManager.checkInfo.call(this,"image",["img"],this.functions.onImageUpload,i,!0)},resetFileInfo:function(){this.plugins.fileManager.resetInfo.call(this,"image",this.functions.onImageUpload)},create_image:function(t,e,i,l,n,s,o){const a=this.plugins.image,r=this.context.image;this.context.resizing._resize_plugin="image";let c=this.util.createElement("IMG");c.src=t,c.alt=o,c.setAttribute("data-rotate","0"),e=a.onRender_link.call(this,c,e?e.cloneNode(!1):null),r._resizing&&c.setAttribute("data-proportion",r._proportionChecked);const u=this.plugins.component.set_cover.call(this,e),f=this.plugins.component.set_container.call(this,u,"se-image-container");r._captionChecked&&(r._caption=this.plugins.component.create_caption.call(this),u.appendChild(r._caption)),r._element=c,r._cover=u,r._container=f,a.applySize.call(this,i,l),a.setAlign.call(this,n,c,u,f),c.onload=a._image_create_onload.bind(this,c,r.svgDefaultSize,f),this.insertComponent(f,!0,!0,!0)&&this.plugins.fileManager.setInfo.call(this,"image",c,this.functions.onImageUpload,s,!0),this.context.resizing._resize_plugin=""},_image_create_onload:function(t,e,i){if(t.offsetWidth===0&&this.plugins.image.applySize.call(this,e,""),this.options.mediaAutoSelect)this.selectComponent(t,"image");else{const l=this.appendFormatTag(i,null);l&&this.setRange(l,0,l,0)}},update_image:function(t,e,i){const l=this.context.image;let n=l._element,s=l._cover,o=l._container,a=!1;s===null&&(a=!0,n=l._element.cloneNode(!0),s=this.plugins.component.set_cover.call(this,n)),o===null?(s=s.cloneNode(!0),n=s.querySelector("img"),a=!0,o=this.plugins.component.set_container.call(this,s,"se-image-container")):a&&(o.innerHTML="",o.appendChild(s),l._cover=s,l._element=n,a=!1);let r;const c=this.util.isNumber(l.inputX.value)?l.inputX.value+l.sizeUnit:l.inputX.value,u=this.util.isNumber(l.inputY.value)?l.inputY.value+l.sizeUnit:l.inputY.value;/%$/.test(n.style.width)?r=c!==o.style.width||u!==o.style.height:r=c!==n.style.width||u!==n.style.height,n.alt=l._altText;let f=!1;l._captionChecked?l._caption||(l._caption=this.plugins.component.create_caption.call(this),s.appendChild(l._caption),f=!0):l._caption&&(this.util.removeItem(l._caption),l._caption=null,f=!0);let p=null;const m=this.plugins.anchor.createAnchor.call(this,l.anchorCtx,!0);if(m)l._linkElement!==m||a&&!o.contains(m)?(l._linkElement=m.cloneNode(!1),s.insertBefore(this.plugins.image.onRender_link.call(this,n,l._linkElement),l._caption),p=l._element):l._linkElement.setAttribute("data-image-link","image");else if(l._linkElement!==null){const _=n;if(_.setAttribute("data-image-link",""),s.contains(l._linkElement)){const g=_.cloneNode(!0);s.removeChild(l._linkElement),s.insertBefore(g,l._caption),l._element=n=g}}let d=null;if(a){if(d=this.util.isRangeFormatElement(l._element.parentNode)||this.util.isWysiwygDiv(l._element.parentNode)?l._element:this.util.isAnchor(l._element.parentNode)?l._element.parentNode:this.util.getFormatElement(l._element)||l._element,this.util.getParentElement(l._element,this.util.isNotCheckingNode))d=p?m:l._element,d.parentNode.replaceChild(o,d);else if(this.util.isListCell(d)){const _=this.util.getParentElement(l._element,function(g){return g.parentNode===d});d.insertBefore(o,_),this.util.removeItem(l._element),this.util.removeEmptyNode(_,null,!0)}else if(this.util.isFormatElement(d)){const _=this.util.getParentElement(l._element,function(g){return g.parentNode===d});d=this.util.splitElement(d,_),d.parentNode.insertBefore(o,d),this.util.removeItem(l._element),this.util.removeEmptyNode(d,null,!0),d.children.length===0&&(d.innerHTML=this.util.htmlRemoveWhiteSpace(d.innerHTML))}else if(this.util.isFormatElement(d.parentNode)){const _=d.parentNode;_.parentNode.insertBefore(o,d.previousSibling?_.nextElementSibling:_),l.__updateTags.map(function(g){return d.contains(g)}).length===0&&this.util.removeItem(d)}else d=this.util.isFigures(d.parentNode)?d.parentNode:d,d.parentNode.replaceChild(o,d);n=o.querySelector("img"),l._element=n,l._cover=s,l._container=o}p&&(a?(this.util.removeItem(p),this.util.getListChildren(m,function(_){return/IMG/i.test(_.tagName)}).length===0&&this.util.removeItem(m)):this.util.removeItem(m)),(f||!l._onlyPercentage&&r)&&!t&&(/\d+/.test(n.style.height)||this.context.resizing._rotateVertical&&l._captionChecked)&&(/%$/.test(l.inputX.value)||/%$/.test(l.inputY.value)?this.plugins.resizing.resetTransform.call(this,n):this.plugins.resizing.setTransformSize.call(this,n,this.util.getNumber(l.inputX.value,0),this.util.getNumber(l.inputY.value,0))),l._resizing&&(n.setAttribute("data-proportion",l._proportionChecked),r&&this.plugins.image.applySize.call(this)),this.plugins.image.setAlign.call(this,null,n,null,null),t&&this.plugins.fileManager.setInfo.call(this,"image",n,this.functions.onImageUpload,null,!0),e&&this.selectComponent(n,"image"),i||this.history.push(!1)},update_src:function(t,e,i){e.src=t,this._w.setTimeout(this.plugins.fileManager.setInfo.bind(this,"image",e,this.functions.onImageUpload,i,!0)),this.selectComponent(e,"image")},onModifyMode:function(t,e){if(!t)return;const i=this.context.image;i._linkElement=i.anchorCtx.linkAnchor=this.util.isAnchor(t.parentNode)?t.parentNode:null,i._element=t,i._cover=this.util.getParentElement(t,"FIGURE"),i._container=this.util.getParentElement(t,this.util.isMediaComponent),i._caption=this.util.getChildElement(i._cover,"FIGCAPTION"),i._align=t.getAttribute("data-align")||t.style.float||"none",t.style.float="",this.plugins.anchor.setCtx(i._linkElement,i.anchorCtx),e&&(i._element_w=e.w,i._element_h=e.h,i._element_t=e.t,i._element_l=e.l);let l=i._element.getAttribute("data-size")||i._element.getAttribute("data-origin"),n,s;l?(l=l.split(","),n=l[0],s=l[1]):e&&(n=e.w,s=e.h),i._origin_w=n||t.style.width||t.width||"",i._origin_h=s||t.style.height||t.height||""},openModify:function(t){const e=this.context.image;e.imgUrlFile&&(e._v_src._linkValue=e.previewSrc.textContent=e.imgUrlFile.value=e._element.src),e._altText=e.altText.value=e._element.alt,(e.modal.querySelector('input[name="suneditor_image_radio"][value="'+e._align+'"]')||e.modal.querySelector('input[name="suneditor_image_radio"][value="none"]')).checked=!0,e._align=e.modal.querySelector('input[name="suneditor_image_radio"]:checked').value,e._captionChecked=e.captionCheckEl.checked=!!e._caption,e._resizing&&this.plugins.resizing._module_setModifyInputSize.call(this,e,this.plugins.image),t||this.plugins.dialog.open.call(this,"image",!0)},applySize:function(t,e){const i=this.context.image;return t||(t=i.inputX.value||this.options.imageWidth),e||(e=i.inputY.value||this.options.imageHeight),i._onlyPercentage&&t||/%$/.test(t)?(this.plugins.image.setPercentSize.call(this,t,e),!0):((!t||t==="auto")&&(!e||e==="auto")?this.plugins.image.setAutoSize.call(this):this.plugins.image.setSize.call(this,t,e,!1),!1)},sizeRevert:function(){this.plugins.resizing._module_sizeRevert.call(this,this.context.image)},setSize:function(t,e,i,l){const n=this.context.image,s=/^(rw|lw)$/.test(l)&&/\d+/.test(n._element.style.height);/^(th|bh)$/.test(l)&&/\d+/.test(n._element.style.width)||(n._element.style.width=this.util.isNumber(t)?t+n.sizeUnit:t,this.plugins.image.cancelPercentAttr.call(this)),s||(n._element.style.height=this.util.isNumber(e)?e+n.sizeUnit:/%$/.test(e)?"":e),n._align==="center"&&this.plugins.image.setAlign.call(this,null,null,null,null),i||n._element.removeAttribute("data-percentage"),this.plugins.resizing._module_saveCurrentSize.call(this,n)},setAutoSize:function(){const t=this.context.image;t._caption&&(t._caption.style.marginTop=""),this.plugins.resizing.resetTransform.call(this,t._element),this.plugins.image.cancelPercentAttr.call(this),t._element.style.maxWidth="",t._element.style.width="",t._element.style.height="",t._cover.style.width="",t._cover.style.height="",this.plugins.image.setAlign.call(this,null,null,null,null),t._element.setAttribute("data-percentage","auto,auto"),this.plugins.resizing._module_saveCurrentSize.call(this,t)},setOriginSize:function(){const t=this.context.image;t._element.removeAttribute("data-percentage"),this.plugins.resizing.resetTransform.call(this,t._element),this.plugins.image.cancelPercentAttr.call(this);const e=(t._element.getAttribute("data-origin")||"").split(","),i=e[0],l=e[1];e&&(t._onlyPercentage||/%$/.test(i)&&(/%$/.test(l)||!/\d/.test(l))?this.plugins.image.setPercentSize.call(this,i,l):this.plugins.image.setSize.call(this,i,l),this.plugins.resizing._module_saveCurrentSize.call(this,t))},setPercentSize:function(t,e){const i=this.context.image;e=e&&!/%$/.test(e)&&!this.util.getNumber(e,0)?this.util.isNumber(e)?e+"%":e:this.util.isNumber(e)?e+i.sizeUnit:e||"";const l=/%$/.test(e);i._container.style.width=this.util.isNumber(t)?t+"%":t,i._container.style.height="",i._cover.style.width="100%",i._cover.style.height=l?e:"",i._element.style.width="100%",i._element.style.height=l?"":e,i._element.style.maxWidth="",i._align==="center"&&this.plugins.image.setAlign.call(this,null,null,null,null),i._element.setAttribute("data-percentage",t+","+e),this.plugins.resizing.setCaptionPosition.call(this,i._element),this.plugins.resizing._module_saveCurrentSize.call(this,i)},cancelPercentAttr:function(){const t=this.context.image;t._cover.style.width="",t._cover.style.height="",t._container.style.width="",t._container.style.height="",this.util.removeClass(t._container,this.context.image._floatClassRegExp),this.util.addClass(t._container,"__se__float-"+t._align),t._align==="center"&&this.plugins.image.setAlign.call(this,null,null,null,null)},setAlign:function(t,e,i,l){const n=this.context.image;t||(t=n._align),e||(e=n._element),i||(i=n._cover),l||(l=n._container),/%$/.test(e.style.width)&&t==="center"?(l.style.minWidth="100%",i.style.width=l.style.width):(l.style.minWidth="",i.style.width=this.context.resizing._rotateVertical?e.style.height||e.offsetHeight:!e.style.width||e.style.width==="auto"?"":e.style.width||"100%"),this.util.hasClass(l,"__se__float-"+t)||(this.util.removeClass(l,n._floatClassRegExp),this.util.addClass(l,"__se__float-"+t)),e.setAttribute("data-align",t)},init:function(){const t=this.context.image;t.imgInputFile&&(t.imgInputFile.value=""),t.imgUrlFile&&(t._v_src._linkValue=t.previewSrc.textContent=t.imgUrlFile.value=""),t.imgInputFile&&t.imgUrlFile&&(t.imgUrlFile.removeAttribute("disabled"),t.previewSrc.style.textDecoration=""),t.altText.value="",t.modal.querySelector('input[name="suneditor_image_radio"][value="none"]').checked=!0,t.captionCheckEl.checked=!1,t._element=null,this.plugins.image.openTab.call(this,"init"),t._resizing&&(t.inputX.value=this.options.imageWidth===t._defaultSizeX?"":this.options.imageWidth,t.inputY.value=this.options.imageHeight===t._defaultSizeY?"":this.options.imageHeight,t.proportion.checked=!0,t._ratio=!1,t._ratioX=1,t._ratioY=1),this.plugins.anchor.init.call(this,t.anchorCtx)}},Lt={name:"video",display:"dialog",add:function(t){t.addModule([X,Q,kt,J]);const e=t.options,i=t.context,l=i.video={_infoList:[],_infoIndex:0,_uploadFileLength:0,focusElement:null,sizeUnit:e._videoSizeUnit,_align:"none",_floatClassRegExp:"__se__float\\-[a-z]+",_youtubeQuery:e.youtubeQuery,_vimeoQuery:e.vimeoQuery,_videoRatio:e.videoRatio*100+"%",_defaultRatio:e.videoRatio*100+"%",_linkValue:"",_element:null,_cover:null,_container:null,inputX:null,inputY:null,_element_w:1,_element_h:1,_element_l:0,_element_t:0,_defaultSizeX:"100%",_defaultSizeY:e.videoRatio*100+"%",_origin_w:e.videoWidth==="100%"?"":e.videoWidth,_origin_h:e.videoHeight==="56.25%"?"":e.videoHeight,_proportionChecked:!0,_resizing:e.videoResizing,_resizeDotHide:!e.videoHeightShow,_rotation:e.videoRotation,_alignHide:!e.videoAlignShow,_onlyPercentage:e.videoSizeOnlyPercentage,_ratio:!1,_ratioX:1,_ratioY:1,_captionShow:!1};let n=this.setDialog(t);l.modal=n,l.videoInputFile=n.querySelector("._se_video_file"),l.videoUrlFile=n.querySelector(".se-input-url"),l.focusElement=l.videoUrlFile||l.videoInputFile,l.preview=n.querySelector(".se-link-preview"),n.querySelector("form").addEventListener("submit",this.submit.bind(t)),l.videoInputFile&&n.querySelector(".se-dialog-files-edge-button").addEventListener("click",this._removeSelectedFiles.bind(l.videoInputFile,l.videoUrlFile,l.preview)),l.videoInputFile&&l.videoUrlFile&&l.videoInputFile.addEventListener("change",this._fileInputChange.bind(l)),l.videoUrlFile&&l.videoUrlFile.addEventListener("input",this._onLinkPreview.bind(l.preview,l,e.linkProtocol)),l.proportion={},l.videoRatioOption={},l.inputX={},l.inputY={},e.videoResizing&&(l.proportion=n.querySelector("._se_video_check_proportion"),l.videoRatioOption=n.querySelector(".se-video-ratio"),l.inputX=n.querySelector("._se_video_size_x"),l.inputY=n.querySelector("._se_video_size_y"),l.inputX.value=e.videoWidth,l.inputY.value=e.videoHeight,l.inputX.addEventListener("keyup",this.setInputSize.bind(t,"x")),l.inputY.addEventListener("keyup",this.setInputSize.bind(t,"y")),l.inputX.addEventListener("change",this.setRatio.bind(t)),l.inputY.addEventListener("change",this.setRatio.bind(t)),l.proportion.addEventListener("change",this.setRatio.bind(t)),l.videoRatioOption.addEventListener("change",this.setVideoRatio.bind(t)),n.querySelector(".se-dialog-btn-revert").addEventListener("click",this.sizeRevert.bind(t))),i.dialog.modal.appendChild(n),n=null},setDialog:function(t){const e=t.options,i=t.lang,l=t.util.createElement("DIV");l.className="se-dialog-content",l.style.display="none";let n='<form method="post" enctype="multipart/form-data"><div class="se-dialog-header"><button type="button" data-command="close" class="se-btn se-dialog-close" title="'+i.dialogBox.close+'" aria-label="'+i.dialogBox.close+'">'+t.icons.cancel+'</button><span class="se-modal-title">'+i.dialogBox.videoBox.title+'</span></div><div class="se-dialog-body">';if(e.videoFileInput&&(n+='<div class="se-dialog-form"><label>'+i.dialogBox.videoBox.file+'</label><div class="se-dialog-form-files"><input class="se-input-form _se_video_file" type="file" accept="'+e.videoAccept+'"'+(e.videoMultipleFile?' multiple="multiple"':"")+'/><button type="button" data-command="filesRemove" class="se-btn se-dialog-files-edge-button se-file-remove" title="'+i.controller.remove+'" aria-label="'+i.controller.remove+'">'+t.icons.cancel+"</button></div></div>"),e.videoUrlInput&&(n+='<div class="se-dialog-form"><label>'+i.dialogBox.videoBox.url+'</label><input class="se-input-form se-input-url" type="text" /><pre class="se-link-preview"></pre></div>'),e.videoResizing){const s=e.videoRatioList||[{name:"16:9",value:.5625},{name:"4:3",value:.75},{name:"21:9",value:.4285}],o=e.videoRatio,a=e.videoSizeOnlyPercentage,r=a?' style="display: none !important;"':"",c=e.videoHeightShow?"":' style="display: none !important;"',u=e.videoRatioShow?"":' style="display: none !important;"',f=!a&&!e.videoHeightShow&&!e.videoRatioShow?' style="display: none !important;"':"";n+='<div class="se-dialog-form"><div class="se-dialog-size-text"><label class="size-w">'+i.dialogBox.width+'</label><label class="se-dialog-size-x">&nbsp;</label><label class="size-h"'+c+">"+i.dialogBox.height+'</label><label class="size-h"'+u+">("+i.dialogBox.ratio+')</label></div><input class="se-input-control _se_video_size_x" placeholder="100%"'+(a?' type="number" min="1"':'type="text"')+(a?' max="100"':"")+'/><label class="se-dialog-size-x"'+f+">"+(a?"%":"x")+'</label><input class="se-input-control _se_video_size_y" placeholder="'+e.videoRatio*100+'%"'+(a?' type="number" min="1"':'type="text"')+(a?' max="100"':"")+c+'/><select class="se-input-select se-video-ratio" title="'+i.dialogBox.ratio+'" aria-label="'+i.dialogBox.ratio+'"'+u+">",c||(n+='<option value=""> - </option>');for(let p=0,m=s.length;p<m;p++)n+='<option value="'+s[p].value+'"'+(o.toString()===s[p].value.toString()?" selected":"")+">"+s[p].name+"</option>";n+='</select><button type="button" title="'+i.dialogBox.revertButton+'" aria-label="'+i.dialogBox.revertButton+'" class="se-btn se-dialog-btn-revert" style="float: right;">'+t.icons.revert+'</button></div><div class="se-dialog-form se-dialog-form-footer"'+r+f+'><label><input type="checkbox" class="se-dialog-btn-check _se_video_check_proportion" checked/>&nbsp;'+i.dialogBox.proportion+"</label></div>"}return n+='</div><div class="se-dialog-footer"><div'+(e.videoAlignShow?"":' style="display: none"')+'><label><input type="radio" name="suneditor_video_radio" class="se-dialog-btn-radio" value="none" checked>'+i.dialogBox.basic+'</label><label><input type="radio" name="suneditor_video_radio" class="se-dialog-btn-radio" value="left">'+i.dialogBox.left+'</label><label><input type="radio" name="suneditor_video_radio" class="se-dialog-btn-radio" value="center">'+i.dialogBox.center+'</label><label><input type="radio" name="suneditor_video_radio" class="se-dialog-btn-radio" value="right">'+i.dialogBox.right+'</label></div><button type="submit" class="se-btn-primary" title="'+i.dialogBox.submitButton+'" aria-label="'+i.dialogBox.submitButton+'"><span>'+i.dialogBox.submitButton+"</span></button></div></form>",l.innerHTML=n,l},_fileInputChange:function(){this.videoInputFile.value?(this.videoUrlFile.setAttribute("disabled",!0),this.preview.style.textDecoration="line-through"):(this.videoUrlFile.removeAttribute("disabled"),this.preview.style.textDecoration="")},_removeSelectedFiles:function(t,e){this.value="",t&&(t.removeAttribute("disabled"),e.style.textDecoration="")},_onLinkPreview:function(t,e,i){const l=i.target.value.trim();/^<iframe.*\/iframe>$/.test(l)?(t._linkValue=l,this.textContent='<IFrame :src=".."></IFrame>'):t._linkValue=this.textContent=l?e&&l.indexOf("://")===-1&&l.indexOf("#")!==0?e+l:l.indexOf("://")===-1?"/"+l:l:""},_setTagAttrs:function(t){t.setAttribute("controls",!0);const e=this.options.videoTagAttrs;if(e)for(let i in e)this.util.hasOwn(e,i)&&t.setAttribute(i,e[i])},createVideoTag:function(){const t=this.util.createElement("VIDEO");return this.plugins.video._setTagAttrs.call(this,t),t},_setIframeAttrs:function(t){t.frameBorder="0",t.allowFullscreen=!0;const e=this.options.videoIframeAttrs;if(e)for(let i in e)this.util.hasOwn(e,i)&&t.setAttribute(i,e[i])},createIframeTag:function(){const t=this.util.createElement("IFRAME");return this.plugins.video._setIframeAttrs.call(this,t),t},fileTags:["iframe","video"],select:function(t){this.plugins.video.onModifyMode.call(this,t,this.plugins.resizing.call_controller_resize.call(this,t,"video"))},destroy:function(t){const e=t||this.context.video._element,i=this.context.video._container,l=e.getAttribute("data-index")*1;if(typeof this.functions.onVideoDeleteBefore=="function"&&this.functions.onVideoDeleteBefore(e,i,l,this)===!1)return;let n=i.previousElementSibling||i.nextElementSibling;const s=i.parentNode;this.util.removeItem(i),this.plugins.video.init.call(this),this.controllersOff(),s!==this.context.element.wysiwyg&&this.util.removeItemAllParents(s,function(o){return o.childNodes.length===0},null),this.focusEdge(n),this.plugins.fileManager.deleteInfo.call(this,"video",l,this.functions.onVideoUpload),this.history.push(!1)},on:function(t){const e=this.context.video;t?e.videoInputFile&&this.options.videoMultipleFile&&e.videoInputFile.removeAttribute("multiple"):(e.inputX.value=e._origin_w=this.options.videoWidth===e._defaultSizeX?"":this.options.videoWidth,e.inputY.value=e._origin_h=this.options.videoHeight===e._defaultSizeY?"":this.options.videoHeight,e.proportion.disabled=!0,e.videoInputFile&&this.options.videoMultipleFile&&e.videoInputFile.setAttribute("multiple","multiple")),e._resizing&&this.plugins.video.setVideoRatioSelect.call(this,e._origin_h||e._defaultRatio)},open:function(){this.plugins.dialog.open.call(this,"video",this.currentControllerName==="video")},setVideoRatio:function(t){const e=this.context.video,i=t.target.options[t.target.selectedIndex].value;e._defaultSizeY=e._videoRatio=i?i*100+"%":e._defaultSizeY,e.inputY.placeholder=i?i*100+"%":"",e.inputY.value=""},setInputSize:function(t,e){if(e&&e.keyCode===32){e.preventDefault();return}const i=this.context.video;this.plugins.resizing._module_setInputSize.call(this,i,t),t==="y"&&this.plugins.video.setVideoRatioSelect.call(this,e.target.value||i._defaultRatio)},setRatio:function(){this.plugins.resizing._module_setRatio.call(this,this.context.video)},submit:function(t){const e=this.context.video,i=this.plugins.video;t.preventDefault(),t.stopPropagation(),e._align=e.modal.querySelector('input[name="suneditor_video_radio"]:checked').value;try{e.videoInputFile&&e.videoInputFile.files.length>0?(this.showLoading(),i.submitAction.call(this,this.context.video.videoInputFile.files)):e.videoUrlFile&&e._linkValue.length>0&&(this.showLoading(),i.setup_url.call(this,e._linkValue))}catch(l){throw this.closeLoading(),Error('[SUNEDITOR.video.submit.fail] cause : "'+l.message+'"')}finally{this.plugins.dialog.close.call(this)}return!1},submitAction:function(t){if(t.length===0)return;let e=0,i=[];for(let o=0,a=t.length;o<a;o++)/video/i.test(t[o].type)&&(i.push(t[o]),e+=t[o].size);const l=this.options.videoUploadSizeLimit;if(l>0){let o=0;const a=this.context.video._infoList;for(let r=0,c=a.length;r<c;r++)o+=a[r].size*1;if(e+o>l){this.closeLoading();const r="[SUNEDITOR.videoUpload.fail] Size of uploadable total videos: "+l/1e3+"KB";(typeof this.functions.onVideoUploadError!="function"||this.functions.onVideoUploadError(r,{limitSize:l,currentSize:o,uploadSize:e},this))&&this.functions.noticeOpen(r);return}}const n=this.context.video;n._uploadFileLength=i.length;const s={inputWidth:n.inputX.value,inputHeight:n.inputY.value,align:n._align,isUpdate:this.context.dialog.updateModal,element:n._element};if(typeof this.functions.onVideoUploadBefore=="function"){const o=this.functions.onVideoUploadBefore(i,s,this,(function(a){a&&this._w.Array.isArray(a.result)?this.plugins.video.register.call(this,s,a):this.plugins.video.upload.call(this,s,a)}).bind(this));if(typeof o>"u")return;if(!o){this.closeLoading();return}typeof o=="object"&&o.length>0&&(i=o)}this.plugins.video.upload.call(this,s,i)},error:function(t,e){if(this.closeLoading(),typeof this.functions.onVideoUploadError!="function"||this.functions.onVideoUploadError(t,e,this))throw this.functions.noticeOpen(t),Error("[SUNEDITOR.plugin.video.error] response: "+t)},upload:function(t,e){if(!e){this.closeLoading();return}if(typeof e=="string"){this.plugins.video.error.call(this,e,null);return}const i=this.options.videoUploadUrl,l=this.context.dialog.updateModal?1:e.length;if(typeof i=="string"&&i.length>0){const n=new FormData;for(let s=0;s<l;s++)n.append("file-"+s,e[s]);this.plugins.fileManager.upload.call(this,i,this.options.videoUploadHeader,n,this.plugins.video.callBack_videoUpload.bind(this,t),this.functions.onVideoUploadError)}else throw Error('[SUNEDITOR.videoUpload.fail] cause : There is no "videoUploadUrl" option.')},callBack_videoUpload:function(t,e){if(typeof this.functions.videoUploadHandler=="function")this.functions.videoUploadHandler(e,t,this);else{const i=JSON.parse(e.responseText);i.errorMessage?this.plugins.video.error.call(this,i.errorMessage,i):this.plugins.video.register.call(this,t,i)}},register:function(t,e){const i=e.result,l=this.plugins.video.createVideoTag.call(this);for(let n=0,s=i.length,o;n<s;n++)o={name:i[n].name,size:i[n].size},this.plugins.video.create_video.call(this,t.isUpdate?t.element:l.cloneNode(!1),i[n].url,t.inputWidth,t.inputHeight,t.align,o,t.isUpdate);this.closeLoading()},setup_url:function(t){try{const e=this.context.video;if(t||(t=e._linkValue),!t||/^<iframe.*\/iframe>$/.test(t)&&(t=new this._w.DOMParser().parseFromString(t,"text/html").querySelector("iframe").src,t.length===0))return!1;if(/youtu\.?be/.test(t)){if(/^http/.test(t)||(t="https://"+t),t=t.replace("watch?v=",""),/^\/\/.+\/embed\//.test(t)||(t=t.replace(t.match(/\/\/.+\//)[0],"//www.youtube.com/embed/").replace("&","?&")),e._youtubeQuery.length>0)if(/\?/.test(t)){const i=t.split("?");t=i[0]+"?"+e._youtubeQuery+"&"+i[1]}else t+="?"+e._youtubeQuery}else if(/vimeo\.com/.test(t)&&(t.endsWith("/")&&(t=t.slice(0,-1)),t="https://player.vimeo.com/video/"+t.slice(t.lastIndexOf("/")+1),e._vimeoQuery.length>0))if(/\?/.test(t)){const i=t.split("?");t=i[0]+"?"+e._vimeoQuery+"&"+i[1]}else t+="?"+e._vimeoQuery;this.plugins.video.create_video.call(this,this.plugins.video[!/embed|iframe|player|\/e\/|\.php|\.html?/.test(t)&&!/vimeo\.com/.test(t)?"createVideoTag":"createIframeTag"].call(this),t,e.inputX.value,e.inputY.value,e._align,null,this.context.dialog.updateModal)}catch(e){throw Error('[SUNEDITOR.video.upload.fail] cause : "'+e.message+'"')}finally{this.closeLoading()}},create_video:function(t,e,i,l,n,s,o){this.context.resizing._resize_plugin="video";const a=this.context.video;let r=null,c=null,u=!1;if(o){if(t=a._element,t.src!==e){u=!0;const _=/youtu\.?be/.test(e),g=/vimeo\.com/.test(e);if((_||g)&&!/^iframe$/i.test(t.nodeName)){const h=this.plugins.video.createIframeTag.call(this);h.src=e,t.parentNode.replaceChild(h,t),a._element=t=h}else if(!_&&!g&&!/^video$/i.test(t.nodeName)){const h=this.plugins.video.createVideoTag.call(this);h.src=e,t.parentNode.replaceChild(h,t),a._element=t=h}else t.src=e}c=a._container,r=this.util.getParentElement(t,"FIGURE")}else u=!0,t.src=e,a._element=t,r=this.plugins.component.set_cover.call(this,t),c=this.plugins.component.set_container.call(this,r,"se-video-container");a._cover=r,a._container=c;const f=this.plugins.resizing._module_getSizeX.call(this,a)!==(i||a._defaultSizeX)||this.plugins.resizing._module_getSizeY.call(this,a)!==(l||a._videoRatio),p=!o||f;a._resizing&&(this.context.video._proportionChecked=a.proportion.checked,t.setAttribute("data-proportion",a._proportionChecked));let m=!1;p&&(m=this.plugins.video.applySize.call(this)),m&&n==="center"||this.plugins.video.setAlign.call(this,null,t,r,c);let d=!0;if(o)a._resizing&&this.context.resizing._rotateVertical&&p&&this.plugins.resizing.setTransformSize.call(this,t,null,null);else if(d=this.insertComponent(c,!1,!0,!this.options.mediaAutoSelect),!this.options.mediaAutoSelect){const _=this.appendFormatTag(c,null);_&&this.setRange(_,0,_,0)}d&&(u&&this.plugins.fileManager.setInfo.call(this,"video",t,this.functions.onVideoUpload,s,!0),o&&(this.selectComponent(t,"video"),this.history.push(!1))),this.context.resizing._resize_plugin=""},_update_videoCover:function(t){if(!t)return;const e=this.context.video;/^video$/i.test(t.nodeName)?this.plugins.video._setTagAttrs.call(this,t):this.plugins.video._setIframeAttrs.call(this,t);let i=this.util.isRangeFormatElement(t.parentNode)||this.util.isWysiwygDiv(t.parentNode)?t:this.util.getFormatElement(t)||t;const l=t;e._element=t=t.cloneNode(!0);const n=e._cover=this.plugins.component.set_cover.call(this,t),s=e._container=this.plugins.component.set_container.call(this,n,"se-video-container");try{const o=i.querySelector("figcaption");let a=null;o&&(a=this.util.createElement("DIV"),a.innerHTML=o.innerHTML,this.util.removeItem(o));const r=(t.getAttribute("data-size")||t.getAttribute("data-origin")||"").split(",");this.plugins.video.applySize.call(this,r[0]||l.style.width||l.width||"",r[1]||l.style.height||l.height||"");const c=this.util.getFormatElement(l);if(c&&(e._align=c.style.textAlign||c.style.float),this.plugins.video.setAlign.call(this,null,t,n,s),this.util.getParentElement(l,this.util.isNotCheckingNode))l.parentNode.replaceChild(s,l);else if(this.util.isListCell(i)){const u=this.util.getParentElement(l,function(f){return f.parentNode===i});i.insertBefore(s,u),this.util.removeItem(l),this.util.removeEmptyNode(u,null,!0)}else if(this.util.isFormatElement(i)){const u=this.util.getParentElement(l,function(f){return f.parentNode===i});i=this.util.splitElement(i,u),i.parentNode.insertBefore(s,i),this.util.removeItem(l),this.util.removeEmptyNode(i,null,!0),i.children.length===0&&(i.innerHTML=this.util.htmlRemoveWhiteSpace(i.innerHTML))}else i.parentNode.replaceChild(s,i);a&&i.parentNode.insertBefore(a,s.nextElementSibling)}catch(o){console.warn("[SUNEDITOR.video.error] Maybe the video tag is nested.",o)}this.plugins.fileManager.setInfo.call(this,"video",t,this.functions.onVideoUpload,null,!0),this.plugins.video.init.call(this)},onModifyMode:function(t,e){const i=this.context.video;i._element=t,i._cover=this.util.getParentElement(t,"FIGURE"),i._container=this.util.getParentElement(t,this.util.isMediaComponent),i._align=t.style.float||t.getAttribute("data-align")||"none",t.style.float="",e&&(i._element_w=e.w,i._element_h=e.h,i._element_t=e.t,i._element_l=e.l);let l=i._element.getAttribute("data-size")||i._element.getAttribute("data-origin"),n,s;l?(l=l.split(","),n=l[0],s=l[1]):e&&(n=e.w,s=e.h),i._origin_w=n||t.style.width||t.width||"",i._origin_h=s||t.style.height||t.height||""},openModify:function(t){const e=this.context.video;if(e.videoUrlFile&&(e._linkValue=e.preview.textContent=e.videoUrlFile.value=e._element.src||(e._element.querySelector("source")||"").src||""),(e.modal.querySelector('input[name="suneditor_video_radio"][value="'+e._align+'"]')||e.modal.querySelector('input[name="suneditor_video_radio"][value="none"]')).checked=!0,e._resizing){this.plugins.resizing._module_setModifyInputSize.call(this,e,this.plugins.video);const i=e._videoRatio=this.plugins.resizing._module_getSizeY.call(this,e);this.plugins.video.setVideoRatioSelect.call(this,i)||(e.inputY.value=e._onlyPercentage?this.util.getNumber(i,2):i)}t||this.plugins.dialog.open.call(this,"video",!0)},setVideoRatioSelect:function(t){let e=!1;const i=this.context.video,l=i.videoRatioOption.options;/%$/.test(t)||i._onlyPercentage?t=this.util.getNumber(t,2)/100+"":(!this.util.isNumber(t)||t*1>=1)&&(t=""),i.inputY.placeholder="";for(let n=0,s=l.length;n<s;n++)l[n].value===t?(e=l[n].selected=!0,i.inputY.placeholder=t?t*100+"%":""):l[n].selected=!1;return e},checkFileInfo:function(){this.plugins.fileManager.checkInfo.call(this,"video",["iframe","video"],this.functions.onVideoUpload,this.plugins.video._update_videoCover.bind(this),!0)},resetFileInfo:function(){this.plugins.fileManager.resetInfo.call(this,"video",this.functions.onVideoUpload)},applySize:function(t,e){const i=this.context.video;return t||(t=i.inputX.value||this.options.videoWidth),e||(e=i.inputY.value||this.options.videoHeight),i._onlyPercentage||/%$/.test(t)||!t?(this.plugins.video.setPercentSize.call(this,t||"100%",e||(/%$/.test(i._videoRatio)?i._videoRatio:i._defaultRatio)),!0):((!t||t==="auto")&&(!e||e==="auto")?this.plugins.video.setAutoSize.call(this):this.plugins.video.setSize.call(this,t,e||i._videoRatio||i._defaultRatio,!1),!1)},sizeRevert:function(){this.plugins.resizing._module_sizeRevert.call(this,this.context.video)},setSize:function(t,e,i,l){const n=this.context.video,s=/^(rw|lw)$/.test(l),o=/^(th|bh)$/.test(l);o||(t=this.util.getNumber(t,0)),s||(e=this.util.isNumber(e)?e+n.sizeUnit:e||""),t=t?t+n.sizeUnit:"",o||(n._element.style.width=t),s||(n._cover.style.paddingBottom=n._cover.style.height=e),!o&&!/%$/.test(t)&&(n._cover.style.width=t,n._container.style.width=""),!s&&!/%$/.test(e)?n._element.style.height=e:n._element.style.height="",i||n._element.removeAttribute("data-percentage"),this.plugins.resizing._module_saveCurrentSize.call(this,n)},setAutoSize:function(){this.plugins.video.setPercentSize.call(this,100,this.context.video._defaultRatio)},setOriginSize:function(t){const e=this.context.video;e._element.removeAttribute("data-percentage"),this.plugins.resizing.resetTransform.call(this,e._element),this.plugins.video.cancelPercentAttr.call(this);const i=((t?e._element.getAttribute("data-size"):"")||e._element.getAttribute("data-origin")||"").split(",");if(i){const l=i[0],n=i[1];e._onlyPercentage||/%$/.test(l)&&(/%$/.test(n)||!/\d/.test(n))?this.plugins.video.setPercentSize.call(this,l,n):this.plugins.video.setSize.call(this,l,n),this.plugins.resizing._module_saveCurrentSize.call(this,e)}},setPercentSize:function(t,e){const i=this.context.video;e=e&&!/%$/.test(e)&&!this.util.getNumber(e,0)?this.util.isNumber(e)?e+"%":e:this.util.isNumber(e)?e+i.sizeUnit:e||i._defaultRatio,i._container.style.width=this.util.isNumber(t)?t+"%":t,i._container.style.height="",i._cover.style.width="100%",i._cover.style.height=e,i._cover.style.paddingBottom=e,i._element.style.width="100%",i._element.style.height="100%",i._element.style.maxWidth="",i._align==="center"&&this.plugins.video.setAlign.call(this,null,null,null,null),i._element.setAttribute("data-percentage",t+","+e),this.plugins.resizing._module_saveCurrentSize.call(this,i)},cancelPercentAttr:function(){const t=this.context.video;t._cover.style.width="",t._cover.style.height="",t._cover.style.paddingBottom="",t._container.style.width="",t._container.style.height="",this.util.removeClass(t._container,this.context.video._floatClassRegExp),this.util.addClass(t._container,"__se__float-"+t._align),t._align==="center"&&this.plugins.video.setAlign.call(this,null,null,null,null)},setAlign:function(t,e,i,l){const n=this.context.video;t||(t=n._align),e||(e=n._element),i||(i=n._cover),l||(l=n._container),/%$/.test(e.style.width)&&t==="center"?(l.style.minWidth="100%",i.style.width=l.style.width,i.style.height=i.style.height,i.style.paddingBottom=/%$/.test(i.style.height)?this.util.getNumber(this.util.getNumber(i.style.height,2)/100*this.util.getNumber(i.style.width,2),2)+"%":i.style.height):(l.style.minWidth="",i.style.width=this.context.resizing._rotateVertical?e.style.height||e.offsetHeight:e.style.width||"100%",i.style.paddingBottom=i.style.height),this.util.hasClass(l,"__se__float-"+t)||(this.util.removeClass(l,n._floatClassRegExp),this.util.addClass(l,"__se__float-"+t)),e.setAttribute("data-align",t)},init:function(){const t=this.context.video;t.videoInputFile&&(t.videoInputFile.value=""),t.videoUrlFile&&(t._linkValue=t.preview.textContent=t.videoUrlFile.value=""),t.videoInputFile&&t.videoUrlFile&&(t.videoUrlFile.removeAttribute("disabled"),t.preview.style.textDecoration=""),t._origin_w=this.options.videoWidth,t._origin_h=this.options.videoHeight,t.modal.querySelector('input[name="suneditor_video_radio"][value="none"]').checked=!0,t._resizing&&(t.inputX.value=this.options.videoWidth===t._defaultSizeX?"":this.options.videoWidth,t.inputY.value=this.options.videoHeight===t._defaultSizeY?"":this.options.videoHeight,t.proportion.checked=!0,t.proportion.disabled=!0,this.plugins.video.setVideoRatioSelect.call(this,t._defaultRatio))}},It={name:"audio",display:"dialog",add:function(t){t.addModule([X,Q,J]);const e=t.context,i=e.audio={_infoList:[],_infoIndex:0,_uploadFileLength:0,focusElement:null,targetSelect:null,_origin_w:t.options.audioWidth,_origin_h:t.options.audioHeight,_linkValue:"",_element:null,_cover:null,_container:null};let l=this.setDialog(t);i.modal=l,i.audioInputFile=l.querySelector("._se_audio_files"),i.audioUrlFile=l.querySelector(".se-input-url"),i.focusElement=i.audioInputFile||i.audioUrlFile,i.preview=l.querySelector(".se-link-preview");let n=this.setController(t);i.controller=n,l.querySelector("form").addEventListener("submit",this.submit.bind(t)),i.audioInputFile&&l.querySelector(".se-dialog-files-edge-button").addEventListener("click",this._removeSelectedFiles.bind(i.audioInputFile,i.audioUrlFile,i.preview)),i.audioInputFile&&i.audioUrlFile&&i.audioInputFile.addEventListener("change",this._fileInputChange.bind(i)),n.addEventListener("click",this.onClick_controller.bind(t)),i.audioUrlFile&&i.audioUrlFile.addEventListener("input",this._onLinkPreview.bind(i.preview,i,t.options.linkProtocol)),e.dialog.modal.appendChild(l),e.element.relative.appendChild(n),l=null,n=null},setDialog:function(t){const e=t.options,i=t.lang,l=t.util.createElement("DIV");l.className="se-dialog-content",l.style.display="none";let n='<form method="post" enctype="multipart/form-data"><div class="se-dialog-header"><button type="button" data-command="close" class="se-btn se-dialog-close" title="'+i.dialogBox.close+'" aria-label="'+i.dialogBox.close+'">'+t.icons.cancel+'</button><span class="se-modal-title">'+i.dialogBox.audioBox.title+'</span></div><div class="se-dialog-body">';return e.audioFileInput&&(n+='<div class="se-dialog-form"><label>'+i.dialogBox.audioBox.file+'</label><div class="se-dialog-form-files"><input class="se-input-form _se_audio_files" type="file" accept="'+e.audioAccept+'"'+(e.audioMultipleFile?' multiple="multiple"':"")+'/><button type="button" data-command="filesRemove" class="se-btn se-dialog-files-edge-button se-file-remove" title="'+i.controller.remove+'" aria-label="'+i.controller.remove+'">'+t.icons.cancel+"</button></div></div>"),e.audioUrlInput&&(n+='<div class="se-dialog-form"><label>'+i.dialogBox.audioBox.url+'</label><input class="se-input-form se-input-url" type="text" /><pre class="se-link-preview"></pre></div>'),n+='</div><div class="se-dialog-footer"><button type="submit" class="se-btn-primary" title="'+i.dialogBox.submitButton+'" aria-label="'+i.dialogBox.submitButton+'"><span>'+i.dialogBox.submitButton+"</span></button></div></form>",l.innerHTML=n,l},setController:function(t){const e=t.lang,i=t.icons,l=t.util.createElement("DIV");return l.className="se-controller se-controller-link",l.innerHTML='<div class="se-arrow se-arrow-up"></div><div class="link-content"><div class="se-btn-group"><button type="button" data-command="update" tabindex="-1" class="se-tooltip">'+i.edit+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+e.controller.edit+'</span></span></button><button type="button" data-command="delete" tabindex="-1" class="se-tooltip">'+i.delete+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+e.controller.remove+"</span></span></button></div></div>",l},_fileInputChange:function(){this.audioInputFile.value?(this.audioUrlFile.setAttribute("disabled",!0),this.preview.style.textDecoration="line-through"):(this.audioUrlFile.removeAttribute("disabled"),this.preview.style.textDecoration="")},_removeSelectedFiles:function(t,e){this.value="",t&&(t.removeAttribute("disabled"),e.style.textDecoration="")},_createAudioTag:function(){const t=this.util.createElement("AUDIO");this.plugins.audio._setTagAttrs.call(this,t);const e=this.context.audio._origin_w,i=this.context.audio._origin_h;return t.setAttribute("origin-size",e+","+i),t.style.cssText=(e?"width:"+e+"; ":"")+(i?"height:"+i+";":""),t},_setTagAttrs:function(t){t.setAttribute("controls",!0);const e=this.options.audioTagAttrs;if(e)for(let i in e)this.util.hasOwn(e,i)&&t.setAttribute(i,e[i])},_onLinkPreview:function(t,e,i){const l=i.target.value.trim();t._linkValue=this.textContent=l?e&&l.indexOf("://")===-1&&l.indexOf("#")!==0?e+l:l.indexOf("://")===-1?"/"+l:l:""},fileTags:["audio"],select:function(t){this.plugins.audio.onModifyMode.call(this,t)},destroy:function(t){t=t||this.context.audio._element;const e=this.util.getParentElement(t,this.util.isComponent)||t,i=t.getAttribute("data-index")*1;if(typeof this.functions.onAudioDeleteBefore=="function"&&this.functions.onAudioDeleteBefore(t,e,i,this)===!1)return;const l=e.previousElementSibling||e.nextElementSibling,n=e.parentNode;this.util.removeItem(e),this.plugins.audio.init.call(this),this.controllersOff(),n!==this.context.element.wysiwyg&&this.util.removeItemAllParents(n,function(s){return s.childNodes.length===0},null),this.focusEdge(l),this.plugins.fileManager.deleteInfo.call(this,"audio",i,this.functions.onAudioUpload),this.history.push(!1)},checkFileInfo:function(){this.plugins.fileManager.checkInfo.call(this,"audio",["audio"],this.functions.onAudioUpload,this.plugins.audio.updateCover.bind(this),!1)},resetFileInfo:function(){this.plugins.fileManager.resetInfo.call(this,"audio",this.functions.onAudioUpload)},on:function(t){const e=this.context.audio;t?e._element?(this.context.dialog.updateModal=!0,e._linkValue=e.preview.textContent=e.audioUrlFile.value=e._element.src,e.audioInputFile&&this.options.audioMultipleFile&&e.audioInputFile.removeAttribute("multiple")):e.audioInputFile&&this.options.audioMultipleFile&&e.audioInputFile.removeAttribute("multiple"):(this.plugins.audio.init.call(this),e.audioInputFile&&this.options.audioMultipleFile&&e.audioInputFile.setAttribute("multiple","multiple"))},open:function(){this.plugins.dialog.open.call(this,"audio",this.currentControllerName==="audio")},submit:function(t){const e=this.context.audio;t.preventDefault(),t.stopPropagation();try{e.audioInputFile&&e.audioInputFile.files.length>0?(this.showLoading(),this.plugins.audio.submitAction.call(this,e.audioInputFile.files)):e.audioUrlFile&&e._linkValue.length>0&&(this.showLoading(),this.plugins.audio.setupUrl.call(this,e._linkValue))}catch(i){throw this.closeLoading(),Error('[SUNEDITOR.audio.submit.fail] cause : "'+i.message+'"')}finally{this.plugins.dialog.close.call(this)}return!1},submitAction:function(t){if(t.length===0)return;let e=0,i=[];for(let o=0,a=t.length;o<a;o++)/audio/i.test(t[o].type)&&(i.push(t[o]),e+=t[o].size);const l=this.options.audioUploadSizeLimit;if(l>0){let o=0;const a=this.context.audio._infoList;for(let r=0,c=a.length;r<c;r++)o+=a[r].size*1;if(e+o>l){this.closeLoading();const r="[SUNEDITOR.audioUpload.fail] Size of uploadable total audios: "+l/1e3+"KB";(typeof this.functions.onAudioUploadError!="function"||this.functions.onAudioUploadError(r,{limitSize:l,currentSize:o,uploadSize:e},this))&&this.functions.noticeOpen(r);return}}const n=this.context.audio;n._uploadFileLength=i.length;const s={isUpdate:this.context.dialog.updateModal,element:n._element};if(typeof this.functions.onAudioUploadBefore=="function"){const o=this.functions.onAudioUploadBefore(i,s,this,(function(a){a&&this._w.Array.isArray(a.result)?this.plugins.audio.register.call(this,s,a):this.plugins.audio.upload.call(this,s,a)}).bind(this));if(typeof o>"u")return;if(!o){this.closeLoading();return}typeof o=="object"&&o.length>0&&(i=o)}this.plugins.audio.upload.call(this,s,i)},error:function(t,e){if(this.closeLoading(),typeof this.functions.onAudioUploadError!="function"||this.functions.onAudioUploadError(t,e,this))throw this.functions.noticeOpen(t),Error("[SUNEDITOR.plugin.audio.exception] response: "+t)},upload:function(t,e){if(!e){this.closeLoading();return}if(typeof e=="string"){this.plugins.audio.error.call(this,e,null);return}const i=this.options.audioUploadUrl,l=this.context.dialog.updateModal?1:e.length,n=new FormData;for(let s=0;s<l;s++)n.append("file-"+s,e[s]);this.plugins.fileManager.upload.call(this,i,this.options.audioUploadHeader,n,this.plugins.audio.callBack_upload.bind(this,t),this.functions.onAudioUploadError)},callBack_upload:function(t,e){if(typeof this.functions.audioUploadHandler=="function")this.functions.audioUploadHandler(e,t,this);else{const i=JSON.parse(e.responseText);i.errorMessage?this.plugins.audio.error.call(this,i.errorMessage,i):this.plugins.audio.register.call(this,t,i)}},register:function(t,e){const i=e.result;for(let l=0,n=i.length,s,o;l<n;l++)t.isUpdate?o=t.element:o=this.plugins.audio._createAudioTag.call(this),s={name:i[l].name,size:i[l].size},this.plugins.audio.create_audio.call(this,o,i[l].url,s,t.isUpdate);this.closeLoading()},setupUrl:function(t){try{if(t.length===0)return!1;this.plugins.audio.create_audio.call(this,this.plugins.audio._createAudioTag.call(this),t,null,this.context.dialog.updateModal)}catch(e){throw Error('[SUNEDITOR.audio.audio.fail] cause : "'+e.message+'"')}finally{this.closeLoading()}},create_audio:function(t,e,i,l){const n=this.context.audio;if(l)if(n._element&&(t=n._element),t&&t.src!==e)t.src=e,this.selectComponent(t,"audio");else{this.selectComponent(t,"audio");return}else{t.src=e;const s=this.plugins.component.set_cover.call(this,t),o=this.plugins.component.set_container.call(this,s,"");if(!this.insertComponent(o,!1,!0,!this.options.mediaAutoSelect)){this.focus();return}if(!this.options.mediaAutoSelect){const a=this.appendFormatTag(o,null);a&&this.setRange(a,0,a,0)}}this.plugins.fileManager.setInfo.call(this,"audio",t,this.functions.onAudioUpload,i,!1),l&&this.history.push(!1)},updateCover:function(t){const e=this.context.audio;this.plugins.audio._setTagAttrs.call(this,t);let i=this.util.isRangeFormatElement(t.parentNode)||this.util.isWysiwygDiv(t.parentNode)?t:this.util.getFormatElement(t)||t;const l=t;e._element=t=t.cloneNode(!1);const n=this.plugins.component.set_cover.call(this,t),s=this.plugins.component.set_container.call(this,n,"se-audio-container");try{if(this.util.getParentElement(l,this.util.isNotCheckingNode))l.parentNode.replaceChild(s,l);else if(this.util.isListCell(i)){const o=this.util.getParentElement(l,function(a){return a.parentNode===i});i.insertBefore(s,o),this.util.removeItem(l),this.util.removeEmptyNode(o,null,!0)}else if(this.util.isFormatElement(i)){const o=this.util.getParentElement(l,function(a){return a.parentNode===i});i=this.util.splitElement(i,o),i.parentNode.insertBefore(s,i),this.util.removeItem(l),this.util.removeEmptyNode(i,null,!0),i.children.length===0&&(i.innerHTML=this.util.htmlRemoveWhiteSpace(i.innerHTML))}else i.parentNode.replaceChild(s,i)}catch(o){console.warn("[SUNEDITOR.audio.error] Maybe the audio tag is nested.",o)}this.plugins.fileManager.setInfo.call(this,"audio",t,this.functions.onAudioUpload,null,!1),this.plugins.audio.init.call(this)},onModifyMode:function(t){const e=this.context.audio;this.setControllerPosition(e.controller,t,"bottom",{left:0,top:0}),this.controllersOn(e.controller,t,this.plugins.audio.onControllerOff.bind(this,t),"audio"),this.util.addClass(t,"active"),e._element=t,e._cover=this.util.getParentElement(t,"FIGURE"),e._container=this.util.getParentElement(t,this.util.isComponent)},openModify:function(t){if(this.context.audio.audioUrlFile){const e=this.context.audio;e._linkValue=e.preview.textContent=e.audioUrlFile.value=e._element.src}t||this.plugins.dialog.open.call(this,"audio",!0)},onClick_controller:function(t){t.stopPropagation();const e=t.target.getAttribute("data-command");e&&(t.preventDefault(),/update/.test(e)?this.plugins.audio.openModify.call(this,!1):this.plugins.audio.destroy.call(this,this.context.audio._element),this.controllersOff())},onControllerOff:function(t){this.util.removeClass(t,"active"),this.context.audio.controller.style.display="none"},init:function(){if(this.context.dialog.updateModal)return;const t=this.context.audio;t.audioInputFile&&(t.audioInputFile.value=""),t.audioUrlFile&&(t._linkValue=t.preview.textContent=t.audioUrlFile.value=""),t.audioInputFile&&t.audioUrlFile&&(t.audioUrlFile.removeAttribute("disabled"),t.preview.style.textDecoration=""),t._element=null}},et="https://katex.org/docs/supported.html",Bt={name:"math",display:"dialog",add:function(t){t.addModule([X]);const e=t.context;e.math={focusElement:null,previewElement:null,fontSizeElement:null,defaultFontSize:"",_mathExp:null};let i=this.setDialog(t);e.math.modal=i,e.math.focusElement=i.querySelector(".se-math-exp"),e.math.previewElement=i.querySelector(".se-math-preview"),e.math.fontSizeElement=i.querySelector(".se-math-size"),e.math.focusElement.addEventListener("paste",function(n){typeof t.functions.onPasteMath=="function"&&t.functions.onPasteMath(n,t)},!1),e.math.focusElement.addEventListener(t.util.isIE?"textinput":"input",this._renderMathExp.bind(t,e.math),!1),e.math.fontSizeElement.addEventListener("change",(function(n){this.fontSize=n.target.value}).bind(e.math.previewElement.style),!1);let l=this.setController_MathButton(t);e.math.mathController=l,e.math._mathExp=null,i.querySelector("form").addEventListener("submit",this.submit.bind(t),!1),l.addEventListener("click",this.onClick_mathController.bind(t)),e.math.previewElement.style.fontSize=e.math.defaultFontSize,e.dialog.modal.appendChild(i),e.element.relative.appendChild(l),i=null,l=null},setDialog:function(t){const e=t.lang,i=t.util.createElement("DIV"),l=t.options.mathFontSize;let n=l[0].value;i.className="se-dialog-content",i.style.display="none";let s='<form><div class="se-dialog-header"><button type="button" data-command="close" class="se-btn se-dialog-close" title="'+e.dialogBox.close+'" aria-label="'+e.dialogBox.close+'">'+t.icons.cancel+'</button><span class="se-modal-title">'+e.dialogBox.mathBox.title+'</span></div><div class="se-dialog-body"><div class="se-dialog-form"><label>'+e.dialogBox.mathBox.inputLabel+' (<a href="'+et+'" target="_blank">KaTeX</a>)</label><textarea class="se-input-form se-math-exp" type="text"></textarea></div><div class="se-dialog-form"><label>'+e.dialogBox.mathBox.fontSizeLabel+'</label><select class="se-input-select se-math-size">';for(let o=0,a=l.length,r;o<a;o++)r=l[o],r.default&&(n=r.value),s+='<option value="'+r.value+'"'+(r.default?" selected":"")+">"+r.text+"</option>";return s+='</select></div><div class="se-dialog-form"><label>'+e.dialogBox.mathBox.previewLabel+'</label><p class="se-math-preview"></p></div></div><div class="se-dialog-footer"><button type="submit" class="se-btn-primary" title="'+e.dialogBox.submitButton+'" aria-label="'+e.dialogBox.submitButton+'"><span>'+e.dialogBox.submitButton+"</span></button></div></form>",t.context.math.defaultFontSize=n,i.innerHTML=s,i},setController_MathButton:function(t){const e=t.lang,i=t.util.createElement("DIV");return i.className="se-controller se-controller-link",i.innerHTML='<div class="se-arrow se-arrow-up"></div><div class="link-content"><div class="se-btn-group"><button type="button" data-command="update" tabindex="-1" class="se-btn se-tooltip">'+t.icons.edit+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+e.controller.edit+'</span></span></button><button type="button" data-command="delete" tabindex="-1" class="se-btn se-tooltip">'+t.icons.delete+'<span class="se-tooltip-inner"><span class="se-tooltip-text">'+e.controller.remove+"</span></span></button></div></div>",i},open:function(){this.plugins.dialog.open.call(this,"math",this.currentControllerName==="math")},managedTags:function(){return{className:"katex",method:function(t){if(!t.getAttribute("data-exp")||!this.options.katex)return;const e=this._d.createRange().createContextualFragment(this.plugins.math._renderer.call(this,this.util.HTMLDecoder(t.getAttribute("data-exp"))));t.innerHTML=e.querySelector(".katex").innerHTML,t.setAttribute("contenteditable",!1)}}},_renderer:function(t){let e="";try{this.util.removeClass(this.context.math.focusElement,"se-error"),e=this.options.katex.src.renderToString(t,{throwOnError:!0,displayMode:!0})}catch(i){this.util.addClass(this.context.math.focusElement,"se-error"),e='<span class="se-math-katex-error">Katex syntax error. (Refer <a href="'+et+'" target="_blank">KaTeX</a>)</span>',console.warn("[SUNEDITOR.math.Katex.error] ",i)}return e},_renderMathExp:function(t,e){t.previewElement.innerHTML=this.plugins.math._renderer.call(this,e.target.value)},submit:function(t){this.showLoading(),t.preventDefault(),t.stopPropagation();const e=(function(){if(this.context.math.focusElement.value.trim().length===0)return!1;const i=this.context.math,l=i.focusElement.value,n=i.previewElement.querySelector(".katex");if(!n)return!1;if(n.className="__se__katex "+n.className,n.setAttribute("contenteditable",!1),n.setAttribute("data-exp",this.util.HTMLEncoder(l)),n.setAttribute("data-font-size",i.fontSizeElement.value),n.style.fontSize=i.fontSizeElement.value,this.context.dialog.updateModal){const s=this.util.getParentElement(i._mathExp,".katex");s.parentNode.replaceChild(n,s),this.setRange(n,0,n,1)}else{const s=this.getSelectedElements();if(s.length>1){const a=this.util.createElement(s[0].nodeName);if(a.appendChild(n),!this.insertNode(a,null,!0))return!1}else if(!this.insertNode(n,null,!0))return!1;const o=this.util.createTextNode(this.util.zeroWidthSpace);n.parentNode.insertBefore(o,n.nextSibling),this.setRange(n,0,n,1)}return i.focusElement.value="",i.fontSizeElement.value="1em",i.previewElement.style.fontSize="1em",i.previewElement.innerHTML="",!0}).bind(this);try{e()&&(this.plugins.dialog.close.call(this),this.history.push(!1))}catch{this.plugins.dialog.close.call(this)}finally{this.closeLoading()}return!1},active:function(t){if(!t)this.controllerArray.indexOf(this.context.math.mathController)>-1&&this.controllersOff();else if(t.getAttribute("data-exp"))return this.controllerArray.indexOf(this.context.math.mathController)<0&&(this.setRange(t,0,t,1),this.plugins.math.call_controller.call(this,t)),!0;return!1},on:function(t){if(!t)this.plugins.math.init.call(this);else{const e=this.context.math;if(e._mathExp){const i=this.util.HTMLDecoder(e._mathExp.getAttribute("data-exp")),l=e._mathExp.getAttribute("data-font-size")||"1em";this.context.dialog.updateModal=!0,e.focusElement.value=i,e.fontSizeElement.value=l,e.previewElement.innerHTML=this.plugins.math._renderer.call(this,i),e.previewElement.style.fontSize=l}}},call_controller:function(t){this.context.math._mathExp=t;const e=this.context.math.mathController;this.setControllerPosition(e,t,"bottom",{left:0,top:0}),this.controllersOn(e,t,"math")},onClick_mathController:function(t){t.stopPropagation();const e=t.target.getAttribute("data-command")||t.target.parentNode.getAttribute("data-command");e&&(t.preventDefault(),/update/.test(e)?(this.context.math.focusElement.value=this.util.HTMLDecoder(this.context.math._mathExp.getAttribute("data-exp")),this.plugins.dialog.open.call(this,"math",!0)):(this.util.removeItem(this.context.math._mathExp),this.context.math._mathExp=null,this.focus(),this.history.push(!1)),this.controllersOff())},init:function(){const t=this.context.math;t.mathController.style.display="none",t._mathExp=null,t.focusElement.value="",t.previewElement.innerHTML=""}};var Nt={exports:{}};(function(t){(function(e,i){t.exports=e.document?i(e,!0):function(l){if(!l.document)throw new Error("SUNEDITOR_MODULES a window with a document");return i(l)}})(typeof window<"u"?window:T,function(e,i){const l={name:"fileBrowser",_xmlHttp:null,_loading:null,add:function(n){const s=n.context;s.fileBrowser={_closeSignal:!1,area:null,header:null,tagArea:null,body:null,list:null,tagElements:null,items:[],selectedTags:[],selectorHandler:null,contextPlugin:"",columnSize:4};let o=n.util.createElement("DIV");o.className="se-file-browser sun-editor-common";let a=n.util.createElement("DIV");a.className="se-file-browser-back";let r=n.util.createElement("DIV");r.className="se-file-browser-inner",r.innerHTML=this.set_browser(n),o.appendChild(a),o.appendChild(r),this._loading=o.querySelector(".se-loading-box"),s.fileBrowser.area=o,s.fileBrowser.header=r.querySelector(".se-file-browser-header"),s.fileBrowser.titleArea=r.querySelector(".se-file-browser-title"),s.fileBrowser.tagArea=r.querySelector(".se-file-browser-tags"),s.fileBrowser.body=r.querySelector(".se-file-browser-body"),s.fileBrowser.list=r.querySelector(".se-file-browser-list"),s.fileBrowser.tagArea.addEventListener("click",this.onClickTag.bind(n)),s.fileBrowser.list.addEventListener("click",this.onClickFile.bind(n)),r.addEventListener("mousedown",this._onMouseDown_browser.bind(n)),r.addEventListener("click",this._onClick_browser.bind(n)),s.element.relative.appendChild(o),o=null,a=null,r=null},set_browser:function(n){const s=n.lang;return'<div class="se-file-browser-content"><div class="se-file-browser-header"><button type="button" data-command="close" class="se-btn se-file-browser-close" class="close" title="'+s.dialogBox.close+'" aria-label="'+s.dialogBox.close+'">'+n.icons.cancel+'</button><span class="se-file-browser-title"></span><div class="se-file-browser-tags"></div></div><div class="se-file-browser-body"><div class="se-loading-box sun-editor-common"><div class="se-loading-effect"></div></div><div class="se-file-browser-list"></div></div></div>'},_onMouseDown_browser:function(n){/se-file-browser-inner/.test(n.target.className)?this.context.fileBrowser._closeSignal=!0:this.context.fileBrowser._closeSignal=!1},_onClick_browser:function(n){n.stopPropagation(),(/close/.test(n.target.getAttribute("data-command"))||this.context.fileBrowser._closeSignal)&&this.plugins.fileBrowser.close.call(this)},open:function(n,s){this.plugins.fileBrowser._bindClose&&(this._d.removeEventListener("keydown",this.plugins.fileBrowser._bindClose),this.plugins.fileBrowser._bindClose=null),this.plugins.fileBrowser._bindClose=(function(c){/27/.test(c.keyCode)&&this.plugins.fileBrowser.close.call(this)}).bind(this),this._d.addEventListener("keydown",this.plugins.fileBrowser._bindClose);const o=this.context.fileBrowser;o.contextPlugin=n,o.selectorHandler=s;const a=this.context[n],r=a.listClass;this.util.hasClass(o.list,r)||(o.list.className="se-file-browser-list "+r),this.options.popupDisplay==="full"?o.area.style.position="fixed":o.area.style.position="absolute",o.titleArea.textContent=a.title,o.area.style.display="block",this.plugins.fileBrowser._drawFileList.call(this,this.context[n].url,this.context[n].header)},_bindClose:null,close:function(){const n=this.plugins.fileBrowser;n._xmlHttp&&n._xmlHttp.abort(),n._bindClose&&(this._d.removeEventListener("keydown",n._bindClose),n._bindClose=null);const s=this.context.fileBrowser;s.area.style.display="none",s.selectorHandler=null,s.selectedTags=[],s.items=[],s.list.innerHTML=s.tagArea.innerHTML=s.titleArea.textContent="",typeof this.plugins[s.contextPlugin].init=="function"&&this.plugins[s.contextPlugin].init.call(this),s.contextPlugin=""},showBrowserLoading:function(){this._loading.style.display="block"},closeBrowserLoading:function(){this._loading.style.display="none"},_drawFileList:function(n,s){const o=this.plugins.fileBrowser,a=o._xmlHttp=this.util.getXMLHttpRequest();if(a.onreadystatechange=o._callBackGet.bind(this,a),a.open("get",n,!0),s!==null&&typeof s=="object"&&this._w.Object.keys(s).length>0)for(let r in s)a.setRequestHeader(r,s[r]);a.send(null),this.plugins.fileBrowser.showBrowserLoading()},_callBackGet:function(n){if(n.readyState===4){if(this.plugins.fileBrowser._xmlHttp=null,n.status===200)try{const s=JSON.parse(n.responseText);s.result.length>0?this.plugins.fileBrowser._drawListItem.call(this,s.result,!0):s.nullMessage&&(this.context.fileBrowser.list.innerHTML=s.nullMessage)}catch(s){throw Error('[SUNEDITOR.fileBrowser.drawList.fail] cause : "'+s.message+'"')}finally{this.plugins.fileBrowser.closeBrowserLoading(),this.context.fileBrowser.body.style.maxHeight=this._w.innerHeight-this.context.fileBrowser.header.offsetHeight-50+"px"}else if(this.plugins.fileBrowser.closeBrowserLoading(),n.status!==0){const s=n.responseText?JSON.parse(n.responseText):n,o="[SUNEDITOR.fileBrowser.get.serverException] status: "+n.status+", response: "+(s.errorMessage||n.responseText);throw Error(o)}}},_drawListItem:function(n,s){const o=this.context.fileBrowser,a=this.context[o.contextPlugin],r=[],c=n.length,u=a.columnSize||o.columnSize,f=u<=1?1:Math.round(c/u)||1,p=a.itemTemplateHandler;let m="",d='<div class="se-file-item-column">',_=1;for(let g=0,h,b;g<c;g++)if(h=n[g],b=h.tag?typeof h.tag=="string"?h.tag.split(","):h.tag:[],b=h.tag=b.map(function(v){return v.trim()}),d+=p(h),(g+1)%f===0&&_<u&&g+1<c&&(_++,d+='</div><div class="se-file-item-column">'),s&&b.length>0)for(let v=0,x=b.length,y;v<x;v++)y=b[v],y&&r.indexOf(y)===-1&&(r.push(y),m+='<a title="'+y+'" aria-label="'+y+'">'+y+"</a>");d+="</div>",o.list.innerHTML=d,s&&(o.items=n,o.tagArea.innerHTML=m,o.tagElements=o.tagArea.querySelectorAll("A"))},onClickTag:function(n){const s=n.target;if(!this.util.isAnchor(s))return;const o=s.textContent,a=this.plugins.fileBrowser,r=this.context.fileBrowser,c=r.tagArea.querySelector('a[title="'+o+'"]'),u=r.selectedTags,f=u.indexOf(o);f>-1?(u.splice(f,1),this.util.removeClass(c,"on")):(u.push(o),this.util.addClass(c,"on")),a._drawListItem.call(this,u.length===0?r.items:r.items.filter(function(p){return p.tag.some(function(m){return u.indexOf(m)>-1})}),!1)},onClickFile:function(n){n.preventDefault(),n.stopPropagation();const s=this.context.fileBrowser,o=s.list;let a=n.target,r=null;if(a===o)return;for(;o!==a.parentNode&&(r=a.getAttribute("data-command"),!r);)a=a.parentNode;if(!r)return;(s.selectorHandler||this.context[s.contextPlugin].selectorHandler)(a,a.parentNode.querySelector(".__se__img_name").textContent),this.plugins.fileBrowser.close.call(this)}};return typeof i>"u"&&(e.SUNEDITOR_MODULES||Object.defineProperty(e,"SUNEDITOR_MODULES",{enumerable:!0,writable:!1,configurable:!1,value:{}}),Object.defineProperty(e.SUNEDITOR_MODULES,"fileBrowser",{enumerable:!0,writable:!1,configurable:!1,value:l})),l})})(Nt);var Ht=Nt.exports;const qt=Y(Ht),Mt={name:"imageGallery",add:function(t){t.addModule([qt]);const e=t.context;e.imageGallery={title:t.lang.toolbar.imageGallery,url:t.options.imageGalleryUrl,header:t.options.imageGalleryHeader,listClass:"se-image-list",itemTemplateHandler:this.drawItems,selectorHandler:this.setImage.bind(t),columnSize:4}},open:function(t){this.plugins.fileBrowser.open.call(this,"imageGallery",t)},drawItems:function(t){const e=t.src.split("/").pop();return'<div class="se-file-item-img"><img src="'+(t.thumbnail||t.src)+'" alt="'+(t.alt||e)+'" data-command="pick" data-value="'+(t.src||t.thumbnail)+'"><div class="se-file-img-name se-file-name-back"></div><div class="se-file-img-name __se__img_name">'+(t.name||e)+"</div></div>"},setImage:function(t,e){this.callPlugin("image",(function(){const i={name:e,size:0};this.plugins.image.create_image.call(this,t.getAttribute("data-value"),null,this.context.image._origin_w,this.context.image._origin_h,"none",i,t.alt)}).bind(this),null)}},Vt={blockquote:st,align:ot,font:at,fontSize:rt,fontColor:ut,hiliteColor:dt,horizontalRule:ht,list:ft,table:pt,formatBlock:gt,lineHeight:mt,template:_t,paragraphStyle:bt,textStyle:vt,link:wt,image:zt,video:Lt,audio:It,math:Bt,imageGallery:Mt},Wt=Object.freeze(Object.defineProperty({__proto__:null,align:ot,audio:It,blockquote:st,default:Vt,font:at,fontColor:ut,fontSize:rt,formatBlock:gt,hiliteColor:dt,horizontalRule:ht,image:zt,imageGallery:Mt,lineHeight:mt,link:wt,list:ft,math:Bt,paragraphStyle:bt,table:pt,template:_t,textStyle:vt,video:Lt},Symbol.toStringTag,{value:"Module"})),Yt=nt(Wt),Xt=nt(At);var Z={};Object.defineProperty(Z,"__esModule",{value:!0});var $t=function(t){switch(typeof t){case"object":return t;case"string":return Dt("suneditor/src/lang/".concat(t,".js"));default:return}};Z.default=$t;var P={};Object.defineProperty(P,"__esModule",{value:!0});P.uploadBeforeEvents=P.events=void 0;P.events=["onMouseDown","onScroll","onInput","onClick","onKeyUp","onKeyDown","onFocus","onImageUpload","onAudioUpload","onVideoUpload","onImageUploadError","onVideoUploadError","onAudioUploadError","onSave","onSetToolbarButtons","imageUploadHandler","toggleCodeView","toggleFullScreen","showInline","showController","onCopy","onCut","onDrop","onPaste"];P.uploadBeforeEvents=["onImageUploadBefore","onVideoUploadBefore","onAudioUploadBefore"];var W=T&&T.__assign||function(){return W=Object.assign||function(t){for(var e,i=1,l=arguments.length;i<l;i++){e=arguments[i];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])}return t},W.apply(this,arguments)},jt=T&&T.__createBinding||(Object.create?function(t,e,i,l){l===void 0&&(l=i);var n=Object.getOwnPropertyDescriptor(e,i);(!n||("get"in n?!e.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return e[i]}}),Object.defineProperty(t,l,n)}:function(t,e,i,l){l===void 0&&(l=i),t[l]=e[i]}),Gt=T&&T.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),Kt=T&&T.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var i in t)i!=="default"&&Object.prototype.hasOwnProperty.call(t,i)&&jt(e,t,i);return Gt(e,t),e},tt=T&&T.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(K,"__esModule",{value:!0});var U=Kt(Tt),Qt=tt(Yt),Jt=tt(Xt),it=tt(Z),lt=P,Zt=function(t){var e=t.name,i=t.lang,l=t.setOptions,n=l===void 0?{}:l,s=t.placeholder,o=t.width,a=o===void 0?"100%":o,r=t.height,c=t.defaultValue,u=t.setContents,f=t.setDefaultStyle,p=t.getSunEditorInstance,m=t.appendContents,d=t.setAllPlugins,_=d===void 0?!0:d,g=t.disable,h=g===void 0?!1:g,b=t.readOnly,v=b===void 0?!1:b,x=t.hide,y=x===void 0?!1:x,w=t.hideToolbar,C=w===void 0?!1:w,B=t.disableToolbar,z=B===void 0?!1:B,L=t.onChange,N=t.autoFocus,E=t.onBlur,S=t.onLoad,M=(0,U.useRef)(null),k=(0,U.useRef)(null),O=(0,U.useRef)(!0);return(0,U.useEffect)(function(){var I,A=W(W({},n),{lang:i?(0,it.default)(i):n.lang,width:a??n.width,placeholder:s??n.placeholder,plugins:(I=n.plugins)!==null&&I!==void 0?I:_?Qt.default:void 0,height:r??n.height,value:c??n.value,defaultStyle:f??n.defaultStyle});return e&&A.value&&(M.current.value=A.value),k.current=Jt.default.create(M.current,A),p&&p(k.current),k.current.onload=function(D,R){return R||(u&&(k.current.setContents(u),k.current.core.focusEdge(null)),m&&k.current.appendContents(m),k.current.util.isIE&&k.current.core._createDefaultRange(),h&&k.current.disable(),v&&k.current.readOnly(!0),y&&k.current.hide(),C&&k.current.toolbar.hide(),z&&k.current.toolbar.disable(),N===!1?k.current.core.context.element.wysiwyg.blur():N&&k.current.core.context.element.wysiwyg.focus()),S==null?void 0:S(R)},k.current.onChange=function(D){e&&M.current&&(M.current.value=D),L&&L(D)},E&&(k.current.onBlur=function(D){return E(D,k.current.getContents(!0))}),lt.uploadBeforeEvents.forEach(function(D){var R=t[D];k.current&&R&&(k.current[D]=function(H,q,$,V){return R(H,q,V)})}),lt.events.forEach(function(D){var R=t[D];R&&k.current&&(k.current[D]=R)}),function(){k.current&&k.current.destroy(),k.current=null}},[]),(0,U.useEffect)(function(){var I;O.current||(I=k.current)===null||I===void 0||I.setOptions({lang:(0,it.default)(i)})},[i]),(0,U.useEffect)(function(){var I;O.current||(I=k.current)===null||I===void 0||I.setOptions({placeholder:s,height:r,width:a})},[s,r,a]),(0,U.useEffect)(function(){var I;f&&!O.current&&((I=k.current)===null||I===void 0||I.setDefaultStyle(f))},[f]),(0,U.useEffect)(function(){var I,A;!O.current&&u!==void 0&&!(!((I=k.current)===null||I===void 0)&&I.core.hasFocus)&&((A=k.current)===null||A===void 0||A.setContents(u))},[u]),(0,U.useEffect)(function(){var I,A,D;!O.current&&m!==void 0&&!(!((I=k.current)===null||I===void 0)&&I.core.hasFocus)&&((A=k.current)===null||A===void 0||A.appendContents(m),(D=k.current)===null||D===void 0||D.core.focusEdge(null))},[m]),(0,U.useEffect)(function(){var I,A,D,R,H,q,$,V,G;O.current||((I=k.current)===null||I===void 0||I.readOnly(v),C?(A=k.current)===null||A===void 0||A.toolbar.hide():(D=k.current)===null||D===void 0||D.toolbar.show(),z?(R=k.current)===null||R===void 0||R.toolbar.disable():(H=k.current)===null||H===void 0||H.toolbar.enable(),h?(q=k.current)===null||q===void 0||q.disable():($=k.current)===null||$===void 0||$.enable(),y?(V=k.current)===null||V===void 0||V.hide():(G=k.current)===null||G===void 0||G.show())},[h,C,z,y,v]),(0,U.useEffect)(function(){O.current=!1},[]),U.default.createElement("textarea",W({style:{visibility:"hidden"},ref:M},{name:e}))};K.default=Zt;var F={};Object.defineProperty(F,"__esModule",{value:!0});F.formatting=F.complex=F.basic=void 0;F.basic=[["font","fontSize"],["fontColor"],["horizontalRule"],["link","image"]];F.complex=[["undo","redo"],["font","fontSize","formatBlock"],["bold","underline","italic","strike","subscript","superscript"],["removeFormat"],"/",["fontColor","hiliteColor"],["outdent","indent"],["align","horizontalRule","list","table"],["link","image","video"],["fullScreen","showBlocks","codeView"],["preview","print"],["save","template"]];F.formatting=[["undo","redo"],["bold","underline","italic","strike","subscript","superscript"],["removeFormat"],["outdent","indent"],["fullScreen","showBlocks","codeView"],["preview","print"]];var te=T&&T.__createBinding||(Object.create?function(t,e,i,l){l===void 0&&(l=i);var n=Object.getOwnPropertyDescriptor(e,i);(!n||("get"in n?!e.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return e[i]}}),Object.defineProperty(t,l,n)}:function(t,e,i,l){l===void 0&&(l=i),t[l]=e[i]}),ee=T&&T.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),ie=T&&T.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var i in t)i!=="default"&&Object.prototype.hasOwnProperty.call(t,i)&&te(e,t,i);return ee(e,t),e},le=T&&T.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(j,"__esModule",{value:!0});var ne=j.buttonList=void 0,se=le(K);ne=j.buttonList=ie(F);var ce=j.default=se.default;export{ce as _,ne as b};
