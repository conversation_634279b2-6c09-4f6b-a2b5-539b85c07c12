import React from 'react';

const AddVoiceover = ({ handleAddVoiceoverBtnClick }) => {
  return (
    <div
      className='flex h-40 w-1/3 cursor-pointer items-center justify-center rounded-md border border-orange-500 bg-orange-600 px-2 py-1.5 shadow hover:bg-orange-700'
      onClick={handleAddVoiceoverBtnClick}
    >
      <p className='text-base font-semibold text-white'>Add Voiceover</p>
    </div>
  );
};

export default AddVoiceover;
