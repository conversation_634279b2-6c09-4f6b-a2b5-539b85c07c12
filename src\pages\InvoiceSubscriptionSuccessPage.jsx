import React, { useState, useEffect } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import {
  GlobalContext,
  showToast,
  refreshSubscriptionData,
} from "Src/globalContext";
import { AuthContext } from "Src/authContext";
import { CheckCircle } from "lucide-react";

const InvoiceSubscriptionSuccessPage = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { state: authState } = React.useContext(AuthContext);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const invoiceId = searchParams.get("invoice_id");

  useEffect(() => {
    const handleSubscriptionSuccess = async () => {
      try {
        // Check if we have an invoice_id parameter
        if (invoiceId) {
          showToast(
            globalDispatch,
            "Subscription activated successfully! Payment has been processed.",
            5000,
            "success"
          );

          // Force refresh subscription data to update the UI
          setRefreshing(true);
          const refreshSuccess = await refreshSubscriptionData(globalDispatch);

          if (refreshSuccess) {
            showToast(
              globalDispatch,
              "Subscription data updated successfully!",
              3000,
              "success"
            );
          }
        } else {
          // If no invoice_id, redirect to member login
          navigate("/member/login");
        }
      } catch (error) {
        console.error("Error handling subscription success:", error);
        showToast(
          globalDispatch,
          "Subscription activated but there was an issue updating your account. Please refresh the page.",
          5000,
          "warning"
        );
      } finally {
        setLoading(false);
        setRefreshing(false);
      }
    };

    handleSubscriptionSuccess();
  }, [invoiceId, navigate, globalDispatch]);

  const handleBackToSubscription = () => {
    // Check if user is authenticated and redirect based on role
    if (!authState.isAuthenticated) {
      navigate("/login");
      return;
    }

    // Redirect based on user role
    if (authState.role === "manager") {
      navigate("/manager/projects");
    } else if (authState.role === "member") {
      navigate("/member/subscription");
    } else {
      navigate("/login");
    }
  };

  const handleViewDashboard = () => {
    // Check if user is authenticated and redirect based on role
    if (!authState.isAuthenticated) {
      navigate("/login");
      return;
    }

    // Redirect to dashboard based on user role
    if (authState.role === "manager") {
      navigate("/manager/projects");
    } else if (authState.role === "member") {
      navigate("/member/dashboard");
    } else {
      navigate("/login");
    }
  };

  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="h-16 w-16 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <div className="flex h-screen flex-col items-center justify-center">
        <div className="text-center">
          <div className="mb-4 flex justify-center">
            <div className="rounded-full bg-success/20 p-4">
              <CheckCircle className="h-16 w-16 text-success" />
            </div>
          </div>
          <h1 className="mb-4 text-4xl font-bold text-success">
            Subscription Activated Successfully!
          </h1>
          <p className="mb-6 text-lg text-bodydark">
            Your subscription has been activated and payment has been processed
            successfully. You now have access to all the features included in
            your plan.
          </p>

          {refreshing && (
            <div className="mb-6 flex items-center justify-center gap-2 text-primary">
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
              <span>Updating your account...</span>
            </div>
          )}

          {invoiceId && (
            <div className="mb-6 rounded-lg border border-stroke bg-meta-4/20 p-4">
              <p className="text-sm text-bodydark">
                <span className="font-semibold">Transaction ID:</span>{" "}
                <span className="font-mono text-white">{invoiceId}</span>
              </p>
            </div>
          )}

          <div className="mb-6 rounded-lg border border-stroke bg-meta-4/20 p-6">
            <h3 className="mb-4 text-lg font-semibold text-white">
              What happens next?
            </h3>
            <ul className="space-y-2 text-left text-bodydark">
              <li className="flex items-start">
                <span className="mr-2 text-success">•</span>
                Your Invoice Addon subscription is now active and ready to use
              </li>
              <li className="flex items-start">
                <span className="mr-2 text-success">•</span>
                Access all add on features included in your plan
              </li>
            </ul>
          </div>

          <div className="flex justify-center gap-4">
            <button
              onClick={handleViewDashboard}
              className="rounded bg-primary px-6 py-3 text-white hover:bg-opacity-90"
            >
              Go to Dashboard
            </button>
            <button
              onClick={handleBackToSubscription}
              className="rounded border border-stroke px-6 py-3 text-white hover:bg-meta-4"
            >
              Manage Subscription
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InvoiceSubscriptionSuccessPage;
