import MkdSDK from '../utils/MkdSDK';

let sdk = new MkdSDK();

export const getAllSurveyAPI = async () => {
  try {
    const uri = '/v3/api/custom/equality_record/survey';
    const res = await sdk.callRawAPI(uri, [], 'GET');
    return res;
  } catch (error) {}
};

export const getSurveyDetailsAPI = async (id) => {
  try {
    const uri = `/v3/api/custom/equality_record/survey/view/${id}`;
    const res = await sdk.callRawAPI(uri, [], 'GET');
    return res;
  } catch (error) {}
};

export const createSurveyAPI = async (payload) => {
  try {
    const uri = '/v3/api/custom/equality_record/survey';
    const res = await sdk.callRawAPI(uri, payload, 'POST');
    return res;
  } catch (error) {}
};

export const updateSurveyAPI = async (payload) => {
  try {
    const uri = '/v3/api/custom/equality_record/survey/update';
    const res = await sdk.callRawAPI(uri, payload, 'PUT');
    return res;
  } catch (error) {}
};

export const updateSurveyClientAPI = async (payload) => {
  try {
    const uri = '/v3/api/custom/equality_record/survey/update_from_client';
    const res = await sdk.callRawAPI(uri, payload, 'PUT');
    return res;
  } catch (error) {}
};

export const updateSurveyThemeOfTheRoutineAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/survey/update/theme/${payload.id}`;
    const res = await sdk.callRawAPI(uri, payload, 'PUT');
    return res;
  } catch (error) {}
};

export const updateSurveyNotificationAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/survey/notification/${payload.id}`;
    const res = await sdk.callRawAPI(uri, payload, 'PUT');
    return res;
  } catch (error) {}
};

export const getSurveyDetails = async (payload) => {
  try {
    const uri = '/v3/api/custom/equality_record/survey/details';
    const res = await sdk.callRawAPI(uri, payload, 'POST');
    return res;
  } catch (error) {}
};

export const getSurveyEmailTemplateAPI = async () => {
  try {
    const uri = '/v3/api/custom/equality_record/survey/email_template';
    const res = await sdk.callRawAPI(uri, [], 'GET');
    return res;
  } catch (error) {}
};

export const updateSurveyEmailTemplateAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/survey/email_template/${payload.id}`;
    const res = await sdk.callRawAPI(uri, payload, 'PUT');
    return res;
  } catch (error) {}
};
