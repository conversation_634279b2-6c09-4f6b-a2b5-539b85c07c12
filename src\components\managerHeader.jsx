import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  Boxes,
  Calendar,
  ChevronDown,
  Disc3,
  Edit3,
  FolderKanban,
  Layers,
  PanelLeftClose,
  Settings,
  UserCog,
  UserCog2,
} from "lucide-react";
import React from "react";
import { NavLink, useLocation } from "react-router-dom";
import { AuthContext } from "../authContext";
import { GlobalContext } from "../globalContext";

export const ManagerHeader = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch, state } = React.useContext(GlobalContext);
  const location = useLocation();
  const [isSettingsOpen, setIsSettingsOpen] = React.useState(false);

  const handleLogout = () => {
    globalDispatch({
      type: "TOGGLE_PROFILE",
      payload: { showProfile: false },
    });
    dispatch({
      type: "LOGOUT",
    });
  };

  let toggleOpen = (open) =>
    globalDispatch({
      type: "OPEN_SIDEBAR",
      payload: { isOpen: open },
    });

  return (
    <aside
      className={`absolute left-0 top-0 z-[9] flex h-screen w-[220px] flex-col overflow-y-hidden border-r border-strokedark bg-boxdark text-bodydark1 duration-300 ease-linear lg:static lg:translate-x-0 dark:bg-boxdark ${
        !state.isOpen ? "-translate-x-full" : ""
      }`}
    >
      {/* Sidebar Header */}
      <div className="py-5.5 lg:py-6.5 flex items-center justify-between gap-2 px-3 pl-6 lg:pt-[20px]">
        <div className="flex justify-center items-center lg:flex">
          <img
            crossOrigin="anonymous"
            src={
              state.siteLogo ??
              `${window.location.origin}/new/cheerEQ-2-Ed2.png`
            }
            className="h-auto w-[170px]"
            alt="logo"
          />
        </div>
        {/* <button onClick={() => toggleOpen(!state.isOpen)} className="lg:block">
          <PanelLeftClose className="text-bodydark" />
        </button> */}
      </div>

      {/* Sidebar Menu */}
      <div className="flex overflow-y-auto flex-col duration-300 ease-linear no-scrollbar custom-overflow">
        <nav className="px-2 py-2 mt-2 lg:mt-3 lg:px-3">
          <div>
            <h3 className="mb-4 ml-4 text-sm font-semibold text-bodydark2">
              MENU
            </h3>

            <ul className="mb-6 flex flex-col gap-1.5">
              {/* Projects */}
              <li>
                <NavLink
                  to="/manager/projects"
                  className={`group relative flex items-center gap-2.5 rounded-sm px-4 py-2 font-medium duration-300 ease-in-out hover:bg-graydark dark:hover:bg-meta-4 ${
                    state.path === "projects"
                      ? "bg-graydark dark:bg-meta-4"
                      : ""
                  }`}
                >
                  <FolderKanban className="w-4 h-4" />
                  <span className="text-[15px]">Projects</span>
                </NavLink>
              </li>

              {/* Invoices */}
              <li>
                <NavLink
                  to="/manager/invoices"
                  className={`group relative flex items-center gap-2.5 rounded-sm px-4 py-2 font-medium duration-300 ease-in-out hover:bg-graydark dark:hover:bg-meta-4 ${
                    state.path === "invoices"
                      ? "bg-graydark dark:bg-meta-4"
                      : ""
                  }`}
                >
                  <FontAwesomeIcon
                    icon="fa-solid fa-file-invoice"
                    className="w-4 h-4"
                  />
                  <span className="text-[15px]">Invoices</span>
                </NavLink>
              </li>

              {/* Edits */}
              <li>
                <NavLink
                  to="/manager/edits"
                  className={`group relative flex items-center gap-2.5 rounded-sm px-4 py-2 font-medium duration-300 ease-in-out hover:bg-graydark dark:hover:bg-meta-4 ${
                    state.path === "edits" ? "bg-graydark dark:bg-meta-4" : ""
                  }`}
                >
                  <Edit3 className="w-4 h-4" />
                  <span className="text-[15px]">Edits</span>
                </NavLink>
              </li>

              {/* Project Calendar */}
              <li>
                <NavLink
                  to="/manager/project-calendar"
                  className={`group relative flex items-center gap-2.5 rounded-sm px-4 py-2 font-medium duration-300 ease-in-out hover:bg-graydark dark:hover:bg-meta-4 ${
                    state.path === "project-calendar"
                      ? "bg-graydark dark:bg-meta-4"
                      : ""
                  }`}
                >
                  <Calendar className="w-4 h-4" />
                  <span className="text-[15px]">Project Calendar</span>
                </NavLink>
              </li>

              <li>
                <NavLink
                  to="/manager/count-tracks"
                  className={`group relative flex items-center gap-2.5 rounded-sm px-4 py-2 font-medium duration-300 ease-in-out hover:bg-graydark dark:hover:bg-meta-4 ${
                    state.path === "count-tracks"
                      ? "bg-graydark dark:bg-meta-4"
                      : ""
                  }`}
                >
                  <Disc3 className="w-4 h-4" />
                  <span className="text-[15px]">Count Tracks</span>
                </NavLink>
              </li>

              {/* Settings Dropdown */}
              <li>
                <button
                  onClick={() => setIsSettingsOpen(!isSettingsOpen)}
                  className={`group relative flex w-full items-center justify-between gap-2.5 rounded-sm px-4 py-2 font-medium duration-300 ease-in-out hover:bg-graydark dark:hover:bg-meta-4 ${
                    state.path.includes("setting")
                      ? "bg-graydark dark:bg-meta-4"
                      : ""
                  }`}
                >
                  <div className="flex items-center">
                    <Settings className="w-4 h-4" />
                    <span className="ml-2 text-[15px]">Setting</span>
                  </div>
                  <ChevronDown
                    className={`h-4 w-4 transition-transform duration-200 ${
                      isSettingsOpen ? "rotate-180" : ""}`}
                  />
                </button>

                {/* Dropdown Menu */}
                <div
                  className={`mt-2 space-y-1 px-4 ${
                    isSettingsOpen ? "block" : "hidden"
                  }`}
                >
                  <li>
                    <NavLink
                      to="/manager/settings"
                      className={`group relative flex items-center gap-2.5 rounded-sm px-4 py-2 font-medium duration-300 ease-in-out hover:bg-graydark dark:hover:bg-meta-4 ${
                        state.path === "settings"
                          ? "bg-graydark dark:bg-meta-4"
                          : ""
                      }`}
                    >
                      <Settings className="w-4 h-4" />
                      <span className="text-[15px]">General</span>
                    </NavLink>
                  </li>
                  <NavLink
                    to="/manager/clients"
                    className={`group flex items-center gap-2.5 rounded-sm px-4 py-2 font-medium duration-300 ease-in-out hover:bg-graydark dark:hover:bg-meta-4 ${
                      location.pathname === "/manager/clients"
                        ? "text-primary"
                        : "text-bodydark1"
                    }`}
                  >
                    <UserCog className="w-4 h-4" />
                    <span className="text-sm">Clients</span>
                  </NavLink>

                  <NavLink
                    to="/manager/mix-types"
                    className={`group flex items-center gap-2.5 rounded-sm px-4 py-2 font-medium duration-300 ease-in-out hover:bg-graydark dark:hover:bg-meta-4 ${
                      location.pathname === "/manager/mix-types"
                        ? "text-primary"
                        : "text-bodydark1"
                    }`}
                  >
                    <Boxes className="w-4 h-4" />
                    <span className="text-sm">Mix Types</span>
                  </NavLink>

                  <NavLink
                    to="/manager/mix-seasons"
                    className={`group flex items-center gap-2.5 rounded-sm px-4 py-2 font-medium duration-300 ease-in-out hover:bg-graydark dark:hover:bg-meta-4 ${
                      location.pathname === "/manager/mix-seasons"
                        ? "text-primary"
                        : "text-bodydark1"
                    }`}
                  >
                    <Layers className="w-4 h-4" />
                    <span className="text-sm">Mix Seasons</span>
                  </NavLink>

                  <NavLink
                    to="/manager/profile"
                    className={`group flex items-center gap-2.5 rounded-sm px-4 py-2 font-medium duration-300 ease-in-out hover:bg-graydark dark:hover:bg-meta-4 ${
                      location.pathname === "/manager/profile"
                        ? "text-primary"
                        : "text-bodydark1"
                    }`}
                  >
                    <UserCog2 className="w-4 h-4" />
                    <span className="text-sm">Profile</span>
                  </NavLink>
                </div>
              </li>

              {/* Members */}

              {/* Logout */}
              <li>
                <NavLink
                  to="/manager/login"
                  onClick={() => handleLogout()}
                  className="group relative flex items-center gap-2.5 rounded-sm px-4 py-2 font-medium duration-300 ease-in-out hover:bg-graydark dark:hover:bg-meta-4"
                >
                  <FontAwesomeIcon
                    icon="fa-solid fa-right-from-bracket"
                    className="w-4 h-4"
                    color="#CD4631"
                  />
                  <span className="text-[15px] text-[#CD4631]">Logout</span>
                </NavLink>
              </li>
            </ul>
          </div>
        </nav>
      </div>
    </aside>
  );
};

export default ManagerHeader;
