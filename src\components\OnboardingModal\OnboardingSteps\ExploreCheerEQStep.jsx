import React, { useState } from "react";
import { ClipLoader } from "react-spinners";

const features = [
  {
    icon: "/figma-icons/icon-sparkles.svg",
    title: "Project Management",
    desc: "Organize, track, and manage all your music projects in one place.",
  },
  {
    icon: "/figma-icons/icon-file-earmark-text.svg",
    title: "Client Portal",
    desc: "Give your clients a seamless, branded experience for project collaboration.",
  },
  {
    icon: "/figma-icons/icon-credit-card.svg",
    title: "Automated Billing",
    desc: "Streamline payments and invoicing with built-in automation.",
  },
  {
    icon: "/figma-icons/icon-building-office-2.svg",
    title: "Team Collaboration",
    desc: "Empower your team with shared calendars, reminders, and more.",
  },
];

const ExploreCheerEQStep = ({ stepData, userDetails, onComplete }) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleContinue = async () => {
    try {
      setIsLoading(true);

      const featureTourData = {
        feature_tour_viewed: true,
        feature_tour_completed_at: new Date().toISOString(),
      };

      onComplete(featureTourData);
    } catch (error) {
      console.error("Error completing feature tour:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="mx-auto flex w-full max-w-[397px] flex-col items-center gap-6 bg-white p-0">
      <div className="flex w-full flex-col items-center" style={{ gap: 64 }}>
        <div className="flex w-full flex-col items-center gap-2">
          <div className="mb-2 flex h-12 w-12 items-center justify-center rounded-full border-8 border-[#F6F7F8] bg-[#E0E6FC]">
            <img
              src="/figma-icons/icon-sparkles.svg"
              alt="Featured icon"
              className="h-8 w-8"
            />
          </div>
          <h2 className="font-inter text-center text-[18px] font-semibold leading-[28px] text-[#131E2B]">
            Explore CheerEQ Features
          </h2>
          <p className="font-inter max-w-[400px] text-center text-[14px] font-normal leading-[20px] text-[#667484]">
            Get a glimpse of the powerful tools and dedicated sections designed
            to streamline your program operations.
          </p>
        </div>
      </div>
      <div className="mt-8 flex w-full flex-col items-center gap-6">
        <div className="flex w-full flex-col gap-6">
          <div className="flex w-full flex-col gap-2">
            <p className="font-inter text-left text-[14px] font-normal leading-[20px] text-[#667484]">
              <b>Create a Project</b> - The Project section is your central
              place to create and organize cheer music projects. Define key
              details like Mix Season, Mix Date, Program Name, and Team Name.
              Set critical timelines such as Routine Submission and Estimated
              Delivery Dates. Easily manage Payment Status and Program Owner
              contacts to streamline every project.
            </p>
          </div>
          <div className="flex w-full flex-col gap-2">
            <p className="font-inter text-left text-[14px] font-normal leading-[20px] text-[#667484]">
              <b>Create a Work Order</b> - The Work Orders section helps you
              streamline service delivery and track every task within your
              projects. Here, you can assign Due Dates, designate Writers,
              Artists, and Engineers to specific sub-projects. Easily manage
              Engineer Costs and even set work orders to Auto Approve. Add
              Writer Notes for detailed instructions, ensuring efficient task
              management and smooth project execution.
            </p>
          </div>
        </div>
      </div>
      <button
        onClick={handleContinue}
        disabled={isLoading}
        className="font-inter mt-8 w-full rounded-[10px] bg-[#3C50E0] py-3 text-base font-semibold text-[#F5F7FF] transition hover:bg-[#2B3EB4] disabled:opacity-50"
        type="button"
      >
        {isLoading ? (
          <div className="flex items-center justify-center gap-2">
            <ClipLoader size={16} color="#fff" />
            Loading...
          </div>
        ) : (
          "Continue"
        )}
      </button>
    </div>
  );
};

export default ExploreCheerEQStep;
