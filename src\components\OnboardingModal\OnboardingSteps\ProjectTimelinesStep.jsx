import React, { useState, useEffect, useContext } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { ClipLoader } from "react-spinners";
import { GlobalContext, showToast } from "../../../globalContext";

// Validation schema
const schema = yup.object().shape({
  survey_timing_weeks: yup
    .string()
    .required("Please select survey timing weeks"),
  survey_timing_day: yup.string().required("Please select survey timing day"),
  routine_submission_weeks: yup
    .string()
    .required("Please select routine submission weeks"),
  routine_submission_day: yup
    .string()
    .required("Please select routine submission day"),
  estimated_delivery_weeks: yup
    .string()
    .required("Please select estimated delivery weeks"),
  estimated_delivery_day: yup
    .string()
    .required("Please select estimated delivery day"),
  percentage_of_total_project_cost: yup
    .number()
    .min(0)
    .max(100)
    .required("Please enter percentage"),
});

const ProjectTimelinesStep = ({ userDetails, onComplete, onSkip }) => {
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      survey_timing_weeks: "3",
      survey_timing_day: "Monday",
      routine_submission_weeks: "3",
      routine_submission_day: "Monday",
      estimated_delivery_weeks: "3",
      estimated_delivery_day: "Monday",
      percentage_of_total_project_cost: 3,
    },
  });

  useEffect(() => {
    // Set initial values from userDetails if they exist
    if (userDetails) {
      let surveyData = {};
      let estimated_delivery =
        userDetails?.estimated_delivery && userDetails?.estimated_delivery;
      let routine_submission =
        userDetails?.routine_submission_date &&
        userDetails?.routine_submission_date;
      let depositPercent = 50;
      try {
        if (userDetails.survey && typeof userDetails.survey === "string") {
          surveyData = JSON.parse(userDetails.survey);
        } else if (
          userDetails.survey &&
          typeof userDetails.survey === "object"
        ) {
          surveyData = userDetails.survey;
        }
      } catch (error) {
        console.error("Error parsing survey data:", error);
      }

      // Set form values from survey data
      setValue("survey_timing_weeks", surveyData?.weeks || "3");
      setValue("survey_timing_day", surveyData?.day || "Monday");
      setValue("routine_submission_weeks", routine_submission.weeks || "3");
      setValue("routine_submission_day", routine_submission.day || "Monday");
      setValue("estimated_delivery_weeks", estimated_delivery.weeks || "3");
      setValue("estimated_delivery_day", estimated_delivery.day || "Monday");
      setValue("percentage_of_total_project_cost", depositPercent || 50);
    }
  }, [userDetails, setValue]);

  const onSubmit = async (data) => {
    try {
      setIsLoading(true);

      // Prepare project timelines data
      const timelinesData = {
        survey_week: data.survey_timing_weeks,
        survey_day: data.survey_timing_day,
        routine_submission_weeks: data.routine_submission_weeks,
        routine_submission_day: data.routine_submission_day,
        estimated_delivery_weeks: data.estimated_delivery_weeks,
        estimated_delivery_day: data.estimated_delivery_day,
        deposit_percent: parseInt(data.percentage_of_total_project_cost),
      };

      onComplete(timelinesData);
    } catch (error) {
      console.error("Error saving project timelines:", error);
      showToast(
        globalDispatch,
        "Failed to save project timelines",
        4000,
        "error"
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="mx-auto max-w-[400px] space-y-6">
      {/* Header */}
      <div className="text-center">
        <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-lg bg-blue-50">
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M8 2V6"
              stroke="#3B82F6"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M16 2V6"
              stroke="#3B82F6"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <rect
              x="3"
              y="4"
              width="18"
              height="18"
              rx="2"
              ry="2"
              stroke="#3B82F6"
              strokeWidth="2"
            />
            <path
              d="M3 10H21"
              stroke="#3B82F6"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>
        <h2 className="text-xl font-semibold text-gray-900">
          Project Timelines & Defaults
        </h2>
        <p className="mt-1 text-sm text-[#667484]">
          Automate project timelines & set initial deposits.
        </p>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Default Project Timelines */}
        <div className="space-y-4">
          <div>
            <h3 className="text-base font-semibold text-[#3C50E0]">
              Default Project Timelines
            </h3>
            <p className="text-sm text-[#667484]">
              Automate standard project milestones for new projects.
            </p>
          </div>

          <div className="space-y-4">
            {/* Survey Timing */}
            <div>
              <label className="mb-2 block text-sm font-medium text-[#667484]">
                <b>Survey Timing</b> - Default time to send music survey to
                clients, helping the producer with mix themes and notes.
              </label>
              <div className="relative flex items-center gap-2">
                <input
                  type="number"
                  min="1"
                  max="52"
                  {...register("survey_timing_weeks")}
                  className="w-16 rounded border border-[#D5D7DA] bg-white px-2 py-1 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500"
                />
                <span className=" text-sm italic text-[#667484]">week(s)</span>
                <span className="text-sm text-[#667484]">
                  before mix production on
                </span>
                <select
                  {...register("survey_timing_day")}
                  className="black-select w-24 rounded border border-[#D5D7DA] bg-white px-2 py-1 text-sm text-black text-gray-900 focus:border-blue-500 focus:ring-blue-500"
                >
                  <option value="Monday">Monday</option>
                  <option value="Tuesday">Tuesday</option>
                  <option value="Wednesday">Wednesday</option>
                  <option value="Thursday">Thursday</option>
                  <option value="Friday">Friday</option>
                  <option value="Saturday">Saturday</option>
                  <option value="Sunday">Sunday</option>
                </select>
              </div>
              {errors.survey_timing_weeks && (
                <p className="mt-1 text-sm text-red-500">
                  {errors.survey_timing_weeks.message}
                </p>
              )}
              {errors.survey_timing_day && (
                <p className="mt-1 text-sm text-red-500">
                  {errors.survey_timing_day.message}
                </p>
              )}
            </div>

            {/* Routine Submission */}
            <div>
              <label className="mb-2 block text-sm font-medium text-[#667484]">
                <b>Routine Submission</b> - Default internal deadline for
                clients to turn in routine videos and 8-count sheets.
              </label>
              <div className="relative flex items-center gap-2">
                <input
                  type="number"
                  min="1"
                  max="52"
                  {...register("routine_submission_weeks")}
                  className="w-16 rounded border border-[#D5D7DA] bg-white px-2 py-1 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500"
                />
                <span className=" text-sm italic text-[#667484]">week(s)</span>
                <span className="text-sm text-[#667484]">
                  before mix production on
                </span>
                <select
                  {...register("routine_submission_day")}
                  className="black-select w-24 rounded border border-[#D5D7DA] bg-white px-2 py-1 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500"
                >
                  <option value="Monday">Monday</option>
                  <option value="Tuesday">Tuesday</option>
                  <option value="Wednesday">Wednesday</option>
                  <option value="Thursday">Thursday</option>
                  <option value="Friday">Friday</option>
                  <option value="Saturday">Saturday</option>
                  <option value="Sunday">Sunday</option>
                </select>
              </div>
              {errors.routine_submission_weeks && (
                <p className="mt-1 text-sm text-red-500">
                  {errors.routine_submission_weeks.message}
                </p>
              )}
              {errors.routine_submission_day && (
                <p className="mt-1 text-sm text-red-500">
                  {errors.routine_submission_day.message}
                </p>
              )}
            </div>

            {/* Estimated Delivery */}
            <div>
              <label className="mb-2 block text-sm font-medium text-[#667484]">
                <b> Estimated Delivery</b> - Default estimated date for project
                to be completed.
              </label>
              <div className="relative flex items-center gap-2">
                <input
                  type="number"
                  min="1"
                  max="52"
                  {...register("estimated_delivery_weeks")}
                  className="w-16 rounded border border-[#D5D7DA] bg-white px-2 py-1 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500"
                />
                <span className=" text-sm italic text-[#667484]">week(s)</span>
                <span className="text-sm text-[#667484]">
                  after mix production on &nbsp;&nbsp;
                </span>
                <select
                  {...register("estimated_delivery_day")}
                  className="black-select w-24 rounded border border-[#D5D7DA] bg-white px-2 py-1 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500"
                >
                  <option value="Monday">Monday</option>
                  <option value="Tuesday">Tuesday</option>
                  <option value="Wednesday">Wednesday</option>
                  <option value="Thursday">Thursday</option>
                  <option value="Friday">Friday</option>
                  <option value="Saturday">Saturday</option>
                  <option value="Sunday">Sunday</option>
                </select>
              </div>
              {errors.estimated_delivery_weeks && (
                <p className="mt-1 text-sm text-red-500">
                  {errors.estimated_delivery_weeks.message}
                </p>
              )}
              {errors.estimated_delivery_day && (
                <p className="mt-1 text-sm text-red-500">
                  {errors.estimated_delivery_day.message}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Financial Defaults */}
        <div className="space-y-4">
          <div>
            <h3 className="text-base font-semibold text-[#3C50E0]">
              Financial Defaults
            </h3>
            <p className="text-sm text-[#667484]">
              Set standard financial terms for new projects.
            </p>
          </div>

          <div className="flex items-center gap-4">
            <div className="relative inline-flex items-center">
              <input
                type="number"
                min="0"
                max="100"
                {...register("percentage_of_total_project_cost")}
                className="w-16 rounded border border-[#D5D7DA] bg-white px-2 py-1 pr-6 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500"
              />
              <span className="absolute right-2 text-sm text-[#667484]">%</span>
            </div>
            {errors.percentage_of_total_project_cost && (
              <p className="mt-1 text-sm text-red-500">
                {errors.percentage_of_total_project_cost.message}
              </p>
            )}
            <label className="mb-2 block text-sm font-medium text-[#667484]">
              Percentage of total project cost required as an upfront deposit.
            </label>
          </div>
        </div>

        {/* Action Buttons - centered at bottom */}
        <div className="flex justify-center gap-3 pt-6">
          <button
            type="button"
            onClick={onSkip}
            disabled={isLoading}
            className="flex h-[44px] w-[193px] items-center justify-center rounded-lg border border-[#D5D7DA] bg-white px-6 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50"
          >
            Skip for Now
          </button>
          <button
            type="submit"
            disabled={isLoading}
            className="flex h-[44px] w-[200px] items-center justify-center gap-2 rounded-lg bg-[#3C50E0] px-8 py-3 font-medium text-white transition-colors hover:bg-primary/90 disabled:cursor-not-allowed disabled:opacity-50"
          >
            {isLoading ? (
              <>
                <ClipLoader size={14} color="#ffffff" />
                Saving...
              </>
            ) : (
              "Continue"
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ProjectTimelinesStep;
