import React, { useState, useEffect } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import { GlobalContext, showToast } from "Src/globalContext";
import { AuthContext } from "Src/authContext";
import { CheckCircle } from "lucide-react";

const MemberAddSuccessPage = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { state: authState } = React.useContext(AuthContext);
  const [loading, setLoading] = useState(true);

  const invoiceId = searchParams.get("invoice_id");

  useEffect(() => {
    // Check if we have an invoice_id parameter
    if (invoiceId) {
      showToast(
        globalDispatch,
        "Member added successfully! Payment has been processed.",
        5000,
        "success"
      );
    } else {
      // If no invoice_id, redirect to login
      navigate("/login");
    }
    setLoading(false);
  }, [invoiceId, navigate, globalDispatch]);

  const handleBackToSettings = () => {
    // Check if user is authenticated and redirect based on role
    if (!authState.isAuthenticated) {
      navigate("/login");
      return;
    }

    // Redirect based on user role
    if (authState.role === "manager") {
      navigate("/manager/settings");
    } else if (authState.role === "member") {
      navigate("/member/setting");
    } else {
      navigate("/login");
    }
  };

  const handleViewMembers = () => {
    // Check if user is authenticated and redirect based on role
    if (!authState.isAuthenticated) {
      navigate("/login");
      return;
    }

    // Redirect to member list based on user role
    if (authState.role === "manager") {
      navigate("/manager/settings");
    } else if (authState.role === "member") {
      navigate("/member/setting");
    } else {
      navigate("/login");
    }
  };

  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="h-16 w-16 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <div className="flex h-screen flex-col items-center justify-center">
        <div className="text-center">
          <div className="mb-4 flex justify-center">
            <div className="rounded-full bg-success/20 p-4">
              <CheckCircle className="h-16 w-16 text-success" />
            </div>
          </div>
          <h1 className="mb-4 text-4xl font-bold text-success">
            Member Added Successfully!
          </h1>
          <p className="mb-6 text-lg text-bodydark">
            The new member has been added to your company and payment has been processed successfully.
          </p>
          {invoiceId && (
            <div className="mb-6 rounded-lg border border-stroke bg-meta-4/20 p-4">
              <p className="text-sm text-bodydark">
                <span className="font-semibold">Transaction ID:</span>{" "}
                <span className="font-mono text-white">{invoiceId}</span>
              </p>
            </div>
          )}
          <div className="flex justify-center gap-4">
            <button
              onClick={handleViewMembers}
              className="rounded bg-primary px-6 py-3 text-white hover:bg-opacity-90"
            >
              View Members
            </button>
            <button
              onClick={handleBackToSettings}
              className="rounded border border-stroke px-6 py-3 text-white hover:bg-meta-4"
            >
              Back to Settings
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MemberAddSuccessPage;
