import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { GlobalContext, showToast } from "../globalContext";
import { AuthContext } from "../authContext";
import MkdSDK from "../utils/MkdSDK";
import { ClipLoader } from "react-spinners";
import { getUserDetailsByIdAPI } from "Src/services/userService";

let sdk = new MkdSDK();

const MemberLoginPage = () => {
  const schema = yup
    .object({
      email: yup.string().email().required(),
      password: yup.string().required(),
    })
    .required();
  const location = useLocation();

  const searchParams = new URLSearchParams(location.search);

  const projectParam = searchParams.get("project");

  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: GlobalDispatch, state } = React.useContext(GlobalContext);

  const [submitLoading, setSubmitLoading] = useState(false);

  const navigate = useNavigate();
  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  console.log(state.siteLogo, "logopath");
  React.useEffect(() => {
    const userId = localStorage.getItem("user");
    localStorage.clear("pending_verification_user");

    if (userId) {
      (async function () {
        try {
          const result = await getUserDetailsByIdAPI(userId);

          if (!result?.error) {
            localStorage.setItem("photo", result?.model?.photo);
            localStorage.setItem(
              "UserSubscription",
              result?.model?.subscription
            );
          }
        } catch (error) {}
      })();
    }
  }, []);

  const onSubmit = async (data) => {
    try {
      setSubmitLoading(true);
      const result = await sdk.login(data.email, data.password, "member");
      if (!result.error) {
        // Check if user is verified
        if (result.verify === 0) {
          showToast(
            GlobalDispatch,
            "Please check your email for verification link before logging in.",
            5000,
            "warning"
          );
          setSubmitLoading(false);
          return;
        }

        // Set up basic localStorage first (for session safety during redirects)
        localStorage.setItem("UserSubscription", result?.subscription);
        localStorage.setItem("member_photo", result?.company_logo);
        localStorage.setItem("license_logo", result?.license_company_logo);
        localStorage.setItem("token", result?.token);
        localStorage.setItem("user", result.user_id);
        localStorage.removeItem("memberTypeViewEdits");
        localStorage.removeItem("memberCompletedViewEdits");
        localStorage.removeItem("memberPendingViewEdits");

        // Fetch user details to check onboarding status before dispatch
        const userDetails = await getUserDetailsByIdAPI(result.user_id);

        if (userDetails.error) {
          throw new Error("Failed to fetch user details");
        }

        // Determine user type based on main_member_details
        const isSubMember =
          result.main_member_details && !result.main_member_details.is_self;
        const isMainMember =
          !result.main_member_details || result.main_member_details.is_self;

        // Check onboarding completion - both main and sub members need onboarding
        let onboardingComplete = false;
        if (userDetails.model.steps) {
          try {
            const stepData = JSON.parse(userDetails.model.steps);
            // Validate that all 8 steps are complete and onboarding_complete is true
            const allStepsComplete =
              stepData.step_1_complete &&
              stepData.step_2_complete &&
              stepData.step_3_complete &&
              stepData.step_4_complete &&
              stepData.step_5_complete &&
              stepData.step_6_complete &&
              stepData.step_7_complete &&
              stepData.step_8_complete &&
              stepData.onboarding_complete === true;

            onboardingComplete = allStepsComplete;
            console.log("Onboarding validation:", {
              allStepsComplete,
              onboarding_complete: stepData.onboarding_complete,
              finalStatus: onboardingComplete,
            });
          } catch (e) {
            console.error("Error parsing step data:", e);
            // If steps exist but can't be parsed, or don't match current structure,
            // user needs to go through onboarding
            onboardingComplete = false;
          }
        }

        // Check subscription status - only main members need subscription
        const hasPlan = result.plan_id;

        // Set subscription status
        localStorage.setItem("is_plan", hasPlan ? true : false);

        // Always dispatch login to authenticate user
        dispatch({
          type: "LOGIN",
          payload: result,
        });

        showToast(GlobalDispatch, "Successfully Logged In", 4000, "success");

        // Handle navigation based on project parameter first
        if (projectParam) {
          localStorage.setItem("projectClientId", "");
          localStorage.setItem("projectTeamName", "");
          localStorage.setItem("projectMixTypeId", "");
          localStorage.setItem("projectMixDateStart", "");
          localStorage.setItem("projectMixDateEnd", "");
          localStorage.setItem("projectPageSize", "");
          navigate(`/member/view-project/${projectParam}`);
          return;
        }

        // Remove old subscription flag since subscription is now handled in onboarding
        localStorage.removeItem("needs_subscription");

        // Only main members need onboarding - sub members skip onboarding entirely
        let needsOnboarding = false;

        // Sub members (non-main members) never need onboarding since main member has already completed it
        if (isMainMember) {
          if (!result.steps) {
            needsOnboarding = true;
          } else {
            try {
              const stepData = JSON.parse(result.steps);

              // Check if user has ANY new onboarding structure fields
              const hasAnyNewFields =
                stepData.hasOwnProperty("plan_chosen") ||
                stepData.hasOwnProperty("legal_agreements_complete") ||
                stepData.hasOwnProperty("business_profile_complete") ||
                stepData.hasOwnProperty("subscription_complete") ||
                stepData.hasOwnProperty("feature_tour_complete") ||
                stepData.hasOwnProperty("business_identity_complete") ||
                stepData.hasOwnProperty("mandatory_completion_screen_viewed");

              // Check if user has COMPLETE new step structure
              const hasCompleteNewStepStructure =
                stepData.hasOwnProperty("legal_agreements_complete") &&
                stepData.hasOwnProperty("business_profile_complete") &&
                stepData.hasOwnProperty("subscription_complete") &&
                stepData.hasOwnProperty("feature_tour_complete") &&
                stepData.hasOwnProperty("business_identity_complete") &&
                stepData.hasOwnProperty("mandatory_completion_screen_viewed");

              // Check if all mandatory steps are complete
              const allMandatoryComplete =
                stepData.legal_agreements_complete &&
                stepData.business_profile_complete &&
                stepData.plan_chosen &&
                stepData.feature_tour_complete &&
                stepData.business_identity_complete;
              const completionScreenViewed =
                stepData.mandatory_completion_screen_viewed === true;

              // Force onboarding for users with ANY new structure fields
              // This ensures users with partial new structure go through complete new onboarding
              needsOnboarding =
                (hasAnyNewFields && !hasCompleteNewStepStructure) || // Partial new structure - force complete onboarding
                !hasAnyNewFields || // Old structure only - needs new onboarding
                !stepData.onboarding_complete ||
                (allMandatoryComplete && !completionScreenViewed);

              console.log("Onboarding structure check:", {
                hasAnyNewFields,
                hasCompleteNewStepStructure,
                onboarding_complete: stepData.onboarding_complete,
                needsOnboarding,
                stepData,
              });
            } catch (e) {
              console.error("Error parsing step data:", e);
              needsOnboarding = true;
            }
          }
        } // End of isMainMember check - sub members skip onboarding entirely

        if (needsOnboarding) {
          localStorage.setItem("needs_onboarding", "true");
        } else {
          localStorage.removeItem("needs_onboarding");
        }

        console.log("Login navigation logic:", {
          isMainMember,
          isSubMember,
          hasPlan,
          hasSteps: !!result.steps,
          needsOnboarding,
          onboardingComplete,
          note: isSubMember
            ? "Sub-member: Skipping onboarding entirely"
            : "Main member: Checking onboarding status",
        });

        // Navigate to dashboard - dashboard will handle modals based on flags
        navigate("/member/dashboard");
      } else {
        setSubmitLoading(false);
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
      }
    } catch (error) {
      setSubmitLoading(false);

      showToast(GlobalDispatch, error.message, 4000, "error");
      setError("email", {
        type: "manual",
        message: error.response.data.message
          ? error.response.data.message
          : error.message,
      });
    }
  };

  return (
    <div className="max-w-screen flex h-full">
      <div className="shadow-default flex min-h-screen w-full items-center justify-center rounded border border-form-strokedark bg-boxdark dark:border-form-strokedark dark:bg-boxdark">
        <div className="flex w-full flex-wrap items-center">
          {/* Left Side - Image */}
          <div className="hidden w-full xl:block xl:w-1/2">
            <div className="py-17.5 px-26 text-center">
              <Link className="mb-5.5 inline-block" to="/">
                <img
                  crossOrigin="anonymous"
                  src={
                    state.siteLogo ??
                    `${window.location.origin}/new/cheerEQ-2-Ed2.png`
                  }
                  className="h-auto w-[300px] dark:hidden"
                  alt="Logo"
                />
              </Link>

              <p className="2xl:px-20">
                Welcome back! Please sign in to access your account.
              </p>

              <span className="mt-15 inline-block">
                {/* You can add your login illustration SVG here */}
              </span>
            </div>
          </div>

          {/* Right Side - Login Form */}
          <div className="w-full border-form-strokedark px-12 xl:w-1/2 xl:border-l-2 dark:border-form-strokedark">
            <div className="sm:p-12.5 xl:p-17.5 w-full p-4">
              <h2 className="mb-9 text-2xl font-bold text-white sm:text-title-xl2 dark:text-white">
                Sign In to Your Account
              </h2>

              <form onSubmit={handleSubmit(onSubmit)}>
                <div className="mb-4">
                  <label className="mb-2.5 block font-medium text-white dark:text-white">
                    Email
                  </label>
                  <div className="relative">
                    <input
                      type="email"
                      placeholder="Enter your email"
                      {...register("email")}
                      className="w-full rounded-lg border border-form-strokedark bg-form-input py-4 pl-6 pr-10 text-white outline-none focus:border-primary focus-visible:shadow-none dark:border-form-strokedark dark:bg-form-input dark:text-white dark:focus:border-primary"
                    />
                    {errors.email && (
                      <span className="mt-1 text-sm text-red-500">
                        {errors.email.message}
                      </span>
                    )}
                  </div>
                </div>

                <div className="mb-6">
                  <label className="mb-2.5 block font-medium text-white dark:text-white">
                    Password
                  </label>
                  <div className="relative">
                    <input
                      type="password"
                      placeholder="Enter your password"
                      {...register("password")}
                      className="w-full rounded-lg border border-form-strokedark bg-form-input py-4 pl-6 pr-10 text-white outline-none focus:border-primary focus-visible:shadow-none dark:border-form-strokedark dark:bg-form-input dark:text-white dark:focus:border-primary"
                    />
                    {errors.password && (
                      <span className="mt-1 text-sm text-red-500">
                        {errors.password.message}
                      </span>
                    )}
                  </div>
                </div>

                <div className="mb-5">
                  <button
                    type="submit"
                    disabled={submitLoading}
                    className="w-full cursor-pointer rounded-lg border border-primary bg-primary p-4 text-white transition hover:bg-opacity-90 disabled:opacity-50"
                  >
                    {submitLoading ? (
                      <ClipLoader size={18} color="#fff" />
                    ) : (
                      "Sign In"
                    )}
                  </button>
                </div>

                <div className="mt-6 text-center">
                  <Link
                    to="/member/forgot"
                    className="text-primary hover:underline"
                  >
                    Forgot Password?
                  </Link>
                </div>

                <div className="mt-6 text-center">
                  <p className="text-white">
                    Don't have an account?{" "}
                    <Link
                      to="/member/register"
                      className="text-primary hover:underline"
                    >
                      Sign Up
                    </Link>
                  </p>
                </div>

                <div className="mt-6 text-center">
                  <Link to="/" className="text-white hover:text-primary">
                    Back to Home
                  </Link>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MemberLoginPage;
