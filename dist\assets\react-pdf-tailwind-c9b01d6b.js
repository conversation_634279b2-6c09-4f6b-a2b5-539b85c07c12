import{g as Oe}from"./vendor-94843817.js";import{p as Se,l as oe,c as Ae,a as Pe,b as Ce}from"./@tailwindcss/forms-68ac209c.js";var le={},ae={},X={};(function(o){Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"default",{enumerable:!0,get:function(){return P}});function P(u){if(u=`${u}`,u==="0")return"0";if(/^[+-]?(\d+|\d*\.\d+)(e[+-]?\d+)?(%|\w+)?$/.test(u))return u.replace(/^[+-]?/,v=>v==="-"?"":"-");let j=["var","calc","min","max","clamp"];for(const v of j)if(u.includes(`${v}(`))return`calc(${u} * -1)`}})(X);var ue={};(function(o){Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"default",{enumerable:!0,get:function(){return P}});const P=["preflight","container","accessibility","pointerEvents","visibility","position","inset","isolation","zIndex","order","gridColumn","gridColumnStart","gridColumnEnd","gridRow","gridRowStart","gridRowEnd","float","clear","margin","boxSizing","lineClamp","display","aspectRatio","size","height","maxHeight","minHeight","width","minWidth","maxWidth","flex","flexShrink","flexGrow","flexBasis","tableLayout","captionSide","borderCollapse","borderSpacing","transformOrigin","translate","rotate","skew","scale","transform","animation","cursor","touchAction","userSelect","resize","scrollSnapType","scrollSnapAlign","scrollSnapStop","scrollMargin","scrollPadding","listStylePosition","listStyleType","listStyleImage","appearance","columns","breakBefore","breakInside","breakAfter","gridAutoColumns","gridAutoFlow","gridAutoRows","gridTemplateColumns","gridTemplateRows","flexDirection","flexWrap","placeContent","placeItems","alignContent","alignItems","justifyContent","justifyItems","gap","space","divideWidth","divideStyle","divideColor","divideOpacity","placeSelf","alignSelf","justifySelf","overflow","overscrollBehavior","scrollBehavior","textOverflow","hyphens","whitespace","textWrap","wordBreak","borderRadius","borderWidth","borderStyle","borderColor","borderOpacity","backgroundColor","backgroundOpacity","backgroundImage","gradientColorStops","boxDecorationBreak","backgroundSize","backgroundAttachment","backgroundClip","backgroundPosition","backgroundRepeat","backgroundOrigin","fill","stroke","strokeWidth","objectFit","objectPosition","padding","textAlign","textIndent","verticalAlign","fontFamily","fontSize","fontWeight","textTransform","fontStyle","fontVariantNumeric","lineHeight","letterSpacing","textColor","textOpacity","textDecoration","textDecorationColor","textDecorationStyle","textDecorationThickness","textUnderlineOffset","fontSmoothing","placeholderColor","placeholderOpacity","caretColor","accentColor","opacity","backgroundBlendMode","mixBlendMode","boxShadow","boxShadowColor","outlineStyle","outlineWidth","outlineOffset","outlineColor","ringWidth","ringColor","ringOpacity","ringOffsetWidth","ringOffsetColor","blur","brightness","contrast","dropShadow","grayscale","hueRotate","invert","saturate","sepia","filter","backdropBlur","backdropBrightness","backdropContrast","backdropGrayscale","backdropHueRotate","backdropInvert","backdropOpacity","backdropSaturate","backdropSepia","backdropFilter","transitionProperty","transitionDelay","transitionDuration","transitionTimingFunction","willChange","contain","content","forcedColorAdjust"]})(ue);var se={};(function(o){Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"default",{enumerable:!0,get:function(){return P}});function P(u,j){return u===void 0?j:Array.isArray(u)?u:[...new Set(j.filter(m=>u!==!1&&u[m]!==!1).concat(Object.keys(u).filter(m=>u[m]!==!1)))]}})(se);var fe={};(function(o){Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"defaults",{enumerable:!0,get:function(){return P}});function P(u,...j){for(let e of j){for(let h in e){var v;!(u==null||(v=u.hasOwnProperty)===null||v===void 0)&&v.call(u,h)||(u[h]=e[h])}for(let h of Object.getOwnPropertySymbols(e)){var m;!(u==null||(m=u.hasOwnProperty)===null||m===void 0)&&m.call(u,h)||(u[h]=e[h])}}return u}})(fe);var ce={};(function(o){Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"toPath",{enumerable:!0,get:function(){return P}});function P(u){if(Array.isArray(u))return u;let j=u.split("[").length-1,v=u.split("]").length-1;if(j!==v)throw new Error(`Path is invalid. Has unbalanced brackets: ${u}`);return u.split(/\.(?![^\[]*\])|[\[\]]/g).filter(Boolean)}})(ce);var de={},I={};(function(o){Object.defineProperty(o,"__esModule",{value:!0});function P(c,r){for(var s in r)Object.defineProperty(c,s,{enumerable:!0,get:r[s]})}P(o,{flagEnabled:function(){return h},issueFlagNotices:function(){return t},default:function(){return i}});const u=v(Se),j=v(oe);function v(c){return c&&c.__esModule?c:{default:c}}let m={optimizeUniversalDefaults:!1,generalizedModifiers:!0,disableColorOpacityUtilitiesByDefault:!1,relativeContentPathsByDefault:!1},e={future:["hoverOnlyWhenSupported","respectDefaultRingColorOpacity","disableColorOpacityUtilitiesByDefault","relativeContentPathsByDefault"],experimental:["optimizeUniversalDefaults","generalizedModifiers"]};function h(c,r){if(e.future.includes(r)){var s,n,_;return c.future==="all"||((_=(n=c==null||(s=c.future)===null||s===void 0?void 0:s[r])!==null&&n!==void 0?n:m[r])!==null&&_!==void 0?_:!1)}if(e.experimental.includes(r)){var l,O,d;return c.experimental==="all"||((d=(O=c==null||(l=c.experimental)===null||l===void 0?void 0:l[r])!==null&&O!==void 0?O:m[r])!==null&&d!==void 0?d:!1)}return!1}function y(c){if(c.experimental==="all")return e.experimental;var r;return Object.keys((r=c==null?void 0:c.experimental)!==null&&r!==void 0?r:{}).filter(s=>e.experimental.includes(s)&&c.experimental[s])}function t(c){if({}.JEST_WORKER_ID===void 0&&y(c).length>0){let r=y(c).map(s=>u.default.yellow(s)).join(", ");j.default.warn("experimental-flags-enabled",[`You have enabled experimental features: ${r}`,"Experimental features in Tailwind CSS are not covered by semver, may introduce breaking changes, and can change at any time."])}}const i=e})(I);(function(o){Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"normalizeConfig",{enumerable:!0,get:function(){return m}});const P=I,u=v(oe);function j(e){if(typeof WeakMap!="function")return null;var h=new WeakMap,y=new WeakMap;return(j=function(t){return t?y:h})(e)}function v(e,h){if(!h&&e&&e.__esModule)return e;if(e===null||typeof e!="object"&&typeof e!="function")return{default:e};var y=j(h);if(y&&y.has(e))return y.get(e);var t={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var c in e)if(c!=="default"&&Object.prototype.hasOwnProperty.call(e,c)){var r=i?Object.getOwnPropertyDescriptor(e,c):null;r&&(r.get||r.set)?Object.defineProperty(t,c,r):t[c]=e[c]}return t.default=e,y&&y.set(e,t),t}function m(e){if((()=>{if(e.purge||!e.content||!Array.isArray(e.content)&&!(typeof e.content=="object"&&e.content!==null))return!1;if(Array.isArray(e.content))return e.content.every(t=>typeof t=="string"?!0:!(typeof(t==null?void 0:t.raw)!="string"||t!=null&&t.extension&&typeof(t==null?void 0:t.extension)!="string"));if(typeof e.content=="object"&&e.content!==null){if(Object.keys(e.content).some(t=>!["files","relative","extract","transform"].includes(t)))return!1;if(Array.isArray(e.content.files)){if(!e.content.files.every(t=>typeof t=="string"?!0:!(typeof(t==null?void 0:t.raw)!="string"||t!=null&&t.extension&&typeof(t==null?void 0:t.extension)!="string")))return!1;if(typeof e.content.extract=="object"){for(let t of Object.values(e.content.extract))if(typeof t!="function")return!1}else if(!(e.content.extract===void 0||typeof e.content.extract=="function"))return!1;if(typeof e.content.transform=="object"){for(let t of Object.values(e.content.transform))if(typeof t!="function")return!1}else if(!(e.content.transform===void 0||typeof e.content.transform=="function"))return!1;if(typeof e.content.relative!="boolean"&&typeof e.content.relative<"u")return!1}return!0}return!1})()||u.default.warn("purge-deprecation",["The `purge`/`content` options have changed in Tailwind CSS v3.0.","Update your configuration file to eliminate this warning.","https://tailwindcss.com/docs/upgrade-guide#configure-content-sources"]),e.safelist=(()=>{var t;let{content:i,purge:c,safelist:r}=e;return Array.isArray(r)?r:Array.isArray(i==null?void 0:i.safelist)?i.safelist:Array.isArray(c==null?void 0:c.safelist)?c.safelist:Array.isArray(c==null||(t=c.options)===null||t===void 0?void 0:t.safelist)?c.options.safelist:[]})(),e.blocklist=(()=>{let{blocklist:t}=e;if(Array.isArray(t)){if(t.every(i=>typeof i=="string"))return t;u.default.warn("blocklist-invalid",["The `blocklist` option must be an array of strings.","https://tailwindcss.com/docs/content-configuration#discarding-classes"])}return[]})(),typeof e.prefix=="function")u.default.warn("prefix-function",["As of Tailwind CSS v3.0, `prefix` cannot be a function.","Update `prefix` in your configuration to be a string to eliminate this warning.","https://tailwindcss.com/docs/upgrade-guide#prefix-cannot-be-a-function"]),e.prefix="";else{var y;e.prefix=(y=e.prefix)!==null&&y!==void 0?y:""}e.content={relative:(()=>{let{content:t}=e;return t!=null&&t.relative?t.relative:(0,P.flagEnabled)(e,"relativeContentPathsByDefault")})(),files:(()=>{let{content:t,purge:i}=e;return Array.isArray(i)?i:Array.isArray(i==null?void 0:i.content)?i.content:Array.isArray(t)?t:Array.isArray(t==null?void 0:t.content)?t.content:Array.isArray(t==null?void 0:t.files)?t.files:[]})(),extract:(()=>{let t=(()=>{var r,s,n,_,l,O,d,$,E,R;return!((r=e.purge)===null||r===void 0)&&r.extract?e.purge.extract:!((s=e.content)===null||s===void 0)&&s.extract?e.content.extract:!((n=e.purge)===null||n===void 0||(_=n.extract)===null||_===void 0)&&_.DEFAULT?e.purge.extract.DEFAULT:!((l=e.content)===null||l===void 0||(O=l.extract)===null||O===void 0)&&O.DEFAULT?e.content.extract.DEFAULT:!((d=e.purge)===null||d===void 0||($=d.options)===null||$===void 0)&&$.extractors?e.purge.options.extractors:!((E=e.content)===null||E===void 0||(R=E.options)===null||R===void 0)&&R.extractors?e.content.options.extractors:{}})(),i={},c=(()=>{var r,s,n,_;if(!((r=e.purge)===null||r===void 0||(s=r.options)===null||s===void 0)&&s.defaultExtractor)return e.purge.options.defaultExtractor;if(!((n=e.content)===null||n===void 0||(_=n.options)===null||_===void 0)&&_.defaultExtractor)return e.content.options.defaultExtractor})();if(c!==void 0&&(i.DEFAULT=c),typeof t=="function")i.DEFAULT=t;else if(Array.isArray(t))for(let{extensions:r,extractor:s}of t??[])for(let n of r)i[n]=s;else typeof t=="object"&&t!==null&&Object.assign(i,t);return i})(),transform:(()=>{let t=(()=>{var c,r,s,n,_,l;return!((c=e.purge)===null||c===void 0)&&c.transform?e.purge.transform:!((r=e.content)===null||r===void 0)&&r.transform?e.content.transform:!((s=e.purge)===null||s===void 0||(n=s.transform)===null||n===void 0)&&n.DEFAULT?e.purge.transform.DEFAULT:!((_=e.content)===null||_===void 0||(l=_.transform)===null||l===void 0)&&l.DEFAULT?e.content.transform.DEFAULT:{}})(),i={};return typeof t=="function"?i.DEFAULT=t:typeof t=="object"&&t!==null&&Object.assign(i,t),i})()};for(let t of e.content.files)if(typeof t=="string"&&/{([^,]*?)}/g.test(t)){u.default.warn("invalid-glob-braces",[`The glob pattern ${(0,u.dim)(t)} in your Tailwind CSS configuration is invalid.`,`Update it to ${(0,u.dim)(t.replace(/{([^,]*?)}/g,"$1"))} to silence this warning.`]);break}return e}})(de);var pe={};(function(o){Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"default",{enumerable:!0,get:function(){return P}});function P(u){if(Object.prototype.toString.call(u)!=="[object Object]")return!1;const j=Object.getPrototypeOf(u);return j===null||Object.getPrototypeOf(j)===null}})(pe);var ge={},me={};(function(o){Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"default",{enumerable:!0,get:function(){return P}});function P(u){return u.replace(/\\,/g,"\\2c ")}})(me);var J={},Q={},ye={};(function(o){Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"default",{enumerable:!0,get:function(){return P}});const P={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}})(ye);(function(o){Object.defineProperty(o,"__esModule",{value:!0});function P(n,_){for(var l in _)Object.defineProperty(n,l,{enumerable:!0,get:_[l]})}P(o,{parseColor:function(){return r},formatColor:function(){return s}});const u=j(ye);function j(n){return n&&n.__esModule?n:{default:n}}let v=/^#([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})?$/i,m=/^#([a-f\d])([a-f\d])([a-f\d])([a-f\d])?$/i,e=/(?:\d+|\d*\.\d+)%?/,h=/(?:\s*,\s*|\s+)/,y=/\s*[,/]\s*/,t=/var\(--(?:[^ )]*?)(?:,(?:[^ )]*?|var\(--[^ )]*?\)))?\)/,i=new RegExp(`^(rgba?)\\(\\s*(${e.source}|${t.source})(?:${h.source}(${e.source}|${t.source}))?(?:${h.source}(${e.source}|${t.source}))?(?:${y.source}(${e.source}|${t.source}))?\\s*\\)$`),c=new RegExp(`^(hsla?)\\(\\s*((?:${e.source})(?:deg|rad|grad|turn)?|${t.source})(?:${h.source}(${e.source}|${t.source}))?(?:${h.source}(${e.source}|${t.source}))?(?:${y.source}(${e.source}|${t.source}))?\\s*\\)$`);function r(n,{loose:_=!1}={}){var l,O;if(typeof n!="string")return null;if(n=n.trim(),n==="transparent")return{mode:"rgb",color:["0","0","0"],alpha:"0"};if(n in u.default)return{mode:"rgb",color:u.default[n].map(W=>W.toString())};let d=n.replace(m,(W,M,L,p,S)=>["#",M,M,L,L,p,p,S?S+S:""].join("")).match(v);if(d!==null)return{mode:"rgb",color:[parseInt(d[1],16),parseInt(d[2],16),parseInt(d[3],16)].map(W=>W.toString()),alpha:d[4]?(parseInt(d[4],16)/255).toString():void 0};var $;let E=($=n.match(i))!==null&&$!==void 0?$:n.match(c);if(E===null)return null;let R=[E[2],E[3],E[4]].filter(Boolean).map(W=>W.toString());return R.length===2&&R[0].startsWith("var(")?{mode:E[1],color:[R[0]],alpha:R[1]}:!_&&R.length!==3||R.length<3&&!R.some(W=>/^var\(.*?\)$/.test(W))?null:{mode:E[1],color:R,alpha:(l=E[5])===null||l===void 0||(O=l.toString)===null||O===void 0?void 0:O.call(l)}}function s({mode:n,color:_,alpha:l}){let O=l!==void 0;return n==="rgba"||n==="hsla"?`${n}(${_.join(", ")}${O?`, ${l}`:""})`:`${n}(${_.join(" ")}${O?` / ${l}`:""})`}})(Q);(function(o){Object.defineProperty(o,"__esModule",{value:!0});function P(m,e){for(var h in e)Object.defineProperty(m,h,{enumerable:!0,get:e[h]})}P(o,{withAlphaValue:function(){return j},default:function(){return v}});const u=Q;function j(m,e,h){if(typeof m=="function")return m({opacityValue:e});let y=(0,u.parseColor)(m,{loose:!0});return y===null?h:(0,u.formatColor)({...y,alpha:e})}function v({color:m,property:e,variable:h}){let y=[].concat(e);if(typeof m=="function")return{[h]:"1",...Object.fromEntries(y.map(i=>[i,m({opacityVariable:h,opacityValue:`var(${h}, 1)`})]))};const t=(0,u.parseColor)(m);return t===null?Object.fromEntries(y.map(i=>[i,m])):t.alpha!==void 0?Object.fromEntries(y.map(i=>[i,m])):{[h]:"1",...Object.fromEntries(y.map(i=>[i,(0,u.formatColor)({...t,alpha:`var(${h}, 1)`})]))}}})(J);var Z={},ve={},N={};(function(o){Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"splitAtTopLevelOnly",{enumerable:!0,get:function(){return P}});function P(u,j){let v=[],m=[],e=0,h=!1;for(let y=0;y<u.length;y++){let t=u[y];v.length===0&&t===j[0]&&!h&&(j.length===1||u.slice(y,y+j.length)===j)&&(m.push(u.slice(e,y)),e=y+j.length),h=h?!1:t==="\\",t==="("||t==="["||t==="{"?v.push(t):(t===")"&&v[v.length-1]==="("||t==="]"&&v[v.length-1]==="["||t==="}"&&v[v.length-1]==="{")&&v.pop()}return m.push(u.slice(e)),m}})(N);(function(o){Object.defineProperty(o,"__esModule",{value:!0});function P(y,t){for(var i in t)Object.defineProperty(y,i,{enumerable:!0,get:t[i]})}P(o,{parseBoxShadowValue:function(){return e},formatBoxShadowValue:function(){return h}});const u=N;let j=new Set(["inset","inherit","initial","revert","unset"]),v=/\ +(?![^(]*\))/g,m=/^-?(\d+|\.\d+)(.*?)$/g;function e(y){return(0,u.splitAtTopLevelOnly)(y,",").map(i=>{let c=i.trim(),r={raw:c},s=c.split(v),n=new Set;for(let _ of s)m.lastIndex=0,!n.has("KEYWORD")&&j.has(_)?(r.keyword=_,n.add("KEYWORD")):m.test(_)?n.has("X")?n.has("Y")?n.has("BLUR")?n.has("SPREAD")||(r.spread=_,n.add("SPREAD")):(r.blur=_,n.add("BLUR")):(r.y=_,n.add("Y")):(r.x=_,n.add("X")):r.color?(r.unknown||(r.unknown=[]),r.unknown.push(_)):r.color=_;return r.valid=r.x!==void 0&&r.y!==void 0,r})}function h(y){return y.map(t=>t.valid?[t.keyword,t.x,t.y,t.blur,t.spread,t.color].filter(Boolean).join(" "):t.raw).join(", ")}})(ve);(function(o){Object.defineProperty(o,"__esModule",{value:!0});function P(a,x){for(var T in x)Object.defineProperty(a,T,{enumerable:!0,get:x[T]})}P(o,{normalize:function(){return y},normalizeAttributeSelectors:function(){return t},url:function(){return c},number:function(){return r},percentage:function(){return s},length:function(){return l},lineWidth:function(){return d},shadow:function(){return $},color:function(){return E},image:function(){return R},gradient:function(){return M},position:function(){return p},familyName:function(){return S},genericName:function(){return f},absoluteSize:function(){return w},relativeSize:function(){return C}});const u=Q,j=ve,v=N;let m=["min","max","clamp","calc"];function e(a){return m.some(x=>new RegExp(`^${x}\\(.*\\)`).test(a))}const h=new Set(["scroll-timeline-name","timeline-scope","view-timeline-name","font-palette","anchor-name","anchor-scope","position-anchor","position-try-options","scroll-timeline","animation-timeline","view-timeline","position-try"]);function y(a,x=null,T=!0){let k=x&&h.has(x.property);return a.startsWith("--")&&!k?`var(${a})`:a.includes("url(")?a.split(/(url\(.*?\))/g).filter(Boolean).map(D=>/^url\(.*?\)$/.test(D)?D:y(D,x,!1)).join(""):(a=a.replace(/([^\\])_+/g,(D,q)=>q+" ".repeat(D.length-1)).replace(/^_/g," ").replace(/\\_/g,"_"),T&&(a=a.trim()),a=i(a),a)}function t(a){return a.includes("=")&&(a=a.replace(/(=.*)/g,(x,T)=>{if(T[1]==="'"||T[1]==='"')return T;if(T.length>2){let k=T[T.length-1];if(T[T.length-2]===" "&&(k==="i"||k==="I"||k==="s"||k==="S"))return`="${T.slice(1,-2)}" ${T[T.length-1]}`}return`="${T.slice(1)}"`})),a}function i(a){let x=["theme"],T=["min-content","max-content","fit-content","safe-area-inset-top","safe-area-inset-right","safe-area-inset-bottom","safe-area-inset-left","titlebar-area-x","titlebar-area-y","titlebar-area-width","titlebar-area-height","keyboard-inset-top","keyboard-inset-right","keyboard-inset-bottom","keyboard-inset-left","keyboard-inset-width","keyboard-inset-height","radial-gradient","linear-gradient","conic-gradient","repeating-radial-gradient","repeating-linear-gradient","repeating-conic-gradient","anchor-size"];return a.replace(/(calc|min|max|clamp)\(.+\)/g,k=>{let D="";function q(){let F=D.trimEnd();return F[F.length-1]}for(let F=0;F<k.length;F++){let U=function(z){return z.split("").every((B,V)=>k[F+V]===B)},H=function(z){let B=1/0;for(let _e of z){let Y=k.indexOf(_e,F);Y!==-1&&Y<B&&(B=Y)}let V=k.slice(F,B);return F+=V.length-1,V},G=k[F];if(U("var"))D+=H([")",","]);else if(T.some(z=>U(z))){let z=T.find(B=>U(B));D+=z,F+=z.length-1}else x.some(z=>U(z))?D+=H([")"]):U("[")?D+=H(["]"]):["+","-","*","/"].includes(G)&&!["(","+","-","*","/",","].includes(q())?D+=` ${G} `:D+=G}return D.replace(/\s+/g," ")})}function c(a){return a.startsWith("url(")}function r(a){return!isNaN(Number(a))||e(a)}function s(a){return a.endsWith("%")&&r(a.slice(0,-1))||e(a)}let _=`(?:${["cm","mm","Q","in","pc","pt","px","em","ex","ch","rem","lh","rlh","vw","vh","vmin","vmax","vb","vi","svw","svh","lvw","lvh","dvw","dvh","cqw","cqh","cqi","cqb","cqmin","cqmax"].join("|")})`;function l(a){return a==="0"||new RegExp(`^[+-]?[0-9]*.?[0-9]+(?:[eE][+-]?[0-9]+)?${_}$`).test(a)||e(a)}let O=new Set(["thin","medium","thick"]);function d(a){return O.has(a)}function $(a){let x=(0,j.parseBoxShadowValue)(y(a));for(let T of x)if(!T.valid)return!1;return!0}function E(a){let x=0;return(0,v.splitAtTopLevelOnly)(a,"_").every(k=>(k=y(k),k.startsWith("var(")?!0:(0,u.parseColor)(k,{loose:!0})!==null?(x++,!0):!1))?x>0:!1}function R(a){let x=0;return(0,v.splitAtTopLevelOnly)(a,",").every(k=>(k=y(k),k.startsWith("var(")?!0:c(k)||M(k)||["element(","image(","cross-fade(","image-set("].some(D=>k.startsWith(D))?(x++,!0):!1))?x>0:!1}let W=new Set(["conic-gradient","linear-gradient","radial-gradient","repeating-conic-gradient","repeating-linear-gradient","repeating-radial-gradient"]);function M(a){a=y(a);for(let x of W)if(a.startsWith(`${x}(`))return!0;return!1}let L=new Set(["center","top","right","bottom","left"]);function p(a){let x=0;return(0,v.splitAtTopLevelOnly)(a,"_").every(k=>(k=y(k),k.startsWith("var(")?!0:L.has(k)||l(k)||s(k)?(x++,!0):!1))?x>0:!1}function S(a){let x=0;return(0,v.splitAtTopLevelOnly)(a,",").every(k=>(k=y(k),k.startsWith("var(")?!0:k.includes(" ")&&!/(['"])([^"']+)\1/g.test(k)||/^\d/g.test(k)?!1:(x++,!0)))?x>0:!1}let A=new Set(["serif","sans-serif","monospace","cursive","fantasy","system-ui","ui-serif","ui-sans-serif","ui-monospace","ui-rounded","math","emoji","fangsong"]);function f(a){return A.has(a)}let b=new Set(["xx-small","x-small","small","medium","large","x-large","xx-large","xxx-large"]);function w(a){return b.has(a)}let g=new Set(["larger","smaller"]);function C(a){return g.has(a)}})(Z);var he={};(function(o){Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"backgroundSize",{enumerable:!0,get:function(){return j}});const P=Z,u=N;function j(v){let m=["cover","contain"];return(0,u.splitAtTopLevelOnly)(v,",").every(e=>{let h=(0,u.splitAtTopLevelOnly)(e,"_").filter(Boolean);return h.length===1&&m.includes(h[0])?!0:h.length!==1&&h.length!==2?!1:h.every(y=>(0,P.length)(y)||(0,P.percentage)(y)||y==="auto")})}})(he);(function(o){Object.defineProperty(o,"__esModule",{value:!0});function P(p,S){for(var A in S)Object.defineProperty(p,A,{enumerable:!0,get:S[A]})}P(o,{updateAllClasses:function(){return t},asValue:function(){return r},parseColorFormat:function(){return _},asColor:function(){return O},asLookupValue:function(){return d},typeMap:function(){return E},coerceValue:function(){return M},getMatchingTypes:function(){return L}});const u=y(me),j=J,v=Z,m=y(X),e=he,h=I;function y(p){return p&&p.__esModule?p:{default:p}}function t(p,S){p.walkClasses(A=>{A.value=S(A.value),A.raws&&A.raws.value&&(A.raws.value=(0,u.default)(A.raws.value))})}function i(p,S){if(!s(p))return;let A=p.slice(1,-1);if(S(A))return(0,v.normalize)(A)}function c(p,S={},A){let f=S[p];if(f!==void 0)return(0,m.default)(f);if(s(p)){let b=i(p,A);return b===void 0?void 0:(0,m.default)(b)}}function r(p,S={},{validate:A=()=>!0}={}){var f;let b=(f=S.values)===null||f===void 0?void 0:f[p];return b!==void 0?b:S.supportsNegativeValues&&p.startsWith("-")?c(p.slice(1),S.values,A):i(p,A)}function s(p){return p.startsWith("[")&&p.endsWith("]")}function n(p){let S=p.lastIndexOf("/"),A=p.lastIndexOf("[",S),f=p.indexOf("]",S);return p[S-1]==="]"||p[S+1]==="["||A!==-1&&f!==-1&&A<S&&S<f&&(S=p.lastIndexOf("/",A)),S===-1||S===p.length-1?[p,void 0]:s(p)&&!p.includes("]/[")?[p,void 0]:[p.slice(0,S),p.slice(S+1)]}function _(p){if(typeof p=="string"&&p.includes("<alpha-value>")){let S=p;return({opacityValue:A=1})=>S.replace(/<alpha-value>/g,A)}return p}function l(p){return(0,v.normalize)(p.slice(1,-1))}function O(p,S={},{tailwindConfig:A={}}={}){var f;if(((f=S.values)===null||f===void 0?void 0:f[p])!==void 0){var b;return _((b=S.values)===null||b===void 0?void 0:b[p])}let[w,g]=n(p);if(g!==void 0){var C,a,x,T;let k=(T=(C=S.values)===null||C===void 0?void 0:C[w])!==null&&T!==void 0?T:s(w)?w.slice(1,-1):void 0;return k===void 0?void 0:(k=_(k),s(g)?(0,j.withAlphaValue)(k,l(g)):((a=A.theme)===null||a===void 0||(x=a.opacity)===null||x===void 0?void 0:x[g])===void 0?void 0:(0,j.withAlphaValue)(k,A.theme.opacity[g]))}return r(p,S,{validate:v.color})}function d(p,S={}){var A;return(A=S.values)===null||A===void 0?void 0:A[p]}function $(p){return(S,A)=>r(S,A,{validate:p})}let E={any:r,color:O,url:$(v.url),image:$(v.image),length:$(v.length),percentage:$(v.percentage),position:$(v.position),lookup:d,"generic-name":$(v.genericName),"family-name":$(v.familyName),number:$(v.number),"line-width":$(v.lineWidth),"absolute-size":$(v.absoluteSize),"relative-size":$(v.relativeSize),shadow:$(v.shadow),size:$(e.backgroundSize)},R=Object.keys(E);function W(p,S){let A=p.indexOf(S);return A===-1?[void 0,p]:[p.slice(0,A),p.slice(A+1)]}function M(p,S,A,f){if(A.values&&S in A.values)for(let{type:w}of p??[]){let g=E[w](S,A,{tailwindConfig:f});if(g!==void 0)return[g,w,null]}if(s(S)){let w=S.slice(1,-1),[g,C]=W(w,":");if(!/^[\w-_]+$/g.test(g))C=w;else if(g!==void 0&&!R.includes(g))return[];if(C.length>0&&R.includes(g))return[r(`[${C}]`,A),g,null]}let b=L(p,S,A,f);for(let w of b)return w;return[]}function*L(p,S,A,f){let b=(0,h.flagEnabled)(f,"generalizedModifiers"),[w,g]=n(S);if(b&&A.modifiers!=null&&(A.modifiers==="any"||typeof A.modifiers=="object"&&(g&&s(g)||g in A.modifiers))||(w=S,g=void 0),g!==void 0&&w===""&&(w="DEFAULT"),g!==void 0&&typeof A.modifiers=="object"){var a,x;let T=(x=(a=A.modifiers)===null||a===void 0?void 0:a[g])!==null&&x!==void 0?x:null;T!==null?g=T:s(g)&&(g=l(g))}for(let{type:T}of p??[]){let k=E[T](w,A,{tailwindConfig:f});k!==void 0&&(yield[k,T,g??null])}}})(ge);var be={};(function(o){Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"default",{enumerable:!0,get:function(){return P}});function P(u){return typeof u=="function"?u({}):u}})(be);(function(o){Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"default",{enumerable:!0,get:function(){return A}});const P=s(X),u=s(ue),j=s(se),v=s(Ae),m=fe,e=ce,h=de,y=s(pe),t=Pe,i=ge,c=J,r=s(be);function s(f){return f&&f.__esModule?f:{default:f}}function n(f){return typeof f=="function"}function _(f,...b){let w=b.pop();for(let g of b)for(let C in g){let a=w(f[C],g[C]);a===void 0?(0,y.default)(f[C])&&(0,y.default)(g[C])?f[C]=_({},f[C],g[C],w):f[C]=g[C]:f[C]=a}return f}const l={colors:v.default,negative(f){return Object.keys(f).filter(b=>f[b]!=="0").reduce((b,w)=>{let g=(0,P.default)(f[w]);return g!==void 0&&(b[`-${w}`]=g),b},{})},breakpoints(f){return Object.keys(f).filter(b=>typeof f[b]=="string").reduce((b,w)=>({...b,[`screen-${w}`]:f[w]}),{})}};function O(f,...b){return n(f)?f(...b):f}function d(f){return f.reduce((b,{extend:w})=>_(b,w,(g,C)=>g===void 0?[C]:Array.isArray(g)?[C,...g]:[C,g]),{})}function $(f){return{...f.reduce((b,w)=>(0,m.defaults)(b,w),{}),extend:d(f)}}function E(f,b){if(Array.isArray(f)&&(0,y.default)(f[0]))return f.concat(b);if(Array.isArray(b)&&(0,y.default)(b[0])&&(0,y.default)(f))return[f,...b];if(Array.isArray(b))return b}function R({extend:f,...b}){return _(b,f,(w,g)=>!n(w)&&!g.some(n)?_({},w,...g,E):(C,a)=>_({},...[w,...g].map(x=>O(x,C,a)),E))}function*W(f){let b=(0,e.toPath)(f);if(b.length===0||(yield b,Array.isArray(f)))return;let w=/^(.*?)\s*\/\s*([^/]+)$/,g=f.match(w);if(g!==null){let[,C,a]=g,x=(0,e.toPath)(C);x.alpha=a,yield x}}function M(f){const b=(w,g)=>{for(const C of W(w)){let a=0,x=f;for(;x!=null&&a<C.length;)x=x[C[a++]],x=n(x)&&(C.alpha===void 0||a<=C.length-1)?x(b,l):x;if(x!==void 0){if(C.alpha!==void 0){let T=(0,i.parseColorFormat)(x);return(0,c.withAlphaValue)(T,C.alpha,(0,r.default)(T))}return(0,y.default)(x)?(0,t.cloneDeep)(x):x}}return g};return Object.assign(b,{theme:b,...l}),Object.keys(f).reduce((w,g)=>(w[g]=n(f[g])?f[g](b,l):f[g],w),{})}function L(f){let b=[];return f.forEach(w=>{b=[...b,w];var g;const C=(g=w==null?void 0:w.plugins)!==null&&g!==void 0?g:[];C.length!==0&&C.forEach(a=>{a.__isOptionsFunction&&(a=a());var x;b=[...b,...L([(x=a==null?void 0:a.config)!==null&&x!==void 0?x:{}])]})}),b}function p(f){return[...f].reduceRight((w,g)=>n(g)?g({corePlugins:w}):(0,j.default)(g,w),u.default)}function S(f){return[...f].reduceRight((w,g)=>[...w,...g],[])}function A(f){let b=[...L(f),{prefix:"",important:!1,separator:":"}];var w,g;return(0,h.normalizeConfig)((0,m.defaults)({theme:M(R($(b.map(C=>(w=C==null?void 0:C.theme)!==null&&w!==void 0?w:{})))),corePlugins:p(b.map(C=>C.corePlugins)),plugins:S(f.map(C=>(g=C==null?void 0:C.plugins)!==null&&g!==void 0?g:[]))},...b))}})(ae);var xe={};(function(o){Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"default",{enumerable:!0,get:function(){return v}});const P=j(Ce),u=I;function j(m){return m&&m.__esModule?m:{default:m}}function v(m){var e;const h=((e=m==null?void 0:m.presets)!==null&&e!==void 0?e:[P.default]).slice().reverse().flatMap(i=>v(i instanceof Function?i():i)),y={respectDefaultRingColorOpacity:{theme:{ringColor:({theme:i})=>({DEFAULT:"#3b82f67f",...i("colors")})}},disableColorOpacityUtilitiesByDefault:{corePlugins:{backgroundOpacity:!1,borderOpacity:!1,divideOpacity:!1,placeholderOpacity:!1,ringOpacity:!1,textOpacity:!1}}},t=Object.keys(y).filter(i=>(0,u.flagEnabled)(m,i)).map(i=>y[i]);return[m,...t,...h]}})(xe);(function(o){Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"default",{enumerable:!0,get:function(){return v}});const P=j(ae),u=j(xe);function j(m){return m&&m.__esModule?m:{default:m}}function v(...m){let[,...e]=(0,u.default)(m[0]);return(0,P.default)([...m,...e])}})(le);let K=le;var je=(K.__esModule?K:{default:K}).default;const ke=Oe(je);var $e=1,Te=12;function we(o){return Math.round(o*1e6)/1e6}function ee(o){return o.startsWith(".")&&(o=`0${o}`),Number(o).toString()===o}function Ee(o){return`${o.charAt(0).toUpperCase()}${o.slice(1).toLowerCase()}`}function te(o,P=Te){return we(P*o)}function Re(o){return we($e*o)}var We=["zIndex","top","right","bottom","left","translate","scale","rotate","order","margin"];function re(o){return typeof o=="string"&&We.includes(o)}var De=["borderRadius","borderWidth","flexBasis","fontFamily","fontSize","fontWeight","gap","height","inset","letterSpacing","lineHeight","margin","maxHeight","maxWidth","minHeight","minWidth","objectPosition","opacity","order","padding","rotate","scale","textIndent","transformOrigin","translate","width","zIndex"];function Fe(o){return typeof o=="string"&&De.includes(o)}var ne={flex:{display:"flex"},hidden:{display:"none"},"object-contain":{objectFit:"contain"},"object-cover":{objectFit:"cover"},"object-fill":{objectFit:"fill"},"object-none":{objectFit:"none"},"object-scale-down":{objectFit:"scale-down"},"overflow-hidden":{overflow:"hidden"},absolute:{position:"absolute"},relative:{position:"relative"},"flex-row":{flexDirection:"row"},"flex-row-reverse":{flexDirection:"row-reverse"},"flex-col":{flexDirection:"column"},"flex-col-reverse":{flexDirection:"column-reverse"},"flex-wrap":{flexWrap:"wrap"},"flex-wrap-reverse":{flexWrap:"wrap-reverse"},"flex-nowrap":{flexWrap:"nowrap"},"flex-1":{flex:"1 1 0%"},"flex-auto":{flex:"1 1 auto"},"flex-initial":{flex:"0 1 auto"},"flex-none":{flex:"none"},grow:{flexGrow:1},"grow-0":{flexGrow:0},shrink:{flexShrink:1},"shrink-0":{flexShrink:0},"justify-start":{justifyContent:"flex-start"},"justify-end":{justifyContent:"flex-end"},"justify-center":{justifyContent:"center"},"justify-between":{justifyContent:"space-between"},"justify-around":{justifyContent:"space-around"},"justify-evenly":{justifyContent:"space-evenly"},"content-start":{alignContent:"flex-start"},"content-end":{alignContent:"flex-end"},"content-center":{alignContent:"center"},"content-between":{alignContent:"space-between"},"content-around":{alignContent:"space-around"},"items-start":{alignItems:"flex-start"},"items-end":{alignItems:"flex-end"},"items-center":{alignItems:"center"},"items-baseline":{alignItems:"baseline"},"items-stretch":{alignItems:"stretch"},"self-auto":{alignSelf:"auto"},"self-start":{alignSelf:"flex-start"},"self-end":{alignSelf:"flex-end"},"self-center":{alignSelf:"center"},"self-baseline":{alignSelf:"baseline"},"self-stretch":{alignSelf:"stretch"},italic:{fontStyle:"italic"},"not-italic":{fontStyle:"normal"},"text-left":{textAlign:"left"},"text-center":{textAlign:"center"},"text-right":{textAlign:"right"},"text-justify":{textAlign:"justify"},underline:{textDecoration:"underline"},"decoration-solid":{textDecorationStyle:"solid"},"decoration-double":{textDecorationStyle:"double"},"decoration-dotted":{textDecorationStyle:"dotted"},"decoration-dashed":{textDecorationStyle:"dashed"},"decoration-wavy":{textDecorationStyle:"wavy"},"line-through":{textDecoration:"line-through"},"no-underline":{textDecoration:"none"},uppercase:{textTransform:"uppercase"},lowercase:{textTransform:"lowercase"},capitalize:{textTransform:"capitalize"},truncate:{overflow:"hidden",textOverflow:"ellipsis"},"text-ellipsis":{textOverflow:"ellipsis"},"bg-inherit":{backgroundColor:"inherit"},"bg-current":{backgroundColor:"currentColor"},"bg-transparent":{backgroundColor:"transparent"},"border-solid":{borderStyle:"solid"},"border-dashed":{borderStyle:"dashed"},"border-dotted":{borderStyle:"dotted"}},ie={object:"objectPosition",top:"top",right:"right",bottom:"bottom",left:"left",z:"zIndex",basis:"flexBasis",flex:"flex","gap-x":["gap","columnGap"],"gap-y":["gap","rowGap"],gap:"gap",grow:"flexGrow",shrink:"flexShrink",order:"order",m:"margin",mx:["margin",["marginLeft","marginRight"]],my:["margin",["marginTop","marginBottom"]],ml:["margin","marginLeft"],mr:["margin","marginRight"],mt:["margin","marginTop"],mb:["margin","marginBottom"],p:"padding",px:["padding",["paddingLeft","paddingRight"]],py:["padding",["paddingTop","paddingBottom"]],pl:["padding","paddingLeft"],pr:["padding","paddingRight"],pt:["padding","paddingTop"],pb:["padding","paddingBottom"],w:"width","min-w":"minWidth","max-w":"maxWidth",h:"height","min-h":"minHeight","max-h":"maxHeight",leading:"lineHeight",tracking:"letterSpacing",indent:"textIndent",bg:"backgroundColor",opacity:"opacity",origin:"transformOrigin"};function Me(o,P){const j=ke({content:["./dummy/path.js"],theme:o.theme??{}}).theme,v={};function m(i,c,r){if(i===void 0)return;const s=r?-1:1;if(typeof i=="number")return s*i;switch(c){case"lineHeight":return i.endsWith("rem")?s*Number(i.replace("rem","")):s*Number(i);default:if(i.endsWith("px"))return Re(s*Number(i.replace("px","")));if(i.endsWith("rem"))return te(s*Number(i.replace("rem","")),P==null?void 0:P.ptPerRem);if(i.endsWith("em"))return te(s*Number(i.replace("em","")),P==null?void 0:P.ptPerRem);if(r&&c&&re(c)){const n=["deg","%"].find(_=>i.endsWith(_));if(n)return`${s*Number(i.replace(n,""))}${n}`}return ee(i)?s*Number(i):i}}function e(i){if(i.startsWith("[")&&i.endsWith("]"))return i.slice(1,i.length-1).replaceAll("_"," ")}function h(i,c,r){const s=i.split("-"),n=e(i);if(n)return["#","rgb","hsl"].some(l=>n.startsWith(l))?{value:n,type:"color",isCustom:!0}:["px","rem"].some(l=>n.endsWith(l))?{value:m(n,c,r),type:"unit",isCustom:!0}:{value:m(n,c,r),type:"other",isCustom:!0};if(s[0]&&s[0]in j.colors&&c!=="fontWeight"){const l=j.colors[s[0]];return{value:typeof l=="string"?l:s[1]?l==null?void 0:l[s[1]]:void 0,type:"color",isCustom:!1,additionalProperties:void 0}}if(s.length===0||!c)return{value:void 0};const _=["top","right","bottom","left"].includes(c)?"inset":c;if(Fe(_)){const l=j[_][i];if(Array.isArray(l)){const O=l[1]&&l[1]!==null&&typeof l[1]=="object"?Object.fromEntries(Object.entries(l[1]).map(([d,$])=>[d,m($,d)])):null;return{value:m(l[0],c,r),type:"unit",isCustom:!1,...O?{additionalProperties:O}:null}}return{value:m(l,c,r),type:"unit",isCustom:!1}}return{value:void 0}}function y(i){const c=i.split(":"),r=c[c.length-1];if(r&&r in ne)return ne[r];const s=r?r.startsWith("-"):!1,n=r?r.slice(s?1:0).split("-"):[],_=Object.keys(ie).find(l=>{const O=l.split("-"),d=n.slice(0,O.length).join("-");return l===d});if(_){const l=i.split(`${_}-`)[1],O=ie[_],d=Array.isArray(O)?O[0]:O,$=Array.isArray(O)?Array.isArray(O[1])?O[1]:[O[1]]:[O];if(!l||s&&!re(d))return;const{value:E,additionalProperties:R}=h(l,d,s);return{...Object.fromEntries($.map(W=>[W,E])),...R??null}}switch(n[0]){case"inset":{const l=["x","y"].find($=>$===n[1]),O=n.slice(l?2:1).join("-"),{value:d}=h(O,"inset",s);switch(l){case"x":return{left:d,right:d};case"y":return{top:d,bottom:d};default:return{top:d,right:d,bottom:d,left:d}}}case"font":{const l=n.slice(1).join("-"),O=e(l);if(O)return ee(O)?{fontWeight:parseInt(O)}:{fontFamily:O};if(j.fontFamily&&l in j.fontFamily){const{value:$}=h(l,"fontFamily");return{fontFamily:$}}const{value:d}=h(l,"fontWeight");return{fontWeight:d}}case"text":{const l=n.slice(1).join("-"),{value:O,additionalProperties:d,type:$}=h(l,"fontSize");return $==="color"?{color:O}:{fontSize:O,...d}}case"decoration":{const l=n.slice(1).join("-"),{value:O,type:d}=h(l,"textDecorationColor");return d==="color"?{textDecorationColor:O}:void 0}case"rounded":{const l=["t","r","b","l","tl","tr","br","bl"].find($=>$===n[1]),O=n.slice(l?2:1).join("-"),{value:d}=h(O||"DEFAULT","borderRadius");switch(l){case"t":return{borderTopLeftRadius:d,borderTopRightRadius:d};case"r":return{borderTopRightRadius:d,borderBottomRightRadius:d};case"b":return{borderBottomRightRadius:d,borderBottomLeftRadius:d};case"l":return{borderBottomLeftRadius:d,borderTopLeftRadius:d};case"tl":return{borderTopLeftRadius:d};case"tr":return{borderTopRightRadius:d};case"br":return{borderBottomRightRadius:d};case"bl":return{borderBottomLeftRadius:d};default:return{borderRadius:d}}}case"border":{const l=["x","y","t","r","b","l"].find(R=>R===n[1]),O=n.slice(l?2:1).join("-"),{value:d,type:$}=h(O||"DEFAULT","borderWidth"),E=Ee($==="color"?"color":"width");switch(l){case"x":return{[`borderLeft${E}`]:d,[`borderRight${E}`]:d};case"y":return{[`borderTop${E}`]:d,[`borderBottom${E}`]:d};case"t":return{[`borderTop${E}`]:d};case"r":return{[`borderRight${E}`]:d};case"b":return{[`borderBottom${E}`]:d};case"l":return{[`borderLeft${E}`]:d};default:return{[`border${E}`]:d}}}case"scale":{const l=["x","y"].find($=>$===n[1]),O=n.slice(l?2:1).join("-"),{value:d}=h(O,"scale",s);switch(l){case"x":return{transform:`scaleX(${d})`};case"y":return{transform:`scaleY(${d})`};default:return{transform:`scale(${d})`}}}case"rotate":{const{value:l}=h(n.slice(1).join("-"),"rotate",s);return{transform:`rotate(${l})`}}case"translate":{const l=["x","y"].find($=>$===n[1]),O=n.slice(l?2:1).join("-"),{value:d}=h(O,"translate",s);switch(l){case"x":return{transform:`translateX(${d})`};case"y":return{transform:`translateY(${d})`};default:return{transform:`translate(${d})`}}}}}function t(i){console.warn(`react-pdf-tailwind: Invalid class "${i}"`)}return function(i){return i.split(" ").map(r=>r.trim()).map(r=>{if(r in v)return v[r];const s=y(r);if(s&&Object.values(s).every(n=>typeof n<"u"))return v[r]=s,s;t(r)}).reduce((r,s)=>{if(!s)return r;if("transform"in s){const{transform:n,..._}=s;return{...r,...n?{transform:[r.transform??"",n].join(" ").trim()}:null,..._}}return{...r,...s}},{})}}export{Me as c};
