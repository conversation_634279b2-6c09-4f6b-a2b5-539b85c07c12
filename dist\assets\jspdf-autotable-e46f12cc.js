import{c as Q,d as K}from"./vendor-94843817.js";import{j as Z}from"./jspdf-0ca12480.js";var X={exports:{}};const V=Q(Z);/*!
 * 
 *               jsPDF AutoTable plugin v3.8.4
 *
 *               Copyright (c) 2024 <PERSON>, https://github.com/simonbengt<PERSON>/jsPDF-AutoTable
 *               Licensed under the MIT License.
 *               http://opensource.org/licenses/mit-license
 *
 */(function(J,q){(function(G,I){J.exports=I(function(){try{return V}catch{}}())})(typeof globalThis<"u"?globalThis:typeof K<"u"?K:typeof window<"u"?window:typeof self<"u"?self:K,function(N){return function(){var G={172:function(z,m){var T=this&&this.__extends||function(){var c=function(b,a){return c=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(w,y){w.__proto__=y}||function(w,y){for(var h in y)Object.prototype.hasOwnProperty.call(y,h)&&(w[h]=y[h])},c(b,a)};return function(b,a){if(typeof a!="function"&&a!==null)throw new TypeError("Class extends value "+String(a)+" is not a constructor or null");c(b,a);function w(){this.constructor=b}b.prototype=a===null?Object.create(a):(w.prototype=a.prototype,new w)}}();Object.defineProperty(m,"__esModule",{value:!0}),m.CellHookData=m.HookData=void 0;var x=function(){function c(b,a,w){this.table=a,this.pageNumber=a.pageNumber,this.pageCount=this.pageNumber,this.settings=a.settings,this.cursor=w,this.doc=b.getDocument()}return c}();m.HookData=x;var P=function(c){T(b,c);function b(a,w,y,h,r,t){var e=c.call(this,a,w,t)||this;return e.cell=y,e.row=h,e.column=r,e.section=h.section,e}return b}(x);m.CellHookData=P},340:function(z,m,T){Object.defineProperty(m,"__esModule",{value:!0});var x=T(4),P=T(136),c=T(744),b=T(776),a=T(664),w=T(972);function y(h){h.API.autoTable=function(){for(var r=[],t=0;t<arguments.length;t++)r[t]=arguments[t];var e;r.length===1?e=r[0]:(console.error("Use of deprecated autoTable initiation"),e=r[2]||{},e.columns=r[0],e.body=r[1]);var l=(0,b.parseInput)(this,e),f=(0,w.createTable)(this,l);return(0,a.drawTable)(this,f),this},h.API.lastAutoTable=!1,h.API.previousAutoTable=!1,h.API.autoTable.previous=!1,h.API.autoTableText=function(r,t,e,l){(0,P.default)(r,t,e,l,this)},h.API.autoTableSetDefaults=function(r){return c.DocHandler.setDefaults(r,this),this},h.autoTableSetDefaults=function(r,t){c.DocHandler.setDefaults(r,t)},h.API.autoTableHtmlToJson=function(r,t){var e;if(t===void 0&&(t=!1),typeof window>"u")return console.error("Cannot run autoTableHtmlToJson in non browser environment"),null;var l=new c.DocHandler(this),f=(0,x.parseHtml)(l,r,window,t,!1),n=f.head,s=f.body,v=((e=n[0])===null||e===void 0?void 0:e.map(function(p){return p.content}))||[];return{columns:v,rows:s,data:s}},h.API.autoTableEndPosY=function(){console.error("Use of deprecated function: autoTableEndPosY. Use doc.lastAutoTable.finalY instead.");var r=this.lastAutoTable;return r&&r.finalY?r.finalY:0},h.API.autoTableAddPageContent=function(r){return console.error("Use of deprecated function: autoTableAddPageContent. Use jsPDF.autoTableSetDefaults({didDrawPage: () => {}}) instead."),h.API.autoTable.globalDefaults||(h.API.autoTable.globalDefaults={}),h.API.autoTable.globalDefaults.addPageContent=r,this},h.API.autoTableAddPage=function(){return console.error("Use of deprecated function: autoTableAddPage. Use doc.addPage()"),this.addPage(),this}}m.default=y},136:function(z,m){Object.defineProperty(m,"__esModule",{value:!0});function T(x,P,c,b,a){b=b||{};var w=1.15,y=a.internal.scaleFactor,h=a.internal.getFontSize()/y,r=a.getLineHeightFactor?a.getLineHeightFactor():w,t=h*r,e=/\r\n|\r|\n/g,l="",f=1;if((b.valign==="middle"||b.valign==="bottom"||b.halign==="center"||b.halign==="right")&&(l=typeof x=="string"?x.split(e):x,f=l.length||1),c+=h*(2-w),b.valign==="middle"?c-=f/2*t:b.valign==="bottom"&&(c-=f*t),b.halign==="center"||b.halign==="right"){var n=h;if(b.halign==="center"&&(n*=.5),l&&f>=1){for(var s=0;s<l.length;s++)a.text(l[s],P-a.getStringUnitWidth(l[s])*n,c),c+=t;return a}P-=a.getStringUnitWidth(x)*n}return b.halign==="justify"?a.text(x,P,c,{maxWidth:b.maxWidth||100,align:"justify"}):a.text(x,P,c),a}m.default=T},420:function(z,m){Object.defineProperty(m,"__esModule",{value:!0}),m.getPageAvailableWidth=m.parseSpacing=m.getFillStyle=m.addTableBorder=m.getStringWidth=void 0;function T(a,w,y){y.applyStyles(w,!0);var h=Array.isArray(a)?a:[a],r=h.map(function(t){return y.getTextWidth(t)}).reduce(function(t,e){return Math.max(t,e)},0);return r}m.getStringWidth=T;function x(a,w,y,h){var r=w.settings.tableLineWidth,t=w.settings.tableLineColor;a.applyStyles({lineWidth:r,lineColor:t});var e=P(r,!1);e&&a.rect(y.x,y.y,w.getWidth(a.pageSize().width),h.y-y.y,e)}m.addTableBorder=x;function P(a,w){var y=a>0,h=w||w===0;return y&&h?"DF":y?"S":h?"F":null}m.getFillStyle=P;function c(a,w){var y,h,r,t;if(a=a||w,Array.isArray(a)){if(a.length>=4)return{top:a[0],right:a[1],bottom:a[2],left:a[3]};if(a.length===3)return{top:a[0],right:a[1],bottom:a[2],left:a[1]};if(a.length===2)return{top:a[0],right:a[1],bottom:a[0],left:a[1]};a.length===1?a=a[0]:a=w}return typeof a=="object"?(typeof a.vertical=="number"&&(a.top=a.vertical,a.bottom=a.vertical),typeof a.horizontal=="number"&&(a.right=a.horizontal,a.left=a.horizontal),{left:(y=a.left)!==null&&y!==void 0?y:w,top:(h=a.top)!==null&&h!==void 0?h:w,right:(r=a.right)!==null&&r!==void 0?r:w,bottom:(t=a.bottom)!==null&&t!==void 0?t:w}):(typeof a!="number"&&(a=w),{top:a,right:a,bottom:a,left:a})}m.parseSpacing=c;function b(a,w){var y=c(w.settings.margin,0);return a.pageSize().width-(y.left+y.right)}m.getPageAvailableWidth=b},796:function(z,m){var T=this&&this.__extends||function(){var b=function(a,w){return b=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(y,h){y.__proto__=h}||function(y,h){for(var r in h)Object.prototype.hasOwnProperty.call(h,r)&&(y[r]=h[r])},b(a,w)};return function(a,w){if(typeof w!="function"&&w!==null)throw new TypeError("Class extends value "+String(w)+" is not a constructor or null");b(a,w);function y(){this.constructor=a}a.prototype=w===null?Object.create(w):(y.prototype=w.prototype,new y)}}();Object.defineProperty(m,"__esModule",{value:!0}),m.getTheme=m.defaultStyles=m.HtmlRowInput=void 0;var x=function(b){T(a,b);function a(w){var y=b.call(this)||this;return y._element=w,y}return a}(Array);m.HtmlRowInput=x;function P(b){return{font:"helvetica",fontStyle:"normal",overflow:"linebreak",fillColor:!1,textColor:20,halign:"left",valign:"top",fontSize:10,cellPadding:5/b,lineColor:200,lineWidth:0,cellWidth:"auto",minCellHeight:0,minCellWidth:0}}m.defaultStyles=P;function c(b){var a={striped:{table:{fillColor:255,textColor:80,fontStyle:"normal"},head:{textColor:255,fillColor:[41,128,185],fontStyle:"bold"},body:{},foot:{textColor:255,fillColor:[41,128,185],fontStyle:"bold"},alternateRow:{fillColor:245}},grid:{table:{fillColor:255,textColor:80,fontStyle:"normal",lineWidth:.1},head:{textColor:255,fillColor:[26,188,156],fontStyle:"bold",lineWidth:0},body:{},foot:{textColor:255,fillColor:[26,188,156],fontStyle:"bold",lineWidth:0},alternateRow:{}},plain:{head:{fontStyle:"bold"},foot:{fontStyle:"bold"}}};return a[b]}m.getTheme=c},903:function(z,m,T){Object.defineProperty(m,"__esModule",{value:!0}),m.parseCss=void 0;var x=T(420);function P(y,h,r,t,e){var l={},f=1.3333333333333333,n=b(h,function(d){return e.getComputedStyle(d).backgroundColor});n!=null&&(l.fillColor=n);var s=b(h,function(d){return e.getComputedStyle(d).color});s!=null&&(l.textColor=s);var v=w(t,r);v&&(l.cellPadding=v);var p="borderTopColor",g=f*r,o=t.borderTopWidth;if(t.borderBottomWidth===o&&t.borderRightWidth===o&&t.borderLeftWidth===o){var C=(parseFloat(o)||0)/g;C&&(l.lineWidth=C)}else l.lineWidth={top:(parseFloat(t.borderTopWidth)||0)/g,right:(parseFloat(t.borderRightWidth)||0)/g,bottom:(parseFloat(t.borderBottomWidth)||0)/g,left:(parseFloat(t.borderLeftWidth)||0)/g},l.lineWidth.top||(l.lineWidth.right?p="borderRightColor":l.lineWidth.bottom?p="borderBottomColor":l.lineWidth.left&&(p="borderLeftColor"));var k=b(h,function(d){return e.getComputedStyle(d)[p]});k!=null&&(l.lineColor=k);var j=["left","right","center","justify"];j.indexOf(t.textAlign)!==-1&&(l.halign=t.textAlign),j=["middle","bottom","top"],j.indexOf(t.verticalAlign)!==-1&&(l.valign=t.verticalAlign);var u=parseInt(t.fontSize||"");isNaN(u)||(l.fontSize=u/f);var i=c(t);i&&(l.fontStyle=i);var S=(t.fontFamily||"").toLowerCase();return y.indexOf(S)!==-1&&(l.font=S),l}m.parseCss=P;function c(y){var h="";return(y.fontWeight==="bold"||y.fontWeight==="bolder"||parseInt(y.fontWeight)>=700)&&(h="bold"),(y.fontStyle==="italic"||y.fontStyle==="oblique")&&(h+="italic"),h}function b(y,h){var r=a(y,h);if(!r)return null;var t=r.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d*\.?\d*))?\)$/);if(!t||!Array.isArray(t))return null;var e=[parseInt(t[1]),parseInt(t[2]),parseInt(t[3])],l=parseInt(t[4]);return l===0||isNaN(e[0])||isNaN(e[1])||isNaN(e[2])?null:e}function a(y,h){var r=h(y);return r==="rgba(0, 0, 0, 0)"||r==="transparent"||r==="initial"||r==="inherit"?y.parentElement==null?null:a(y.parentElement,h):r}function w(y,h){var r=[y.paddingTop,y.paddingRight,y.paddingBottom,y.paddingLeft],t=96/(72/h),e=(parseInt(y.lineHeight)-parseInt(y.fontSize))/h/2,l=r.map(function(n){return parseInt(n||"0")/t}),f=(0,x.parseSpacing)(l,0);return e>f.top&&(f.top=e),e>f.bottom&&(f.bottom=e),f}},744:function(z,m){Object.defineProperty(m,"__esModule",{value:!0}),m.DocHandler=void 0;var T={},x=function(){function P(c){this.jsPDFDocument=c,this.userStyles={textColor:c.getTextColor?this.jsPDFDocument.getTextColor():0,fontSize:c.internal.getFontSize(),fontStyle:c.internal.getFont().fontStyle,font:c.internal.getFont().fontName,lineWidth:c.getLineWidth?this.jsPDFDocument.getLineWidth():0,lineColor:c.getDrawColor?this.jsPDFDocument.getDrawColor():0}}return P.setDefaults=function(c,b){b===void 0&&(b=null),b?b.__autoTableDocumentDefaults=c:T=c},P.unifyColor=function(c){return Array.isArray(c)?c:typeof c=="number"?[c,c,c]:typeof c=="string"?[c]:null},P.prototype.applyStyles=function(c,b){var a,w,y;b===void 0&&(b=!1),c.fontStyle&&this.jsPDFDocument.setFontStyle&&this.jsPDFDocument.setFontStyle(c.fontStyle);var h=this.jsPDFDocument.internal.getFont(),r=h.fontStyle,t=h.fontName;if(c.font&&(t=c.font),c.fontStyle){r=c.fontStyle;var e=this.getFontList()[t];e&&e.indexOf(r)===-1&&(this.jsPDFDocument.setFontStyle&&this.jsPDFDocument.setFontStyle(e[0]),r=e[0])}if(this.jsPDFDocument.setFont(t,r),c.fontSize&&this.jsPDFDocument.setFontSize(c.fontSize),!b){var l=P.unifyColor(c.fillColor);l&&(a=this.jsPDFDocument).setFillColor.apply(a,l),l=P.unifyColor(c.textColor),l&&(w=this.jsPDFDocument).setTextColor.apply(w,l),l=P.unifyColor(c.lineColor),l&&(y=this.jsPDFDocument).setDrawColor.apply(y,l),typeof c.lineWidth=="number"&&this.jsPDFDocument.setLineWidth(c.lineWidth)}},P.prototype.splitTextToSize=function(c,b,a){return this.jsPDFDocument.splitTextToSize(c,b,a)},P.prototype.rect=function(c,b,a,w,y){return this.jsPDFDocument.rect(c,b,a,w,y)},P.prototype.getLastAutoTable=function(){return this.jsPDFDocument.lastAutoTable||null},P.prototype.getTextWidth=function(c){return this.jsPDFDocument.getTextWidth(c)},P.prototype.getDocument=function(){return this.jsPDFDocument},P.prototype.setPage=function(c){this.jsPDFDocument.setPage(c)},P.prototype.addPage=function(){return this.jsPDFDocument.addPage()},P.prototype.getFontList=function(){return this.jsPDFDocument.getFontList()},P.prototype.getGlobalOptions=function(){return T||{}},P.prototype.getDocumentOptions=function(){return this.jsPDFDocument.__autoTableDocumentDefaults||{}},P.prototype.pageSize=function(){var c=this.jsPDFDocument.internal.pageSize;return c.width==null&&(c={width:c.getWidth(),height:c.getHeight()}),c},P.prototype.scaleFactor=function(){return this.jsPDFDocument.internal.scaleFactor},P.prototype.getLineHeightFactor=function(){var c=this.jsPDFDocument;return c.getLineHeightFactor?c.getLineHeightFactor():1.15},P.prototype.getLineHeight=function(c){return c/this.scaleFactor()*this.getLineHeightFactor()},P.prototype.pageNumber=function(){var c=this.jsPDFDocument.internal.getCurrentPageInfo();return c?c.pageNumber:this.jsPDFDocument.internal.getNumberOfPages()},P}();m.DocHandler=x},4:function(z,m,T){Object.defineProperty(m,"__esModule",{value:!0}),m.parseHtml=void 0;var x=T(903),P=T(796);function c(w,y,h,r,t){var e,l;r===void 0&&(r=!1),t===void 0&&(t=!1);var f;typeof y=="string"?f=h.document.querySelector(y):f=y;var n=Object.keys(w.getFontList()),s=w.scaleFactor(),v=[],p=[],g=[];if(!f)return console.error("Html table could not be found with input: ",y),{head:v,body:p,foot:g};for(var o=0;o<f.rows.length;o++){var C=f.rows[o],k=(l=(e=C==null?void 0:C.parentElement)===null||e===void 0?void 0:e.tagName)===null||l===void 0?void 0:l.toLowerCase(),j=b(n,s,h,C,r,t);j&&(k==="thead"?v.push(j):k==="tfoot"?g.push(j):p.push(j))}return{head:v,body:p,foot:g}}m.parseHtml=c;function b(w,y,h,r,t,e){for(var l=new P.HtmlRowInput(r),f=0;f<r.cells.length;f++){var n=r.cells[f],s=h.getComputedStyle(n);if(t||s.display!=="none"){var v=void 0;e&&(v=(0,x.parseCss)(w,n,y,s,h)),l.push({rowSpan:n.rowSpan,colSpan:n.colSpan,styles:v,_element:n,content:a(n)})}}var p=h.getComputedStyle(r);if(l.length>0&&(t||p.display!=="none"))return l}function a(w){var y=w.cloneNode(!0);return y.innerHTML=y.innerHTML.replace(/\n/g,"").replace(/ +/g," "),y.innerHTML=y.innerHTML.split(/<br.*?>/).map(function(h){return h.trim()}).join(`
`),y.innerText||y.textContent||""}},776:function(z,m,T){Object.defineProperty(m,"__esModule",{value:!0}),m.parseInput=void 0;var x=T(4),P=T(356),c=T(420),b=T(744),a=T(792);function w(f,n){var s=new b.DocHandler(f),v=s.getDocumentOptions(),p=s.getGlobalOptions();(0,a.default)(s,p,v,n);var g=(0,P.assign)({},p,v,n),o;typeof window<"u"&&(o=window);var C=y(p,v,n),k=h(p,v,n),j=r(s,g),u=e(s,g,o);return{id:n.tableId,content:u,hooks:k,styles:C,settings:j}}m.parseInput=w;function y(f,n,s){for(var v={styles:{},headStyles:{},bodyStyles:{},footStyles:{},alternateRowStyles:{},columnStyles:{}},p=function(k){if(k==="columnStyles"){var j=f[k],u=n[k],i=s[k];v.columnStyles=(0,P.assign)({},j,u,i)}else{var S=[f,n,s],d=S.map(function(W){return W[k]||{}});v[k]=(0,P.assign)({},d[0],d[1],d[2])}},g=0,o=Object.keys(v);g<o.length;g++){var C=o[g];p(C)}return v}function h(f,n,s){for(var v=[f,n,s],p={didParseCell:[],willDrawCell:[],didDrawCell:[],willDrawPage:[],didDrawPage:[]},g=0,o=v;g<o.length;g++){var C=o[g];C.didParseCell&&p.didParseCell.push(C.didParseCell),C.willDrawCell&&p.willDrawCell.push(C.willDrawCell),C.didDrawCell&&p.didDrawCell.push(C.didDrawCell),C.willDrawPage&&p.willDrawPage.push(C.willDrawPage),C.didDrawPage&&p.didDrawPage.push(C.didDrawPage)}return p}function r(f,n){var s,v,p,g,o,C,k,j,u,i,S,d,W=(0,c.parseSpacing)(n.margin,40/f.scaleFactor()),H=(s=t(f,n.startY))!==null&&s!==void 0?s:W.top,D;n.showFoot===!0?D="everyPage":n.showFoot===!1?D="never":D=(v=n.showFoot)!==null&&v!==void 0?v:"everyPage";var F;n.showHead===!0?F="everyPage":n.showHead===!1?F="never":F=(p=n.showHead)!==null&&p!==void 0?p:"everyPage";var R=(g=n.useCss)!==null&&g!==void 0?g:!1,A=n.theme||(R?"plain":"striped"),_=!!n.horizontalPageBreak,M=(o=n.horizontalPageBreakRepeat)!==null&&o!==void 0?o:null;return{includeHiddenHtml:(C=n.includeHiddenHtml)!==null&&C!==void 0?C:!1,useCss:R,theme:A,startY:H,margin:W,pageBreak:(k=n.pageBreak)!==null&&k!==void 0?k:"auto",rowPageBreak:(j=n.rowPageBreak)!==null&&j!==void 0?j:"auto",tableWidth:(u=n.tableWidth)!==null&&u!==void 0?u:"auto",showHead:F,showFoot:D,tableLineWidth:(i=n.tableLineWidth)!==null&&i!==void 0?i:0,tableLineColor:(S=n.tableLineColor)!==null&&S!==void 0?S:200,horizontalPageBreak:_,horizontalPageBreakRepeat:M,horizontalPageBreakBehaviour:(d=n.horizontalPageBreakBehaviour)!==null&&d!==void 0?d:"afterAllRows"}}function t(f,n){var s=f.getLastAutoTable(),v=f.scaleFactor(),p=f.pageNumber(),g=!1;if(s&&s.startPageNumber){var o=s.startPageNumber+s.pageNumber-1;g=o===p}return typeof n=="number"?n:(n==null||n===!1)&&g&&(s==null?void 0:s.finalY)!=null?s.finalY+20/v:null}function e(f,n,s){var v=n.head||[],p=n.body||[],g=n.foot||[];if(n.html){var o=n.includeHiddenHtml;if(s){var C=(0,x.parseHtml)(f,n.html,s,o,n.useCss)||{};v=C.head||v,p=C.body||v,g=C.foot||v}else console.error("Cannot parse html in non browser environment")}var k=n.columns||l(v,p,g);return{columns:k,head:v,body:p,foot:g}}function l(f,n,s){var v=f[0]||n[0]||s[0]||[],p=[];return Object.keys(v).filter(function(g){return g!=="_element"}).forEach(function(g){var o=1,C;Array.isArray(v)?C=v[parseInt(g)]:C=v[g],typeof C=="object"&&!Array.isArray(C)&&(o=(C==null?void 0:C.colSpan)||1);for(var k=0;k<o;k++){var j=void 0;Array.isArray(v)?j=p.length:j=g+(k>0?"_".concat(k):"");var u={dataKey:j};p.push(u)}}),p}},792:function(z,m){Object.defineProperty(m,"__esModule",{value:!0});function T(P,c,b,a){for(var w=function(t){t&&typeof t!="object"&&console.error("The options parameter should be of type object, is: "+typeof t),typeof t.extendWidth<"u"&&(t.tableWidth=t.extendWidth?"auto":"wrap",console.error("Use of deprecated option: extendWidth, use tableWidth instead.")),typeof t.margins<"u"&&(typeof t.margin>"u"&&(t.margin=t.margins),console.error("Use of deprecated option: margins, use margin instead.")),t.startY&&typeof t.startY!="number"&&(console.error("Invalid value for startY option",t.startY),delete t.startY),!t.didDrawPage&&(t.afterPageContent||t.beforePageContent||t.afterPageAdd)&&(console.error("The afterPageContent, beforePageContent and afterPageAdd hooks are deprecated. Use didDrawPage instead"),t.didDrawPage=function(g){P.applyStyles(P.userStyles),t.beforePageContent&&t.beforePageContent(g),P.applyStyles(P.userStyles),t.afterPageContent&&t.afterPageContent(g),P.applyStyles(P.userStyles),t.afterPageAdd&&g.pageNumber>1&&g.afterPageAdd(g),P.applyStyles(P.userStyles)}),["createdHeaderCell","drawHeaderRow","drawRow","drawHeaderCell"].forEach(function(g){t[g]&&console.error('The "'.concat(g,'" hook has changed in version 3.0, check the changelog for how to migrate.'))}),[["showFoot","showFooter"],["showHead","showHeader"],["didDrawPage","addPageContent"],["didParseCell","createdCell"],["headStyles","headerStyles"]].forEach(function(g){var o=g[0],C=g[1];t[C]&&(console.error("Use of deprecated option ".concat(C,". Use ").concat(o," instead")),t[o]=t[C])}),[["padding","cellPadding"],["lineHeight","rowHeight"],"fontSize","overflow"].forEach(function(g){var o=typeof g=="string"?g:g[0],C=typeof g=="string"?g:g[1];typeof t[o]<"u"&&(typeof t.styles[C]>"u"&&(t.styles[C]=t[o]),console.error("Use of deprecated option: "+o+", use the style "+C+" instead."))});for(var e=0,l=["styles","bodyStyles","headStyles","footStyles"];e<l.length;e++){var f=l[e];x(t[f]||{})}for(var n=t.columnStyles||{},s=0,v=Object.keys(n);s<v.length;s++){var p=v[s];x(n[p]||{})}},y=0,h=[c,b,a];y<h.length;y++){var r=h[y];w(r)}}m.default=T;function x(P){P.rowHeight?(console.error("Use of deprecated style rowHeight. It is renamed to minCellHeight."),P.minCellHeight||(P.minCellHeight=P.rowHeight)):P.columnWidth&&(console.error("Use of deprecated style columnWidth. It is renamed to cellWidth."),P.cellWidth||(P.cellWidth=P.columnWidth))}},260:function(z,m,T){Object.defineProperty(m,"__esModule",{value:!0}),m.Column=m.Cell=m.Row=m.Table=void 0;var x=T(796),P=T(172),c=T(420),b=function(){function h(r,t){this.pageNumber=1,this.pageCount=1,this.id=r.id,this.settings=r.settings,this.styles=r.styles,this.hooks=r.hooks,this.columns=t.columns,this.head=t.head,this.body=t.body,this.foot=t.foot}return h.prototype.getHeadHeight=function(r){return this.head.reduce(function(t,e){return t+e.getMaxCellHeight(r)},0)},h.prototype.getFootHeight=function(r){return this.foot.reduce(function(t,e){return t+e.getMaxCellHeight(r)},0)},h.prototype.allRows=function(){return this.head.concat(this.body).concat(this.foot)},h.prototype.callCellHooks=function(r,t,e,l,f,n){for(var s=0,v=t;s<v.length;s++){var p=v[s],g=new P.CellHookData(r,this,e,l,f,n),o=p(g)===!1;if(e.text=Array.isArray(e.text)?e.text:[e.text],o)return!1}return!0},h.prototype.callEndPageHooks=function(r,t){r.applyStyles(r.userStyles);for(var e=0,l=this.hooks.didDrawPage;e<l.length;e++){var f=l[e];f(new P.HookData(r,this,t))}},h.prototype.callWillDrawPageHooks=function(r,t){for(var e=0,l=this.hooks.willDrawPage;e<l.length;e++){var f=l[e];f(new P.HookData(r,this,t))}},h.prototype.getWidth=function(r){if(typeof this.settings.tableWidth=="number")return this.settings.tableWidth;if(this.settings.tableWidth==="wrap"){var t=this.columns.reduce(function(l,f){return l+f.wrappedWidth},0);return t}else{var e=this.settings.margin;return r-e.left-e.right}},h}();m.Table=b;var a=function(){function h(r,t,e,l,f){f===void 0&&(f=!1),this.height=0,this.raw=r,r instanceof x.HtmlRowInput&&(this.raw=r._element,this.element=r._element),this.index=t,this.section=e,this.cells=l,this.spansMultiplePages=f}return h.prototype.getMaxCellHeight=function(r){var t=this;return r.reduce(function(e,l){var f;return Math.max(e,((f=t.cells[l.index])===null||f===void 0?void 0:f.height)||0)},0)},h.prototype.hasRowSpan=function(r){var t=this;return r.filter(function(e){var l=t.cells[e.index];return l?l.rowSpan>1:!1}).length>0},h.prototype.canEntireRowFit=function(r,t){return this.getMaxCellHeight(t)<=r},h.prototype.getMinimumRowHeight=function(r,t){var e=this;return r.reduce(function(l,f){var n=e.cells[f.index];if(!n)return 0;var s=t.getLineHeight(n.styles.fontSize),v=n.padding("vertical"),p=v+s;return p>l?p:l},0)},h}();m.Row=a;var w=function(){function h(r,t,e){var l,f;this.contentHeight=0,this.contentWidth=0,this.wrappedWidth=0,this.minReadableWidth=0,this.minWidth=0,this.width=0,this.height=0,this.x=0,this.y=0,this.styles=t,this.section=e,this.raw=r;var n=r;r!=null&&typeof r=="object"&&!Array.isArray(r)?(this.rowSpan=r.rowSpan||1,this.colSpan=r.colSpan||1,n=(f=(l=r.content)!==null&&l!==void 0?l:r.title)!==null&&f!==void 0?f:r,r._element&&(this.raw=r._element)):(this.rowSpan=1,this.colSpan=1);var s=n!=null?""+n:"",v=/\r\n|\r|\n/g;this.text=s.split(v)}return h.prototype.getTextPos=function(){var r;if(this.styles.valign==="top")r=this.y+this.padding("top");else if(this.styles.valign==="bottom")r=this.y+this.height-this.padding("bottom");else{var t=this.height-this.padding("vertical");r=this.y+t/2+this.padding("top")}var e;if(this.styles.halign==="right")e=this.x+this.width-this.padding("right");else if(this.styles.halign==="center"){var l=this.width-this.padding("horizontal");e=this.x+l/2+this.padding("left")}else e=this.x+this.padding("left");return{x:e,y:r}},h.prototype.getContentHeight=function(r,t){t===void 0&&(t=1.15);var e=Array.isArray(this.text)?this.text.length:1,l=this.styles.fontSize/r*t,f=e*l+this.padding("vertical");return Math.max(f,this.styles.minCellHeight)},h.prototype.padding=function(r){var t=(0,c.parseSpacing)(this.styles.cellPadding,0);return r==="vertical"?t.top+t.bottom:r==="horizontal"?t.left+t.right:t[r]},h}();m.Cell=w;var y=function(){function h(r,t,e){this.wrappedWidth=0,this.minReadableWidth=0,this.minWidth=0,this.width=0,this.dataKey=r,this.raw=t,this.index=e}return h.prototype.getMaxCustomCellWidth=function(r){for(var t=0,e=0,l=r.allRows();e<l.length;e++){var f=l[e],n=f.cells[this.index];n&&typeof n.styles.cellWidth=="number"&&(t=Math.max(t,n.styles.cellWidth))}return t},h}();m.Column=y},356:function(z,m){Object.defineProperty(m,"__esModule",{value:!0}),m.assign=void 0;function T(x,P,c,b,a){if(x==null)throw new TypeError("Cannot convert undefined or null to object");for(var w=Object(x),y=1;y<arguments.length;y++){var h=arguments[y];if(h!=null)for(var r in h)Object.prototype.hasOwnProperty.call(h,r)&&(w[r]=h[r])}return w}m.assign=T},972:function(z,m,T){Object.defineProperty(m,"__esModule",{value:!0}),m.createTable=void 0;var x=T(744),P=T(260),c=T(324),b=T(796),a=T(356);function w(f,n){var s=new x.DocHandler(f),v=y(n,s.scaleFactor()),p=new P.Table(n,v);return(0,c.calculateWidths)(s,p),s.applyStyles(s.userStyles),p}m.createTable=w;function y(f,n){var s=f.content,v=e(s.columns);if(s.head.length===0){var p=r(v,"head");p&&s.head.push(p)}if(s.foot.length===0){var p=r(v,"foot");p&&s.foot.push(p)}var g=f.settings.theme,o=f.styles;return{columns:v,head:h("head",s.head,v,o,g,n),body:h("body",s.body,v,o,g,n),foot:h("foot",s.foot,v,o,g,n)}}function h(f,n,s,v,p,g){var o={},C=n.map(function(k,j){for(var u=0,i={},S=0,d=0,W=0,H=s;W<H.length;W++){var D=H[W];if(o[D.index]==null||o[D.index].left===0)if(d===0){var F=void 0;Array.isArray(k)?F=k[D.index-S-u]:F=k[D.dataKey];var R={};typeof F=="object"&&!Array.isArray(F)&&(R=(F==null?void 0:F.styles)||{});var A=l(f,D,j,p,v,g,R),_=new P.Cell(F,A,f);i[D.dataKey]=_,i[D.index]=_,d=_.colSpan-1,o[D.index]={left:_.rowSpan-1,times:d}}else d--,S++;else o[D.index].left--,d=o[D.index].times,u++}return new P.Row(k,j,f,i)});return C}function r(f,n){var s={};return f.forEach(function(v){if(v.raw!=null){var p=t(n,v.raw);p!=null&&(s[v.dataKey]=p)}}),Object.keys(s).length>0?s:null}function t(f,n){if(f==="head"){if(typeof n=="object")return n.header||n.title||null;if(typeof n=="string"||typeof n=="number")return n}else if(f==="foot"&&typeof n=="object")return n.footer;return null}function e(f){return f.map(function(n,s){var v,p,g;return typeof n=="object"?g=(p=(v=n.dataKey)!==null&&v!==void 0?v:n.key)!==null&&p!==void 0?p:s:g=s,new P.Column(g,n,s)})}function l(f,n,s,v,p,g,o){var C=(0,b.getTheme)(v),k;f==="head"?k=p.headStyles:f==="body"?k=p.bodyStyles:f==="foot"&&(k=p.footStyles);var j=(0,a.assign)({},C.table,C[f],p.styles,k),u=p.columnStyles[n.dataKey]||p.columnStyles[n.index]||{},i=f==="body"?u:{},S=f==="body"&&s%2===0?(0,a.assign)({},C.alternateRow,p.alternateRowStyles):{},d=(0,b.defaultStyles)(g),W=(0,a.assign)({},d,j,S,i);return(0,a.assign)(W,o)}},664:function(z,m,T){Object.defineProperty(m,"__esModule",{value:!0}),m.addPage=m.drawTable=void 0;var x=T(420),P=T(260),c=T(744),b=T(356),a=T(136),w=T(224);function y(u,i){var S=i.settings,d=S.startY,W=S.margin,H={x:W.left,y:d},D=i.getHeadHeight(i.columns)+i.getFootHeight(i.columns),F=d+W.bottom+D;if(S.pageBreak==="avoid"){var R=i.body,A=R.reduce(function(B,L){return B+L.height},0);F+=A}var _=new c.DocHandler(u);(S.pageBreak==="always"||S.startY!=null&&F>_.pageSize().height)&&(j(_),H.y=W.top),i.callWillDrawPageHooks(_,H);var M=(0,b.assign)({},H);i.startPageNumber=_.pageNumber(),S.horizontalPageBreak?h(_,i,M,H):(_.applyStyles(_.userStyles),(S.showHead==="firstPage"||S.showHead==="everyPage")&&i.head.forEach(function(B){return p(_,i,B,H,i.columns)}),_.applyStyles(_.userStyles),i.body.forEach(function(B,L){var E=L===i.body.length-1;v(_,i,B,E,M,H,i.columns)}),_.applyStyles(_.userStyles),(S.showFoot==="lastPage"||S.showFoot==="everyPage")&&i.foot.forEach(function(B){return p(_,i,B,H,i.columns)})),(0,x.addTableBorder)(_,i,M,H),i.callEndPageHooks(_,H),i.finalY=H.y,u.lastAutoTable=i,u.previousAutoTable=i,u.autoTable&&(u.autoTable.previous=i),_.applyStyles(_.userStyles)}m.drawTable=y;function h(u,i,S,d){var W=(0,w.calculateAllColumnsCanFitInPage)(u,i),H=i.settings;if(H.horizontalPageBreakBehaviour==="afterAllRows")W.forEach(function(A,_){u.applyStyles(u.userStyles),_>0?k(u,i,S,d,A.columns,!0):r(u,i,d,A.columns),t(u,i,S,d,A.columns),l(u,i,d,A.columns)});else for(var D=-1,F=W[0],R=function(){var A=D;if(F){u.applyStyles(u.userStyles);var _=F.columns;D>=0?k(u,i,S,d,_,!0):r(u,i,d,_),A=e(u,i,D+1,d,_),l(u,i,d,_)}var M=A-D;W.slice(1).forEach(function(B){u.applyStyles(u.userStyles),k(u,i,S,d,B.columns,!0),e(u,i,D+1,d,B.columns,M),l(u,i,d,B.columns)}),D=A};D<i.body.length-1;)R()}function r(u,i,S,d){var W=i.settings;u.applyStyles(u.userStyles),(W.showHead==="firstPage"||W.showHead==="everyPage")&&i.head.forEach(function(H){return p(u,i,H,S,d)})}function t(u,i,S,d,W){u.applyStyles(u.userStyles),i.body.forEach(function(H,D){var F=D===i.body.length-1;v(u,i,H,F,S,d,W)})}function e(u,i,S,d,W,H){u.applyStyles(u.userStyles),H=H??i.body.length;var D=Math.min(S+H,i.body.length),F=-1;return i.body.slice(S,D).forEach(function(R,A){var _=S+A===i.body.length-1,M=C(u,i,_,d);R.canEntireRowFit(M,W)&&(p(u,i,R,d,W),F=S+A)}),F}function l(u,i,S,d){var W=i.settings;u.applyStyles(u.userStyles),(W.showFoot==="lastPage"||W.showFoot==="everyPage")&&i.foot.forEach(function(H){return p(u,i,H,S,d)})}function f(u,i,S){var d=S.getLineHeight(u.styles.fontSize),W=u.padding("vertical"),H=Math.floor((i-W)/d);return Math.max(0,H)}function n(u,i,S,d){var W={};u.spansMultiplePages=!0,u.height=0;for(var H=0,D=0,F=S.columns;D<F.length;D++){var R=F[D],A=u.cells[R.index];if(A){Array.isArray(A.text)||(A.text=[A.text]);var _=new P.Cell(A.raw,A.styles,A.section);_=(0,b.assign)(_,A),_.text=[];var M=f(A,i,d);A.text.length>M&&(_.text=A.text.splice(M,A.text.length));var B=d.scaleFactor(),L=d.getLineHeightFactor();A.contentHeight=A.getContentHeight(B,L),A.contentHeight>=i&&(A.contentHeight=i,_.styles.minCellHeight-=i),A.contentHeight>u.height&&(u.height=A.contentHeight),_.contentHeight=_.getContentHeight(B,L),_.contentHeight>H&&(H=_.contentHeight),W[R.index]=_}}var E=new P.Row(u.raw,-1,u.section,W,!0);E.height=H;for(var Y=0,$=S.columns;Y<$.length;Y++){var R=$[Y],_=E.cells[R.index];_&&(_.height=E.height);var A=u.cells[R.index];A&&(A.height=u.height)}return E}function s(u,i,S,d){var W=u.pageSize().height,H=d.settings.margin,D=H.top+H.bottom,F=W-D;i.section==="body"&&(F-=d.getHeadHeight(d.columns)+d.getFootHeight(d.columns));var R=i.getMinimumRowHeight(d.columns,u),A=R<S;if(R>F)return console.error("Will not be able to print row ".concat(i.index," correctly since it's minimum height is larger than page height")),!0;if(!A)return!1;var _=i.hasRowSpan(d.columns),M=i.getMaxCellHeight(d.columns)>F;return M?(_&&console.error("The content of row ".concat(i.index," will not be drawn correctly since drawing rows with a height larger than the page height and has cells with rowspans is not supported.")),!0):!(_||d.settings.rowPageBreak==="avoid")}function v(u,i,S,d,W,H,D){var F=C(u,i,d,H);if(S.canEntireRowFit(F,D))p(u,i,S,H,D);else if(s(u,S,F,i)){var R=n(S,F,i,u);p(u,i,S,H,D),k(u,i,W,H,D),v(u,i,R,d,W,H,D)}else k(u,i,W,H,D),v(u,i,S,d,W,H,D)}function p(u,i,S,d,W){d.x=i.settings.margin.left;for(var H=0,D=W;H<D.length;H++){var F=D[H],R=S.cells[F.index];if(!R){d.x+=F.width;continue}u.applyStyles(R.styles),R.x=d.x,R.y=d.y;var A=i.callCellHooks(u,i.hooks.willDrawCell,R,S,F,d);if(A===!1){d.x+=F.width;continue}g(u,R,d);var _=R.getTextPos();(0,a.default)(R.text,_.x,_.y,{halign:R.styles.halign,valign:R.styles.valign,maxWidth:Math.ceil(R.width-R.padding("left")-R.padding("right"))},u.getDocument()),i.callCellHooks(u,i.hooks.didDrawCell,R,S,F,d),d.x+=F.width}d.y+=S.height}function g(u,i,S){var d=i.styles;if(u.getDocument().setFillColor(u.getDocument().getFillColor()),typeof d.lineWidth=="number"){var W=(0,x.getFillStyle)(d.lineWidth,d.fillColor);W&&u.rect(i.x,S.y,i.width,i.height,W)}else typeof d.lineWidth=="object"&&(d.fillColor&&u.rect(i.x,S.y,i.width,i.height,"F"),o(u,i,S,d.lineWidth))}function o(u,i,S,d){var W,H,D,F;d.top&&(W=S.x,H=S.y,D=S.x+i.width,F=S.y,d.right&&(D+=.5*d.right),d.left&&(W-=.5*d.left),R(d.top,W,H,D,F)),d.bottom&&(W=S.x,H=S.y+i.height,D=S.x+i.width,F=S.y+i.height,d.right&&(D+=.5*d.right),d.left&&(W-=.5*d.left),R(d.bottom,W,H,D,F)),d.left&&(W=S.x,H=S.y,D=S.x,F=S.y+i.height,d.top&&(H-=.5*d.top),d.bottom&&(F+=.5*d.bottom),R(d.left,W,H,D,F)),d.right&&(W=S.x+i.width,H=S.y,D=S.x+i.width,F=S.y+i.height,d.top&&(H-=.5*d.top),d.bottom&&(F+=.5*d.bottom),R(d.right,W,H,D,F));function R(A,_,M,B,L){u.getDocument().setLineWidth(A),u.getDocument().line(_,M,B,L,"S")}}function C(u,i,S,d){var W=i.settings.margin.bottom,H=i.settings.showFoot;return(H==="everyPage"||H==="lastPage"&&S)&&(W+=i.getFootHeight(i.columns)),u.pageSize().height-d.y-W}function k(u,i,S,d,W,H){W===void 0&&(W=[]),H===void 0&&(H=!1),u.applyStyles(u.userStyles),i.settings.showFoot==="everyPage"&&!H&&i.foot.forEach(function(F){return p(u,i,F,d,W)}),i.callEndPageHooks(u,d);var D=i.settings.margin;(0,x.addTableBorder)(u,i,S,d),j(u),i.pageNumber++,i.pageCount++,d.x=D.left,d.y=D.top,S.y=D.top,i.callWillDrawPageHooks(u,d),i.settings.showHead==="everyPage"&&(i.head.forEach(function(F){return p(u,i,F,d,W)}),u.applyStyles(u.userStyles))}m.addPage=k;function j(u){var i=u.pageNumber();u.setPage(i+1);var S=u.pageNumber();return S===i?(u.addPage(),!0):!1}},224:function(z,m,T){Object.defineProperty(m,"__esModule",{value:!0}),m.calculateAllColumnsCanFitInPage=void 0;var x=T(420);function P(b,a,w){var y;w===void 0&&(w={});var h=(0,x.getPageAvailableWidth)(b,a),r=new Map,t=[],e=[],l=[];a.settings.horizontalPageBreakRepeat,Array.isArray(a.settings.horizontalPageBreakRepeat)?l=a.settings.horizontalPageBreakRepeat:(typeof a.settings.horizontalPageBreakRepeat=="string"||typeof a.settings.horizontalPageBreakRepeat=="number")&&(l=[a.settings.horizontalPageBreakRepeat]),l.forEach(function(v){var p=a.columns.find(function(g){return g.dataKey===v||g.index===v});p&&!r.has(p.index)&&(r.set(p.index,!0),t.push(p.index),e.push(a.columns[p.index]),h-=p.wrappedWidth)});for(var f=!0,n=(y=w==null?void 0:w.start)!==null&&y!==void 0?y:0;n<a.columns.length;){if(r.has(n)){n++;continue}var s=a.columns[n].wrappedWidth;if(f||h>=s)f=!1,t.push(n),e.push(a.columns[n]),h-=s;else break;n++}return{colIndexes:t,columns:e,lastIndex:n-1}}function c(b,a){for(var w=[],y=0;y<a.columns.length;y++){var h=P(b,a,{start:y});h.columns.length&&(w.push(h),y=h.lastIndex)}return w}m.calculateAllColumnsCanFitInPage=c},324:function(z,m,T){Object.defineProperty(m,"__esModule",{value:!0}),m.ellipsize=m.resizeColumns=m.calculateWidths=void 0;var x=T(420);function P(t,e){c(t,e);var l=[],f=0;e.columns.forEach(function(s){var v=s.getMaxCustomCellWidth(e);v?s.width=v:(s.width=s.wrappedWidth,l.push(s)),f+=s.width});var n=e.getWidth(t.pageSize().width)-f;n&&(n=b(l,n,function(s){return Math.max(s.minReadableWidth,s.minWidth)})),n&&(n=b(l,n,function(s){return s.minWidth})),n=Math.abs(n),!e.settings.horizontalPageBreak&&n>.1/t.scaleFactor()&&(n=n<1?n:Math.round(n),console.warn("Of the table content, ".concat(n," units width could not fit page"))),w(e),y(e,t),a(e)}m.calculateWidths=P;function c(t,e){var l=t.scaleFactor(),f=e.settings.horizontalPageBreak,n=(0,x.getPageAvailableWidth)(t,e);e.allRows().forEach(function(s){for(var v=0,p=e.columns;v<p.length;v++){var g=p[v],o=s.cells[g.index];if(o){var C=e.hooks.didParseCell;e.callCellHooks(t,C,o,s,g,null);var k=o.padding("horizontal");o.contentWidth=(0,x.getStringWidth)(o.text,o.styles,t)+k;var j=(0,x.getStringWidth)(o.text.join(" ").split(/[^\S\u00A0]+/),o.styles,t);if(o.minReadableWidth=j+o.padding("horizontal"),typeof o.styles.cellWidth=="number")o.minWidth=o.styles.cellWidth,o.wrappedWidth=o.styles.cellWidth;else if(o.styles.cellWidth==="wrap"||f===!0)o.contentWidth>n?(o.minWidth=n,o.wrappedWidth=n):(o.minWidth=o.contentWidth,o.wrappedWidth=o.contentWidth);else{var u=10/l;o.minWidth=o.styles.minCellWidth||u,o.wrappedWidth=o.contentWidth,o.minWidth>o.wrappedWidth&&(o.wrappedWidth=o.minWidth)}}}}),e.allRows().forEach(function(s){for(var v=0,p=e.columns;v<p.length;v++){var g=p[v],o=s.cells[g.index];if(o&&o.colSpan===1)g.wrappedWidth=Math.max(g.wrappedWidth,o.wrappedWidth),g.minWidth=Math.max(g.minWidth,o.minWidth),g.minReadableWidth=Math.max(g.minReadableWidth,o.minReadableWidth);else{var C=e.styles.columnStyles[g.dataKey]||e.styles.columnStyles[g.index]||{},k=C.cellWidth||C.minCellWidth;k&&typeof k=="number"&&(g.minWidth=k,g.wrappedWidth=k)}o&&(o.colSpan>1&&!g.minWidth&&(g.minWidth=o.minWidth),o.colSpan>1&&!g.wrappedWidth&&(g.wrappedWidth=o.minWidth))}})}function b(t,e,l){for(var f=e,n=t.reduce(function(u,i){return u+i.wrappedWidth},0),s=0;s<t.length;s++){var v=t[s],p=v.wrappedWidth/n,g=f*p,o=v.width+g,C=l(v),k=o<C?C:o;e-=k-v.width,v.width=k}if(e=Math.round(e*1e10)/1e10,e){var j=t.filter(function(u){return e<0?u.width>l(u):!0});j.length&&(e=b(j,e,l))}return e}m.resizeColumns=b;function a(t){for(var e={},l=1,f=t.allRows(),n=0;n<f.length;n++)for(var s=f[n],v=0,p=t.columns;v<p.length;v++){var g=p[v],o=e[g.index];if(l>1)l--,delete s.cells[g.index];else if(o)o.cell.height+=s.height,l=o.cell.colSpan,delete s.cells[g.index],o.left--,o.left<=1&&delete e[g.index];else{var C=s.cells[g.index];if(!C)continue;if(C.height=s.height,C.rowSpan>1){var k=f.length-n,j=C.rowSpan>k?k:C.rowSpan;e[g.index]={cell:C,left:j,row:s}}}}}function w(t){for(var e=t.allRows(),l=0;l<e.length;l++)for(var f=e[l],n=null,s=0,v=0,p=0;p<t.columns.length;p++){var g=t.columns[p];if(v-=1,v>1&&t.columns[p+1])s+=g.width,delete f.cells[g.index];else if(n){var o=n;delete f.cells[g.index],n=null,o.width=g.width+s}else{var o=f.cells[g.index];if(!o)continue;if(v=o.colSpan,s=0,o.colSpan>1){n=o,s+=g.width;continue}o.width=g.width+s}}}function y(t,e){for(var l={count:0,height:0},f=0,n=t.allRows();f<n.length;f++){for(var s=n[f],v=0,p=t.columns;v<p.length;v++){var g=p[v],o=s.cells[g.index];if(o){e.applyStyles(o.styles,!0);var C=o.width-o.padding("horizontal");if(o.styles.overflow==="linebreak")o.text=e.splitTextToSize(o.text,C+1/e.scaleFactor(),{fontSize:o.styles.fontSize});else if(o.styles.overflow==="ellipsize")o.text=h(o.text,C,o.styles,e,"...");else if(o.styles.overflow==="hidden")o.text=h(o.text,C,o.styles,e,"");else if(typeof o.styles.overflow=="function"){var k=o.styles.overflow(o.text,C);typeof k=="string"?o.text=[k]:o.text=k}o.contentHeight=o.getContentHeight(e.scaleFactor(),e.getLineHeightFactor());var j=o.contentHeight/o.rowSpan;o.rowSpan>1&&l.count*l.height<j*o.rowSpan?l={height:j,count:o.rowSpan}:l&&l.count>0&&l.height>j&&(j=l.height),j>s.height&&(s.height=j)}}l.count--}}function h(t,e,l,f,n){return t.map(function(s){return r(s,e,l,f,n)})}m.ellipsize=h;function r(t,e,l,f,n){var s=1e4*f.scaleFactor();if(e=Math.ceil(e*s)/s,e>=(0,x.getStringWidth)(t,l,f))return t;for(;e<(0,x.getStringWidth)(t+n,l,f)&&!(t.length<=1);)t=t.substring(0,t.length-1);return t.trim()+n}},964:function(z){if(typeof N>"u"){var m=new Error("Cannot find module 'undefined'");throw m.code="MODULE_NOT_FOUND",m}z.exports=N}},I={};function O(z){var m=I[z];if(m!==void 0)return m.exports;var T=I[z]={exports:{}};return G[z].call(T.exports,T,T.exports,O),T.exports}var U={};return function(){var z=U;Object.defineProperty(z,"__esModule",{value:!0}),z.Cell=z.Column=z.Row=z.Table=z.CellHookData=z.__drawTable=z.__createTable=z.applyPlugin=void 0;var m=O(340),T=O(776),x=O(664),P=O(972),c=O(260);Object.defineProperty(z,"Table",{enumerable:!0,get:function(){return c.Table}});var b=O(172);Object.defineProperty(z,"CellHookData",{enumerable:!0,get:function(){return b.CellHookData}});var a=O(260);Object.defineProperty(z,"Cell",{enumerable:!0,get:function(){return a.Cell}}),Object.defineProperty(z,"Column",{enumerable:!0,get:function(){return a.Column}}),Object.defineProperty(z,"Row",{enumerable:!0,get:function(){return a.Row}});function w(e){(0,m.default)(e)}z.applyPlugin=w;function y(e,l){var f=(0,T.parseInput)(e,l),n=(0,P.createTable)(e,f);(0,x.drawTable)(e,n)}function h(e,l){var f=(0,T.parseInput)(e,l);return(0,P.createTable)(e,f)}z.__createTable=h;function r(e,l){(0,x.drawTable)(e,l)}z.__drawTable=r;try{var t=O(964);t.jsPDF&&(t=t.jsPDF),w(t)}catch{}z.default=y}(),U}()})})(X);
