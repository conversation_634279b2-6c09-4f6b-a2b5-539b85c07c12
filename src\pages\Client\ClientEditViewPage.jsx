import ClientEightCountTab from "Components/Client/ClientViewProjectDetails/ClientEightCountTab";
import VideoSection from "Components/Client/ClientViewProjectDetails/VideoSection";
import MusicSection from "Components/Client/musicSection";
import ConfirmModal from "Components/Modal/ConfirmModal";
import DuoConfirmModal from "Components/Modal/DuoConfirmModal";
import React, { useEffect, useState } from "react";
import { useLocation, useNavigate, useParams } from "react-router";
import { AuthContext, tokenExpireError } from "Src/authContext";
import { GlobalContext, showToast } from "Src/globalContext";
import { getProjectDetailsClientAPI } from "Src/services/clientProjectDetailsService";
import { deleteEditAPI, viewEditDetails } from "Src/services/editService";
import {
  getSurveyByProjectIdAPI,
  updateProjectAPI,
} from "Src/services/projectService";
import { getSurveyDetails } from "Src/services/surveyService";
import { validateUuidv4 } from "Utils/utils";

const ClientEditViewPage = () => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [notes, setNotes] = useState("");
  const [submittedIdeas, setSubmittedIdeas] = React.useState([]);
  const [viewModel, setViewModel] = React.useState({});
  const [surveyLink, setSurveyLink] = React.useState("");
  const [edit_complete, set_edit_complete] = useState(false);
  const [viewModelEdit, setViewModelEdit] = React.useState({});
  const { dispatch, state: authState } = React.useContext(AuthContext);
  const [loader, setLoader] = useState(false);
  const BASE_URL = "https://equalitydev.manaknightdigital.com/";
  const params = useParams();
  const projectID = params?.project_id;
  const isOpen = "view-mode";
  const [showRealDeleteEditModal, setShowRealDeleteEditModal] =
    React.useState(false);
  const [projectDetails, setProjectDetails] = useState(null);
  const [showDeleteEditModal, setShowDeleteEditModal] = React.useState(false);
  console.log(viewModel);
  const handleRealDeleteEditModalClose = () => {
    setShowRealDeleteEditModal(false);
  };

  const location = useLocation();
  useEffect(() => {
    document
      .getElementById("mainContainer")
      .scrollTo({ top: 0, behavior: "smooth" });

    window.scrollTo({ top: 0 });
  }, [location.pathname]);

  const openSecondDelete = () => {
    setShowRealDeleteEditModal(false);

    setTimeout(() => {
      setShowDeleteEditModal(true);
    }, 500);
  };

  const navigate = useNavigate();

  const handleDeleteEdit = async (id) => {
    await deleteEditAPI(id);
    await updateProjectAPI({
      id: projectID,
      discount: 0,
      payment_status: 1,
    });

    setShowDeleteEditModal(false);
    navigate("/client/edits");
    // await getData();
    showToast(globalDispatch, `Edit Deleted`, 5000);
  };

  const getSurveyByProjectId = async (id) => {
    try {
      const result = await getSurveyByProjectIdAPI(id);

      if (!result.error) {
        setSurveyLink(BASE_URL + "survey/" + result.model.uuidv4);
      } else {
        showToast(globalDispatch, result.message, 5000, "error");
      }
    } catch (error) {
      tokenExpireError(dispatch, error.message);
    }
  };

  const getEditDetails = async () => {
    const res = await viewEditDetails(params.id);
    set_edit_complete(res.model.edit_status === 1 ? true : false);
    setViewModelEdit(res.model);
  };

  const getProjectDetails = async (id) => {
    try {
      console.log("kdjdj");
      const result = await getProjectDetailsClientAPI(Number(id));
      console.log(result);
      if (!result.error) {
        console.log(result, "dkjjdjdjjdjjdj");
        setViewModel(result.model);
        // setProjectTotal(result.model?.total);
        // setProgramName(result.model.program_name);
        // setTeamName(result.model.team_name);
        // setThemeOfTheRoutine(result.model?.theme_of_the_routine);
      } else {
        showToast(globalDispatch, result.message, 5000, "error");
      }
    } catch (error) {
      tokenExpireError(dispatch, error.message);
    }
  };

  useEffect(() => {
    getEditDetails();
  }, []);

  useEffect(() => {
    (async function () {
      setLoader(true);

      await getSurveyByProjectId(projectID);
      const project = await getProjectDetails(projectID);
      setProjectDetails(project);
      const result = await getSurveyByProjectIdAPI(Number(projectID));

      if (!result.error) {
        const url = new URL(BASE_URL + "survey/" + result.model.uuidv4);
        const uuidv4 = url.pathname.split("/survey/")[1];

        if (!uuidv4) {
          showToast(globalDispatch, "Invalid URL", 5000, "error");
        } else {
          const checkUuidv4 = validateUuidv4(uuidv4);
          if (!checkUuidv4) {
            showToast(globalDispatch, "Invalid URL", 5000, "error");
          } else {
            (async function () {
              const result = await getSurveyDetails({
                uuidv4,
              });
              if (!result.error) {
                if (result.model.status === 0) {
                  setSubmittedIdeas([]);
                } else if (result.model.status === 1) {
                  setSubmittedIdeas(result.model.ideas);
                }
              } else {
              }
            })();
          }
        }
      }
      setLoader(false);
    })();
  }, []);

  // Add download tracking states
  const [hasVideoDownloads, setHasVideoDownloads] = React.useState(false);
  const [hasMusicDownloads, setHasMusicDownloads] = React.useState(false);

  // Add useEffect for tracking downloads
  React.useEffect(() => {
    const interval = setInterval(() => {
      if (window.downloadManager?.downloads) {
        // Check for video downloads
        const activeVideoDownloads = Array.from(
          window.downloadManager.downloads.values()
        ).some(
          (download) =>
            download.type === "video" && download.status === "downloading"
        );

        // Check for music downloads
        const activeMusicDownloads = Array.from(
          window.downloadManager.downloads.values()
        ).some(
          (download) =>
            download.type === "music" && download.status === "downloading"
        );

        setHasVideoDownloads(activeVideoDownloads);
        setHasMusicDownloads(activeMusicDownloads);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  return (
    <>
      {" "}
      {showRealDeleteEditModal ? (
        <ConfirmModal
          confirmText={`Are you sure you want to delete this edit?`}
          setModalClose={handleRealDeleteEditModalClose}
          setFormYes={openSecondDelete}
        />
      ) : null}
      {showDeleteEditModal ? (
        <DuoConfirmModal
          confirmText={`Are you sure you want to delete this edit? This action cannot be undone.`}
          setModalClose={setShowDeleteEditModal}
          setFormYes={() => {
            handleDeleteEdit(params?.id);
          }}
        />
      ) : null}
      <div
        id="mainContainer"
        className="max-w-screen h-full p-4 md:p-4 lg:p-6 xl:p-8"
      >
        <div className="shadow-default rounded border border-strokedark bg-boxdark pt-10 dark:border-strokedark dark:bg-boxdark">
          {/* Header Section */}
          <div className="sm:px-6.5 border-b border-strokedark px-4 py-4 dark:border-strokedark">
            <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
              <div>
                <div className="flex items-center gap-1">
                  <h3 className="text-xl font-medium text-white">
                    Edit Request for
                  </h3>
                  <p className="text-xl font-medium text-white">
                    {viewModelEdit?.program_name || "N/A"}
                  </p>
                  <span>-</span>
                  <span
                    onClick={() => {
                      localStorage.setItem("ClientProjectTeamName", "");
                      localStorage.setItem("ClientProjectMixTypeId", "");
                      localStorage.setItem("ClientProjectMixDateStart", "");
                      localStorage.setItem("ClientProjectMixDateEnd", "");
                      localStorage.setItem("clientProjectPageSize", "");
                      localStorage.setItem("ClientProjectProducerName", "");
                      navigate(`/client/view-project/${projectID}`);
                    }}
                    className="cursor-pointer text-xl text-primary hover:text-opacity-90"
                  >
                    {viewModelEdit?.team_name || "N/A"}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <span>Producer Name</span>
                  <span>{viewModelEdit?.producer}</span>
                </div>
              </div>
              <div className="flex flex-wrap items-center gap-3">
                <button
                  onClick={() => navigate(-1)}
                  className="inline-flex items-center justify-center rounded-md border border-strokedark bg-meta-4 px-6 py-2.5 text-center font-medium text-white hover:bg-opacity-90"
                >
                  Back
                </button>
              </div>
            </div>
          </div>

          {/* Content Section */}
          <div className="p-4 md:p-6 2xl:p-10">
            <div className="mb-8">
              <div className="space-y-4">
                {/* Project Info */}

                {/* Producer Notes */}
                <div className="mb-10">
                  <span className="text-sm text-white">Notes for Producer</span>
                  <div
                    className="ml-auto mt-2 flex w-[70%] border-b border-strokedark pb-4 text-base text-white  dark:border-strokedark"
                    dangerouslySetInnerHTML={{
                      __html:
                        viewModelEdit?.producer_notes?.replace(
                          /\n/g,
                          "<br> "
                        ) || "N/A",
                    }}
                  />
                </div>

                {/* Video & Music Sections */}
                <div className="space-y-6">
                  <div className="custom-overflow max-h-[300px] overflow-y-auto rounded border border-strokedark bg-boxdark p-4">
                    <VideoSection
                      viewModel={viewModel}
                      projectID={projectID}
                      edit_complete={true}
                      hasDownloads={hasVideoDownloads}
                    />
                  </div>

                  <ClientEightCountTab
                    action={isOpen}
                    edit_eight_count_id={viewModelEdit?.eight_count}
                    surveyLink={surveyLink}
                    viewModel={viewModel}
                    projectID={projectID}
                    edit_complete={true}
                    submittedIdeas={submittedIdeas}
                  />

                  {viewModelEdit?.edit_status === 1 && (
                    <div className="custom-overflow max-h-[300px] overflow-y-auto rounded border border-strokedark bg-boxdark p-4">
                      <MusicSection
                        viewModel={viewModel}
                        projectID={projectID}
                        edit_complete={edit_complete}
                        hasDownloads={hasMusicDownloads}
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>
            {
              <div className="mt-8 flex w-full items-center justify-center">
                <button
                  onClick={() => setShowRealDeleteEditModal(true)}
                  className="inline-flex items-center justify-center rounded-md bg-primary px-6 py-2.5 text-center font-medium text-white hover:bg-opacity-90"
                >
                  <span className="text-[14px] font-medium">Delete</span>
                </button>
              </div>
            }
          </div>
        </div>{" "}
      </div>
    </>
  );
};

export default ClientEditViewPage;
