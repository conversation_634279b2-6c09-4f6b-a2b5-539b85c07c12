import React, { Fragment } from "react";
import { useLocation } from "react-router";
import { Dialog, Transition } from "@headlessui/react";
import { AuthContext } from "Src/authContext";

export default function SessionExpiredModal() {
  const { state, dispatch } = React.useContext(AuthContext);
  const { pathname } = useLocation();

  React.useEffect(() => {
    let modalTimeout;
    if (state.sessionExpired) {
      modalTimeout = setTimeout(() => {
        dispatch({ type: "LOGOUT" });
        if (localStorage.getItem("role") === "admin") {
          window.location.href = `/admin/login?redirect_uri=${pathname}`;
        } else if (localStorage.getItem("role") === "member") {
          window.location.href = `/member/login?redirect_uri=${pathname}`;
        } else {
          window.location.href = `/login?redirect_uri=${pathname}`;
        }
      }, 4000);
    }

    return () => clearTimeout(modalTimeout);
  }, [state.sessionExpired, state.subscriptionStatus]);

  if (!state.sessionExpired) return null;

  return (
    <div className="relative w-full min-h-screen">
      <Transition appear show={true} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={() => {}}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-25" />
          </Transition.Child>

          <div className="overflow-y-auto fixed inset-0">
            <div className="flex justify-center items-center p-4 min-h-full text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="overflow-hidden p-6 w-full max-w-md text-left align-middle bg-white rounded-2xl shadow-xl transition-all transform">
                  <Dialog.Title
                    as="h3"
                    className="text-lg font-medium leading-6 text-gray-900"
                  >
                    Session Expired
                  </Dialog.Title>
                  <div className="mt-2">
                    <p className="text-sm text-gray-500">
                      Your current login session has expired. Redirecting to
                      login page shortly
                    </p>
                  </div>

                  <div className="mt-4">
                    <button
                      type="button"
                      className="inline-flex justify-center px-4 py-2 text-sm font-medium text-blue-900 bg-blue-100 rounded-md border border-transparent hover:bg-blue-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"
                      onClick={() => {}}
                    >
                      Got it, thanks!
                    </button>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </div>
  );
}
