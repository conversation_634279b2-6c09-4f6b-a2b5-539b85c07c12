import React from "react";
import { useNavigate } from "react-router";
import moment from "moment";
import CustomSelect2 from "Components/CustomSelect2";
import { Link } from "react-router-dom";

const MasterProjectTableRow = ({
  authState,
  columns,
  row, // subproject
  producer,
  producers,
  statusStr,
  projectId,
  mixDate,
  setProducerPayload,
  setProducerCostPayload,
}) => {
  const navigate = useNavigate();

  const [selectedProducerId, setSelectedProducerId] = React.useState(null);
  const [tempProducerId, setTempProducerId] = React.useState(null);
  const [producerCost, setProducerCost] = React.useState(0);
  const [totalCost, setTotalCost] = React.useState(0);

  const handleProducerChange = (value) => {
    if (value === "") {
      setSelectedProducerId(null);
      setProducerCost(0);
      setTotalCost(0);
      return;
    }
    const producer = producers.find((x) => x.id === Number(value));
    if (producer && producer.is_producer) {
      setSelectedProducerId(producer.id);
      let producerCost =
        producer && producer.producer_cost ? Number(producer.producer_cost) : 0;
      setProducerCost(producerCost);
      setTotalCost(producerCost);
      setProducerPayload({
        subproject_id: Number(row.id),
        employee_type: "producer",
        old_employee_id: tempProducerId ?? null,
        new_employee_id: Number(producer.id),
        employee_cost: Number(producer.producer_cost),
      });
    } else {
      setSelectedProducerId(null);
      setProducerCost(0);
      setTotalCost(0);
    }
  };

  React.useEffect(() => {
    if (producer) {
      let producerCost =
        producer && producer.length > 0 ? Number(producer[0].employee_cost) : 0;
      setSelectedProducerId(producer[0]?.employee_id);
      setProducerCost(producerCost);
      setTotalCost(producerCost);
    }
  }, [producer]);

  return (
    <>
      {columns.map((cell, index) => {
        if (cell.accessor === "mix_date") {
          return (
            <td key={index} className="whitespace-nowrap px-6 py-2">
              {moment(mixDate).format("MM/DD/YYYY")}
            </td>
          );
        }
        if (cell.accessor === "name") {
          return (
            <td key={index} className="whitespace-nowrap px-6 py-2">
              {row["program_name"]}
              <br />
              <Link
                to={`/${authState.role}/view-project/${projectId}`}
                className="cursor-pointer text-blue-500"
                onClick={() => {
                  localStorage.setItem("projectClientId", "");
                  localStorage.setItem("projectTeamName", "");
                  localStorage.setItem("projectMixTypeId", "");
                  localStorage.setItem("projectMixDateStart", "");
                  localStorage.setItem("projectMixDateEnd", "");
                  localStorage.setItem("projectPageSize", "");
                }}
              >
                {row["team_name"]}
              </Link>
            </td>
          );
        }
        if (cell.accessor === "eight_count") {
          return (
            <td key={index} className="whitespace-nowrap px-6 py-2">
              N/A
            </td>
          );
        }
        if (cell.accessor === "producer") {
          return (
            <td key={index} className="whitespace-nowrap px-6 py-2">
              <CustomSelect2
                className="w-24 rounded-lg border border-stone-500 bg-stone-700 p-2.5 text-sm text-white placeholder-stone-300 focus:border-blue-500 focus:ring-blue-500"
                name="producer"
                id="producer"
                label="Producer"
                value={selectedProducerId}
                onChange={(value) => {
                  handleProducerChange(value);
                }}
              >
                <option value="">Select</option>
                {producers?.map((row) => (
                  <option key={row.id} value={row.id}>
                    {row.name}
                  </option>
                ))}
              </CustomSelect2>
            </td>
          );
        }
        if (cell.accessor === "producer_cost") {
          return (
            <td key={index} className="whitespace-nowrap px-6 py-2">
              <input
                type="number"
                className="w-16 rounded-lg border border-stone-500 bg-stone-700 p-2.5 text-sm text-white placeholder-stone-300 focus:border-blue-500 focus:ring-blue-500"
                placeholder="Cost"
                value={producerCost}
                min="0"
                onChange={(e) => {
                  setProducerCost(Number(e.target.value));
                  setTotalCost(Number(e.target.value));
                  setProducerCostPayload({
                    subproject_id: Number(row.id),
                    employee_type: "producer",
                    employee_id: Number(selectedProducerId),
                    employee_cost: Number(e.target.value),
                  });
                }}
              />
            </td>
          );
        }
        if (cell.accessor === "total_cost") {
          return (
            <td key={index} className="whitespace-nowrap px-6 py-2">
              <input
                className="w-16 rounded-lg border border-zinc-500 bg-zinc-700 p-2.5 text-sm text-white placeholder-stone-300 focus:border-blue-500 focus:ring-blue-500"
                type="text"
                name="total_cost"
                id="total_cost"
                value={totalCost}
                disabled
              />
            </td>
          );
        }
        if (
          cell.accessor === "writer" ||
          cell.accessor === "writer_cost" ||
          cell.accessor === "artist" ||
          cell.accessor === "artist_cost" ||
          cell.accessor === "engineer" ||
          cell.accessor === "engineer_cost"
        ) {
          return (
            <td key={index} className="whitespace-nowrap px-6 py-2">
              N/A
            </td>
          );
        }
        if (cell.accessor === "status") {
          return (
            <td key={index} className="whitespace-nowrap px-6 py-2">
              {statusStr}
            </td>
          );
        }
        if (cell.accessor === "create_at") {
          return (
            <td key={index} className="whitespace-nowrap px-6 py-2">
              {moment(row["create_at"]).format("MM/DD/YYYY")}
            </td>
          );
        }
        return (
          <td key={index} className="whitespace-nowrap px-6 py-2">
            {row[cell.accessor]}
          </td>
        );
      })}
    </>
  );
};

export default MasterProjectTableRow;
