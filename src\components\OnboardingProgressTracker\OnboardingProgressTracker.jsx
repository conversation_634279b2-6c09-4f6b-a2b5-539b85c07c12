import React, { useState, useEffect, useMemo } from "react";

const OnboardingProgressTracker = ({ stepData, onReopen }) => {
  const [isVisible, setIsVisible] = useState(true);
  const [isMinimized, setIsMinimized] = useState(false);
  const [isFullyMinimized, setIsFullyMinimized] = useState(false);

  // Use the same step structure as NewOnboardingModal
  const mandatorySteps = useMemo(
    () => [
      {
        id: 1,
        title: "Choose Your Plan",
        icon: "💳",
        key: "plan_chosen",
      },
      {
        id: 2,
        title: "Legal Agreements",
        icon: "📋",
        key: "legal_agreements_complete",
      },
      {
        id: 3,
        title: "Explore CheerEQ Features",
        icon: "✨",
        key: "feature_tour_complete",
      },
      {
        id: 4,
        title: "Your Business Identity",
        icon: "🏢",
        key: "business_identity_complete",
      },
    ],
    []
  );

  const optionalSteps = useMemo(
    () => [
      {
        id: 5,
        title: "Secure Your Financials",
        icon: "💳",
        key: "payment_billing_complete",
      },
      {
        id: 6,
        title: "Client Service Agreement",
        icon: "📝",
        key: "client_service_agreement_complete",
      },
      {
        id: 7,
        title: "Edit Policy Upload",
        icon: "📤",
        key: "edit_policy_complete",
      },
      {
        id: 8,
        title: "Project Management Settings",
        icon: "⚙️",
        key: "project_management_complete",
      },
      {
        id: 9,
        title: "Project Timelines & Defaults",
        icon: "📅",
        key: "project_timelines_complete",
      },
      {
        id: 10,
        title: "Full Contact Details",
        icon: "📞",
        key: "contact_details_complete",
      },
    ],
    []
  );

  // Helper to determine if all mandatory steps are complete
  const allMandatoryComplete = (stepData) =>
    mandatorySteps.every((step) => stepData[step.key]);

  // Helper to determine if all optional steps are complete
  const allOptionalComplete = (stepData) =>
    optionalSteps.every((step) => stepData[step.key]);

  const getCompletedSteps = () => {
    if (
      allMandatoryComplete(stepData) &&
      stepData.mandatory_completion_screen_viewed
    ) {
      // When all mandatory complete and completion screen viewed, show ONLY optional steps progress
      const completedOptional = optionalSteps.filter(
        (step) => stepData[step.key]
      ).length;
      console.log("OnboardingProgressTracker - Optional phase:", {
        completedOptional,
        totalOptional: optionalSteps.length,
        optionalSteps: optionalSteps.map((step) => ({
          [step.key]: stepData[step.key],
        })),
      });
      return completedOptional; // Only count optional steps completed
    } else {
      // When in mandatory phase, count completed mandatory steps only (no pre-steps)
      const completedMandatory = mandatorySteps.filter(
        (step) => stepData[step.key]
      ).length;
      console.log("OnboardingProgressTracker - Mandatory phase:", {
        completedMandatory,
        totalMandatory: mandatorySteps.length,
        mandatorySteps: mandatorySteps.map((step) => ({
          [step.key]: stepData[step.key],
        })),
      });
      return completedMandatory;
    }
  };

  const getTotalSteps = () => {
    if (
      allMandatoryComplete(stepData) &&
      stepData.mandatory_completion_screen_viewed
    ) {
      // When all mandatory complete and completion screen viewed, show only optional steps total
      return optionalSteps.length;
    } else {
      // When in mandatory phase, total = 4 mandatory steps only
      return mandatorySteps.length;
    }
  };

  const getProgressPercentage = () => {
    if (
      allMandatoryComplete(stepData) &&
      stepData.mandatory_completion_screen_viewed
    ) {
      // When all mandatory complete and completion screen viewed, calculate based on optional steps only
      const completedOptional = optionalSteps.filter(
        (step) => stepData[step.key]
      ).length;
      return Math.round((completedOptional / optionalSteps.length) * 100);
    } else {
      // When in mandatory phase, calculate based on mandatory steps only
      const completedMandatory = mandatorySteps.filter(
        (step) => stepData[step.key]
      ).length;
      return Math.round((completedMandatory / mandatorySteps.length) * 100);
    }
  };

  const handleToggleMinimize = () => {
    setIsMinimized(!isMinimized);
  };

  const handleFullyMinimize = () => {
    setIsFullyMinimized(true);
    setIsMinimized(false);
  };

  const handleExpandFromFullyMinimized = () => {
    setIsFullyMinimized(false);
    setIsMinimized(true);
  };

  const handleClose = () => {
    setIsVisible(false);
  };

  // Debug: Log when stepData changes
  React.useEffect(() => {
    console.log("📊 OnboardingProgressTracker - stepData updated:", stepData);
    if (stepData && Object.keys(stepData).length > 0) {
      console.log(
        "📊 OnboardingProgressTracker - Has step data, will calculate progress"
      );
    } else {
      console.log(
        "📊 OnboardingProgressTracker - No step data available, showing 0 progress"
      );
    }
  }, [stepData]);

  // Hide tracker if all onboarding is complete
  const isFullyComplete =
    allMandatoryComplete(stepData) &&
    stepData.mandatory_completion_screen_viewed &&
    allOptionalComplete(stepData);

  if (!isVisible || isFullyComplete) return null;

  // If fully minimized, show just the circular icon
  if (isFullyMinimized) {
    return (
      <div className="fixed bottom-4 right-4 z-40">
        <button
          onClick={handleExpandFromFullyMinimized}
          className="flex h-16 w-16 items-center justify-center rounded-full bg-[#3C50E0] shadow-lg transition-all duration-200 hover:scale-105 hover:bg-[#2B3EB4]"
          title="Onboarding Checklist"
        >
          <svg
            width="24"
            height="24"
            viewBox="0 0 36 36"
            fill="none"
            xmlns="http://www.w3.org2000/svg"
          >
            <rect x="2" y="2" width="32" height="32" rx="16" fill="#E0E6FC" />
            <rect
              x="2"
              y="2"
              width="32"
              height="32"
              rx="16"
              stroke="#F5F7FF"
              stroke-width="4"
            />
            <path
              d="M19.5 10C19.6326 10 19.7598 10.0527 19.8536 10.1464C19.9473 10.2402 20 10.3674 20 10.5C20 10.6326 20.0527 10.7598 20.1464 10.8536C20.2402 10.9473 20.3674 11 20.5 11C20.6326 11 20.7598 11.0527 20.8536 11.1464C20.9473 11.2402 21 11.3674 21 11.5V12C21 12.1326 20.9473 12.2598 20.8536 12.3536C20.7598 12.4473 20.6326 12.5 20.5 12.5H15.5C15.3674 12.5 15.2402 12.4473 15.1464 12.3536C15.0527 12.2598 15 12.1326 15 12V11.5C15 11.3674 15.0527 11.2402 15.1464 11.1464C15.2402 11.0527 15.3674 11 15.5 11C15.6326 11 15.7598 10.9473 15.8536 10.8536C15.9473 10.7598 16 10.6326 16 10.5C16 10.3674 16.0527 10.2402 16.1464 10.1464C16.2402 10.0527 16.3674 10 16.5 10H19.5Z"
              fill="#3C50E0"
            />
            <path
              d="M13 12.5C13 12.3674 13.0527 12.2402 13.1464 12.1464C13.2402 12.0527 13.3674 12 13.5 12H14C14.1326 12 14.2598 11.9473 14.3536 11.8536C14.4473 11.7598 14.5 11.6326 14.5 11.5C14.5 11.3674 14.4473 11.2402 14.3536 11.1464C14.2598 11.0527 14.1326 11 14 11H13.5C13.1022 11 12.7206 11.158 12.4393 11.4393C12.158 11.7206 12 12.1022 12 12.5V24.5C12 24.8978 12.158 25.2794 12.4393 25.5607C12.7206 25.842 13.1022 26 13.5 26H22.5C22.8978 26 23.2794 25.842 23.5607 25.5607C23.842 25.2794 24 24.8978 24 24.5V12.5C24 12.1022 23.842 11.7206 23.5607 11.4393C23.2794 11.158 22.8978 11 22.5 11H22C21.8674 11 21.7402 11.0527 21.6464 11.1464C21.5527 11.2402 21.5 11.3674 21.5 11.5C21.5 11.6326 21.5527 11.7598 21.6464 11.8536C21.7402 11.9473 21.8674 12 22 12H22.5C22.6326 12 22.7598 12.0527 22.8536 12.1464C22.9473 12.2402 23 12.3674 23 12.5V24.5C23 24.6326 22.9473 24.7598 22.8536 24.8536C22.7598 24.9473 22.6326 25 22.5 25H13.5C13.3674 25 13.2402 24.9473 13.1464 24.8536C13.0527 24.7598 13 24.6326 13 24.5V12.5Z"
              fill="#3C50E0"
            />
            <path
              d="M21.0006 17.5001C21.0006 17.6329 20.9479 17.7603 20.854 17.8541L17.854 20.8541C17.8076 20.9007 17.7524 20.9376 17.6916 20.9629C17.6309 20.9881 17.5658 21.001 17.5 21.001C17.4342 21.001 17.3691 20.9881 17.3084 20.9629C17.2476 20.9376 17.1925 20.9007 17.146 20.8541L15.646 19.3541C15.5521 19.2603 15.4994 19.1329 15.4994 19.0001C15.4994 18.8674 15.5521 18.74 15.646 18.6461C15.7399 18.5523 15.8672 18.4995 16 18.4995C16.1328 18.4995 16.2601 18.5523 16.354 18.6461L17.5 19.7931L20.146 17.1461C20.2399 17.0523 20.3672 16.9995 20.5 16.9995C20.6328 16.9995 20.7601 17.0523 20.854 17.1461C20.9479 17.24 21.0006 17.3674 21.0006 17.5001Z"
              fill="#3C50E0"
            />
          </svg>
        </button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-40">
      <div className="w-80 rounded-xl border border-gray-200 bg-[#F5F7FF] shadow-lg">
        {/* Header */}
        <div className="flex items-center justify-between px-4 pt-4">
          <div className="flex items-center gap-2">
            <svg
              width="36"
              height="36"
              viewBox="0 0 36 36"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <rect x="2" y="2" width="32" height="32" rx="16" fill="#E0E6FC" />
              <rect
                x="2"
                y="2"
                width="32"
                height="32"
                rx="16"
                stroke="#F5F7FF"
                stroke-width="4"
              />
              <path
                d="M19.5 10C19.6326 10 19.7598 10.0527 19.8536 10.1464C19.9473 10.2402 20 10.3674 20 10.5C20 10.6326 20.0527 10.7598 20.1464 10.8536C20.2402 10.9473 20.3674 11 20.5 11C20.6326 11 20.7598 11.0527 20.8536 11.1464C20.9473 11.2402 21 11.3674 21 11.5V12C21 12.1326 20.9473 12.2598 20.8536 12.3536C20.7598 12.4473 20.6326 12.5 20.5 12.5H15.5C15.3674 12.5 15.2402 12.4473 15.1464 12.3536C15.0527 12.2598 15 12.1326 15 12V11.5C15 11.3674 15.0527 11.2402 15.1464 11.1464C15.2402 11.0527 15.3674 11 15.5 11C15.6326 11 15.7598 10.9473 15.8536 10.8536C15.9473 10.7598 16 10.6326 16 10.5C16 10.3674 16.0527 10.2402 16.1464 10.1464C16.2402 10.0527 16.3674 10 16.5 10H19.5Z"
                fill="#3C50E0"
              />
              <path
                d="M13 12.5C13 12.3674 13.0527 12.2402 13.1464 12.1464C13.2402 12.0527 13.3674 12 13.5 12H14C14.1326 12 14.2598 11.9473 14.3536 11.8536C14.4473 11.7598 14.5 11.6326 14.5 11.5C14.5 11.3674 14.4473 11.2402 14.3536 11.1464C14.2598 11.0527 14.1326 11 14 11H13.5C13.1022 11 12.7206 11.158 12.4393 11.4393C12.158 11.7206 12 12.1022 12 12.5V24.5C12 24.8978 12.158 25.2794 12.4393 25.5607C12.7206 25.842 13.1022 26 13.5 26H22.5C22.8978 26 23.2794 25.842 23.5607 25.5607C23.842 25.2794 24 24.8978 24 24.5V12.5C24 12.1022 23.842 11.7206 23.5607 11.4393C23.2794 11.158 22.8978 11 22.5 11H22C21.8674 11 21.7402 11.0527 21.6464 11.1464C21.5527 11.2402 21.5 11.3674 21.5 11.5C21.5 11.6326 21.5527 11.7598 21.6464 11.8536C21.7402 11.9473 21.8674 12 22 12H22.5C22.6326 12 22.7598 12.0527 22.8536 12.1464C22.9473 12.2402 23 12.3674 23 12.5V24.5C23 24.6326 22.9473 24.7598 22.8536 24.8536C22.7598 24.9473 22.6326 25 22.5 25H13.5C13.3674 25 13.2402 24.9473 13.1464 24.8536C13.0527 24.7598 13 24.6326 13 24.5V12.5Z"
                fill="#3C50E0"
              />
              <path
                d="M21.0006 17.5001C21.0006 17.6329 20.9479 17.7603 20.854 17.8541L17.854 20.8541C17.8076 20.9007 17.7524 20.9376 17.6916 20.9629C17.6309 20.9881 17.5658 21.001 17.5 21.001C17.4342 21.001 17.3691 20.9881 17.3084 20.9629C17.2476 20.9376 17.1925 20.9007 17.146 20.8541L15.646 19.3541C15.5521 19.2603 15.4994 19.1329 15.4994 19.0001C15.4994 18.8674 15.5521 18.74 15.646 18.6461C15.7399 18.5523 15.8672 18.4995 16 18.4995C16.1328 18.4995 16.2601 18.5523 16.354 18.6461L17.5 19.7931L20.146 17.1461C20.2399 17.0523 20.3672 16.9995 20.5 16.9995C20.6328 16.9995 20.7601 17.0523 20.854 17.1461C20.9479 17.24 21.0006 17.3674 21.0006 17.5001Z"
                fill="#3C50E0"
              />
            </svg>

            <div className="text-sm font-medium text-[#3C50E0]">
              Onboarding Checklist
            </div>
          </div>
          <div className="flex items-center gap-1">
            <button
              onClick={handleFullyMinimize}
              className="rounded p-1 text-[#1F2E8A] hover:bg-gray-100"
              title="Minimize to icon"
            >
              <svg
                className="h-4 w-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M20 12H4"
                />
              </svg>
            </button>
            <button
              onClick={handleToggleMinimize}
              className="rounded p-1 text-[#1F2E8A] hover:bg-gray-100"
              title="Minimize"
            >
              <svg
                className={`h-4 w-4 transition-transform ${
                  isMinimized ? "rotate-180" : ""
                }`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        {!isMinimized && (
          <div className="border-b border-gray-200 py-4">
            {/* Progress Bar */}
            <div className="mb-4 px-4">
              <div className="h-2 w-full rounded-full bg-gray-300">
                <div
                  className="h-2 rounded-full bg-[#3C50E0] transition-all duration-300"
                  style={{ width: `${getProgressPercentage()}%` }}
                ></div>
              </div>
            </div>

            {/* Steps List */}
            <div className="space-y-2">
              {/* Show optional steps when mandatory is complete and completion screen viewed */}
              {allMandatoryComplete(stepData) &&
                stepData.mandatory_completion_screen_viewed &&
                optionalSteps.map((step) => {
                  const isComplete = stepData[step.key];
                  return (
                    <div
                      key={step.id}
                      className="flex items-center gap-3 border-b border-b-[#A0B0F3] px-4 py-2"
                    >
                      <div className="flex h-6 w-6 items-center justify-center rounded-[50%] bg-[#E0E6FC]">
                        {step.id === 5 && (
                          // Secure Your Financials
                          <svg
                            width="16"
                            height="16"
                            viewBox="0 0 19 19"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M2.1875 6.65332H16.8125M2.1875 7.21582H16.8125M4.4375 11.1533H8.9375M4.4375 12.8408H6.6875M3.875 15.0908H15.125C16.057 15.0908 16.8125 14.3353 16.8125 13.4033V5.52832C16.8125 4.59634 16.057 3.84082 15.125 3.84082H3.875C2.94302 3.84082 2.1875 4.59634 2.1875 5.52832V13.4033C2.1875 14.3353 2.94302 15.0908 3.875 15.0908Z"
                              stroke="#3C50E0"
                              strokeWidth="1.5"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        )}
                        {step.id === 6 && (
                          // Client Service Agreement
                          <svg
                            width="16"
                            height="16"
                            viewBox="0 0 16 16"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M6 7C5.86739 7 5.74021 7.05268 5.64645 7.14645C5.55268 7.24021 5.5 7.36739 5.5 7.5C5.5 7.63261 5.55268 7.75979 5.64645 7.85355C5.74021 7.94732 5.86739 8 6 8H11C11.1326 8 11.2598 7.94732 11.3536 7.85355C11.4473 7.75979 11.5 7.63261 11.5 7.5C11.5 7.36739 11.4473 7.24021 11.3536 7.14645C11.2598 7.05268 11.1326 7 11 7H6ZM5.5 9.5C5.5 9.36739 5.55268 9.24021 5.64645 9.14645C5.74021 9.05268 5.86739 9 6 9H11C11.1326 9 11.2598 9.05268 11.3536 9.14645C11.4473 9.24021 11.5 9.36739 11.5 9.5C11.5 9.63261 11.4473 9.75979 11.3536 9.85355C11.2598 9.94732 11.1326 10 11 10H6C5.86739 10 5.74021 9.94732 5.64645 9.85355C5.55268 9.75979 5.5 9.63261 5.5 9.5ZM5.5 11.5C5.5 11.3674 5.55268 11.2402 5.64645 11.1464C5.74021 11.0527 5.86739 11 6 11H8C8.13261 11 8.25979 11.0527 8.35355 11.1464C8.44732 11.2402 8.5 11.3674 8.5 11.5C8.5 11.6326 8.44732 11.7598 8.35355 11.8536C8.25979 11.9473 8.13261 12 8 12H6C5.86739 12 5.74021 11.9473 5.64645 11.8536C5.55268 11.7598 5.5 11.6326 5.5 11.5Z"
                              fill="#3C50E0"
                            />
                            <path
                              d="M10 0H4.5C3.96957 0 3.46086 0.210714 3.08579 0.585786C2.71071 0.960859 2.5 1.46957 2.5 2V14C2.5 14.5304 2.71071 15.0391 3.08579 15.4142C3.46086 15.7893 3.96957 16 4.5 16H12.5C13.0304 16 13.5391 15.7893 13.9142 15.4142C14.2893 15.0391 14.5 14.5304 14.5 14V4.5L10 0ZM10 1V3C10 3.39782 10.158 3.77936 10.4393 4.06066C10.7206 4.34196 11.1022 4.5 11.5 4.5H13.5V14C13.5 14.2652 13.3946 14.5196 13.2071 14.7071C13.0196 14.8946 12.7652 15 12.5 15H4.5C4.23478 15 3.98043 14.8946 3.79289 14.7071C3.60536 14.5196 3.5 14.2652 3.5 14V2C3.5 1.73478 3.60536 1.48043 3.79289 1.29289C3.98043 1.10536 4.23478 1 4.5 1H10Z"
                              fill="#3C50E0"
                            />
                          </svg>
                        )}
                        {step.id === 7 && (
                          // Edit Policy Upload
                          <svg
                            width="16"
                            height="16"
                            viewBox="0 0 16 16"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M9.5 0C9.63261 0 9.75979 0.0526784 9.85355 0.146447C9.94732 0.240215 10 0.367392 10 0.5C10 0.632608 10.0527 0.759785 10.1464 0.853553C10.2402 0.947322 10.3674 1 10.5 1C10.6326 1 10.7598 1.05268 10.8536 1.14645C10.9473 1.24021 11 1.36739 11 1.5V2C11 2.13261 10.9473 2.25979 10.8536 2.35355C10.7598 2.44732 10.6326 2.5 10.5 2.5H5.5C5.36739 2.5 5.24021 2.44732 5.14645 2.35355C5.05268 2.25979 5 2.13261 5 2V1.5C5 1.36739 5.05268 1.24021 5.14645 1.14645C5.24021 1.05268 5.36739 1 5.5 1C5.63261 1 5.75979 0.947322 5.85355 0.853553C5.94732 0.759785 6 0.632608 6 0.5C6 0.367392 6.05268 0.240215 6.14645 0.146447C6.24021 0.0526784 6.36739 0 6.5 0H9.5Z"
                              fill="#3C50E0"
                            />
                            <path
                              d="M3 2.5C3 2.36739 3.05268 2.24021 3.14645 2.14645C3.24021 2.05268 3.36739 2 3.5 2H4C4.13261 2 4.25979 1.94732 4.35355 1.85355C4.44732 1.75979 4.5 1.63261 4.5 1.5C4.5 1.36739 4.44732 1.24021 4.35355 1.14645C4.25979 1.05268 4.13261 1 4 1H3.5C3.10218 1 2.72064 1.15804 2.43934 1.43934C2.15804 1.72064 2 2.10218 2 2.5V14.5C2 14.8978 2.15804 15.2794 2.43934 15.5607C2.72064 15.842 3.10218 16 3.5 16H12.5C12.8978 16 13.2794 15.842 13.5607 15.5607C13.842 15.2794 14 14.8978 14 14.5V2.5C14 2.10218 13.842 1.72064 13.5607 1.43934C13.2794 1.15804 12.8978 1 12.5 1H12C11.8674 1 11.7402 1.05268 11.6464 1.14645C11.5527 1.24021 11.5 1.36739 11.5 1.5C11.5 1.63261 11.5527 1.75979 11.6464 1.85355C11.7402 1.94732 11.8674 2 12 2H12.5C12.6326 2 12.7598 2.05268 12.8536 2.14645C12.9473 2.24021 13 2.36739 13 2.5V14.5C13 14.6326 12.9473 14.7598 12.8536 14.8536C12.7598 14.9473 12.6326 15 12.5 15H3.5C3.36739 15 3.24021 14.9473 3.14645 14.8536C3.05268 14.7598 3 14.6326 3 14.5V2.5Z"
                              fill="#3C50E0"
                            />
                            <path
                              d="M11.0006 7.50014C11.0006 7.63292 10.9479 7.76026 10.854 7.85414L7.85401 10.8541C7.80756 10.9007 7.75239 10.9376 7.69164 10.9629C7.63089 10.9881 7.56577 11.001 7.50001 11.001C7.43424 11.001 7.36912 10.9881 7.30837 10.9629C7.24763 10.9376 7.19245 10.9007 7.14601 10.8541L5.64601 9.35414C5.55212 9.26026 5.49937 9.13292 5.49937 9.00014C5.49937 8.86737 5.55212 8.74003 5.64601 8.64614C5.73989 8.55226 5.86723 8.49951 6.00001 8.49951C6.13278 8.49951 6.26012 8.55226 6.35401 8.64614L7.50001 9.79314L10.146 7.14614C10.2399 7.05226 10.3672 6.99951 10.5 6.99951C10.6328 6.99951 10.7601 7.05226 10.854 7.14614C10.9479 7.24003 11.0006 7.36737 11.0006 7.50014Z"
                              fill="#3C50E0"
                            />
                          </svg>
                        )}
                        {step.id === 8 && (
                          // Project Management Settings
                          <svg
                            width="16"
                            height="16"
                            viewBox="0 0 16 16"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M9.5 0C9.63261 0 9.75979 0.0526784 9.85355 0.146447C9.94732 0.240215 10 0.367392 10 0.5C10 0.632608 10.0527 0.759785 10.1464 0.853553C10.2402 0.947322 10.3674 1 10.5 1C10.6326 1 10.7598 1.05268 10.8536 1.14645C10.9473 1.24021 11 1.36739 11 1.5V2C11 2.13261 10.9473 2.25979 10.8536 2.35355C10.7598 2.44732 10.6326 2.5 10.5 2.5H5.5C5.36739 2.5 5.24021 2.44732 5.14645 2.35355C5.05268 2.25979 5 2.13261 5 2V1.5C5 1.36739 5.05268 1.24021 5.14645 1.14645C5.24021 1.05268 5.36739 1 5.5 1C5.63261 1 5.75979 0.947322 5.85355 0.853553C5.94732 0.759785 6 0.632608 6 0.5C6 0.367392 6.05268 0.240215 6.14645 0.146447C6.24021 0.0526784 6.36739 0 6.5 0H9.5Z"
                              fill="#3C50E0"
                            />
                            <path
                              d="M3 2.5C3 2.36739 3.05268 2.24021 3.14645 2.14645C3.24021 2.05268 3.36739 2 3.5 2H4C4.13261 2 4.25979 1.94732 4.35355 1.85355C4.44732 1.75979 4.5 1.63261 4.5 1.5C4.5 1.36739 4.44732 1.24021 4.35355 1.14645C4.25979 1.05268 4.13261 1 4 1H3.5C3.10218 1 2.72064 1.15804 2.43934 1.43934C2.15804 1.72064 2 2.10218 2 2.5V14.5C2 14.8978 2.15804 15.2794 2.43934 15.5607C2.72064 15.842 3.10218 16 3.5 16H12.5C12.8978 16 13.2794 15.842 13.5607 15.5607C13.842 15.2794 14 14.8978 14 14.5V2.5C14 2.10218 13.842 1.72064 13.5607 1.43934C13.2794 1.15804 12.8978 1 12.5 1H12C11.8674 1 11.7402 1.05268 11.6464 1.14645C11.5527 1.24021 11.5 1.36739 11.5 1.5C11.5 1.63261 11.5527 1.75979 11.6464 1.85355C11.7402 1.94732 11.8674 2 12 2H12.5C12.6326 2 12.7598 2.05268 12.8536 2.14645C12.9473 2.24021 13 2.36739 13 2.5V14.5C13 14.6326 12.9473 14.7598 12.8536 14.8536C12.7598 14.9473 12.6326 15 12.5 15H3.5C3.36739 15 3.24021 14.9473 3.14645 14.8536C3.05268 14.7598 3 14.6326 3 14.5V2.5Z"
                              fill="#3C50E0"
                            />
                            <path
                              d="M11.0006 7.50014C11.0006 7.63292 10.9479 7.76026 10.854 7.85414L7.85401 10.8541C7.80756 10.9007 7.75239 10.9376 7.69164 10.9629C7.63089 10.9881 7.56577 11.001 7.50001 11.001C7.43424 11.001 7.36912 10.9881 7.30837 10.9629C7.24763 10.9376 7.19245 10.9007 7.14601 10.8541L5.64601 9.35414C5.55212 9.26026 5.49937 9.13292 5.49937 9.00014C5.49937 8.86737 5.55212 8.74003 5.64601 8.64614C5.73989 8.55226 5.86723 8.49951 6.00001 8.49951C6.13278 8.49951 6.26012 8.55226 6.35401 8.64614L7.50001 9.79314L10.146 7.14614C10.2399 7.05226 10.3672 6.99951 10.5 6.99951C10.6328 6.99951 10.7601 7.05226 10.854 7.14614C10.9479 7.24003 11.0006 7.36737 11.0006 7.50014Z"
                              fill="#3C50E0"
                            />
                          </svg>
                        )}
                        {step.id === 9 && (
                          // Project Timelines & Defaults
                          <svg
                            width="16"
                            height="16"
                            viewBox="0 0 16 16"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <g clipPath="url(#clip0_3162_4569)">
                              <path
                                d="M3.5 0C3.63261 0 3.75979 0.0526784 3.85355 0.146447C3.94732 0.240215 4 0.367392 4 0.5V1H12V0.5C12 0.367392 12.0527 0.240215 12.1464 0.146447C12.2402 0.0526784 12.3674 0 12.5 0C12.6326 0 12.7598 0.0526784 12.8536 0.146447C12.9473 0.240215 13 0.367392 13 0.5V1H14C14.5304 1 15.0391 1.21071 15.4142 1.58579C15.7893 1.96086 16 2.46957 16 3V14C16 14.5304 15.7893 15.0391 15.4142 15.4142C15.0391 15.7893 14.5304 16 14 16H2C1.46957 16 0.960859 15.7893 0.585786 15.4142C0.210714 15.0391 0 14.5304 0 14V3C0 2.46957 0.210714 1.96086 0.585786 1.58579C0.960859 1.21071 1.46957 1 2 1H3V0.5C3 0.367392 3.05268 0.240215 3.14645 0.146447C3.24021 0.0526784 3.36739 0 3.5 0ZM2 2C1.73478 2 1.48043 2.10536 1.29289 2.29289C1.10536 2.48043 1 2.73478 1 3V14C1 14.2652 1.10536 14.5196 1.29289 14.7071C1.48043 14.8946 1.73478 15 2 15H14C14.2652 15 14.5196 14.8946 14.7071 14.7071C14.8946 14.5196 15 14.2652 15 14V3C15 2.73478 14.8946 2.48043 14.7071 2.29289C14.5196 2.10536 14.2652 2 14 2H2Z"
                                fill="#3C50E0"
                              />
                              <path
                                d="M2.5 4C2.5 3.86739 2.55268 3.74021 2.64645 3.64645C2.74021 3.55268 2.86739 3.5 3 3.5H13C13.1326 3.5 13.2598 3.55268 13.3536 3.64645C13.4473 3.74021 13.5 3.86739 13.5 4V5C13.5 5.13261 13.4473 5.25979 13.3536 5.35355C13.2598 5.44732 13.1326 5.5 13 5.5H3C2.86739 5.5 2.74021 5.44732 2.64645 5.35355C2.55268 5.25979 2.5 5.13261 2.5 5V4ZM9 8C9 7.73478 9.10536 7.48043 9.29289 7.29289C9.48043 7.10536 9.73478 7 10 7H15V9H10C9.73478 9 9.48043 8.89464 9.29289 8.70711C9.10536 8.51957 9 8.26522 9 8ZM1 10H5C5.26522 10 5.51957 10.1054 5.70711 10.2929C5.89464 10.4804 6 10.7348 6 11C6 11.2652 5.89464 11.5196 5.70711 11.7071C5.51957 11.8946 5.26522 12 5 12H1V10Z"
                                fill="#3C50E0"
                              />
                            </g>
                            <defs>
                              <clipPath id="clip0_3162_4569">
                                <rect width="16" height="16" fill="white" />
                              </clipPath>
                            </defs>
                          </svg>
                        )}
                        {step.id === 10 && (
                          // Full Contact Details
                          <svg
                            width="16"
                            height="16"
                            viewBox="0 0 16 16"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <g clipPath="url(#clip0_3162_4578)">
                              <path
                                fillRule="evenodd"
                                clipRule="evenodd"
                                d="M3.1 11.2001C3.14657 11.138 3.20697 11.0876 3.27639 11.0529C3.34582 11.0182 3.42238 11.0001 3.5 11.0001H6C6.13261 11.0001 6.25979 11.0528 6.35355 11.1466C6.44732 11.2403 6.5 11.3675 6.5 11.5001C6.5 11.6327 6.44732 11.7599 6.35355 11.8537C6.25979 11.9474 6.13261 12.0001 6 12.0001H3.75L1.5 15.0001H14.5L12.25 12.0001H10C9.86739 12.0001 9.74021 11.9474 9.64645 11.8537C9.55268 11.7599 9.5 11.6327 9.5 11.5001C9.5 11.3675 9.55268 11.2403 9.64645 11.1466C9.74021 11.0528 9.86739 11.0001 10 11.0001H12.5C12.5776 11.0001 12.6542 11.0182 12.7236 11.0529C12.793 11.0876 12.8534 11.138 12.9 11.2001L15.9 15.2001C15.9557 15.2744 15.9896 15.3627 15.998 15.4552C16.0063 15.5477 15.9887 15.6407 15.9472 15.7237C15.9057 15.8068 15.8419 15.8766 15.7629 15.9254C15.6839 15.9743 15.5929 16.0001 15.5 16.0001H0.5C0.407144 16.0001 0.316123 15.9743 0.237135 15.9254C0.158147 15.8766 0.0943131 15.8068 0.0527866 15.7237C0.0112602 15.6407 -0.00631841 15.5477 0.00202058 15.4552C0.0103596 15.3627 0.0442867 15.2744 0.1 15.2001L3.1 11.2001Z"
                                fill="#3C50E0"
                              />
                              <path
                                fillRule="evenodd"
                                clipRule="evenodd"
                                d="M8 1.00012C7.60603 1.00012 7.21593 1.07771 6.85195 1.22848C6.48797 1.37924 6.15726 1.60022 5.87868 1.8788C5.6001 2.15737 5.37913 2.48809 5.22836 2.85207C5.0776 3.21604 5 3.60615 5 4.00012C5 4.39408 5.0776 4.78419 5.22836 5.14817C5.37913 5.51215 5.6001 5.84286 5.87868 6.12144C6.15726 6.40001 6.48797 6.62099 6.85195 6.77176C7.21593 6.92252 7.60603 7.00012 8 7.00012C8.79565 7.00012 9.55871 6.68405 10.1213 6.12144C10.6839 5.55883 11 4.79577 11 4.00012C11 3.20447 10.6839 2.44141 10.1213 1.8788C9.55871 1.31619 8.79565 1.00012 8 1.00012ZM4 4.00012C4.00007 3.23034 4.22226 2.47694 4.63989 1.83031C5.05753 1.18368 5.65288 0.671289 6.3545 0.354624C7.05613 0.0379591 7.83422 -0.0695301 8.59542 0.0450543C9.35662 0.159639 10.0686 0.491429 10.6459 1.00061C11.2232 1.5098 11.6413 2.17475 11.8501 2.91567C12.0589 3.6566 12.0494 4.44202 11.8228 5.1777C11.5963 5.91339 11.1623 6.56807 10.5729 7.06321C9.98349 7.55835 9.26374 7.8729 8.5 7.96912V13.5001C8.5 13.6327 8.44732 13.7599 8.35355 13.8537C8.25979 13.9474 8.13261 14.0001 8 14.0001C7.86739 14.0001 7.74021 13.9474 7.64645 13.8537C7.55268 13.7599 7.5 13.6327 7.5 13.5001V7.97012C6.53296 7.84828 5.64369 7.37755 4.99921 6.64635C4.35474 5.91515 3.99942 4.9748 4 4.00012Z"
                                fill="#3C50E0"
                              />
                            </g>
                            <defs>
                              <clipPath id="clip0_3162_4578">
                                <rect width="16" height="16" fill="white" />
                              </clipPath>
                            </defs>
                          </svg>
                        )}
                      </div>
                      <span className="flex-1 text-sm font-medium text-[#1F2E8A]">
                        {step.title}
                      </span>
                      {isComplete ? (
                        <svg
                          width="16"
                          height="16"
                          viewBox="0 0 16 16"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <rect
                            x="0.5"
                            y="0.5"
                            width="15"
                            height="15"
                            rx="7.5"
                            fill="#3C50E0"
                          />
                          <rect
                            x="0.5"
                            y="0.5"
                            width="15"
                            height="15"
                            rx="7.5"
                            stroke="#3C50E0"
                          />
                          <path
                            d="M11.3332 5.5L6.74984 10.0833L4.6665 8"
                            stroke="white"
                            stroke-width="1.66667"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                      ) : (
                        <div className="h-4 w-4 rounded-full border-2 border-gray-400"></div>
                      )}
                    </div>
                  );
                })}
            </div>
          </div>
        )}

        {/* Minimized View */}
        {isMinimized && (
          <div className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="h-3 w-3 rounded-full bg-[#1F2E8A]"></div>
                <span className="text-sm text-[#1F2E8A]">
                  {getCompletedSteps()}/{getTotalSteps()} complete
                </span>
              </div>
              <button
                onClick={onReopen}
                className="rounded-lg bg-[#1F2E8A] px-3 py-1.5 text-xs font-medium text-white hover:bg-[#1F2E8A]/90"
              >
                Resume
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default OnboardingProgressTracker;
