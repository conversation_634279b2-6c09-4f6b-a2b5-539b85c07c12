import React, { useState, useEffect } from "react";
import { readImageUrl } from "Context/Global/GlobalActions";

export const ServiceAgreementPDF = ({
  companyLogo,
  licenseCompanyLogo,
  companyName,
  companyEmail,
  companyPhone,
  companyAddress,
  serviceAgreementContent,
  signature,
  initial,
  clientName,
  invoiceId,
  id,
}) => {
  const [logoBase64, setLogoBase64] = useState(null);
  const [signatureBase64, setSignatureBase64] = useState(null);

  // Convert images to base64 on component mount
  useEffect(() => {
    const convertImages = async () => {
      try {
        // Use license_company_logo if available, otherwise fall back to company_logo
        const logoUrl =
          licenseCompanyLogo &&
          licenseCompanyLogo !== "null" &&
          licenseCompanyLogo !== ""
            ? licenseCompanyLogo
            : companyLogo;

        console.log("ServiceAgreementPDF - Logo URLs:", {
          licenseCompanyLogo,
          companyLogo,
          selectedLogoUrl: logoUrl,
        });

        if (logoUrl && logoUrl !== "null" && logoUrl !== "") {
          // Use the existing readImageUrl function to handle CORS properly
          readImageUrl(logoUrl, (result) => {
            if (!result.error) {
              console.log("Logo converted to base64 successfully");
              setLogoBase64(result.data);
            } else {
              console.error("Error converting logo to base64:", result.message);
            }
          });
        } else {
          console.log("No valid logo URL found");
        }
      } catch (error) {
        console.error("Error converting logo to base64:", error);
      }

      try {
        if (signature && signature !== "null") {
          const signatureUrl = signature.startsWith("http")
            ? signature
            : `https://equalityrecords.s3.amazonaws.com/${signature}`;

          // Use the existing readImageUrl function to handle CORS properly
          readImageUrl(signatureUrl, (result) => {
            if (!result.error) {
              setSignatureBase64(result.data);
            } else {
              console.error(
                "Error converting signature to base64:",
                result.message
              );
            }
          });
        }
      } catch (error) {
        console.error("Error converting signature to base64:", error);
      }
    };

    convertImages();
  }, [companyLogo, licenseCompanyLogo, signature]);
  return (
    <div id="pop" className="absolute left-[-9999px] top-[-9999px]">
      <section
        id={`printable-service-agreement-${id || invoiceId}`}
        className="printable-component flex w-full flex-col justify-center bg-white px-[60px] py-[40px] font-sans text-base text-black"
        style={{ maxWidth: "800px", margin: "0 auto" }}
      >
        {/* Header with Company Logo */}
        <div className="mb-8 flex w-full justify-center">
          {logoBase64 ? (
            <img
              className="h-[120px] max-h-[120px] w-[300px] max-w-[300px] object-contain"
              src={logoBase64}
              alt="Company Logo"
            />
          ) : null}
        </div>

        {/* Company Information */}
        <div className="mb-8 text-center">
          <h3 className="mb-2 text-xl font-bold text-gray-800">
            {companyName || "Company Name"}
          </h3>
          <div className="space-y-1 text-sm text-gray-600">
            {companyEmail && <p>{companyEmail}</p>}
            {companyPhone && <p>{companyPhone}</p>}
            {companyAddress && <p>{companyAddress}</p>}
          </div>
        </div>

        {/* Title */}
        <div className="mb-8 text-center">
          <h3 className="text-3xl font-bold text-gray-800">
            Service Agreement
          </h3>
          <div className="mx-auto mt-3 h-[1px] w-[50%] bg-gray-400"></div>
        </div>

        {/* Service Agreement Content */}
        <div
          className="mb-12 text-base leading-relaxed"
          dangerouslySetInnerHTML={{
            __html:
              serviceAgreementContent &&
              serviceAgreementContent !== "null" &&
              serviceAgreementContent !== "undefined" &&
              serviceAgreementContent !== ""
                ? serviceAgreementContent
                : "I hereby approve this invoice",
          }}
        />

        {/* Signature Section */}
        <div className="mt-auto border-t border-gray-300 pt-8">
          <div className="grid grid-cols-2 gap-8">
            {/* Client Signature */}
            <div className="text-center">
              <div className="mb-4">
                <p className="mb-1 text-sm font-medium text-gray-700">
                  Client Name:
                </p>
                <p className="text-base font-semibold text-gray-800">
                  {clientName || "Client Name"}
                </p>
              </div>

              <div className="mb-6 flex h-[80px] items-end justify-center border-b-2 border-gray-400">
                {signatureBase64 ? (
                  <img
                    src={signatureBase64}
                    alt="Signature"
                    className="h-[70px] max-w-[200px] object-contain"
                  />
                ) : (
                  <span className="mb-2 text-lg text-gray-500">Signature</span>
                )}
              </div>

              <div className="text-center">
                <p className="text-sm font-medium text-gray-700">
                  Client Signature
                </p>
                <p className="mt-1 text-xs text-gray-500">
                  {new Date().toLocaleDateString()}
                </p>
              </div>
            </div>

            {/* Initials */}
            <div className="text-center">
              <div className="mb-4">
                <p className="mb-1 text-sm font-medium text-gray-700">
                  Program:
                </p>
                <p className="text-base font-semibold text-gray-800">
                  Service Agreement
                </p>
              </div>

              <div className="mb-6 flex h-[80px] items-center justify-center border-b-2 border-gray-400">
                <span className="text-4xl font-bold text-gray-800">
                  {initial ? initial.toUpperCase() : ""}
                </span>
              </div>

              <div className="text-center">
                <p className="text-sm font-medium text-gray-700">
                  Client Initials
                </p>
                <p className="mt-1 text-xs text-gray-500">Agreement Accepted</p>
              </div>
            </div>
          </div>
        </div>

        {/* Invoice ID Reference */}
        <div className="mt-8 text-center">
          <p className="text-xs text-gray-500">
            Service Agreement for Invoice #{invoiceId || ""}
          </p>
          <p className="mt-1 text-xs text-gray-500">
            Generated on {new Date().toLocaleDateString()}
          </p>
          {/* Licensed by section */}

          <p className="text-lg font-medium text-gray-700">
            Licensed by CHEEREQ
          </p>
        </div>
      </section>
    </div>
  );
};

export default ServiceAgreementPDF;
