import React from "react";
import { GlobalContext, showToast } from "Src/globalContext";

const ResendSurvey = ({
  surveySubmitStatus,
  surveyLink = "#",
  setShowResendSurveyModal,
  ideas = [],
}) => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);

  const copyLinkToClipboard = (surveyLink) => {
    navigator.clipboard.writeText(surveyLink);
    showToast(globalDispatch, "Link copied to clipboard", 3000, "info");
  };

  return (
    <>
      {surveySubmitStatus || ideas.length > 0 ? (
        <div className="flex h-[37px] w-max cursor-default items-center justify-center rounded-md border border-gray-500 bg-gray-600 px-2 py-1 shadow">
          <p className="text-xs font-semibold text-white md:text-sm">
            Survey Submitted
          </p>
        </div>
      ) : (
        <div
          className="flex h-[37px] w-max cursor-pointer items-center justify-center rounded bg-primary px-2 py-1 shadow hover:bg-primary/90"
          onClick={() => setShowResendSurveyModal(true)}
        >
          <p className="text-xs font-semibold text-white md:text-sm">
            Resend Survey
          </p>
        </div>
      )}
    </>
  );
};

export default ResendSurvey;
