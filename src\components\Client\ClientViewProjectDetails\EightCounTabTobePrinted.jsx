import { Document, Font, Image, Page, Text, View } from "@react-pdf/renderer";
import { Component } from "react";
import { createTw } from "react-pdf-tailwind";

const tw = createTw({
  theme: {
    extend: {
      colors: {
        custom: "cornflowerblue",
      },
    },
  },
});

Font.registerHyphenationCallback((word) => {
  // Return entire word as unique part
  return [word];
});

class EightCountTabToBePrinted extends Component {
  shouldComponentUpdate(nextProps, nextState) {
    return nextProps.currentData !== this.props.currentData;
  }

  render() {
    const {
      versions,
      teamDetails,
      submittedIdeas,

      viewModel,
      currentData,

      isGrayed,
      reviseEdit,
    } = this.props;

    const sortedVersions = [...versions].sort((a, b) => b.id - a.id);
    console.log(
      sortedVersions,
      "sorted",
      currentData,
      sortedVersions.findIndex((v) => v.id == currentData.id)
    );
    console.log(viewModel?.company_info?.company_logo);

    // this.componentDidMount = () => {
    //   console.log("hii");
    //   isModifiedCell(1, 3, 8);
    // };
    const isModifiedCell = (value, rowIndex, colIndex) => {
      if (!currentData) return false;

      // Get the parent version for comparison
      let parentVersion;
      if (currentData.duplicate_id) {
        console.log("sorted", "s");
        // If duplicate_id exists, use that version
        parentVersion = versions.find((v) => v.id == currentData.duplicate_id);
      } else {
        // If no duplicate_id, find the last previous version
        console.log("hii");
        console.log(sortedVersions, "sorted");
        const currentIndex = sortedVersions.findIndex(
          (v) => v.id == currentData.id
        );
        console.log("hii", currentIndex + 1, "sorted");
        parentVersion = sortedVersions[currentIndex + 1]; // Get next version (which is previous chronologically)
      }

      console.log("parentVersion", parentVersion, "sorted");
      if (!parentVersion) return false;

      // For version 1 (no uses_modified_system) - compare directly with parent
      if (!currentData.uses_modified_system) {
        console.log("version 1", "sorted");
        const parentData = JSON.parse(parentVersion.json_data);
        const currentValue = value;
        const parentValue = parentData[rowIndex]?.[colIndex];
        return currentValue !== parentValue && currentValue.trim() !== "";
      }

      // For version 2 (uses_modified_system with or without {{modified}} tags)
      if (currentData.uses_modified_system && !currentData.metadata) {
        console.log("version 2", "sorted");
        const parentData = JSON.parse(parentVersion.json_data);
        const currentValue = cleanDisplayValue(value);
        const parentValue = cleanDisplayValue(parentData[rowIndex]?.[colIndex]);
        return currentValue !== parentValue && currentValue.trim() !== "";
      }

      // For version 3 (metadata-based)
      if (currentData.metadata) {
        console.log("version 3", "sorted");
        if (
          window.location.pathname.includes("/edit") &&
          currentData.duplicate_id
        ) {
          return currentData.metadata.modified_cells.some(
            (cell) => cell.row === rowIndex && cell.col === colIndex
          );
        } else {
          const parentData = JSON.parse(parentVersion.json_data);
          const currentValue = value;
          const parentValue = parentData[rowIndex]?.[colIndex];
          return currentValue !== parentValue && currentValue.trim() !== "";
        }
      }

      console.log(parentVersion, "parentsorted");

      return false;
    };

    const cleanDisplayValue = (value) => {
      if (typeof value === "string") {
        return value.replace("{{modified}}", "");
      }
      return value;
    };

    return (
      <>
        <Document>
          <Page
            orientation="portrait"
            size="A2"
            style={{ minWidth: "1050px", padding: "30px", width: "1050px" }}
          >
            <View style={tw("mt-3 bg-white text-black md:m-0 w-full")}>
              <View style={tw("mt-10 w-full min-w-full bg-black p-8 pb-2")}>
                <View style={tw("flex flex-row h-[250px] justify-center")}>
                  {viewModel?.company_info?.company_logo ? (
                    <Image
                      style={tw(
                        "h-[200px] max-h-[200px] w-[500px] max-w-[500px] object-cover"
                      )}
                      src={viewModel?.company_info?.company_logo}
                    />
                  ) : (
                    <Text style={tw("text-center text-3xl font-bold")}>
                      COMPANY LOGO
                    </Text>
                  )}
                </View>
                <Text style={tw("text-center text-xl font-bold text-white")}>
                  EIGHT COUNT SHEETS
                </Text>
              </View>
              <View style={tw("w-full")}>
                <View style={tw("flex flex-row h-[45px] w-full items-start")}>
                  <View
                    style={tw(
                      "flex flex-row h-[45px] max-h-[45px] w-[168px] min-w-[168px] max-w-[168px] items-end justify-end border-b border-r border-b-gray-400 border-r-gray-400 pb-1 pr-2 text-right text-lg font-bold text-black"
                    )}
                  >
                    <Text> PROGRAM:</Text>
                  </View>
                  <Text
                    style={tw(
                      "h-full w-full border border-x-0 border-t-0 border-b-black pb-1 pl-2 pt-4"
                    )}
                  >
                    {viewModel?.program_name}
                  </Text>
                </View>
                <View style={tw("flex flex-row h-[45px] w-full items-start")}>
                  <View style={tw("flex flex-row h-[45px] min-h-full w-[50%]")}>
                    <View
                      style={tw(
                        "flex h-[45px] flex-row max-h-[45px] w-[168px] min-w-[168px] max-w-[168px] items-end justify-end border-b border-r border-b-gray-400 border-r-gray-400 pb-1 pr-2 text-right text-lg font-bold text-black"
                      )}
                    >
                      <Text> CONTACT:</Text>
                    </View>
                    <Text
                      style={tw(
                        "h-full pl-2 w-full border border-x-0 border-t-0 border-b-black pl-2 pb-1 pt-4"
                      )}
                    >
                      {viewModel?.program_owner_phone}
                    </Text>
                  </View>
                  <View style={tw("flex flex-row h-[45px] min-h-full w-[50%]")}>
                    <View
                      style={tw(
                        "flex flex-row h-[45px] max-h-[45px] w-[168px] min-w-[168px] max-w-[168px] items-end justify-end border-b border-l border-r border-b-gray-400 border-l-gray-400 border-r-gray-400 pb-1 pr-2 text-right text-lg font-bold text-black"
                      )}
                    >
                      <Text> PHONE:</Text>
                    </View>
                    <Text
                      style={tw(
                        "h-full w-full border border-x-0 border-t-0 border-b-black pb-1 pl-2 pt-4"
                      )}
                    >
                      {viewModel?.program_owner_phone}
                    </Text>
                  </View>
                </View>
                <View style={tw("flex flex-row h-[45px] w-full items-start")}>
                  <View
                    style={tw(
                      "flex h-[45px] flex-row max-h-[45px] w-[168px] min-w-[168px] max-w-[168px] items-end justify-end border-b border-r border-b-gray-400 border-r-gray-400 pb-1 pr-2 text-right text-lg font-bold text-black"
                    )}
                  >
                    <Text> EMAIL:</Text>
                  </View>
                  <Text
                    style={tw(
                      "h-full w-full border border-x-0 border-t-0 border-b-black pl-2 pb-1 pt-4"
                    )}
                  >
                    {viewModel?.program_owner_email}
                  </Text>
                </View>
              </View>
              <View
                style={tw(
                  "flex justify-center items-center h-[50px] text-xl bg-black"
                )}
              >
                <Text style={tw("text-center font-bold text-white")}>
                  TEAM INFORMATION
                </Text>
              </View>
              <View style={tw("flex flex-row h-[45px] w-full items-start")}>
                <View style={tw("flex flex-row h-[45px] w-[50%]")}>
                  <View
                    style={tw(
                      "flex flex-row h-[45px] max-h-[45px] w-[168px] min-w-[168px] max-w-[168px] items-end justify-end border-b border-r border-b-gray-400 border-r-gray-400 pb-1 pr-2 text-right text-lg font-bold text-black"
                    )}
                  >
                    <Text> TEAM NAME:</Text>
                  </View>
                  <Text
                    style={tw(
                      "h-full w-full border border-x-0 border-t-0 border-b-black pb-1 pl-2 pt-4"
                    )}
                  >
                    {viewModel?.team_name}
                  </Text>
                </View>
                <View style={tw("flex flex-row h-[45px] w-[50%]")}>
                  <View
                    style={tw(
                      "flex h-[45px] flex-row max-h-[45px] w-[168px] min-w-[168px] max-w-[168px] items-end justify-end border-b border-l border-r border-b-gray-400 border-l-gray-400 border-r-gray-400 pb-1 pr-2 text-right text-lg font-bold text-black"
                    )}
                  >
                    <Text> MASCOT:</Text>
                  </View>
                  <Text
                    style={tw(
                      "h-full w-full border border-x-0 border-t-0 border-b-black pb-1 pl-2 pt-4"
                    )}
                  >
                    {teamDetails?.mascot}
                  </Text>
                </View>
              </View>
              <View style={tw("flex flex-row h-[45px] w-full items-start")}>
                <View style={tw("flex flex-row h-[45px] w-[50%]")}>
                  <View
                    style={tw(
                      "flex h-[45px] flex-row max-h-[45px] w-[168px] min-w-[168px] max-w-[168px] items-end justify-end border-b border-r border-b-gray-400 border-r-gray-400 pb-1 pr-2 text-right text-lg font-bold text-black"
                    )}
                  >
                    <Text> DIVISION:</Text>
                  </View>
                  <Text
                    style={tw(
                      "h-full w-full border border-x-0 border-t-0 border-b-black pb-1 pl-2 pt-4"
                    )}
                  >
                    {viewModel?.division}
                  </Text>
                </View>
                <View style={tw("flex flex-row h-[45px] w-[50%]")}>
                  <View
                    style={tw(
                      "flex flex-row h-[45px] max-h-[45px] w-[168px] min-w-[168px] max-w-[168px] items-end justify-end border-b border-l border-r border-b-gray-400 border-l-gray-400 border-r-gray-400 pb-1 pr-2 text-right text-lg font-bold text-black"
                    )}
                  >
                    COLORS:
                  </View>
                  <Text
                    style={tw(
                      "h-full w-full border border-x-0 border-t-0 border-b-black pb-1 pl-2 pt-4"
                    )}
                  >
                    {teamDetails?.colors}
                  </Text>
                </View>
              </View>
              <View style={tw("team-details-eightcount")}>
                <View
                  style={tw(
                    "flex justify-center items-center h-[50px] text-xl bg-black"
                  )}
                >
                  <Text style={tw("text-center font-bold text-white")}>
                    TEAM DETAILS AND IDEAS
                  </Text>
                </View>
                <View
                  style={tw(
                    "team-details-eightcount flex w-full flex-col gap-6 p-4"
                  )}
                >
                  <View style={tw("flex flex-col gap-4")}>
                    <Text>THEME:</Text>
                    <View style={tw("ml-1 mt-3 flex w-full gap-5 text-black")}>
                      <Text>{viewModel?.theme_of_the_routine}</Text>
                    </View>
                  </View>
                  <View style={tw("flex flex-col gap-4")}>
                    <Text>NOTES:</Text>
                    <View style={tw("ml-1 mt-3 flex w-full gap-5 text-black")}>
                      <Text>{teamDetails?.notes}</Text>
                    </View>
                  </View>
                  <View style={tw("flex flex-col gap-4")}>
                    <Text className="mb-3">IDEAS</Text>
                    {submittedIdeas.map((elem, index) => (
                      <View
                        key={index}
                        style={tw("ml-1 flex w-full gap-5 text-black")}
                      >
                        <Text>
                          {elem?.idea_value?.replace(/<br\s*\/?>/gi, "\n")}
                        </Text>
                        <View></View>
                      </View>
                    ))}
                  </View>
                </View>
              </View>
              <View break style={tw("eight-table w-full p-2")}>
                <View
                  style={tw(
                    "eight-table-print w-full rounded-md border-0 border-none"
                  )}
                  id="-container"
                >
                  <View
                    style={{
                      display: "flex",
                      flexDirection: "row",
                      width: "100%",
                      backgroundColor: "black",
                    }}
                  >
                    <View
                      style={tw(
                        "w-[10.8%] border-0 py-7 text-center text-xs font-semibold uppercase tracking-wider text-white"
                      )}
                    >
                      <Text style={tw("text-[18px] font-bold")}> Section</Text>
                    </View>
                    <View
                      style={tw(
                        "w-[2.8%] border-0 py-7 text-center text-sm font-semibold uppercase tracking-wider text-white"
                      )}
                    >
                      <Text> </Text>
                    </View>
                    <View
                      style={tw(
                        "w-[10.8%] border-0 py-7 text-center text-base font-bold uppercase tracking-wider text-white"
                      )}
                    >
                      <Text style={tw("text-[18px] font-bold")}>1</Text>
                    </View>
                    <View
                      style={tw(
                        "w-[10.8%] border-0 py-7 text-center text-base font-bold uppercase tracking-wider text-white"
                      )}
                    >
                      <Text style={tw("text-[18px] font-bold")}>2</Text>
                    </View>
                    <View
                      style={tw(
                        "w-[10.8%] border-0 py-7 text-center text-base font-bold uppercase tracking-wider text-white"
                      )}
                    >
                      <Text style={tw("text-[18px] font-bold")}>3</Text>
                    </View>
                    <View
                      style={tw(
                        "w-[10.8%] border-0 py-7 text-center text-base font-bold uppercase tracking-wider text-white"
                      )}
                    >
                      <Text style={tw("text-[18px] font-bold")}>4</Text>
                    </View>
                    <View
                      style={tw(
                        "w-[10.8%] border-0 py-7 text-center text-base font-bold uppercase tracking-wider text-white"
                      )}
                    >
                      <Text style={tw("text-[18px] font-bold")}>5</Text>
                    </View>
                    <View
                      style={tw(
                        "w-[10.8%] border-0 py-7 text-center text-base font-bold uppercase tracking-wider text-white"
                      )}
                    >
                      <Text style={tw("text-[18px] font-bold")}>6</Text>
                    </View>
                    <View
                      style={tw(
                        "w-[10.8%] border-0 py-7 text-center text-base font-bold uppercase tracking-wider text-white"
                      )}
                    >
                      <Text style={tw("text-[18px] font-bold")}>7</Text>
                    </View>
                    <View
                      style={tw(
                        "w-[10.8%] border-0 py-7 text-center text-base font-bold uppercase tracking-wider text-white"
                      )}
                    >
                      <Text style={tw("text-[18px] font-bold")}>8</Text>
                    </View>
                    {/* Repeat similar cells for other sections */}
                    {/* <Text style={tw('  border-0 py-3 text-center text-xs font-semibold uppercase tracking-wider text-white ')}>
                  2
                </Text> */}
                    {/* More sections */}
                  </View>

                  <View style={tw("rows-table-cont text-black w-full")}>
                    {currentData &&
                      currentData?.json_data &&
                      JSON.parse(currentData?.json_data).map(
                        (rowData, rowIndex) => {
                          let a = "";
                          const duplicate =
                            versions.find(
                              (version) =>
                                version?.id == currentData?.duplicate_id
                            ) || "";

                          let isSame1 = false;

                          if (duplicate) {
                            const duplicateJsonData = duplicate?.json_data
                              ? JSON.parse(duplicate.json_data)
                              : null;
                            const duplicateCell =
                              duplicateJsonData?.[rowIndex]?.[0] || "";
                            isSame1 =
                              rowData?.[0] &&
                              cleanDisplayValue(rowData[0]) ==
                                cleanDisplayValue(duplicateCell);
                          }
                          return (
                            <View
                              break={
                                rowIndex === 19 || rowIndex === 38
                                  ? true
                                  : false
                              }
                              key={rowIndex}
                              style={tw("flex flex-row w-full")}
                            >
                              <View
                                key={0}
                                style={tw(
                                  `h-[80px] w-[10.8%] max-h-[80px] border border-black text-left text-base font-bold uppercase tracking-wider text-black`
                                )}
                              >
                                <View
                                  style={tw(
                                    `flex flex-col items-center justify-center gap-1 h-full`
                                  )}
                                >
                                  <Text
                                    style={tw(
                                      [
                                        duplicate &&
                                        !isSame1 &&
                                        rowData[0]?.length > 0
                                          ? "text-red-700 bg-white"
                                          : "text-black bg-white",
                                        duplicate &&
                                          currentData?.uses_modified_system &&
                                          isModifiedCell(
                                            rowData[0],
                                            rowIndex,
                                            0
                                          ) &&
                                          "text-red-700 bg-white",
                                        "h-[78px] w-full overflow-hidden border-0 p-2 text-center sm:text-[14px]",
                                      ]
                                        .filter(Boolean) // Remove falsy values
                                        .join(" ") // Ensure no unnecessary spaces
                                    )}
                                  >
                                    {rowData[0].replace("{{modified}}", "")}
                                  </Text>
                                </View>
                              </View>

                              <View
                                style={tw(
                                  "w-[3.8%] flex-row items-center justify-center border text-white border-black bg-black py-3 text-center text-base font-bold uppercase tracking-wider"
                                )}
                              >
                                <Text style={tw("text-[18px] font-bold")}>
                                  {" "}
                                  {rowIndex + 1}
                                </Text>
                              </View>
                              {/* Uncomment and adjust as needed */}
                              {rowData.map((cellData, colIndex) => {
                                const duplicate =
                                  versions.find(
                                    (version) =>
                                      version?.id == currentData?.duplicate_id
                                  ) || "";
                                let isSame = false;

                                if (duplicate) {
                                  const duplicateJsonData = duplicate?.json_data
                                    ? JSON.parse(duplicate.json_data)
                                    : null;
                                  const duplicateCell =
                                    duplicateJsonData?.[rowIndex]?.[colIndex] ||
                                    "";
                                  isSame =
                                    cellData?.length > 0 &&
                                    cleanDisplayValue(cellData) ===
                                      cleanDisplayValue(duplicateCell);
                                }

                                return (
                                  <>
                                    {/* {colIndex === 0 && (
                            <Text key={colIndex} style={tw('h-[80px] max-h-[80px] w-[40px]  border border-black bg-white text-left text-xs font-semibold uppercase tracking-wider text-black ')}>
                              {rowData[colIndex]}
                            </Text>
                          )} */}
                                    {colIndex !== 0 && (
                                      <View
                                        key={colIndex}
                                        style={tw(
                                          `h-[80px] w-[10.8%] max-h-[80px] border border-black text-left text-xs font-semibold tracking-wider text-black`
                                        )}
                                      >
                                        <View
                                          style={tw(
                                            `flex flex-col items-center justify-center gap-1 h-[78px]`
                                          )}
                                        >
                                          <View
                                            // style={{
                                            //   display: 'flex',
                                            //   flexDirection: 'row',
                                            //   width: '100%',
                                            //   flexWrap: 'wrap',
                                            // }}
                                            style={tw(
                                              `flex flex-row w-full flex-wrap ${
                                                isGrayed(rowIndex, colIndex)
                                                  ? "h-[54px]"
                                                  : "h-full"
                                              }`
                                            )}
                                          >
                                            <Text
                                              style={tw(
                                                [
                                                  isModifiedCell(
                                                    rowData[colIndex],
                                                    rowIndex,
                                                    colIndex
                                                  )
                                                    ? "text-red-700 bg-white"
                                                    : "bg-white text-black",
                                                  isGrayed(rowIndex, colIndex)
                                                    ? "h-[54px]"
                                                    : "h-full",
                                                  "w-full flex flex-wrap border-0 p-2 text-center sm:text-[11px]",
                                                ]
                                                  .filter(Boolean)
                                                  .join(" ")
                                              )}
                                            >
                                              {rowData[colIndex].replace(
                                                "{{modified}}",
                                                ""
                                              )}
                                            </Text>
                                          </View>
                                          {isGrayed(rowIndex, colIndex) ? (
                                            <View
                                              style={tw(
                                                [
                                                  isModifiedCell(
                                                    rowData[colIndex],
                                                    rowIndex,
                                                    colIndex
                                                  )
                                                    ? "text-red-700 bg-white"
                                                    : "bg-white text-black",
                                                  isGrayed(rowIndex, colIndex)
                                                    ? "border-t border-t-black pt-1"
                                                    : "",
                                                  "flex max-h-[24px] h-[24px] w-full items-center justify-center text-center",
                                                ]
                                                  .filter(Boolean)
                                                  .join(" ")
                                              )}
                                            >
                                              {isGrayed(rowIndex, colIndex) ? (
                                                <Text
                                                  style={tw(
                                                    `${
                                                      isGrayed(
                                                        rowIndex,
                                                        colIndex
                                                      )
                                                        ? "font-medium h-[24px] text-black"
                                                        : ""
                                                    }`
                                                  )}
                                                >
                                                  End{""}
                                                  {isGrayed(rowIndex, colIndex)}
                                                </Text>
                                              ) : null}
                                            </View>
                                          ) : (
                                            <View></View>
                                          )}
                                        </View>
                                      </View>
                                    )}
                                  </>
                                );
                              })}
                            </View>
                          );
                        }
                      )}
                  </View>
                </View>
              </View>
            </View>
          </Page>
        </Document>
      </>
    );
  }
}

export default EightCountTabToBePrinted;
