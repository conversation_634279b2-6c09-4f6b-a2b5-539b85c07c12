import React, { useState, useEffect, useContext } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { ClipLoader } from "react-spinners";
import SunEditor from "suneditor-react";
import { GlobalContext, showToast } from "../../../globalContext";

// Validation schema
const schema = yup.object().shape({
  contract_agreement: yup.string().required("Service agreement is required"),
});

// SunEditor button configuration
const buttonList = {
  complex: [
    ["undo", "redo"],
    ["font", "fontSize", "formatBlock"],
    ["paragraphStyle", "blockquote"],
    ["bold", "underline", "italic", "strike", "subscript", "superscript"],
    ["fontColor", "hiliteColor", "textStyle"],
    ["removeFormat"],
    "/", // Line break
    ["outdent", "indent"],
    ["align", "horizontalRule", "list", "lineHeight"],
    ["table", "link", "image", "video", "audio"],
    ["fullScreen", "showBlocks", "codeView"],
    ["preview", "print"],
    ["save", "template"],
  ],
};

const ClientServiceAgreementStep = ({
  stepData,
  userDetails,
  onComplete,
  onSkip,
}) => {
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const [isLoading, setIsLoading] = useState(false);
  const [contractAgreement, setContractAgreement] = useState("");

  const {
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      contract_agreement: userDetails?.contract_agreement || "",
    },
  });

  useEffect(() => {
    // Set initial contract agreement if it exists
    if (userDetails?.contract_agreement) {
      setContractAgreement(userDetails.contract_agreement);
      setValue("contract_agreement", userDetails.contract_agreement);
    } else {
      // Set default template
      const defaultTemplate = `
        <h3>Client Service Agreement</h3>
        <p>This agreement outlines the terms and conditions for services provided by our company.</p>
        
        <h4>1. Service Scope</h4>
        <p>We provide comprehensive equality record management and compliance tracking services.</p>
        
        <h4>2. Client Responsibilities</h4>
        <ul>
          <li>Provide accurate and timely information</li>
          <li>Comply with data protection requirements</li>
          <li>Maintain confidentiality of sensitive data</li>
        </ul>
        
        <h4>3. Service Standards</h4>
        <p>We commit to delivering high-quality services in accordance with industry standards and regulatory requirements.</p>
        
        <h4>4. Data Protection</h4>
        <p>All client data will be handled in accordance with applicable data protection laws and our privacy policy.</p>
        
        <h4>5. Terms and Conditions</h4>
        <p>This agreement is subject to our standard terms and conditions of service.</p>
      `;
      setContractAgreement(defaultTemplate);
      setValue("contract_agreement", defaultTemplate);
    }
  }, [userDetails, setValue]);

  const getSunEditorInstance = (sunEditor) => {
    // Store editor instance if needed for future use
    console.log("SunEditor instance:", sunEditor);
  };

  const handleEditorChange = (content) => {
    setContractAgreement(content);
    setValue("contract_agreement", content);
  };

  const onSubmit = async (data) => {
    try {
      setIsLoading(true);

      // Validate that content is not empty (excluding HTML tags)
      const textContent = contractAgreement.replace(/<[^>]*>/g, "").trim();
      if (!textContent) {
        showToast(
          globalDispatch,
          "Please enter your service agreement content",
          4000,
          "error"
        );
        return;
      }

      const agreementData = {
        contract_agreement: contractAgreement,
      };

      onComplete(agreementData);
    } catch (error) {
      console.error("Error saving service agreement:", error);
      showToast(
        globalDispatch,
        "Failed to save service agreement",
        4000,
        "error"
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="mx-auto max-w-[500px] space-y-6">
      {/* Header */}
      <div className="text-center">
        <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
          <svg
            width="56"
            height="56"
            viewBox="0 0 56 56"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <rect x="4" y="4" width="48" height="48" rx="24" fill="#E0E6FC" />
            <rect
              x="4"
              y="4"
              width="48"
              height="48"
              rx="24"
              stroke="#F5F7FF"
              stroke-width="8"
            />
            <g clip-path="url(#clip0_3161_2954)">
              <path
                d="M24.25 26.5C24.0511 26.5 23.8603 26.579 23.7197 26.7197C23.579 26.8603 23.5 27.0511 23.5 27.25C23.5 27.4489 23.579 27.6397 23.7197 27.7803C23.8603 27.921 24.0511 28 24.25 28H31.75C31.9489 28 32.1397 27.921 32.2803 27.7803C32.421 27.6397 32.5 27.4489 32.5 27.25C32.5 27.0511 32.421 26.8603 32.2803 26.7197C32.1397 26.579 31.9489 26.5 31.75 26.5H24.25ZM23.5 30.25C23.5 30.0511 23.579 29.8603 23.7197 29.7197C23.8603 29.579 24.0511 29.5 24.25 29.5H31.75C31.9489 29.5 32.1397 29.579 32.2803 29.7197C32.421 29.8603 32.5 30.0511 32.5 30.25C32.5 30.4489 32.421 30.6397 32.2803 30.7803C32.1397 30.921 31.9489 31 31.75 31H24.25C24.0511 31 23.8603 30.921 23.7197 30.7803C23.579 30.6397 23.5 30.4489 23.5 30.25ZM23.5 33.25C23.5 33.0511 23.579 32.8603 23.7197 32.7197C23.8603 32.579 24.0511 32.5 24.25 32.5H27.25C27.4489 32.5 27.6397 32.579 27.7803 32.7197C27.921 32.8603 28 33.0511 28 33.25C28 33.4489 27.921 33.6397 27.7803 33.7803C27.6397 33.921 27.4489 34 27.25 34H24.25C24.0511 34 23.8603 33.921 23.7197 33.7803C23.579 33.6397 23.5 33.4489 23.5 33.25Z"
                fill="#3C50E0"
              />
              <path
                d="M30.25 16H22C21.2044 16 20.4413 16.3161 19.8787 16.8787C19.3161 17.4413 19 18.2044 19 19V37C19 37.7956 19.3161 38.5587 19.8787 39.1213C20.4413 39.6839 21.2044 40 22 40H34C34.7956 40 35.5587 39.6839 36.1213 39.1213C36.6839 38.5587 37 37.7956 37 37V22.75L30.25 16ZM30.25 17.5V20.5C30.25 21.0967 30.4871 21.669 30.909 22.091C31.331 22.5129 31.9033 22.75 32.5 22.75H35.5V37C35.5 37.3978 35.342 37.7794 35.0607 38.0607C34.7794 38.342 34.3978 38.5 34 38.5H22C21.6022 38.5 21.2206 38.342 20.9393 38.0607C20.658 37.7794 20.5 37.3978 20.5 37V19C20.5 18.6022 20.658 18.2206 20.9393 17.9393C21.2206 17.658 21.6022 17.5 22 17.5H30.25Z"
                fill="#3C50E0"
              />
            </g>
            <defs>
              <clipPath id="clip0_3161_2954">
                <rect
                  width="24"
                  height="24"
                  fill="white"
                  transform="translate(16 16)"
                />
              </clipPath>
            </defs>
          </svg>
        </div>
        <h2 className="text-2xl font-bold text-[#131E2B]">
          Client Service Agreement
        </h2>
        <p className="mt-2 text-sm text-[#667484]">
          Protect your business and set clear client expectations by providing
          your custom service agreement below. It will be automatically
          presented when clients book your services.
        </p>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="w-full">
          {/* Manual Entry Option - Only Option */}
          <div className="space-y-4">
            <div className="white  ml-7">
              <SunEditor
                setContents={contractAgreement}
                onChange={handleEditorChange}
                getSunEditorInstance={getSunEditorInstance}
                setOptions={{
                  buttonList: buttonList.complex,
                  height: 400,
                  width: "100%",
                  placeholder: "Enter your service agreement content here...",
                  defaultStyle:
                    "color: #131E2B; background-color: #ffffff; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;",
                  colorList: [
                    ["#131E2B", "#667484", "#3C50E0", "#000000", "#ffffff"],
                    ["#ff0000", "#00ff00", "#0000ff", "#ffff00", "#ff00ff"],
                  ],
                }}
              />
            </div>
          </div>

          {errors.contract_agreement && (
            <p className="mt-1 text-sm text-red-400">
              {errors.contract_agreement.message}
            </p>
          )}
        </div>

        {/* Action Buttons - centered at bottom */}
        <div className="mt-auto flex justify-center gap-4 pt-8">
          <button
            type="button"
            onClick={onSkip}
            disabled={isLoading}
            className="flex h-[44px] w-[200px] items-center justify-center rounded-lg border border-gray-600 px-8 py-3 font-medium text-[#3f4d5d] transition-colors hover:bg-white/80 disabled:cursor-not-allowed disabled:opacity-50"
          >
            Skip for Now
          </button>
          <button
            type="submit"
            disabled={isLoading}
            className="flex h-[44px] w-[200px] items-center justify-center gap-2 rounded-lg bg-[#3C50E0] px-8 py-3 font-medium text-white transition-colors hover:bg-primary/90 disabled:cursor-not-allowed disabled:opacity-50"
          >
            {isLoading ? (
              <>
                <ClipLoader size={16} color="#ffffff" />
                Saving...
              </>
            ) : (
              "Continue"
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ClientServiceAgreementStep;
