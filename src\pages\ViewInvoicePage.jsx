import React, { useState, useEffect, useContext } from "react";
import { useParams, useNavigate } from "react-router-dom";
import MkdSDK from "../utils/MkdSDK";
import { GlobalContext } from "../globalContext";
import moment from "moment";
import { ClipLoader } from "react-spinners";
import { ArrowLeft, RefreshCw, Download, Edit, FileText } from "lucide-react";
import RefundModal from "../components/Modal/RefundModal";
import { getUserDetailsByIdAPI } from "Src/services/userService";
import { PDFDownloadLink } from "@react-pdf/renderer";
import InvoiceQuotePDF from "../components/Invoice/InvoiceQuotePDF";

const ViewInvoicePage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const [loading, setLoading] = useState(true);
  const [invoice, setInvoice] = useState(null);
  const [userDetails, setUserDetails] = useState(null);
  const [totalPrice, setTotalPrice] = useState(0);
  const [totalPaid, setTotalPaid] = useState(0);
  const [balanceDue, setBalanceDue] = useState(0);
  const [companyInfo, setCompanyInfo] = useState(null);
  const [producerMap, setProducerMap] = useState({});
  const [isRefundModalOpen, setIsRefundModalOpen] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [mixSeasons, setMixSeasons] = useState({});
  const [isStatusModalOpen, setIsStatusModalOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [newStatus, setNewStatus] = useState("");

  console.log(mixSeasons, "mixseasons");

  const fetchCompanyInfo = async () => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        "/v3/api/custom/equality_record/user/company/info",
        {},
        "GET"
      );

      if (!response.error && response.company) {
        setCompanyInfo(response);

        // Create a map of producer IDs to names
        const producersMap = {};

        // Add main member
        if (response.company.main_member) {
          producersMap[
            response.company.main_member.id
          ] = `${response.company.main_member.first_name} ${response.company.main_member.last_name}`;
        }

        // Add other members
        if (response.company.members && response.company.members.length > 0) {
          response.company.members.forEach((member) => {
            producersMap[
              member.id
            ] = `${member.first_name} ${member.last_name}`;
          });
        }

        setProducerMap(producersMap);
      }
    } catch (error) {
      console.error("Error fetching company info:", error);
    }
  };

  const fetchMixSeasonsForProducer = async (producerId) => {
    if (!producerId) return;

    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        `/v4/api/records/mix_season?filter=user_id,eq,${producerId}`,
        {},
        "GET"
      );

      if (!response.error && response.list) {
        // Sort seasons in ascending order
        const sortedSeasons = response.list.sort((a, b) => {
          // First try to sort by name if it contains numbers
          const aNum = parseInt(a.name.replace(/\D/g, ""));
          const bNum = parseInt(b.name.replace(/\D/g, ""));

          if (!isNaN(aNum) && !isNaN(bNum)) {
            return aNum - bNum;
          }

          // Fall back to alphabetical sort
          return a.name.localeCompare(b.name);
        });

        // Update the mixSeasons state with the new data for this producer
        setMixSeasons((prev) => ({
          ...prev,
          [producerId]: sortedSeasons,
        }));
      }
    } catch (error) {
      console.error(
        `Error fetching mix seasons for producer ${producerId}:`,
        error
      );
    }
  };

  useEffect(() => {
    const fetchInvoiceDetails = async () => {
      try {
        setLoading(true);
        const sdk = new MkdSDK();
        const result = await sdk.getInvoiceById(id);
        setInvoice(result);

        // Calculate total price
        if (result?.items?.length > 0) {
          const total = result.items.reduce((sum, item) => {
            return sum + parseFloat(item.total || 0);
          }, 0);
          setTotalPrice(total);
        }

        // Calculate total paid amount from successful payments only
        if (result?.payments?.length > 0) {
          const paid = result.payments.reduce((sum, payment) => {
            // Only count successful payments (exclude requires_payment_method)
            if (
              (payment.payment_method === "check" ||
                payment.status === "succeeded") &&
              payment.refunded !== 1 &&
              payment.status !== "requires_payment_method"
            ) {
              return sum + parseFloat(payment.amount || 0);
            }
            return sum;
          }, 0);
          setTotalPaid(paid);

          // Calculate balance due (never negative)
          const totalInvoiceAmount = parseFloat(result.invoice?.total || 0);
          setBalanceDue(Math.max(totalInvoiceAmount - paid, 0));
        } else {
          setTotalPaid(0);
          setBalanceDue(parseFloat(result.invoice?.total || 0));
        }

        // Fetch company info to map producer IDs to names
        await fetchCompanyInfo();

        if (result?.invoice?.user_id) {
          const userResult = await getUserDetailsByIdAPI(
            result.invoice.user_id
          );
          setUserDetails(userResult);
        }

        // Fetch mix seasons for each producer in the invoice items
        if (result.items && result.items.length > 0) {
          const producerIds = new Set();

          // Collect all unique producer IDs
          result.items.forEach((item) => {
            if (item.producer) {
              producerIds.add(item.producer);
            }
          });

          // Fetch mix seasons for each unique producer
          const promises = [];
          producerIds.forEach((producerId) => {
            promises.push(fetchMixSeasonsForProducer(producerId));
          });

          await Promise.all(promises);
        }
      } catch (error) {
        console.error("Error fetching invoice:", error);
        globalDispatch({
          type: "SHOW_TOAST",
          payload: {
            text: "Failed to fetch invoice details",
            type: "error",
          },
        });
      } finally {
        setLoading(false);
      }
    };

    fetchInvoiceDetails();
  }, [id, globalDispatch, refreshTrigger]);

  // Function to handle refund completion
  const handleRefundComplete = () => {
    // Increment the refresh trigger to reload the data
    setRefreshTrigger((prev) => prev + 1);
  };

  // Function to get mix season name from ID
  const getMixSeasonName = (mixSeasonId, producerId) => {
    if (!mixSeasonId || !producerId || !mixSeasons[producerId]) {
      return "Unknown";
    }

    const season = mixSeasons[producerId].find(
      (season) => season.id === parseInt(mixSeasonId)
    );

    return season ? season.name : "Unknown";
  };

  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <ClipLoader color="#fff" size={30} />
      </div>
    );
  }

  if (!invoice) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-danger">Invoice Not Found</h2>
        </div>
      </div>
    );
  }

  return (
    <div className="rounded-lg bg-boxdark p-6">
      {/* Company Information Header */}
      <div className="mb-6 flex items-center justify-between rounded-lg bg-meta-4 p-4">
        <div className="flex items-center gap-4">
          {companyInfo?.company?.manager?.license_company_logo &&
          companyInfo.company.manager.license_company_logo !== "null" ? (
            <img
              src={companyInfo.company.manager.license_company_logo}
              alt={companyInfo.company.manager.company_name || "Company Logo"}
              className="h-16 w-auto object-contain"
            />
          ) : companyInfo?.company?.main_member?.license_company_logo &&
            companyInfo.company.main_member.license_company_logo !== "null" ? (
            <img
              src={companyInfo.company.main_member.license_company_logo}
              alt={
                companyInfo.company.main_member.company_name || "Company Logo"
              }
              className="h-16 w-auto object-contain"
            />
          ) : userDetails?.model?.company_logo ? (
            <img
              src={userDetails.model.company_logo}
              alt={userDetails?.model?.company_name || "Company Logo"}
              className="h-16 w-auto object-contain"
            />
          ) : null}
          <div>
            <h2 className="text-xl font-bold text-white">
              {companyInfo?.company?.manager?.company_name &&
              companyInfo.company.manager.company_name !== "null"
                ? companyInfo.company.manager.company_name
                : companyInfo?.company?.main_member?.company_name &&
                  companyInfo.company.main_member.company_name !== "null"
                ? companyInfo.company.main_member.company_name
                : userDetails?.model?.company_name || "Your Company"}
            </h2>
            <p className="text-sm text-bodydark2">
              {companyInfo?.company?.manager?.office_email &&
              companyInfo.company.manager.office_email !== "null"
                ? companyInfo.company.manager.office_email
                : companyInfo?.company?.main_member?.office_email &&
                  companyInfo.company.main_member.office_email !== "null"
                ? companyInfo.company.main_member.office_email
                : userDetails?.model?.office_email || ""}
            </p>
          </div>
        </div>
        <div className="text-right">
          <h2 className="text-2xl font-bold text-white">
            {invoice.invoice?.status === "quote" ? "QUOTE" : "INVOICE"}
          </h2>
          {/* <div className="flex gap-2 items-center mt-2">
            <span
              className={`rounded px-2.5 py-0.5 text-sm font-medium ${
                invoice.invoice.status === "paid"
                  ? "bg-success/10 text-success"
                  : invoice.invoice.status === "pending"
                  ? "bg-warning/10 text-warning"
                  : "bg-danger/10 text-danger"
              }`}
            >
              {invoice.invoice.status}
            </span>
            {invoice.invoice.payment_status && (
              <span
                className={`rounded px-2.5 py-0.5 text-sm font-medium ${
                  invoice.invoice.payment_status === "succeeded"
                    ? "bg-success/10 text-success"
                    : "bg-warning/10 text-warning"
                }`}
              >
                {invoice.invoice.payment_status.replace(/_/g, " ")}
              </span>
            )}
          </div> */}
        </div>
      </div>

      {/* Header with back button and action buttons */}
      <div className="mb-6 flex items-center justify-between">
        <button
          onClick={() => navigate("/member/invoices")}
          className="flex items-center gap-2 text-white hover:text-primary"
        >
          <ArrowLeft className="h-5 w-5" />
          Back to Invoices
        </button>
        <h2 className="text-2xl font-bold text-white">
          {invoice.invoice?.status === "quote" ? "Quote" : "Invoice"} #
          {invoice.invoice.id}
        </h2>
        <div className="flex items-center gap-3">
          <p className="text-sm text-bodydark2">
            Created: {moment(invoice.invoice.create_at).format("MMM DD, YYYY")}
          </p>
          <div className="flex items-center gap-2">
            {/* Export PDF Button */}
            <PDFDownloadLink
              document={
                <InvoiceQuotePDF
                  data={{
                    selectedClientId: invoice.invoice.client_id,
                    newClientData: {},
                    invoiceDates: {
                      invoiceDate: invoice.invoice.invoice_date,
                      invoiceDueDate: invoice.invoice.due_date,
                    },
                    invoiceData: {
                      invoiceNumber: invoice.invoice.id,
                      items: invoice.items.map((item) => ({
                        price: item.price,
                        quantity: item.quantity || 1,
                        discount: item.discount || 0,
                        mixDate: item.mix_date,
                        teamName: item.team_name,
                        division: item.division,
                        producer: item.producer,
                        mixType:
                          item.description?.split(" - ")[1] || item.description,
                        musicSurveyDue: item.music_survey_due,
                        routineSubmissionDue: item.routine_submission_due,
                        estimatedCompletion: item.estimated_completion,
                        isSpecial: !item.mix_date && !item.team_name,
                        name:
                          !item.mix_date && !item.team_name
                            ? item.description || "Additional Charge"
                            : "",
                      })),
                      notes: invoice.invoice.notes,
                      termsAndConditions: invoice.invoice.terms_and_conditions,
                      depositPercentage:
                        invoice.invoice.deposit_percentage || 0,
                      isQuote: false,
                    },
                    clientDetails: {
                      program: invoice.invoice.program,
                      email: invoice.invoice.client_email,
                    },
                  }}
                  userDetails={userDetails}
                  companyInfo={companyInfo}
                />
              }
              fileName={`invoice-${invoice.invoice.id}-${moment().format(
                "YYYY-MM-DD"
              )}.pdf`}
              className="flex items-center gap-1 rounded bg-meta-4 px-3 py-1.5 text-sm font-medium text-white hover:bg-opacity-80"
            >
              {({ loading: pdfLoading }) => (
                <>
                  <Download className="h-4 w-4" />
                  {pdfLoading ? "Generating..." : "Export PDF"}
                </>
              )}
            </PDFDownloadLink>

            {/* Save as Quote Button */}
            <PDFDownloadLink
              document={
                <InvoiceQuotePDF
                  data={{
                    selectedClientId: invoice.invoice.client_id,
                    newClientData: {},
                    invoiceDates: {
                      invoiceDate: invoice.invoice.invoice_date,
                      invoiceDueDate: invoice.invoice.due_date,
                    },
                    invoiceData: {
                      invoiceNumber: invoice.invoice.id,
                      items: invoice.items.map((item) => ({
                        price: item.price,
                        quantity: item.quantity || 1,
                        discount: item.discount || 0,
                        mixDate: item.mix_date,
                        teamName: item.team_name,
                        division: item.division,
                        producer: item.producer,
                        mixType:
                          item.description?.split(" - ")[1] || item.description,
                        musicSurveyDue: item.music_survey_due,
                        routineSubmissionDue: item.routine_submission_due,
                        estimatedCompletion: item.estimated_completion,
                        isSpecial: !item.mix_date && !item.team_name,
                        name:
                          !item.mix_date && !item.team_name
                            ? item.description || "Additional Charge"
                            : "",
                      })),
                      notes: invoice.invoice.notes,
                      termsAndConditions: invoice.invoice.terms_and_conditions,
                      depositPercentage:
                        invoice.invoice.deposit_percentage || 0,
                      isQuote: true,
                    },
                    clientDetails: {
                      program: invoice.invoice.program,
                      email: invoice.invoice.client_email,
                    },
                  }}
                  userDetails={userDetails}
                  companyInfo={companyInfo}
                />
              }
              fileName={`quote-${invoice.invoice.id}-${moment().format(
                "YYYY-MM-DD"
              )}.pdf`}
              className="flex items-center gap-1 rounded bg-meta-5 px-3 py-1.5 text-sm font-medium text-white hover:bg-opacity-80"
            >
              {({ loading: pdfLoading }) => (
                <>
                  <Download className="h-4 w-4" />
                  {pdfLoading ? "Generating..." : "Save as Quote"}
                </>
              )}
            </PDFDownloadLink>

            {/* Edit Button */}
            <button
              onClick={() => {
                // Store the invoice ID in localStorage to be picked up by InvoicePage
                localStorage.setItem("editInvoiceId", invoice.invoice.id);
                navigate("/member/invoices");
              }}
              className="flex items-center gap-1 rounded bg-primary px-3 py-1.5 text-sm font-medium text-white hover:bg-opacity-80"
            >
              <Edit className="h-4 w-4" />
              Edit
            </button>
          </div>
        </div>
      </div>

      {/* Client Information */}
      <div className="mb-3 pb-2">
        <h3 className="mb-2 text-base font-semibold text-white">
          Client Information
        </h3>
        <div className="grid grid-cols-2 gap-6 rounded-lg bg-meta-4/20 p-4">
          <div>
            <p className="text-sm text-bodydark2">Name</p>
            <p className="mt-1 font-medium">{invoice.invoice.client_name}</p>
          </div>
          <div>
            <p className="text-sm text-bodydark2">Email</p>
            <p className="mt-1 font-medium">{invoice.invoice.client_email}</p>
          </div>
          <div>
            <p className="text-sm text-bodydark2">Program</p>
            <p className="mt-1 font-medium">{invoice.invoice.program}</p>
          </div>
          <div>
            <p className="text-sm text-bodydark2">Invoice Date</p>
            <p className="mt-1 font-medium">
              {moment(invoice.invoice.invoice_date).format("MMM DD, YYYY")}
            </p>
          </div>
        </div>
      </div>

      {/* Notes */}
      <div className="mb-6">
        <h3 className="mb-2 text-base font-semibold text-white">Notes</h3>
        <div className="grid grid-cols-2 gap-6 rounded-lg bg-meta-4/20 p-4">
          <div>
            <p className="text-sm text-bodydark2">Producer Notes</p>
            <p className="mt-1 whitespace-pre-wrap">
              {invoice.invoice.notes || "No producer notes"}
            </p>
          </div>
          <div>
            <p className="text-sm text-bodydark2">Client Notes</p>
            <p className="mt-1 whitespace-pre-wrap">
              {invoice.invoice.feedback_notes || "No client notes"}
            </p>
          </div>
        </div>
      </div>

      {/* Invoice Items */}
      <div className="mb-6">
        <div className="mb-4 flex items-center justify-between">
          <h3 className="text-base font-semibold text-white">Invoice Items</h3>
        </div>
        <div className="custom-overflow min-h-[140px] overflow-x-auto">
          <table className="relative w-full table-auto">
            <thead className="bg-meta-4">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Mix Date
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Producer
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Mix Type
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Team Name
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Division
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Survey Due / Submission Due / Est. Completion
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Mix Season
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Discount
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Price
                </th>
                <th className="px-4 py-3 text-center text-xs font-medium uppercase tracking-wider text-white">
                  Status
                </th>
              </tr>
            </thead>
            <tbody className="text-white">
              {invoice.items?.map((item) => (
                <tr
                  key={item.id}
                  className={`border-b border-stroke/50 text-[12px] hover:bg-primary/5 ${
                    !item.mix_date && !item.team_name ? "bg-meta-4/20" : ""
                  }`}
                >
                  {!item.mix_date && !item.team_name ? (
                    // Special row (charge)
                    <>
                      <td colSpan={3} className="whitespace-nowrap px-4 py-3">
                        {item.description || "Additional Charge"}
                      </td>
                      <td colSpan={3} className="whitespace-nowrap px-4 py-3">
                        {/* Empty cells for team name, division, and dates */}
                      </td>
                      <td className="whitespace-nowrap px-4 py-3">
                        {/* Empty cell for mix season */}
                      </td>
                      <td className="whitespace-nowrap px-4 py-3">
                        {parseFloat(item.discount) > 0
                          ? `${parseFloat(item.discount).toFixed(2)}`
                          : "0"}
                      </td>
                      <td className="whitespace-nowrap px-4 py-3">
                        ${parseFloat(item.total || 0).toFixed(2)}
                      </td>
                      <td className="whitespace-nowrap px-4 py-3 text-center">
                        {item.status === "2" || item.status === 2 ? (
                          <span className="rounded bg-primary/10 px-2 py-1 text-xs font-medium text-primary">
                            Deposit Paid
                          </span>
                        ) : item.status === "1" || item.status === 1 ? (
                          <span className="rounded bg-success/10 px-2 py-1 text-xs font-medium text-success">
                            Complete
                          </span>
                        ) : item.status === "3" || item.status === 3 ? (
                          <span className="rounded bg-success/10 px-2 py-1 text-xs font-medium text-success">
                            Paid in Full
                          </span>
                        ) : item.status === "4" || item.status === 4 ? (
                          <span className="rounded bg-warning/10 px-2 py-1 text-xs font-medium text-warning">
                            Awaiting Edit
                          </span>
                        ) : (
                          <span className="rounded bg-warning/10 px-2 py-1 text-xs font-medium text-warning">
                            Unpaid
                          </span>
                        )}
                      </td>
                    </>
                  ) : (
                    // Regular row
                    <>
                      <td className="whitespace-nowrap px-4 py-3">
                        {item.mix_date
                          ? moment(item.mix_date).format("MM/DD/YYYY")
                          : ""}
                      </td>
                      <td className="whitespace-nowrap px-4 py-3">
                        {item.producer && producerMap[item.producer]
                          ? producerMap[item.producer]
                          : item.producer || ""}
                      </td>
                      <td className="whitespace-nowrap px-4 py-3">
                        {item.description?.split(" - ")[1] ||
                          item.description ||
                          ""}
                      </td>
                      <td className="whitespace-nowrap px-4 py-3">
                        {item.team_name || ""}
                      </td>
                      <td className="whitespace-nowrap px-4 py-3">
                        {item.division || ""}
                      </td>
                      <td className="px-4 py-3">
                        <div className="flex flex-col gap-2">
                          <div className="flex flex-row items-center justify-between gap-1">
                            <span>
                              {item.music_survey_due
                                ? moment(item.music_survey_due).format(
                                    "MM/DD/YYYY"
                                  )
                                : ""}
                            </span>
                          </div>
                          <div className="flex flex-row items-center justify-between gap-1">
                            <span>
                              {item.routine_submission_due
                                ? moment(item.routine_submission_due).format(
                                    "MM/DD/YYYY"
                                  )
                                : ""}
                            </span>
                          </div>
                          <div className="flex flex-row items-center justify-between gap-1">
                            <span>
                              {item.estimated_completion
                                ? moment(item.estimated_completion).format(
                                    "MM/DD/YYYY"
                                  )
                                : ""}
                            </span>
                          </div>
                        </div>
                      </td>
                      <td className="whitespace-nowrap px-4 py-3">
                        {item.mix_season_id
                          ? getMixSeasonName(item.mix_season_id, item.producer)
                          : item.mix_season || ""}
                      </td>
                      <td className="whitespace-nowrap px-4 py-3">
                        {parseFloat(item.discount) > 0
                          ? `${parseFloat(item.discount).toFixed(2)}`
                          : "0"}
                      </td>
                      <td className="whitespace-nowrap px-4 py-3">
                        ${parseFloat(item.total || 0).toFixed(2)}
                      </td>
                      <td className="whitespace-nowrap px-4 py-3 text-center">
                        {item.status === "2" || item.status === 2 ? (
                          <span className="rounded bg-primary/10 px-2 py-1 text-xs font-medium text-primary">
                            Deposit Paid
                          </span>
                        ) : item.status === "1" || item.status === 1 ? (
                          <span className="rounded bg-success/10 px-2 py-1 text-xs font-medium text-success">
                            Complete
                          </span>
                        ) : item.status === "3" || item.status === 3 ? (
                          <span className="rounded bg-success/10 px-2 py-1 text-xs font-medium text-success">
                            Paid in Full
                          </span>
                        ) : item.status === "4" || item.status === 4 ? (
                          <span className="rounded bg-warning/10 px-2 py-1 text-xs font-medium text-warning">
                            Awaiting Edit
                          </span>
                        ) : (
                          <span className="rounded bg-warning/10 px-2 py-1 text-xs font-medium text-warning">
                            Unpaid
                          </span>
                        )}
                      </td>
                    </>
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Invoice Total Section */}
      <div className="mb-6 rounded-lg bg-meta-4/20 p-4">
        <div className="flex justify-end">
          <div className="w-1/3">
            <div className="mb-2 flex items-center justify-between">
              <span className="text-sm font-medium text-white">
                Invoice total:
              </span>
              <span className="text-lg font-bold text-white">
                ${totalPrice.toFixed(2)}
              </span>
            </div>
            <div className="mb-2 flex items-center justify-between">
              <span className="text-sm font-medium text-white">
                Amount paid:
              </span>
              <span className="text-sm text-white">
                ${totalPaid.toFixed(2)}
              </span>
            </div>
            <div className="flex items-center justify-between border-t border-stroke/50 pt-2">
              <span className="text-sm font-medium text-white">
                Balance due:
              </span>
              <span className="text-sm font-bold text-white">
                ${balanceDue.toFixed(2)}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Payment History */}
      <div className="mb-6">
        <div className="mb-4 flex items-center justify-between">
          <h3 className="text-base font-semibold text-white">
            Payment History
          </h3>
        </div>
        <div className="custom-overflow overflow-x-auto">
          <table className="relative w-full table-auto">
            <thead className="bg-meta-4">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Date
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Method
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Status
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium uppercase tracking-wider text-white">
                  Amount
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium uppercase tracking-wider text-white">
                  Fee
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium uppercase tracking-wider text-white">
                  Total
                </th>
                <th className="px-4 py-3 text-center text-xs font-medium uppercase tracking-wider text-white">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="text-white">
              {invoice.payments?.length > 0 ? (
                invoice.payments.map((payment) => (
                  <tr
                    key={payment.id}
                    className={`border-b border-stroke/50 text-[12px] hover:bg-primary/5 ${
                      payment.refunded === 1 ? "bg-danger/5" : ""
                    }`}
                  >
                    <td className="whitespace-nowrap px-4 py-3">
                      {moment(payment.create_at).format("MM/DD/YYYY")}
                    </td>
                    <td className="whitespace-nowrap px-4 py-3 capitalize">
                      {payment.payment_method}
                      {(payment.payment_method === "check" &&
                        payment.check_attachment_url) ||
                      invoice.attachment_url ? (
                        <a
                          href={
                            payment.check_attachment_url ||
                            invoice.attachment_url
                          }
                          target="_blank"
                          rel="noopener noreferrer"
                          className="ml-2 text-primary hover:underline"
                        >
                          (View Check)
                        </a>
                      ) : null}
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      {payment.refunded === 1 ? (
                        <span className="rounded bg-danger/10 px-2 py-1 text-xs font-medium text-danger">
                          Refunded
                        </span>
                      ) : (
                        <span
                          className={`rounded px-2 py-1 text-xs font-medium ${
                            payment.status === "succeeded" ||
                            payment.payment_method === "check"
                              ? "bg-success/10 text-success"
                              : payment.status === "requires_payment_method"
                              ? "bg-danger/10 text-danger"
                              : "bg-warning/10 text-warning"
                          }`}
                        >
                          {payment.payment_method === "check"
                            ? "succeeded"
                            : payment.status === "requires_payment_method"
                            ? "Cancelled"
                            : payment.status.replace(/_/g, " ")}
                        </span>
                      )}
                    </td>
                    <td className="whitespace-nowrap px-4 py-3 text-right">
                      ${parseFloat(payment.amount || 0).toFixed(2)}
                    </td>
                    <td className="whitespace-nowrap px-4 py-3 text-right">
                      ${parseFloat(payment.stripe_fee || 0).toFixed(2)}
                    </td>
                    <td className="whitespace-nowrap px-4 py-3 text-right">
                      ${parseFloat(payment.total_charged || 0).toFixed(2)}
                    </td>
                    <td className="whitespace-nowrap px-4 py-3 text-center">
                      {payment.refunded !== 1 &&
                        (payment.status === "succeeded" ||
                          payment.payment_method === "check") && (
                          <button
                            onClick={() => {
                              setSelectedPayment(payment);
                              setIsRefundModalOpen(true);
                            }}
                            className="rounded bg-danger px-2 py-1 text-xs font-medium text-white hover:bg-danger/70"
                          >
                            Refund
                          </button>
                        )}
                      {payment.refunded === 1 && payment.refund_id && (
                        <span className="text-xs text-bodydark2">
                          Refund ID: {payment.refund_id}
                        </span>
                      )}
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td
                    colSpan={6}
                    className="px-4 py-3 text-center text-bodydark2"
                  >
                    No payment records found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Service Agreement Section */}
      <div className="mb-6 rounded-lg bg-meta-4/20 p-4">
        <h3 className="mb-2 text-base font-semibold text-white">
          Service Agreement
        </h3>
        {invoice.invoice.attachment_url ? (
          <div className="flex items-center gap-4">
            <p className="text-bodydark2">
              Service agreement has been signed and is available for download.
            </p>
            <a
              href={invoice.invoice.attachment_url}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-2 rounded bg-primary px-4 py-2 text-sm font-medium text-white hover:bg-opacity-90"
            >
              <Download className="h-4 w-4" />
              Download Service Agreement PDF
            </a>
          </div>
        ) : (
          <p className="text-bodydark2">
            Service agreement is not available yet.
          </p>
        )}
      </div>

      {/* Check Uploads Section */}
      {invoice.invoice.checks &&
        (() => {
          try {
            const checks = JSON.parse(invoice.invoice.checks);
            const validChecks = checks.filter(
              (check) => check.url && check.id && check.uploadDate
            );
            return (
              validChecks.length > 0 && (
                <div className="mb-6 rounded-lg bg-meta-4/20 p-4">
                  <h3 className="mb-4 text-base font-semibold text-white">
                    Check Uploads
                  </h3>
                  <div className="space-y-2">
                    {validChecks.map((check) => (
                      <div
                        key={check.id}
                        className="flex items-center justify-between rounded border border-stroke/50 bg-boxdark-2/40 p-3"
                      >
                        <div className="flex items-center gap-3">
                          <FileText className="h-4 w-4 text-primary" />
                          <div>
                            <p className="text-sm font-medium text-white">
                              {check.filename || "Unknown File"}
                            </p>
                            <p className="text-xs text-bodydark2">
                              Uploaded:{" "}
                              {moment(check.uploadDate).format(
                                "MMM DD, YYYY HH:mm"
                              )}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <a
                            href={check.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center gap-1 rounded bg-meta-5 px-3 py-1 text-xs font-medium text-white hover:bg-opacity-90"
                          >
                            <FileText className="h-3 w-3" />
                            View
                          </a>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )
            );
          } catch (error) {
            console.error("Error parsing checks data:", error);
            return null;
          }
        })()}

      {/* Refund Modal */}
      <RefundModal
        isOpen={isRefundModalOpen}
        onClose={() => setIsRefundModalOpen(false)}
        payment={selectedPayment}
        onRefundComplete={handleRefundComplete}
        formatCurrency={(amount) => `$${parseFloat(amount || 0).toFixed(2)}`}
      />
    </div>
  );
};

export default ViewInvoicePage;
