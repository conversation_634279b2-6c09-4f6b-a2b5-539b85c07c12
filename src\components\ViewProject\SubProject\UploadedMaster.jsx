import React, { useContext, useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import AudioPlayer from "Components/AudioPlayer";
import FileUpload from "Components/FileUpload/FileUpload";
import ConfirmModal from "Components/Modal/ConfirmModal";
import { Download, MoreVertical } from "lucide-react";
import { GlobalContext, showToast } from "Src/globalContext";

const audioFileTypes = ["mp3", "wav", "ogg", "flac", "aac", "wma", "m4a"];

const UploadedMaster = ({
  canUpload = true,
  uploadedFiles,
  showUploadBtn = true,
  setDeleteFileId,
  setEmployeeType,
  setFileUploadType,
  setFormData,
  uploadedFilesProgressData,
}) => {
  const [activeMenu, setActiveMenu] = useState(null);
  const [localDeleteFileId, setLocalDeleteFileId] = React.useState(null);
  const { dispatch } = useContext(GlobalContext);
  const [showDeleteFileConfirmModal, setShowDeleteFileConfirmModal] =
    React.useState(false);

  const handleDownload = async (url, filename) => {
    try {
      showToast(dispatch, "File Downloading", 3000);
      const response = await fetch(url);
      const blob = await response.blob();
      const blobUrl = window.URL.createObjectURL(blob);

      const link = document.createElement("a");
      link.href = blobUrl;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(blobUrl);
    } catch (error) {
      console.error("Download failed:", error);
    }
    setActiveMenu(null); // Close menu after download starts
  };

  // Close menu when clicking outside
  React.useEffect(() => {
    const handleClickOutside = () => setActiveMenu(null);
    document.addEventListener("click", handleClickOutside);
    return () => document.removeEventListener("click", handleClickOutside);
  }, []);

  return (
    <>
      <div className="flex flex-col">
        {/* Files List */}
        <div className="flex flex-wrap gap-2">
          {uploadedFiles?.map((file, index) => {
            const fileSrc = file.url;
            const fileName = fileSrc.split("/").pop();
            const fileExt = fileSrc.split(".").pop().toLowerCase();
            const isAudio = audioFileTypes.includes(fileExt);

            return (
              <div
                key={index}
                className="w-[30%] min-w-[230px] rounded border border-stroke bg-boxdark p-4 transition-all hover:border-primary"
              >
                <div className="flex items-center gap-3">
                  <div className="min-w-0 flex-1">
                    <div className="mb-2 flex items-center gap-2">
                      <FontAwesomeIcon
                        icon={
                          isAudio ? "fa-solid fa-music" : "fa-solid fa-file"
                        }
                        className="text-bodydark2"
                      />
                      <a
                        href={fileSrc}
                        rel="noreferrer"
                        target="_blank"
                        className="truncate text-sm font-medium text-white hover:text-primary"
                      >
                        {fileName}
                      </a>
                    </div>

                    {isAudio && (
                      <div className="mt-2">
                        <AudioPlayer fileSource={fileSrc} compact={true} />
                      </div>
                    )}
                  </div>

                  {canUpload && (
                    <div className="relative">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setActiveMenu(activeMenu === index ? null : index);
                        }}
                        className="rounded-full p-2 hover:bg-gray-700"
                      >
                        <MoreVertical className="h-5 w-5 text-primary" />
                      </button>

                      {activeMenu === index && (
                        <div className="absolute right-0 z-50 mt-1 w-48 rounded-md bg-boxdark shadow-lg ring-1 ring-black ring-opacity-5">
                          <div className="py-1">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDownload(fileSrc, fileName);
                              }}
                              className="flex w-full items-center px-4 py-2 text-sm text-white hover:bg-gray-700"
                            >
                              <Download className="mr-2 h-4 w-4" />
                              Download
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                setShowDeleteFileConfirmModal(true);
                                setLocalDeleteFileId(file.id);
                              }}
                              className="flex w-full items-center px-4 py-2 text-sm text-danger hover:bg-gray-700"
                            >
                              <FontAwesomeIcon
                                icon="fa-solid fa-trash"
                                className="mr-2 h-4 w-4"
                              />
                              Delete
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* Upload Section */}
        {showUploadBtn && canUpload && (
          <div
            className="mt-6 rounded border border-stroke bg-boxdark-2 p-6"
            onClick={() => {
              setEmployeeType("writer");
              setFileUploadType("instrumental");
            }}
          >
            <div className="flex flex-col items-center">
              <div className="mb-4 text-center">
                <h5 className="mb-1 text-sm font-medium text-white">
                  Upload More Files
                </h5>
                <p className="text-xs text-bodydark2">
                  Drag and drop your files or click to browse
                </p>
              </div>

              <FileUpload
                justify="center"
                items="center"
                maxFileSize={500}
                setFormData={setFormData}
                uploadedFilesProgressData={uploadedFilesProgressData}
              />
            </div>
          </div>
        )}
      </div>

      {/* Confirm Delete Modal */}
      {showDeleteFileConfirmModal && (
        <ConfirmModal
          confirmText="Are you sure you want to delete this file?"
          setModalClose={() => setShowDeleteFileConfirmModal(false)}
          setFormYes={() => {
            setDeleteFileId(localDeleteFileId);
            setShowDeleteFileConfirmModal(false);
          }}
        />
      )}
    </>
  );
};

export default UploadedMaster;
