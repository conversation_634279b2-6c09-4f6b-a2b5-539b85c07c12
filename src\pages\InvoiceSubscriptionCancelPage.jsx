import React, { useState, useEffect } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import { GlobalContext, showToast } from "Src/globalContext";
import { AuthContext } from "Src/authContext";
import { AlertCircle } from "lucide-react";

const InvoiceSubscriptionCancelPage = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { state: authState } = React.useContext(AuthContext);
  const [loading, setLoading] = useState(true);

  const invoiceId = searchParams.get("invoice_id");

  useEffect(() => {
    // Check if we have an invoice_id parameter
    if (invoiceId) {
      showToast(
        globalDispatch,
        "Subscription payment was cancelled. No charges were made to your account.",
        5000,
        "warning"
      );
    } else {
      // If no invoice_id, redirect to member login
      navigate("/member/login");
    }
    setLoading(false);
  }, [invoiceId, navigate, globalDispatch]);

  const handleTryAgain = () => {
    // Check if user is authenticated and redirect based on role
    if (!authState.isAuthenticated) {
      navigate("/member/login");
      return;
    }

    // Redirect back to subscription page to try again
    if (authState.role === "manager") {
      navigate("/manager/projects");
    } else if (authState.role === "member") {
      navigate("/member/subscription");
    } else {
      navigate("/member/login");
    }
  };

  const handleBackToDashboard = () => {
    // Check if user is authenticated and redirect based on role
    if (!authState.isAuthenticated) {
      navigate("/member/login");
      return;
    }

    // Redirect based on user role
    if (authState.role === "manager") {
      navigate("/manager/projects");
    } else if (authState.role === "member") {
      navigate("/member/dashboard");
    } else {
      navigate("/member/login");
    }
  };

  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="h-16 w-16 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <div className="flex h-screen flex-col items-center justify-center">
        <div className="text-center">
          <div className="mb-4 flex justify-center">
            <div className="rounded-full bg-warning/20 p-4">
              <AlertCircle className="h-16 w-16 text-warning" />
            </div>
          </div>
          <h1 className="mb-4 text-4xl font-bold text-warning">
            Subscription Payment Cancelled
          </h1>
          <p className="mb-6 text-lg text-bodydark">
            Your subscription payment was cancelled. No charges were made to
            your account. You can try subscribing again anytime.
          </p>

          {invoiceId && (
            <div className="mb-6 rounded-lg border border-stroke bg-meta-4/20 p-4">
              <p className="text-sm text-bodydark">
                <span className="font-semibold">Transaction ID:</span>{" "}
                <span className="font-mono text-white">{invoiceId}</span>
              </p>
            </div>
          )}

          <div className="mb-6 rounded-lg border border-stroke bg-meta-4/20 p-6">
            <h3 className="mb-4 text-lg font-semibold text-white">
              What you can do next:
            </h3>
            <ul className="space-y-2 text-left text-bodydark">
              <li className="flex items-start">
                <span className="mr-2 text-warning">•</span>
                Try subscribing again with a different payment method
              </li>
              <li className="flex items-start">
                <span className="mr-2 text-warning">•</span>
                Contact support if you're experiencing payment issues
              </li>
              <li className="flex items-start">
                <span className="mr-2 text-warning">•</span>
                Continue using the free features available in your account
              </li>
              <li className="flex items-start">
                <span className="mr-2 text-warning">•</span>
                Review our subscription plans and pricing
              </li>
            </ul>
          </div>

          <div className="flex justify-center gap-4">
            <button
              onClick={handleTryAgain}
              className="rounded bg-primary px-6 py-3 text-white hover:bg-opacity-90"
            >
              Try Again
            </button>
            <button
              onClick={handleBackToDashboard}
              className="rounded border border-stroke px-6 py-3 text-white hover:bg-meta-4"
            >
              Back to Dashboard
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InvoiceSubscriptionCancelPage;
