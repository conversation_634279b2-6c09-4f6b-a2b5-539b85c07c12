// src/Pages/manager/ManagerSettingsPage.jsx
import React, { useContext, useState, useEffect } from "react";
import { useStripe, useElements, CardElement } from "@stripe/react-stripe-js";
import { GlobalContext, showToast } from "Src/globalContext";
import MkdSDK from "Utils/MkdSDK";
import CustomSelect2 from "Components/CustomSelect2";
import SunEditor from "suneditor-react";
import "suneditor/dist/css/suneditor.min.css";
import {
  getUserDetailsByIdAPI,
  updateUserDetailsAPI,
} from "Src/services/userService";
import { uploadS3FilesAPI } from "Src/services/workOrderService";
import LogoUpload from "Components/logoUpload";

const ManagerSettingsPage = () => {
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const stripe = useStripe();
  const elements = useElements();

  // State for member form
  const [email, setEmail] = useState("");
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [phone, setPhone] = useState("");
  const [amount, setAmount] = useState(10000); // Fixed to $100 in cents
  const [paymentMethod] = useState("checkout"); // Changed to checkout
  const [paymentError, setPaymentError] = useState(null);

  // State for tab management
  const [activeTab, setActiveTab] = useState("members");

  // State for member list
  const [members, setMembers] = useState([]);
  const [loading, setLoading] = useState(false);

  // State for project settings
  const [depositPercent, setDepositPercent] = useState(50);
  const [contractAgreement, setContractAgreement] = useState("");
  // eslint-disable-next-line no-unused-vars
  const [editorInstance, setEditorInstance] = useState(null);
  const [surveySettings, setSurveySettings] = useState({
    weeks: 8,
    day: "Monday",
  });
  const [routineSettings, setRoutineSettings] = useState({
    weeks: 1,
    day: "Monday",
  });
  const [deliverySettings, setDeliverySettings] = useState({
    weeks: 1,
    day: "Friday",
  });

  // State for general settings
  const [companyName, setCompanyName] = useState("");
  const [companyAddress, setCompanyAddress] = useState("");
  const [companyPhone, setCompanyPhone] = useState("");
  const [officeEmail, setOfficeEmail] = useState("");
  const [companyLogoUrl, setCompanyLogoUrl] = useState("");
  const [licenseLogoUrl, setLicenseLogoUrl] = useState("");

  // Payment/Stripe state
  const [hasStripe, setHasStripe] = useState(false);
  const [stripeOnboardUrl, setStripeOnboardUrl] = useState("");
  const [stripeLoading, setStripeLoading] = useState(false);

  // SunEditor button list
  const buttonList = {
    complex: [
      ["undo", "redo"],
      ["font", "fontSize", "formatBlock"],
      ["bold", "underline", "italic", "strike", "subscript", "superscript"],
      ["removeFormat"],
      ["fontColor", "hiliteColor"],
      ["indent", "outdent"],
      ["align", "horizontalRule", "list", "table"],
      ["link"],
      ["fullScreen", "showBlocks", "codeView"],
      ["preview"],
    ],
  };

  // Get SunEditor instance
  const getSunEditorInstance = (sunEditor) => {
    setEditorInstance(sunEditor);
  };

  // Update project settings
  const updateProjectSettings = async () => {
    try {
      const userId = localStorage.getItem("user");

      const payload = {
        id: parseInt(userId),
        deposit_percent: depositPercent,
        contract_agreement: contractAgreement,
        survey: JSON.stringify(surveySettings),
        routine_submission_date: JSON.stringify(routineSettings),
        estimated_delivery: JSON.stringify(deliverySettings),
        company_logo: companyLogoUrl,
        license_company_logo: licenseLogoUrl,
        office_email: officeEmail,
      };

      const res = await updateUserDetailsAPI(payload);

      if (!res.error) {
        showToast(
          globalDispatch,
          "Project settings updated successfully",
          4000
        );
      } else {
        showToast(
          globalDispatch,
          res.message || "Failed to update project settings",
          4000,
          "error"
        );
      }
    } catch (error) {
      console.error("Error updating project settings:", error);
      showToast(
        globalDispatch,
        "Failed to update project settings",
        4000,
        "error"
      );
    }
  };

  // Fetch project settings
  const fetchProjectSettings = async () => {
    try {
      const userId = localStorage.getItem("user");

      const response = await getUserDetailsByIdAPI(userId);

      if (!response.error && response.model) {
        // Set deposit percentage
        setDepositPercent(response.model.deposit_percent || 50);

        // Set contract agreement
        setContractAgreement(response.model.contract_agreement || "");

        // Set general settings
        setCompanyName(response.model.company_name || "");
        setCompanyAddress(response.model.company_address || "");
        setCompanyPhone(response.model.phone || "");
        setOfficeEmail(response.model.office_email || "");
        setCompanyLogoUrl(response.model.company_logo || "");
        setLicenseLogoUrl(response.model.license_company_logo || "");

        // Set Stripe information
        setHasStripe(response.model.has_stripe === true);
        // Check for stripe_onboard_url or account_link.url
        const stripeUrl =
          response.model.stripe_onboard_url ||
          response.model.account_link?.url ||
          "";
        setStripeOnboardUrl(stripeUrl);

        // Parse JSON strings if they exist, otherwise use defaults
        const surveyData = response.model.survey
          ? response.model.survey
          : { weeks: 8, day: "Monday" };
        const routineData = response.model.routine_submission_date
          ? response.model.routine_submission_date
          : { weeks: 1, day: "Monday" };
        const deliveryData = response.model.estimated_delivery
          ? response.model.estimated_delivery
          : { weeks: 1, day: "Friday" };

        console.log(
          surveyData,
          routineData,
          deliveryData,
          response.model.estimated_delivery
        );

        setSurveySettings(surveyData);
        setRoutineSettings(routineData);
        setDeliverySettings(deliveryData);
      }
    } catch (error) {
      console.error("Error fetching project settings:", error);
    }
  };

  // Handle company logo upload
  const handleCompanyLogoUpload = async (formData) => {
    try {
      const result = await uploadS3FilesAPI(formData);
      if (!result.error) {
        let attachmentsArr = JSON.parse(result.attachments);
        setCompanyLogoUrl(attachmentsArr[0]);
        showToast(globalDispatch, "Company logo uploaded successfully", 4000);
      } else {
        showToast(
          globalDispatch,
          "Failed to upload company logo",
          4000,
          "error"
        );
      }
    } catch (error) {
      console.error("Error uploading company logo:", error);
      showToast(globalDispatch, "Failed to upload company logo", 4000, "error");
    }
  };

  // Handle license company logo upload
  const handleLicenseLogoUpload = async (formData) => {
    try {
      const result = await uploadS3FilesAPI(formData);
      if (!result.error) {
        let attachmentsArr = JSON.parse(result.attachments);
        setLicenseLogoUrl(attachmentsArr[0]);
        showToast(
          globalDispatch,
          "License company logo uploaded successfully",
          4000
        );
      } else {
        showToast(
          globalDispatch,
          "Failed to upload license company logo",
          4000,
          "error"
        );
      }
    } catch (error) {
      console.error("Error uploading license company logo:", error);
      showToast(
        globalDispatch,
        "Failed to upload license company logo",
        4000,
        "error"
      );
    }
  };

  // Update general settings
  const updateGeneralSettings = async () => {
    try {
      const userId = localStorage.getItem("user");

      const payload = {
        id: parseInt(userId),
        company_name: companyName,
        company_address: companyAddress,
        phone: companyPhone,
        office_email: officeEmail,
        company_logo: companyLogoUrl,
        license_company_logo: licenseLogoUrl,
      };

      const res = await updateUserDetailsAPI(payload);

      if (!res.error) {
        showToast(
          globalDispatch,
          "General settings updated successfully",
          4000
        );
      } else {
        showToast(
          globalDispatch,
          res.message || "Failed to update general settings",
          4000,
          "error"
        );
      }
    } catch (error) {
      console.error("Error updating general settings:", error);
      showToast(
        globalDispatch,
        "Failed to update general settings",
        4000,
        "error"
      );
    }
  };

  // Handle Stripe Connect setup
  const handleConnectStripe = () => {
    if (stripeOnboardUrl) {
      setStripeLoading(true);
      // Open Stripe Connect onboarding in new tab
      window.open(stripeOnboardUrl, "_blank");
      showToast(
        globalDispatch,
        "Redirecting to Stripe Connect setup...",
        3000,
        "info"
      );
      // Reset loading after a delay
      setTimeout(() => setStripeLoading(false), 3000);
    } else {
      showToast(
        globalDispatch,
        "Stripe onboarding URL not available. Please contact support.",
        4000,
        "error"
      );
    }
  };

  // Refresh Stripe status
  const refreshStripeStatus = async () => {
    try {
      setStripeLoading(true);
      const userId = localStorage.getItem("user");
      const res = await getUserDetailsByIdAPI(userId);
      if (!res.error) {
        setHasStripe(res.model?.has_stripe === true);
        // Check for stripe_onboard_url or account_link.url
        const stripeUrl =
          res.model?.stripe_onboard_url || res.model?.account_link?.url || "";
        setStripeOnboardUrl(stripeUrl);
        if (res.model?.has_stripe === true) {
          showToast(
            globalDispatch,
            "Stripe connection verified successfully!",
            4000,
            "success"
          );
        }
      }
    } catch (error) {
      console.error("Error refreshing Stripe status:", error);
      showToast(
        globalDispatch,
        "Failed to refresh Stripe status",
        4000,
        "error"
      );
    } finally {
      setStripeLoading(false);
    }
  };

  // Fetch members on component mount
  useEffect(() => {
    fetchMembers();
    fetchProjectSettings();

    // Set the path in global context
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "manager-settings",
      },
    });
  }, [globalDispatch]);

  // Function to fetch members
  const fetchMembers = async () => {
    setLoading(true);
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        "/v3/api/custom/equality_record/user/company/info",
        {},
        "GET"
      );

      if (!response.error && response.company && response.company.members) {
        // The API returns members in company.members array
        setMembers(response.company.members);
      } else {
        console.error(
          "Error fetching members:",
          response.message || "No members found in response"
        );
      }
    } catch (error) {
      console.error("Error fetching members:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddMember = async () => {
    if (!email || !firstName || !lastName) {
      showToast(
        globalDispatch,
        "Please fill in all required fields",
        4000,
        "error"
      );
      return;
    }

    try {
      const sdk = new MkdSDK();

      // Create member with checkout payment method
      const createMemberResponse = await sdk.callRawAPI(
        "/v3/api/custom/equality_record/user/company/member/create",
        {
          email,
          first_name: firstName,
          last_name: lastName,
          phone,
          payment_info: {
            amount: amount, // Amount in cents (10000 = $100)
            method: "checkout",
          },
        },
        "POST"
      );

      console.log("Create member response:", createMemberResponse);

      if (createMemberResponse.error) {
        throw new Error(
          createMemberResponse.message || "Failed to create member"
        );
      }

      // Check if we have the required checkout URL
      if (!createMemberResponse.stripe_checkout_url) {
        throw new Error("Missing checkout URL in the response");
      }

      // Success - clear form
      setEmail("");
      setFirstName("");
      setLastName("");
      setPhone("");
      setPaymentError(null);

      // Show success toast with countdown
      showToast(
        globalDispatch,
        "Client added successfully! Redirecting to payment checkout to complete the process...",
        6000,
        "success"
      );

      // Redirect to Stripe checkout after 6 seconds
      setTimeout(() => {
        window.location.href = createMemberResponse.stripe_checkout_url;
      }, 6000);

      // Refresh member list
      fetchMembers();
    } catch (error) {
      console.error("Error adding member:", error);
      showToast(
        globalDispatch,
        error.message || "Failed to add member",
        4000,
        "error"
      );
    }
  };

  return (
    <div className="max-w-screen mx-auto p-4 md:p-6 2xl:p-10">
      <div className="mb-6">
        <h2 className="text-title-md2 font-semibold text-white">
          Manager Settings
        </h2>
      </div>

      <div className="rounded border border-strokedark bg-boxdark">
        {/* Tab Navigation */}
        <div className="flex flex-wrap border-b border-strokedark">
          <button
            onClick={() => setActiveTab("members")}
            className={`border-r border-strokedark px-6 py-3 text-sm font-medium transition-colors ${
              activeTab === "members"
                ? "bg-primary text-white"
                : "text-white hover:bg-meta-4"
            }`}
          >
            Members
          </button>

          <button
            onClick={() => setActiveTab("projects")}
            className={`border-r border-strokedark px-6 py-3 text-sm font-medium transition-colors ${
              activeTab === "projects"
                ? "bg-primary text-white"
                : "text-white hover:bg-meta-4"
            }`}
          >
            Projects
          </button>

          <button
            onClick={() => setActiveTab("settings")}
            className={`border-r border-strokedark px-6 py-3 text-sm font-medium transition-colors ${
              activeTab === "settings"
                ? "bg-primary text-white"
                : "text-white hover:bg-meta-4"
            }`}
          >
            General Settings
          </button>

          <button
            onClick={() => setActiveTab("payment")}
            className={`px-6 py-3 text-sm font-medium transition-colors ${
              activeTab === "payment"
                ? "bg-primary text-white"
                : "text-white hover:bg-meta-4"
            }`}
          >
            Payment
          </button>
        </div>

        {/* Tab Content */}
        {activeTab === "members" && (
          <div className="p-6">
            <div className="mb-6">
              <h3 className="text-xl font-semibold text-white">
                Add New Member
              </h3>
              <p className="mt-1 text-sm text-gray-400">
                Add a new member to your company with payment information
              </p>
            </div>

            <form className="max-w-2xl space-y-4">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div>
                  <label className="mb-2 block text-white">Email</label>
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full rounded border border-stroke bg-transparent px-4 py-2 text-white"
                    placeholder="Enter email"
                  />
                </div>
                <div>
                  <label className="mb-2 block text-white">First Name</label>
                  <input
                    type="text"
                    value={firstName}
                    onChange={(e) => setFirstName(e.target.value)}
                    className="w-full rounded border border-stroke bg-transparent px-4 py-2 text-white"
                    placeholder="Enter first name"
                  />
                </div>
                <div>
                  <label className="mb-2 block text-white">Last Name</label>
                  <input
                    type="text"
                    value={lastName}
                    onChange={(e) => setLastName(e.target.value)}
                    className="w-full rounded border border-stroke bg-transparent px-4 py-2 text-white"
                    placeholder="Enter last name"
                  />
                </div>
                <div>
                  <label className="mb-2 block text-white">Phone</label>
                  <input
                    type="tel"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                    className="w-full rounded border border-stroke bg-transparent px-4 py-2 text-white"
                    placeholder="Enter phone number"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div>
                  <label className="mb-2 block text-white">
                    Payment Amount
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      value="$100.00"
                      disabled
                      className="w-full cursor-not-allowed rounded border border-stroke bg-gray-700 px-4 py-2 text-white opacity-70"
                    />
                    <span className="absolute right-3 top-1/2 -translate-y-1/2 transform text-xs text-gray-400">
                      Fixed Amount
                    </span>
                  </div>
                  <p className="mt-1 text-xs text-gray-400">
                    Member creation fee is fixed at $100.00
                  </p>
                </div>
                <div>
                  <label className="mb-2 block text-white">
                    Payment Method
                  </label>
                  <input
                    type="text"
                    value="Stripe Checkout"
                    disabled
                    className="w-full cursor-not-allowed rounded border border-stroke bg-gray-700 px-4 py-2 text-white opacity-70"
                  />
                  <p className="mt-1 text-xs text-gray-400">
                    Secure payment via Stripe
                  </p>
                </div>
              </div>

              {paymentError && (
                <div className="rounded border border-danger bg-danger/10 p-4">
                  <p className="text-sm text-danger">{paymentError}</p>
                </div>
              )}

              <button
                type="button"
                onClick={handleAddMember}
                className="rounded-md bg-primary px-6 py-2 text-white hover:bg-opacity-90"
              >
                Add Member
              </button>
            </form>

            <div className="mt-10">
              <h3 className="mb-4 text-xl font-semibold text-white">
                Member List
              </h3>

              {loading ? (
                <p className="text-white">Loading members...</p>
              ) : members.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="w-full table-auto">
                    <thead>
                      <tr className="bg-meta-4">
                        <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                          Name
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                          Email
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                          Phone
                        </th>
                        {/* <th className="px-4 py-3 text-xs font-medium tracking-wider text-left text-white uppercase">
                          Status
                        </th> */}
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-strokedark">
                      {members.map((member, index) => (
                        <tr key={index} className="hover:bg-meta-4/30">
                          <td className="whitespace-nowrap px-4 py-3 text-sm text-white">
                            {member.first_name} {member.last_name}
                          </td>
                          <td className="whitespace-nowrap px-4 py-3 text-sm text-white">
                            {member.email}
                          </td>
                          <td className="whitespace-nowrap px-4 py-3 text-sm text-white">
                            {member.phone || "N/A"}
                          </td>
                          {/* <td className="px-4 py-3 text-sm whitespace-nowrap">
                            <span
                              className={`rounded-full px-2.5 py-0.5 text-xs font-medium ${
                                member.status === 1
                                  ? "bg-success/10 text-success"
                                  : "bg-danger/10 text-danger"
                              }`}
                            >
                              {member.status === 1 ? "Active" : "Inactive"}
                            </span>
                          </td> */}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <p className="text-white">No members found.</p>
              )}
            </div>
          </div>
        )}

        {activeTab === "projects" && (
          <div className="p-6">
            <div className="mb-6">
              <h4 className="text-xl font-semibold text-white">
                Project Settings
              </h4>
              <p className="mt-1 text-sm text-gray-400">
                Set default values for new projects and invoices
              </p>
            </div>

            <div className="max-w-2xl space-y-6">
              <div className="w-full">
                <label className="mb-2.5 block text-sm font-medium text-white">
                  Default Deposit Percentage
                </label>
                <div className="flex w-full max-w-[200px] items-center">
                  <input
                    type="number"
                    min="0"
                    max="100"
                    value={depositPercent}
                    onChange={(e) => setDepositPercent(Number(e.target.value))}
                    className="w-full rounded border border-form-strokedark bg-form-input px-4 py-2.5 text-white placeholder:text-gray-400 focus:border-primary focus:outline-none"
                  />
                  <span className="ml-2 text-white">%</span>
                </div>
              </div>

              <div className="w-full">
                <label className="mb-2.5 block text-sm font-medium text-white">
                  Contract Agreement
                </label>
                <div className="flex flex-col gap-4">
                  <SunEditor
                    setContents={contractAgreement}
                    onChange={(content) => setContractAgreement(content)}
                    getSunEditorInstance={getSunEditorInstance}
                    setOptions={{
                      buttonList: buttonList.complex,
                      height: 200,
                      width: "100%",
                    }}
                  />
                </div>
              </div>

              <div className="grid gap-6 md:grid-cols-2">
                <div>
                  <label className="mb-2.5 block text-sm font-medium text-white">
                    Survey Timing
                  </label>
                  <div className="flex items-center gap-4">
                    <div className="flex items-center">
                      <input
                        type="number"
                        min="1"
                        value={surveySettings.weeks}
                        onChange={(e) =>
                          setSurveySettings((prev) => ({
                            ...prev,
                            weeks: Number(e.target.value),
                          }))
                        }
                        className="w-20 rounded border border-form-strokedark bg-form-input px-4 py-2.5 text-white placeholder:text-gray-400 focus:border-primary focus:outline-none"
                      />
                      <span className="ml-2 text-white">weeks before</span>
                    </div>
                    <CustomSelect2
                      value={surveySettings.day}
                      onChange={(value) =>
                        setSurveySettings((prev) => ({ ...prev, day: value }))
                      }
                      className="w-32 rounded border border-form-strokedark bg-form-input px-4 py-2.5 text-white focus:border-primary focus:outline-none"
                    >
                      <option value="Monday">Monday</option>
                      <option value="Tuesday">Tuesday</option>
                      <option value="Wednesday">Wednesday</option>
                      <option value="Thursday">Thursday</option>
                      <option value="Friday">Friday</option>
                      <option value="Saturday">Saturday</option>
                      <option value="Sunday">Sunday</option>
                    </CustomSelect2>
                  </div>
                </div>

                <div>
                  <label className="mb-2.5 block text-sm font-medium text-white">
                    Routine Submission
                  </label>
                  <div className="flex items-center gap-4">
                    <div className="flex items-center">
                      <input
                        type="number"
                        min="1"
                        value={routineSettings.weeks}
                        onChange={(e) =>
                          setRoutineSettings((prev) => ({
                            ...prev,
                            weeks: Number(e.target.value),
                          }))
                        }
                        className="w-20 rounded border border-form-strokedark bg-form-input px-4 py-2.5 text-white placeholder:text-gray-400 focus:border-primary focus:outline-none"
                      />
                      <span className="ml-2 text-white">weeks before</span>
                    </div>
                    <CustomSelect2
                      value={routineSettings.day}
                      onChange={(value) =>
                        setRoutineSettings((prev) => ({ ...prev, day: value }))
                      }
                      className="w-32 rounded border border-form-strokedark bg-form-input px-4 py-2.5 text-white focus:border-primary focus:outline-none"
                    >
                      <option value="Monday">Monday</option>
                      <option value="Tuesday">Tuesday</option>
                      <option value="Wednesday">Wednesday</option>
                      <option value="Thursday">Thursday</option>
                      <option value="Friday">Friday</option>
                      <option value="Saturday">Saturday</option>
                      <option value="Sunday">Sunday</option>
                    </CustomSelect2>
                  </div>
                </div>

                <div>
                  <label className="mb-2.5 block text-sm font-medium text-white">
                    Estimated Delivery
                  </label>
                  <div className="flex items-center gap-4">
                    <div className="flex items-center">
                      <input
                        type="number"
                        min="1"
                        value={deliverySettings.weeks}
                        onChange={(e) =>
                          setDeliverySettings((prev) => ({
                            ...prev,
                            weeks: Number(e.target.value),
                          }))
                        }
                        className="w-20 rounded border border-form-strokedark bg-form-input px-4 py-2.5 text-white placeholder:text-gray-400 focus:border-primary focus:outline-none"
                      />
                      <span className="ml-2 text-white">weeks after</span>
                    </div>
                    <CustomSelect2
                      value={deliverySettings.day}
                      onChange={(value) =>
                        setDeliverySettings((prev) => ({ ...prev, day: value }))
                      }
                      className="w-32 rounded border border-form-strokedark bg-form-input px-4 py-2.5 text-white focus:border-primary focus:outline-none"
                    >
                      <option value="Monday">Monday</option>
                      <option value="Tuesday">Tuesday</option>
                      <option value="Wednesday">Wednesday</option>
                      <option value="Thursday">Thursday</option>
                      <option value="Friday">Friday</option>
                      <option value="Saturday">Saturday</option>
                      <option value="Sunday">Sunday</option>
                    </CustomSelect2>
                  </div>
                </div>
              </div>

              <button
                onClick={updateProjectSettings}
                className="mt-6 inline-flex items-center justify-center rounded-md bg-primary px-6 py-2.5 text-center font-medium text-white hover:bg-opacity-90"
              >
                Save Project Settings
              </button>
            </div>
          </div>
        )}

        {activeTab === "settings" && (
          <div className="p-6">
            <div className="mb-6">
              <h3 className="text-xl font-semibold text-white">
                General Settings
              </h3>
              <p className="mt-1 text-sm text-gray-400">
                Configure general settings for your manager account
              </p>
            </div>

            <div className="max-w-2xl space-y-6">
              <div>
                <label className="mb-2.5 block text-sm font-medium text-white">
                  Company Name
                </label>
                <input
                  type="text"
                  value={companyName}
                  onChange={(e) => setCompanyName(e.target.value)}
                  className="w-full rounded border border-form-strokedark bg-form-input px-4 py-2.5 text-white placeholder:text-gray-400 focus:border-primary focus:outline-none"
                />
              </div>

              <div>
                <label className="mb-2.5 block text-sm font-medium text-white">
                  Company Address
                </label>
                <input
                  type="text"
                  value={companyAddress}
                  onChange={(e) => setCompanyAddress(e.target.value)}
                  className="w-full rounded border border-form-strokedark bg-form-input px-4 py-2.5 text-white placeholder:text-gray-400 focus:border-primary focus:outline-none"
                />
              </div>

              <div>
                <label className="mb-2.5 block text-sm font-medium text-white">
                  Phone Number
                </label>
                <input
                  type="tel"
                  value={companyPhone}
                  onChange={(e) => setCompanyPhone(e.target.value)}
                  className="w-full rounded border border-form-strokedark bg-form-input px-4 py-2.5 text-white placeholder:text-gray-400 focus:border-primary focus:outline-none"
                />
              </div>

              <div>
                <label className="mb-2.5 block text-sm font-medium text-white">
                  Office Email
                </label>
                <input
                  type="email"
                  value={officeEmail}
                  onChange={(e) => setOfficeEmail(e.target.value)}
                  className="w-full rounded border border-form-strokedark bg-form-input px-4 py-2.5 text-white placeholder:text-gray-400 focus:border-primary focus:outline-none"
                />
              </div>

              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                <div>
                  <label className="mb-2.5 block text-sm font-medium text-white">
                    Company Logo
                  </label>
                  {companyLogoUrl && (
                    <div className="mb-4">
                      <img
                        src={companyLogoUrl}
                        alt="Company Logo"
                        className="h-24 w-auto object-contain"
                      />
                    </div>
                  )}
                  <LogoUpload
                    maxFileSize={2}
                    setFileUpload={handleCompanyLogoUpload}
                    transparent={true}
                  />
                </div>

                <div>
                  <label className="mb-2.5 block text-sm font-medium text-white">
                    License Company Logo
                  </label>
                  {licenseLogoUrl && (
                    <div className="mb-4">
                      <img
                        src={licenseLogoUrl}
                        alt="License Company Logo"
                        className="h-24 w-auto object-contain"
                      />
                    </div>
                  )}
                  <LogoUpload
                    maxFileSize={2}
                    setFileUpload={handleLicenseLogoUpload}
                    transparent={true}
                  />
                </div>
              </div>

              <button
                onClick={updateGeneralSettings}
                className="mt-6 inline-flex items-center justify-center rounded-md bg-primary px-6 py-2.5 text-center font-medium text-white hover:bg-opacity-90"
              >
                Save General Settings
              </button>
            </div>
          </div>
        )}

        {activeTab === "payment" && (
          <div className="p-6">
            <div className="mb-6">
              <h3 className="text-xl font-semibold text-white">
                Payment & Billing Settings
              </h3>
              <p className="mt-1 text-sm text-gray-400">
                Manage your Stripe Connect integration for seamless payments
              </p>
            </div>

            <div className="max-w-2xl space-y-6">
              {/* Stripe Status Card */}
              <div className="rounded-lg border border-strokedark bg-boxdark p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/20">
                      <svg
                        className="h-6 w-6 text-primary"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
                        />
                      </svg>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-white">
                        Stripe Connect
                      </h4>
                      <p className="text-sm text-gray-400">
                        {hasStripe
                          ? "Your Stripe account is connected and ready"
                          : "Connect your Stripe account to receive payments"}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {hasStripe ? (
                      <div className="flex items-center gap-2">
                        <div className="h-3 w-3 rounded-full bg-green-500"></div>
                        <span className="text-sm font-medium text-green-400">
                          Connected
                        </span>
                      </div>
                    ) : (
                      <div className="flex items-center gap-2">
                        <div className="h-3 w-3 rounded-full bg-red-500"></div>
                        <span className="text-sm font-medium text-red-400">
                          Not Connected
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Status Details */}
                <div className="mt-4 rounded border border-strokedark bg-meta-4/20 p-4">
                  {hasStripe ? (
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-green-400">
                        <svg
                          className="h-4 w-4"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                        <span className="text-sm">
                          Ready to receive payments
                        </span>
                      </div>
                      <div className="flex items-center gap-2 text-green-400">
                        <svg
                          className="h-4 w-4"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                        <span className="text-sm">
                          Invoice processing enabled
                        </span>
                      </div>
                      <div className="flex items-center gap-2 text-green-400">
                        <svg
                          className="h-4 w-4"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                        <span className="text-sm">
                          Secure transaction processing
                        </span>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-gray-400">
                        <svg
                          className="h-4 w-4"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                            clipRule="evenodd"
                          />
                        </svg>
                        <span className="text-sm">
                          Payment processing unavailable
                        </span>
                      </div>
                      <div className="flex items-center gap-2 text-gray-400">
                        <svg
                          className="h-4 w-4"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                            clipRule="evenodd"
                          />
                        </svg>
                        <span className="text-sm">
                          Invoice generation limited
                        </span>
                      </div>
                      <p className="mt-2 text-xs text-gray-500">
                        Connect your Stripe account to unlock full payment
                        capabilities
                      </p>
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="mt-6 flex gap-4">
                  {hasStripe ? (
                    <>
                      <button
                        onClick={refreshStripeStatus}
                        disabled={stripeLoading}
                        className="flex items-center justify-center gap-2 rounded-lg border border-strokedark px-4 py-2 text-sm font-medium text-white hover:bg-meta-4 disabled:cursor-not-allowed disabled:opacity-50"
                      >
                        {stripeLoading ? (
                          <>
                            <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                            Checking...
                          </>
                        ) : (
                          <>
                            <svg
                              className="h-4 w-4"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                              />
                            </svg>
                            Refresh Status
                          </>
                        )}
                      </button>
                      <div className="flex items-center text-sm text-gray-400">
                        <svg
                          className="mr-1 h-4 w-4 text-green-400"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clipRule="evenodd"
                          />
                        </svg>
                        Account verified and active
                      </div>
                    </>
                  ) : (
                    <>
                      <button
                        onClick={handleConnectStripe}
                        disabled={stripeLoading || !stripeOnboardUrl}
                        className="flex items-center justify-center gap-2 rounded-lg bg-primary px-6 py-2 font-medium text-white hover:bg-primary/90 disabled:cursor-not-allowed disabled:opacity-50"
                      >
                        {stripeLoading ? (
                          <>
                            <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                            Connecting...
                          </>
                        ) : (
                          <>
                            <svg
                              className="h-4 w-4"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
                              />
                            </svg>
                            Connect to Stripe
                          </>
                        )}
                      </button>
                      <button
                        onClick={refreshStripeStatus}
                        disabled={stripeLoading}
                        className="flex items-center justify-center gap-2 rounded-lg border border-strokedark px-4 py-2 text-sm font-medium text-white hover:bg-meta-4 disabled:cursor-not-allowed disabled:opacity-50"
                      >
                        {stripeLoading ? (
                          <>
                            <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                            Checking...
                          </>
                        ) : (
                          <>
                            <svg
                              className="h-4 w-4"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                              />
                            </svg>
                            Check Status
                          </>
                        )}
                      </button>
                    </>
                  )}
                </div>
              </div>

              {/* Information Section */}
              <div className="rounded-lg border border-strokedark bg-boxdark p-6">
                <h4 className="mb-4 text-lg font-semibold text-white">
                  About Stripe Connect
                </h4>
                <div className="space-y-3 text-sm text-gray-300">
                  <p>
                    Stripe Connect allows you to receive payments directly from
                    clients and manage your financial operations seamlessly.
                  </p>
                  <div className="space-y-2">
                    <div className="flex items-start gap-2">
                      <span className="text-primary">•</span>
                      <span>
                        Secure payment processing with industry-leading security
                      </span>
                    </div>
                    <div className="flex items-start gap-2">
                      <span className="text-primary">•</span>
                      <span>Direct deposits to your bank account</span>
                    </div>
                    <div className="flex items-start gap-2">
                      <span className="text-primary">•</span>
                      <span>Automated invoice generation and tracking</span>
                    </div>
                    <div className="flex items-start gap-2">
                      <span className="text-primary">•</span>
                      <span>
                        Real-time transaction monitoring and reporting
                      </span>
                    </div>
                    <div className="flex items-start gap-2">
                      <span className="text-primary">•</span>
                      <span>Manage member payments and company billing</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Manager-specific note */}
              <div className="rounded-lg border border-primary/30 bg-primary/10 p-6">
                <div className="flex items-center gap-3">
                  <svg
                    className="h-5 w-5 text-primary"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  <span className="font-medium text-primary">
                    Manager Account
                  </span>
                </div>
                <p className="mt-2 text-sm text-gray-300">
                  As a manager, you have full access to payment setup and can
                  manage Stripe Connect integration for your organization. This
                  includes processing member payments and handling company
                  billing.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ManagerSettingsPage;
