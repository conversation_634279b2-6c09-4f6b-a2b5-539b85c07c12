import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

import React from "react";
import { <PERSON>lipLoader } from "react-spinners";

import { GlobalContext, showToast } from "Src/globalContext";
import { CreateSpecialEditAPI } from "Src/services/editService";

const AddSpecialEdit = ({ isOpen, setIsOpen, getData, userID }) => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [type, setType] = React.useState("");
  const [loader, setLoader] = React.useState(false);
  const [description, setDescription] = React.useState("");

  const [durationOfEditMonth, setDurationOfEditMonth] = React.useState(0);
  const [durationOfEditWeek, setDurationOfEditWeek] = React.useState(0);
  const [durationOfEditDays, setDurationOfEditDay] = React.useState(0);

  const onSubmit = async (e) => {
    e.preventDefault();

    try {
      setLoader(true);
      const payload = {
        edit_type: type,
        request_range: "Special",
        number_of_lines: "Special",
        edit_duration: `${durationOfEditMonth} months, ${durationOfEditWeek} weeks, ${durationOfEditDays} days`,
        note_keys: description,
        user_id: userID,
      };
      const res = await CreateSpecialEditAPI(payload);

      const result = await getData(1, 30, { edit_status: 1 });

      showToast(globalDispatch, "Edits Added", 5000, "success");

      setLoader(false);
      setIsOpen(false);
    } catch (error) {
      setLoader(false);
      showToast(globalDispatch, error.message, 5000);
      throw error;
    }
  };
  return (
    <>
      <div className="fixed inset-0 z-10 overflow-y-auto">
        <div
          className="fixed inset-0 h-full w-full bg-black opacity-40"
          onClick={() => setIsOpen(false)}
        ></div>
        <div className="flex min-h-screen items-center px-4 py-8">
          <div className="relative mx-auto w-full max-w-lg rounded-md bg-white p-4 shadow-lg">
            <form onSubmit={onSubmit} className="mt-3 flex flex-col">
              <div className="flex w-full justify-between">
                <h3 className="text-xl font-bold text-black">
                  Add Special Edit
                </h3>
                <FontAwesomeIcon
                  icon="close"
                  className="h-6 w-6 cursor-pointer text-gray-600"
                  onClick={() => setIsOpen(false)}
                />
              </div>
              <div className="mt-7 flex h-[45px] w-full items-center rounded border-2 border-gray-200">
                <div className="flex h-full items-center justify-center whitespace-nowrap bg-gray-400 px-3">
                  Edit Type
                </div>
                <input
                  type="text"
                  required
                  onChange={(e) => setType(e.target.value)}
                  className="block w-full border-transparent bg-transparent py-2 pl-3 text-black outline-none focus:ring-0 focus-visible:outline-transparent"
                  placeholder=""
                />
              </div>
              <div className="mt-5 flex h-[100px] w-full items-center rounded border-2 border-gray-200">
                <div className="flex h-full items-center justify-center bg-gray-400 px-3">
                  Note
                </div>
                <textarea
                  type="text"
                  name="notes"
                  required
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  className="block h-[100px] w-full resize-none border-transparent bg-transparent py-2 pl-3 text-black outline-none focus:ring-0 focus-visible:outline-transparent"
                  placeholder="Edit type notes"
                />
              </div>
              <div className="mt-7 flex h-[45px] w-full items-end gap-5 rounded ">
                <div className="flex h-full items-center justify-center whitespace-nowrap bg-gray-400 px-3">
                  Duration of Edits
                </div>
                <div className="flex gap-4">
                  <div className="flex flex-col">
                    <h6>Month(s)</h6>
                    <input
                      className="h-10 w-14 p-[2px] text-center text-black"
                      type="number"
                      inputMode="numeric"
                      onChange={(e) => setDurationOfEditMonth(e.target.value)}
                      value={durationOfEditMonth}
                      name=""
                      required
                      id=""
                    />
                  </div>
                  <div className="flex flex-col">
                    <h6>Week(s)</h6>
                    <input
                      inputMode="numeric"
                      onChange={(e) => setDurationOfEditWeek(e.target.value)}
                      className="h-10 w-14 p-[2px] text-center text-black"
                      type="number"
                      value={durationOfEditWeek}
                      name=""
                      id=""
                      required
                    />
                  </div>
                  <div className="flex flex-col">
                    <h6>Day(s)</h6>
                    <input
                      className="h-10 w-14 p-[2px] text-center text-black"
                      type="number"
                      inputMode="numeric"
                      onChange={(e) => setDurationOfEditDay(e.target.value)}
                      value={durationOfEditDays}
                      name=""
                      required
                      id=""
                    />
                  </div>
                </div>
              </div>

              <div className="mt-8 flex w-full items-center justify-end gap-5">
                <button className="w-fit rounded bg-blue-600 px-2 py-1 text-sm font-semibold text-white hover:bg-blue-700 lg:px-3 lg:py-2">
                  {loader ? (
                    <ClipLoader color="white" size={12} />
                  ) : (
                    <span>Submit</span>
                  )}
                </button>
                <button
                  className="w-fit rounded bg-gray-500 px-2 py-1 text-sm font-semibold text-white hover:bg-gray-400 lg:px-3 lg:py-2"
                  onClick={() => setIsOpen(false)}
                >
                  Close
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </>
  );
};

export default AddSpecialEdit;
