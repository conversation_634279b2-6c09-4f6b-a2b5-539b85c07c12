import Spinner from "Components/Spinner";
import { AuthContext } from "Src/authContext";
import { GlobalContext, showToast } from "Src/globalContext";
import { getTeamDetailsAPI } from "Src/services/clientProjectDetailsService";
import React from "react";
import { useParams } from "react-router";
import ClientEditMusicDetailsModal from "../ClientEditMusicDetails";

const ClientMusicDetailsTab = (props) => {
  const { viewModel } = props;
  const projectId = useParams();
  const { state: authState } = React.useContext(AuthContext);
  const [loader, setLoader] = React.useState(false);

  const [data, setData] = React.useState({
    notes: "",
    song_list: "",
    // competitions: "",
    colors: "",
    mascot: "",
    social_media: "",

    // theme: "",
  });
  const [isEditMusicDetailsModalOpen, setIsEditMusicDetailsModalOpen] =
    React.useState(false);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);

  const getData = async () => {
    setLoader(true);
    try {
      const result = await getTeamDetailsAPI(projectId.id);
      setLoader(false);

      if (result?.model) {
        setData(result.model);
      }
    } catch (error) {
      setLoader(false);
      showToast(globalDispatch, error.message, 5000, "error");
    }
  };
  React.useEffect(() => {
    getData();
  }, []);

  function isSameDay(date1, date2) {
    // Get the routine submission date and convert it to a Date object
    let routineDate = new Date(viewModel?.routine_submission_date);

    // Create a new Date object for the "next day" by adding 1 day to the routineDate
    let nextDayUTC = new Date(
      Date.UTC(
        routineDate.getUTCFullYear(),
        routineDate.getUTCMonth(),
        routineDate.getUTCDate() + 1
      )
    );

    // Set the time to midnight UTC
    nextDayUTC.setUTCHours(0, 0, 0, 0);
    let timezoneOffset = nextDayUTC.getTimezoneOffset() * 60000;

    // Apply the timezone offset to the next day to align it with the local time
    let nextDayLocal = new Date(nextDayUTC.getTime() + timezoneOffset);

    // Get the current time in local time
    let currentTimeLocal = new Date();

    // Log the dates for debugging
    console.log("Next Day (Local):", nextDayLocal);
    console.log("Current Local Time:", currentTimeLocal);

    // Compare the current local time with the next day (adjusted to local time)
    return currentTimeLocal > nextDayLocal;
  }

  const currentTimezoneOffset = new Date().getTimezoneOffset();
  console.log("Current Timezone Offset (in minutes):", currentTimezoneOffset);

  console.log(data?.notes?.replace((/\n/g, "<br>")));
  return (
    <>
      {isEditMusicDetailsModalOpen && (
        <ClientEditMusicDetailsModal
          setData={setData}
          data={data}
          getData={getData}
          setIsOpen={setIsEditMusicDetailsModalOpen}
          isOpen={isEditMusicDetailsModalOpen}
        />
      )}
      <div className="mx-auto max-w-full">
        {/* Warning Banner - Commented out but styled for theme consistency */}
        {/* {isSameDay(new Date(), new Date(viewModel?.routine_submission_date)) &&
        authState?.role === "client" ? (
          <div className="mb-6 rounded border border-warning bg-warning/10 p-4">
            <p className="font-medium text-warning">
              Routine submission has been locked because it is past the
              submission deadline. If you need access, email your producer at{" "}
              <a
                target="_blank"
                rel="noreferrer"
                href={viewModel?.company_info.office_email}
                className="underline text-primary hover:text-primary/80"
              >
                {viewModel?.company_info.office_email}
              </a>
            </p>
          </div>
        ) : null} */}

        {/* Header Section */}
        <div className="mb-6 flex items-center justify-between">
          <h2 className="text-3xl font-semibold text-white">Music Details</h2>
          <button
            className="inline-flex items-center justify-center rounded bg-primary px-6 py-3 text-sm font-medium text-white transition-colors hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-boxdark"
            onClick={() => {
              if (
                isSameDay(
                  new Date(),
                  new Date(viewModel?.routine_submission_date)
                ) &&
                authState?.role === "client"
              ) {
                showToast(
                  globalDispatch,
                  "Email the producer office to unlock the team details",
                  7000
                );
              } else {
                setIsEditMusicDetailsModalOpen(true);
              }
            }}
          >
            Edit Details
          </button>
        </div>

        {loader ? (
          <div className="flex w-full justify-center py-12">
            <Spinner />
          </div>
        ) : (
          <div className="rounded border border-strokedark bg-boxdark">
            <div className="grid grid-cols-1 gap-6 p-6 lg:grid-cols-2">
              {/* Notes Section */}
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-white">Notes</h3>
                <div className="rounded border border-form-strokedark bg-form-input p-4">
                  {data?.notes ? (
                    <p
                      className="text-sm leading-relaxed text-bodydark"
                      dangerouslySetInnerHTML={{
                        __html: data.notes.replace(/\n/g, "<br>"),
                      }}
                    />
                  ) : (
                    <p className="text-sm italic text-bodydark2">
                      No notes provided
                    </p>
                  )}
                </div>
              </div>

              {/* Song List Section */}
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-white">Song List</h3>
                <div className="rounded border border-form-strokedark bg-form-input p-4">
                  {data?.song_list ? (
                    <p
                      className="text-sm leading-relaxed text-bodydark"
                      dangerouslySetInnerHTML={{
                        __html: data.song_list.replace(/\n/g, "<br>"),
                      }}
                    />
                  ) : (
                    <p className="text-sm italic text-bodydark2">
                      No song list provided
                    </p>
                  )}
                </div>
              </div>

              {/* Colors Section */}
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-white">Colors</h3>
                <div className="rounded border border-form-strokedark bg-form-input p-4">
                  {data?.colors ? (
                    <p className="text-sm text-bodydark">{data.colors}</p>
                  ) : (
                    <p className="text-sm italic text-bodydark2">
                      No colors specified
                    </p>
                  )}
                </div>
              </div>

              {/* Mascot Section */}
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-white">Mascot</h3>
                <div className="rounded border border-form-strokedark bg-form-input p-4">
                  {data?.mascot ? (
                    <p className="text-sm text-bodydark">{data.mascot}</p>
                  ) : (
                    <p className="text-sm italic text-bodydark2">
                      No mascot specified
                    </p>
                  )}
                </div>
              </div>

              {/* Social Media Section - Full Width */}
              <div className="space-y-3 lg:col-span-2">
                <h3 className="text-lg font-semibold text-white">
                  Social Media
                </h3>
                <div className="rounded border border-form-strokedark bg-form-input p-4">
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    {/* Twitter */}
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium text-white">
                        Twitter
                      </h4>
                      {data.social_media &&
                      JSON.parse(data.social_media)?.twitter ? (
                        <a
                          target="_blank"
                          rel="noreferrer"
                          className="block break-all text-sm text-primary underline hover:text-primary/80"
                          href={JSON.parse(data.social_media).twitter}
                        >
                          {JSON.parse(data.social_media).twitter}
                        </a>
                      ) : (
                        <p className="text-sm italic text-bodydark2">
                          No Twitter account provided
                        </p>
                      )}
                    </div>

                    {/* Instagram */}
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium text-white">
                        Instagram
                      </h4>
                      {data.social_media &&
                      JSON.parse(data.social_media)?.instagram ? (
                        <a
                          target="_blank"
                          rel="noreferrer"
                          className="block break-all text-sm text-primary underline hover:text-primary/80"
                          href={JSON.parse(data.social_media).instagram}
                        >
                          {JSON.parse(data.social_media).instagram}
                        </a>
                      ) : (
                        <p className="text-sm italic text-bodydark2">
                          No Instagram account provided
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default ClientMusicDetailsTab;
