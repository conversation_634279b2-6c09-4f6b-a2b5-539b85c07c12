import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

const IdeasNotesModal = ({ ideas, setModalClose, theme, notes = "" }) => {
  // Add state for active tab
  const [activeTab, setActiveTab] = React.useState("ideas");

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div
        className="fixed inset-0 bg-black/50 backdrop-blur-sm"
        onClick={() => setModalClose(false)}
      />
      <div className="shadow-default w-full max-w-xl transform rounded border border-strokedark bg-boxdark transition-all">
        {/* Modal Header */}
        <div className="flex items-center justify-between border-b border-stroke px-6 py-4">
          <div className="flex items-center gap-3">
            <FontAwesomeIcon
              icon="fa-solid fa-lightbulb"
              className="text-xl text-primary"
            />
            <h3 className="text-xl font-medium text-white">
              Writer Notes,Notes & Ideas
            </h3>
          </div>
          <button
            onClick={() => setModalClose(false)}
            className="hover:text-primary"
          >
            <FontAwesomeIcon icon="fa-solid fa-xmark" className="text-2xl" />
          </button>
        </div>

        {/* Theme Section */}
        <div className="border-b border-stroke px-6 py-4">
          <div className="flex flex-col gap-2">
            <h4 className="text-sm font-medium text-bodydark2">
              Theme of the Routine
            </h4>
            <p className="text-base text-white">
              {theme || "No theme specified"}
            </p>
          </div>
        </div>

        {/* Add Tab Navigation after Theme Section */}
        <div className="border-b border-stroke px-6 py-4">
          <div className="flex space-x-4">
            <button
              className={`pb-2 text-sm font-medium transition-all ${
                activeTab === "ideas"
                  ? "border-b-2 border-primary text-primary"
                  : "text-bodydark2 hover:text-white"
              }`}
              onClick={() => setActiveTab("ideas")}
            >
              Ideas
            </button>
            <button
              className={`pb-2 text-sm font-medium transition-all ${
                activeTab === "notes"
                  ? "border-b-2 border-primary text-primary"
                  : "text-bodydark2 hover:text-white"
              }`}
              onClick={() => setActiveTab("notes")}
            >
              Notes
            </button>
          </div>
        </div>

        {/* Content Section */}
        <div className="max-h-[60vh] overflow-y-auto px-6 py-4">
          {activeTab === "ideas" ? (
            // Ideas Content
            ideas?.length > 0 ? (
              <div className="space-y-4">
                {ideas.map((idea, index) => {
                  const ideaValue = idea.idea_value.replace(/<br>/g, "\n");
                  const ideaKey = idea.idea_key
                    .split("_")
                    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                    .join(" ");

                  return (
                    <div
                      key={index}
                      className="rounded border border-stroke bg-boxdark-2 p-4"
                    >
                      {/* Idea Header */}
                      <div className="mb-2 flex items-center gap-2">
                        <span className="flex h-6 w-6 items-center justify-center rounded-full bg-primary text-xs font-medium text-white">
                          {index + 1}
                        </span>
                        <h5 className="text-sm font-medium text-bodydark2">
                          {ideaKey}
                        </h5>
                      </div>

                      {/* Idea Content */}
                      <p className="whitespace-pre-line text-white">
                        {ideaValue}
                      </p>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-8">
                <div className="mb-3 rounded-full bg-boxdark p-3">
                  <FontAwesomeIcon
                    icon="fa-solid fa-note-sticky"
                    className="h-6 w-6 text-bodydark2"
                  />
                </div>
                <p className="text-sm text-bodydark2">
                  No ideas have been assigned to this subproject
                </p>
              </div>
            )
          ) : // Notes Content
          notes ? (
            <div className="rounded border border-stroke bg-boxdark-2 p-4">
              <p className="whitespace-pre-line text-white">{notes}</p>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-8">
              <div className="mb-3 rounded-full bg-boxdark p-3">
                <FontAwesomeIcon
                  icon="fa-solid fa-note-sticky"
                  className="h-6 w-6 text-bodydark2"
                />
              </div>
              <p className="text-sm text-bodydark2">
                No notes have been added yet
              </p>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="border-t border-stroke px-6 py-4">
          <button
            onClick={() => setModalClose(false)}
            className="flex w-full items-center justify-center rounded-sm bg-primary px-6 py-2 text-sm font-medium text-white hover:bg-opacity-90"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default IdeasNotesModal;
