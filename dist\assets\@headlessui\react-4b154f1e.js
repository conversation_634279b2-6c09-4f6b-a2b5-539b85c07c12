import{r as u,R as b,b as ge,a as Ot}from"../vendor-94843817.js";var Dt=Object.defineProperty,At=(e,t,n)=>t in e?Dt(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,Ne=(e,t,n)=>(At(e,typeof t!="symbol"?t+"":t,n),n);let Nt=class{constructor(){Ne(this,"current",this.detect()),Ne(this,"handoffState","pending"),Ne(this,"currentId",0)}set(t){this.current!==t&&(this.handoffState="pending",this.currentId=0,this.current=t)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return this.current==="server"}get isClient(){return this.current==="client"}detect(){return typeof window>"u"||typeof document>"u"?"server":"client"}handoff(){this.handoffState==="pending"&&(this.handoffState="complete")}get isHandoffComplete(){return this.handoffState==="complete"}},V=new Nt,R=(e,t)=>{V.isServer?u.useEffect(e,t):u.useLayoutEffect(e,t)};function k(e){let t=u.useRef(e);return R(()=>{t.current=e},[e]),t}let y=function(e){let t=k(e);return b.useCallback((...n)=>t.current(...n),[t])};function Ee(e){typeof queueMicrotask=="function"?queueMicrotask(e):Promise.resolve().then(e).catch(t=>setTimeout(()=>{throw t}))}function le(){let e=[],t={addEventListener(n,r,l,a){return n.addEventListener(r,l,a),t.add(()=>n.removeEventListener(r,l,a))},requestAnimationFrame(...n){let r=requestAnimationFrame(...n);return t.add(()=>cancelAnimationFrame(r))},nextFrame(...n){return t.requestAnimationFrame(()=>t.requestAnimationFrame(...n))},setTimeout(...n){let r=setTimeout(...n);return t.add(()=>clearTimeout(r))},microTask(...n){let r={current:!0};return Ee(()=>{r.current&&n[0]()}),t.add(()=>{r.current=!1})},style(n,r,l){let a=n.style.getPropertyValue(r);return Object.assign(n.style,{[r]:l}),this.add(()=>{Object.assign(n.style,{[r]:a})})},group(n){let r=le();return n(r),this.add(()=>r.dispose())},add(n){return e.push(n),()=>{let r=e.indexOf(n);if(r>=0)for(let l of e.splice(r,1))l()}},dispose(){for(let n of e.splice(0))n()}};return t}function Ge(){let[e]=u.useState(le);return u.useEffect(()=>()=>e.dispose(),[e]),e}function Mt(){let e=typeof document>"u";return"useSyncExternalStore"in ge?(t=>t.useSyncExternalStore)(ge)(()=>()=>{},()=>!1,()=>!e):!1}function ce(){let e=Mt(),[t,n]=u.useState(V.isHandoffComplete);return t&&V.isHandoffComplete===!1&&n(!1),u.useEffect(()=>{t!==!0&&n(!0)},[t]),u.useEffect(()=>V.handoff(),[]),e?!1:t}var nt;let Z=(nt=b.useId)!=null?nt:function(){let e=ce(),[t,n]=b.useState(e?()=>V.nextId():null);return R(()=>{t===null&&n(V.nextId())},[t]),t!=null?""+t:void 0};function x(e,t,...n){if(e in t){let l=t[e];return typeof l=="function"?l(...n):l}let r=new Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map(l=>`"${l}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(r,x),r}function Ve(e){return V.isServer?null:e instanceof Node?e.ownerDocument:e!=null&&e.hasOwnProperty("current")&&e.current instanceof Node?e.current.ownerDocument:document}let Be=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>`${e}:not([tabindex='-1'])`).join(",");var D=(e=>(e[e.First=1]="First",e[e.Previous=2]="Previous",e[e.Next=4]="Next",e[e.Last=8]="Last",e[e.WrapAround=16]="WrapAround",e[e.NoScroll=32]="NoScroll",e))(D||{}),ae=(e=>(e[e.Error=0]="Error",e[e.Overflow=1]="Overflow",e[e.Success=2]="Success",e[e.Underflow=3]="Underflow",e))(ae||{}),kt=(e=>(e[e.Previous=-1]="Previous",e[e.Next=1]="Next",e))(kt||{});function It(e=document.body){return e==null?[]:Array.from(e.querySelectorAll(Be)).sort((t,n)=>Math.sign((t.tabIndex||Number.MAX_SAFE_INTEGER)-(n.tabIndex||Number.MAX_SAFE_INTEGER)))}var it=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(it||{});function Ht(e,t=0){var n;return e===((n=Ve(e))==null?void 0:n.body)?!1:x(t,{0(){return e.matches(Be)},1(){let r=e;for(;r!==null;){if(r.matches(Be))return!0;r=r.parentElement}return!1}})}var jt=(e=>(e[e.Keyboard=0]="Keyboard",e[e.Mouse=1]="Mouse",e))(jt||{});typeof window<"u"&&typeof document<"u"&&(document.addEventListener("keydown",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")},!0),document.addEventListener("click",e=>{e.detail===1?delete document.documentElement.dataset.headlessuiFocusVisible:e.detail===0&&(document.documentElement.dataset.headlessuiFocusVisible="")},!0));function re(e){e==null||e.focus({preventScroll:!0})}let Bt=["textarea","input"].join(",");function Ut(e){var t,n;return(n=(t=e==null?void 0:e.matches)==null?void 0:t.call(e,Bt))!=null?n:!1}function ie(e,t=n=>n){return e.slice().sort((n,r)=>{let l=t(n),a=t(r);if(l===null||a===null)return 0;let o=l.compareDocumentPosition(a);return o&Node.DOCUMENT_POSITION_FOLLOWING?-1:o&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function q(e,t,{sorted:n=!0,relativeTo:r=null,skipElements:l=[]}={}){let a=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,o=Array.isArray(e)?n?ie(e):e:It(e);l.length>0&&o.length>1&&(o=o.filter(m=>!l.includes(m))),r=r??a.activeElement;let i=(()=>{if(t&5)return 1;if(t&10)return-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),s=(()=>{if(t&1)return 0;if(t&2)return Math.max(0,o.indexOf(r))-1;if(t&4)return Math.max(0,o.indexOf(r))+1;if(t&8)return o.length-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),d=t&32?{preventScroll:!0}:{},c=0,f=o.length,h;do{if(c>=f||c+f<=0)return 0;let m=s+c;if(t&16)m=(m+f)%f;else{if(m<0)return 3;if(m>=f)return 1}h=o[m],h==null||h.focus(d),c+=i}while(h!==a.activeElement);return t&6&&Ut(h)&&h.select(),2}function ut(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function _t(){return/Android/gi.test(window.navigator.userAgent)}function Wt(){return ut()||_t()}function Se(e,t,n){let r=k(t);u.useEffect(()=>{function l(a){r.current(a)}return document.addEventListener(e,l,n),()=>document.removeEventListener(e,l,n)},[e,n])}function st(e,t,n){let r=k(t);u.useEffect(()=>{function l(a){r.current(a)}return window.addEventListener(e,l,n),()=>window.removeEventListener(e,l,n)},[e,n])}function qt(e,t,n=!0){let r=u.useRef(!1);u.useEffect(()=>{requestAnimationFrame(()=>{r.current=n})},[n]);function l(o,i){if(!r.current||o.defaultPrevented)return;let s=i(o);if(s===null||!s.getRootNode().contains(s)||!s.isConnected)return;let d=function c(f){return typeof f=="function"?c(f()):Array.isArray(f)||f instanceof Set?f:[f]}(e);for(let c of d){if(c===null)continue;let f=c instanceof HTMLElement?c:c.current;if(f!=null&&f.contains(s)||o.composed&&o.composedPath().includes(f))return}return!Ht(s,it.Loose)&&s.tabIndex!==-1&&o.preventDefault(),t(o,s)}let a=u.useRef(null);Se("pointerdown",o=>{var i,s;r.current&&(a.current=((s=(i=o.composedPath)==null?void 0:i.call(o))==null?void 0:s[0])||o.target)},!0),Se("mousedown",o=>{var i,s;r.current&&(a.current=((s=(i=o.composedPath)==null?void 0:i.call(o))==null?void 0:s[0])||o.target)},!0),Se("click",o=>{Wt()||a.current&&(l(o,()=>a.current),a.current=null)},!0),Se("touchend",o=>l(o,()=>o.target instanceof HTMLElement?o.target:null),!0),st("blur",o=>l(o,()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null),!0)}function we(...e){return u.useMemo(()=>Ve(...e),[...e])}function rt(e){var t;if(e.type)return e.type;let n=(t=e.as)!=null?t:"button";if(typeof n=="string"&&n.toLowerCase()==="button")return"button"}function Yt(e,t){let[n,r]=u.useState(()=>rt(e));return R(()=>{r(rt(e))},[e.type,e.as]),R(()=>{n||t.current&&t.current instanceof HTMLButtonElement&&!t.current.hasAttribute("type")&&r("button")},[n,t]),n}let ct=Symbol();function Gt(e,t=!0){return Object.assign(e,{[ct]:t})}function N(...e){let t=u.useRef(e);u.useEffect(()=>{t.current=e},[e]);let n=y(r=>{for(let l of t.current)l!=null&&(typeof l=="function"?l(r):l.current=r)});return e.every(r=>r==null||(r==null?void 0:r[ct]))?void 0:n}function Ke(e,t){let n=u.useRef([]),r=y(e);u.useEffect(()=>{let l=[...n.current];for(let[a,o]of t.entries())if(n.current[a]!==o){let i=r(t,l);return n.current=t,i}},[r,...t])}function xe(...e){return Array.from(new Set(e.flatMap(t=>typeof t=="string"?t.split(" "):[]))).filter(Boolean).join(" ")}var ue=(e=>(e[e.None=0]="None",e[e.RenderStrategy=1]="RenderStrategy",e[e.Static=2]="Static",e))(ue||{}),J=(e=>(e[e.Unmount=0]="Unmount",e[e.Hidden=1]="Hidden",e))(J||{});function A({ourProps:e,theirProps:t,slot:n,defaultTag:r,features:l,visible:a=!0,name:o,mergeRefs:i}){i=i??Vt;let s=dt(t,e);if(a)return Pe(s,n,r,o,i);let d=l??0;if(d&2){let{static:c=!1,...f}=s;if(c)return Pe(f,n,r,o,i)}if(d&1){let{unmount:c=!0,...f}=s;return x(c?0:1,{0(){return null},1(){return Pe({...f,hidden:!0,style:{display:"none"}},n,r,o,i)}})}return Pe(s,n,r,o,i)}function Pe(e,t={},n,r,l){let{as:a=n,children:o,refName:i="ref",...s}=Me(e,["unmount","static"]),d=e.ref!==void 0?{[i]:e.ref}:{},c=typeof o=="function"?o(t):o;"className"in s&&s.className&&typeof s.className=="function"&&(s.className=s.className(t));let f={};if(t){let h=!1,m=[];for(let[p,g]of Object.entries(t))typeof g=="boolean"&&(h=!0),g===!0&&m.push(p);h&&(f["data-headlessui-state"]=m.join(" "))}if(a===u.Fragment&&Object.keys(lt(s)).length>0){if(!u.isValidElement(c)||Array.isArray(c)&&c.length>1)throw new Error(['Passing props on "Fragment"!',"",`The current component <${r} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(s).map(g=>`  - ${g}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(g=>`  - ${g}`).join(`
`)].join(`
`));let h=c.props,m=typeof(h==null?void 0:h.className)=="function"?(...g)=>xe(h==null?void 0:h.className(...g),s.className):xe(h==null?void 0:h.className,s.className),p=m?{className:m}:{};return u.cloneElement(c,Object.assign({},dt(c.props,lt(Me(s,["ref"]))),f,d,{ref:l(c.ref,d.ref)},p))}return u.createElement(a,Object.assign({},Me(s,["ref"]),a!==u.Fragment&&d,a!==u.Fragment&&f),c)}function Vt(...e){return e.every(t=>t==null)?void 0:t=>{for(let n of e)n!=null&&(typeof n=="function"?n(t):n.current=t)}}function dt(...e){if(e.length===0)return{};if(e.length===1)return e[0];let t={},n={};for(let r of e)for(let l in r)l.startsWith("on")&&typeof r[l]=="function"?(n[l]!=null||(n[l]=[]),n[l].push(r[l])):t[l]=r[l];if(t.disabled||t["aria-disabled"])return Object.assign(t,Object.fromEntries(Object.keys(n).map(r=>[r,void 0])));for(let r in n)Object.assign(t,{[r](l,...a){let o=n[r];for(let i of o){if((l instanceof Event||(l==null?void 0:l.nativeEvent)instanceof Event)&&l.defaultPrevented)return;i(l,...a)}}});return t}function O(e){var t;return Object.assign(u.forwardRef(e),{displayName:(t=e.displayName)!=null?t:e.name})}function lt(e){let t=Object.assign({},e);for(let n in t)t[n]===void 0&&delete t[n];return t}function Me(e,t=[]){let n=Object.assign({},e);for(let r of t)r in n&&delete n[r];return n}let Kt="div";var he=(e=>(e[e.None=1]="None",e[e.Focusable=2]="Focusable",e[e.Hidden=4]="Hidden",e))(he||{});function zt(e,t){var n;let{features:r=1,...l}=e,a={ref:t,"aria-hidden":(r&2)===2?!0:(n=l["aria-hidden"])!=null?n:void 0,hidden:(r&4)===4?!0:void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(r&4)===4&&(r&2)!==2&&{display:"none"}}};return A({ourProps:a,theirProps:l,slot:{},defaultTag:Kt,name:"Hidden"})}let be=O(zt),ze=u.createContext(null);ze.displayName="OpenClosedContext";var M=(e=>(e[e.Open=1]="Open",e[e.Closed=2]="Closed",e[e.Closing=4]="Closing",e[e.Opening=8]="Opening",e))(M||{});function Xe(){return u.useContext(ze)}function Xt({value:e,children:t}){return b.createElement(ze.Provider,{value:e},t)}function Qt(e){function t(){document.readyState!=="loading"&&(e(),document.removeEventListener("DOMContentLoaded",t))}typeof window<"u"&&typeof document<"u"&&(document.addEventListener("DOMContentLoaded",t),t())}let Q=[];Qt(()=>{function e(t){t.target instanceof HTMLElement&&t.target!==document.body&&Q[0]!==t.target&&(Q.unshift(t.target),Q=Q.filter(n=>n!=null&&n.isConnected),Q.splice(10))}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})});function Jt(e){let t=e.parentElement,n=null;for(;t&&!(t instanceof HTMLFieldSetElement);)t instanceof HTMLLegendElement&&(n=t),t=t.parentElement;let r=(t==null?void 0:t.getAttribute("disabled"))==="";return r&&Zt(n)?!1:r}function Zt(e){if(!e)return!1;let t=e.previousElementSibling;for(;t!==null;){if(t instanceof HTMLLegendElement)return!1;t=t.previousElementSibling}return!0}var H=(e=>(e.Space=" ",e.Enter="Enter",e.Escape="Escape",e.Backspace="Backspace",e.Delete="Delete",e.ArrowLeft="ArrowLeft",e.ArrowUp="ArrowUp",e.ArrowRight="ArrowRight",e.ArrowDown="ArrowDown",e.Home="Home",e.End="End",e.PageUp="PageUp",e.PageDown="PageDown",e.Tab="Tab",e))(H||{});function ft(e,t,n,r){let l=k(n);u.useEffect(()=>{e=e??window;function a(o){l.current(o)}return e.addEventListener(t,a,r),()=>e.removeEventListener(t,a,r)},[e,t,r])}function de(){let e=u.useRef(!1);return R(()=>(e.current=!0,()=>{e.current=!1}),[]),e}function pt(e){let t=y(e),n=u.useRef(!1);u.useEffect(()=>(n.current=!1,()=>{n.current=!0,Ee(()=>{n.current&&t()})}),[t])}var ve=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(ve||{});function en(){let e=u.useRef(0);return st("keydown",t=>{t.key==="Tab"&&(e.current=t.shiftKey?1:0)},!0),e}function mt(e){if(!e)return new Set;if(typeof e=="function")return new Set(e());let t=new Set;for(let n of e.current)n.current instanceof HTMLElement&&t.add(n.current);return t}let tn="div";var vt=(e=>(e[e.None=1]="None",e[e.InitialFocus=2]="InitialFocus",e[e.TabLock=4]="TabLock",e[e.FocusLock=8]="FocusLock",e[e.RestoreFocus=16]="RestoreFocus",e[e.All=30]="All",e))(vt||{});function nn(e,t){let n=u.useRef(null),r=N(n,t),{initialFocus:l,containers:a,features:o=30,...i}=e;ce()||(o=1);let s=we(n);on({ownerDocument:s},!!(o&16));let d=an({ownerDocument:s,container:n,initialFocus:l},!!(o&2));un({ownerDocument:s,container:n,containers:a,previousActiveElement:d},!!(o&8));let c=en(),f=y(g=>{let v=n.current;v&&(T=>T())(()=>{x(c.current,{[ve.Forwards]:()=>{q(v,D.First,{skipElements:[g.relatedTarget]})},[ve.Backwards]:()=>{q(v,D.Last,{skipElements:[g.relatedTarget]})}})})}),h=Ge(),m=u.useRef(!1),p={ref:r,onKeyDown(g){g.key=="Tab"&&(m.current=!0,h.requestAnimationFrame(()=>{m.current=!1}))},onBlur(g){let v=mt(a);n.current instanceof HTMLElement&&v.add(n.current);let T=g.relatedTarget;T instanceof HTMLElement&&T.dataset.headlessuiFocusGuard!=="true"&&(gt(v,T)||(m.current?q(n.current,x(c.current,{[ve.Forwards]:()=>D.Next,[ve.Backwards]:()=>D.Previous})|D.WrapAround,{relativeTo:g.target}):g.target instanceof HTMLElement&&re(g.target)))}};return b.createElement(b.Fragment,null,!!(o&4)&&b.createElement(be,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:f,features:he.Focusable}),A({ourProps:p,theirProps:i,defaultTag:tn,name:"FocusTrap"}),!!(o&4)&&b.createElement(be,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:f,features:he.Focusable}))}let rn=O(nn),pe=Object.assign(rn,{features:vt});function ln(e=!0){let t=u.useRef(Q.slice());return Ke(([n],[r])=>{r===!0&&n===!1&&Ee(()=>{t.current.splice(0)}),r===!1&&n===!0&&(t.current=Q.slice())},[e,Q,t]),y(()=>{var n;return(n=t.current.find(r=>r!=null&&r.isConnected))!=null?n:null})}function on({ownerDocument:e},t){let n=ln(t);Ke(()=>{t||(e==null?void 0:e.activeElement)===(e==null?void 0:e.body)&&re(n())},[t]),pt(()=>{t&&re(n())})}function an({ownerDocument:e,container:t,initialFocus:n},r){let l=u.useRef(null),a=de();return Ke(()=>{if(!r)return;let o=t.current;o&&Ee(()=>{if(!a.current)return;let i=e==null?void 0:e.activeElement;if(n!=null&&n.current){if((n==null?void 0:n.current)===i){l.current=i;return}}else if(o.contains(i)){l.current=i;return}n!=null&&n.current?re(n.current):q(o,D.First)===ae.Error&&console.warn("There are no focusable elements inside the <FocusTrap />"),l.current=e==null?void 0:e.activeElement})},[r]),l}function un({ownerDocument:e,container:t,containers:n,previousActiveElement:r},l){let a=de();ft(e==null?void 0:e.defaultView,"focus",o=>{if(!l||!a.current)return;let i=mt(n);t.current instanceof HTMLElement&&i.add(t.current);let s=r.current;if(!s)return;let d=o.target;d&&d instanceof HTMLElement?gt(i,d)?(r.current=d,re(d)):(o.preventDefault(),o.stopPropagation(),re(s)):re(r.current)},!0)}function gt(e,t){for(let n of e)if(n.contains(t))return!0;return!1}let ht=u.createContext(!1);function sn(){return u.useContext(ht)}function Ue(e){return b.createElement(ht.Provider,{value:e.force},e.children)}function cn(e){let t=sn(),n=u.useContext(bt),r=we(e),[l,a]=u.useState(()=>{if(!t&&n!==null||V.isServer)return null;let o=r==null?void 0:r.getElementById("headlessui-portal-root");if(o)return o;if(r===null)return null;let i=r.createElement("div");return i.setAttribute("id","headlessui-portal-root"),r.body.appendChild(i)});return u.useEffect(()=>{l!==null&&(r!=null&&r.body.contains(l)||r==null||r.body.appendChild(l))},[l,r]),u.useEffect(()=>{t||n!==null&&a(n.current)},[n,a,t]),l}let dn=u.Fragment;function fn(e,t){let n=e,r=u.useRef(null),l=N(Gt(c=>{r.current=c}),t),a=we(r),o=cn(r),[i]=u.useState(()=>{var c;return V.isServer?null:(c=a==null?void 0:a.createElement("div"))!=null?c:null}),s=u.useContext(_e),d=ce();return R(()=>{!o||!i||o.contains(i)||(i.setAttribute("data-headlessui-portal",""),o.appendChild(i))},[o,i]),R(()=>{if(i&&s)return s.register(i)},[s,i]),pt(()=>{var c;!o||!i||(i instanceof Node&&o.contains(i)&&o.removeChild(i),o.childNodes.length<=0&&((c=o.parentElement)==null||c.removeChild(o)))}),d?!o||!i?null:Ot.createPortal(A({ourProps:{ref:l},theirProps:n,defaultTag:dn,name:"Portal"}),i):null}let pn=u.Fragment,bt=u.createContext(null);function mn(e,t){let{target:n,...r}=e,l={ref:N(t)};return b.createElement(bt.Provider,{value:n},A({ourProps:l,theirProps:r,defaultTag:pn,name:"Popover.Group"}))}let _e=u.createContext(null);function vn(){let e=u.useContext(_e),t=u.useRef([]),n=y(a=>(t.current.push(a),e&&e.register(a),()=>r(a))),r=y(a=>{let o=t.current.indexOf(a);o!==-1&&t.current.splice(o,1),e&&e.unregister(a)}),l=u.useMemo(()=>({register:n,unregister:r,portals:t}),[n,r,t]);return[t,u.useMemo(()=>function({children:a}){return b.createElement(_e.Provider,{value:l},a)},[l])]}let gn=O(fn),hn=O(mn),We=Object.assign(gn,{Group:hn});function bn(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}const En=typeof Object.is=="function"?Object.is:bn,{useState:wn,useEffect:yn,useLayoutEffect:Tn,useDebugValue:$n}=ge;function Sn(e,t,n){const r=t(),[{inst:l},a]=wn({inst:{value:r,getSnapshot:t}});return Tn(()=>{l.value=r,l.getSnapshot=t,ke(l)&&a({inst:l})},[e,r,t]),yn(()=>(ke(l)&&a({inst:l}),e(()=>{ke(l)&&a({inst:l})})),[e]),$n(r),r}function ke(e){const t=e.getSnapshot,n=e.value;try{const r=t();return!En(n,r)}catch{return!0}}function Pn(e,t,n){return t()}const xn=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",Ln=!xn,Fn=Ln?Pn:Sn,Cn="useSyncExternalStore"in ge?(e=>e.useSyncExternalStore)(ge):Fn;function Rn(e){return Cn(e.subscribe,e.getSnapshot,e.getSnapshot)}function On(e,t){let n=e(),r=new Set;return{getSnapshot(){return n},subscribe(l){return r.add(l),()=>r.delete(l)},dispatch(l,...a){let o=t[l].call(n,...a);o&&(n=o,r.forEach(i=>i()))}}}function Dn(){let e;return{before({doc:t}){var n;let r=t.documentElement;e=((n=t.defaultView)!=null?n:window).innerWidth-r.clientWidth},after({doc:t,d:n}){let r=t.documentElement,l=r.clientWidth-r.offsetWidth,a=e-l;n.style(r,"paddingRight",`${a}px`)}}}function An(){return ut()?{before({doc:e,d:t,meta:n}){function r(l){return n.containers.flatMap(a=>a()).some(a=>a.contains(l))}t.microTask(()=>{var l;if(window.getComputedStyle(e.documentElement).scrollBehavior!=="auto"){let i=le();i.style(e.documentElement,"scrollBehavior","auto"),t.add(()=>t.microTask(()=>i.dispose()))}let a=(l=window.scrollY)!=null?l:window.pageYOffset,o=null;t.addEventListener(e,"click",i=>{if(i.target instanceof HTMLElement)try{let s=i.target.closest("a");if(!s)return;let{hash:d}=new URL(s.href),c=e.querySelector(d);c&&!r(c)&&(o=c)}catch{}},!0),t.addEventListener(e,"touchstart",i=>{if(i.target instanceof HTMLElement)if(r(i.target)){let s=i.target;for(;s.parentElement&&r(s.parentElement);)s=s.parentElement;t.style(s,"overscrollBehavior","contain")}else t.style(i.target,"touchAction","none")}),t.addEventListener(e,"touchmove",i=>{if(i.target instanceof HTMLElement)if(r(i.target)){let s=i.target;for(;s.parentElement&&s.dataset.headlessuiPortal!==""&&!(s.scrollHeight>s.clientHeight||s.scrollWidth>s.clientWidth);)s=s.parentElement;s.dataset.headlessuiPortal===""&&i.preventDefault()}else i.preventDefault()},{passive:!1}),t.add(()=>{var i;let s=(i=window.scrollY)!=null?i:window.pageYOffset;a!==s&&window.scrollTo(0,a),o&&o.isConnected&&(o.scrollIntoView({block:"nearest"}),o=null)})})}}:{}}function Nn(){return{before({doc:e,d:t}){t.style(e.documentElement,"overflow","hidden")}}}function Mn(e){let t={};for(let n of e)Object.assign(t,n(t));return t}let ne=On(()=>new Map,{PUSH(e,t){var n;let r=(n=this.get(e))!=null?n:{doc:e,count:0,d:le(),meta:new Set};return r.count++,r.meta.add(t),this.set(e,r),this},POP(e,t){let n=this.get(e);return n&&(n.count--,n.meta.delete(t)),this},SCROLL_PREVENT({doc:e,d:t,meta:n}){let r={doc:e,d:t,meta:Mn(n)},l=[An(),Dn(),Nn()];l.forEach(({before:a})=>a==null?void 0:a(r)),l.forEach(({after:a})=>a==null?void 0:a(r))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});ne.subscribe(()=>{let e=ne.getSnapshot(),t=new Map;for(let[n]of e)t.set(n,n.documentElement.style.overflow);for(let n of e.values()){let r=t.get(n.doc)==="hidden",l=n.count!==0;(l&&!r||!l&&r)&&ne.dispatch(n.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",n),n.count===0&&ne.dispatch("TEARDOWN",n)}});function kn(e,t,n){let r=Rn(ne),l=e?r.get(e):void 0,a=l?l.count>0:!1;return R(()=>{if(!(!e||!t))return ne.dispatch("PUSH",e,n),()=>ne.dispatch("POP",e,n)},[t,e]),a}let Ie=new Map,me=new Map;function ot(e,t=!0){R(()=>{var n;if(!t)return;let r=typeof e=="function"?e():e.current;if(!r)return;function l(){var o;if(!r)return;let i=(o=me.get(r))!=null?o:1;if(i===1?me.delete(r):me.set(r,i-1),i!==1)return;let s=Ie.get(r);s&&(s["aria-hidden"]===null?r.removeAttribute("aria-hidden"):r.setAttribute("aria-hidden",s["aria-hidden"]),r.inert=s.inert,Ie.delete(r))}let a=(n=me.get(r))!=null?n:0;return me.set(r,a+1),a!==0||(Ie.set(r,{"aria-hidden":r.getAttribute("aria-hidden"),inert:r.inert}),r.setAttribute("aria-hidden","true"),r.inert=!0),l},[e,t])}function In({defaultContainers:e=[],portals:t,mainTreeNodeRef:n}={}){var r;let l=u.useRef((r=n==null?void 0:n.current)!=null?r:null),a=we(l),o=y(()=>{var i,s,d;let c=[];for(let f of e)f!==null&&(f instanceof HTMLElement?c.push(f):"current"in f&&f.current instanceof HTMLElement&&c.push(f.current));if(t!=null&&t.current)for(let f of t.current)c.push(f);for(let f of(i=a==null?void 0:a.querySelectorAll("html > *, body > *"))!=null?i:[])f!==document.body&&f!==document.head&&f instanceof HTMLElement&&f.id!=="headlessui-portal-root"&&(f.contains(l.current)||f.contains((d=(s=l.current)==null?void 0:s.getRootNode())==null?void 0:d.host)||c.some(h=>f.contains(h))||c.push(f));return c});return{resolveContainers:o,contains:y(i=>o().some(s=>s.contains(i))),mainTreeNodeRef:l,MainTreeNode:u.useMemo(()=>function(){return n!=null?null:b.createElement(be,{features:he.Hidden,ref:l})},[l,n])}}let Qe=u.createContext(()=>{});Qe.displayName="StackContext";var qe=(e=>(e[e.Add=0]="Add",e[e.Remove=1]="Remove",e))(qe||{});function Hn(){return u.useContext(Qe)}function jn({children:e,onUpdate:t,type:n,element:r,enabled:l}){let a=Hn(),o=y((...i)=>{t==null||t(...i),a(...i)});return R(()=>{let i=l===void 0||l===!0;return i&&o(0,n,r),()=>{i&&o(1,n,r)}},[o,n,r,l]),b.createElement(Qe.Provider,{value:o},e)}let Et=u.createContext(null);function wt(){let e=u.useContext(Et);if(e===null){let t=new Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(t,wt),t}return e}function Bn(){let[e,t]=u.useState([]);return[e.length>0?e.join(" "):void 0,u.useMemo(()=>function(n){let r=y(a=>(t(o=>[...o,a]),()=>t(o=>{let i=o.slice(),s=i.indexOf(a);return s!==-1&&i.splice(s,1),i}))),l=u.useMemo(()=>({register:r,slot:n.slot,name:n.name,props:n.props}),[r,n.slot,n.name,n.props]);return b.createElement(Et.Provider,{value:l},n.children)},[t])]}let Un="p";function _n(e,t){let n=Z(),{id:r=`headlessui-description-${n}`,...l}=e,a=wt(),o=N(t);R(()=>a.register(r),[r,a.register]);let i={ref:o,...a.props,id:r};return A({ourProps:i,theirProps:l,slot:a.slot||{},defaultTag:Un,name:a.name||"Description"})}let Wn=O(_n),qn=Object.assign(Wn,{});var Yn=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(Yn||{}),Gn=(e=>(e[e.SetTitleId=0]="SetTitleId",e))(Gn||{});let Vn={0(e,t){return e.titleId===t.id?e:{...e,titleId:t.id}}},Le=u.createContext(null);Le.displayName="DialogContext";function ye(e){let t=u.useContext(Le);if(t===null){let n=new Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,ye),n}return t}function Kn(e,t,n=()=>[document.body]){kn(e,t,r=>{var l;return{containers:[...(l=r.containers)!=null?l:[],n]}})}function zn(e,t){return x(t.type,Vn,e,t)}let Xn="div",Qn=ue.RenderStrategy|ue.Static;function Jn(e,t){let n=Z(),{id:r=`headlessui-dialog-${n}`,open:l,onClose:a,initialFocus:o,role:i="dialog",__demoMode:s=!1,...d}=e,[c,f]=u.useState(0),h=u.useRef(!1);i=function(){return i==="dialog"||i==="alertdialog"?i:(h.current||(h.current=!0,console.warn(`Invalid role [${i}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog")}();let m=Xe();l===void 0&&m!==null&&(l=(m&M.Open)===M.Open);let p=u.useRef(null),g=N(p,t),v=we(p),T=e.hasOwnProperty("open")||m!==null,S=e.hasOwnProperty("onClose");if(!T&&!S)throw new Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!T)throw new Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!S)throw new Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if(typeof l!="boolean")throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${l}`);if(typeof a!="function")throw new Error(`You provided an \`onClose\` prop to the \`Dialog\`, but the value is not a function. Received: ${a}`);let w=l?0:1,[C,j]=u.useReducer(zn,{titleId:null,descriptionId:null,panelRef:u.createRef()}),L=y(()=>a(!1)),_=y(P=>j({type:0,id:P})),B=ce()?s?!1:w===0:!1,$=c>1,E=u.useContext(Le)!==null,[F,U]=vn(),ee={get current(){var P;return(P=C.panelRef.current)!=null?P:p.current}},{resolveContainers:K,mainTreeNodeRef:Y,MainTreeNode:Oe}=In({portals:F,defaultContainers:[ee]}),te=$?"parent":"leaf",Te=m!==null?(m&M.Closing)===M.Closing:!1,De=(()=>E||Te?!1:B)(),oe=u.useCallback(()=>{var P,G;return(G=Array.from((P=v==null?void 0:v.querySelectorAll("body > *"))!=null?P:[]).find(I=>I.id==="headlessui-portal-root"?!1:I.contains(Y.current)&&I instanceof HTMLElement))!=null?G:null},[Y]);ot(oe,De);let fe=(()=>$?!0:B)(),z=u.useCallback(()=>{var P,G;return(G=Array.from((P=v==null?void 0:v.querySelectorAll("[data-headlessui-portal]"))!=null?P:[]).find(I=>I.contains(Y.current)&&I instanceof HTMLElement))!=null?G:null},[Y]);ot(z,fe);let Ae=(()=>!(!B||$))();qt(K,P=>{P.preventDefault(),L()},Ae);let W=(()=>!($||w!==0))();ft(v==null?void 0:v.defaultView,"keydown",P=>{W&&(P.defaultPrevented||P.key===H.Escape&&(P.preventDefault(),P.stopPropagation(),L()))});let xt=(()=>!(Te||w!==0||E))();Kn(v,xt,K),u.useEffect(()=>{if(w!==0||!p.current)return;let P=new ResizeObserver(G=>{for(let I of G){let $e=I.target.getBoundingClientRect();$e.x===0&&$e.y===0&&$e.width===0&&$e.height===0&&L()}});return P.observe(p.current),()=>P.disconnect()},[w,p,L]);let[Lt,Ft]=Bn(),Ct=u.useMemo(()=>[{dialogState:w,close:L,setTitleId:_},C],[w,C,L,_]),tt=u.useMemo(()=>({open:w===0}),[w]),Rt={ref:g,id:r,role:i,"aria-modal":w===0?!0:void 0,"aria-labelledby":C.titleId,"aria-describedby":Lt};return b.createElement(jn,{type:"Dialog",enabled:w===0,element:p,onUpdate:y((P,G)=>{G==="Dialog"&&x(P,{[qe.Add]:()=>f(I=>I+1),[qe.Remove]:()=>f(I=>I-1)})})},b.createElement(Ue,{force:!0},b.createElement(We,null,b.createElement(Le.Provider,{value:Ct},b.createElement(We.Group,{target:p},b.createElement(Ue,{force:!1},b.createElement(Ft,{slot:tt,name:"Dialog.Description"},b.createElement(pe,{initialFocus:o,containers:K,features:B?x(te,{parent:pe.features.RestoreFocus,leaf:pe.features.All&~pe.features.FocusLock}):pe.features.None},b.createElement(U,null,A({ourProps:Rt,theirProps:d,slot:tt,defaultTag:Xn,features:Qn,visible:w===0,name:"Dialog"}))))))))),b.createElement(Oe,null))}let Zn="div";function er(e,t){let n=Z(),{id:r=`headlessui-dialog-overlay-${n}`,...l}=e,[{dialogState:a,close:o}]=ye("Dialog.Overlay"),i=N(t),s=y(c=>{if(c.target===c.currentTarget){if(Jt(c.currentTarget))return c.preventDefault();c.preventDefault(),c.stopPropagation(),o()}}),d=u.useMemo(()=>({open:a===0}),[a]);return A({ourProps:{ref:i,id:r,"aria-hidden":!0,onClick:s},theirProps:l,slot:d,defaultTag:Zn,name:"Dialog.Overlay"})}let tr="div";function nr(e,t){let n=Z(),{id:r=`headlessui-dialog-backdrop-${n}`,...l}=e,[{dialogState:a},o]=ye("Dialog.Backdrop"),i=N(t);u.useEffect(()=>{if(o.panelRef.current===null)throw new Error("A <Dialog.Backdrop /> component is being used, but a <Dialog.Panel /> component is missing.")},[o.panelRef]);let s=u.useMemo(()=>({open:a===0}),[a]);return b.createElement(Ue,{force:!0},b.createElement(We,null,A({ourProps:{ref:i,id:r,"aria-hidden":!0},theirProps:l,slot:s,defaultTag:tr,name:"Dialog.Backdrop"})))}let rr="div";function lr(e,t){let n=Z(),{id:r=`headlessui-dialog-panel-${n}`,...l}=e,[{dialogState:a},o]=ye("Dialog.Panel"),i=N(t,o.panelRef),s=u.useMemo(()=>({open:a===0}),[a]),d=y(c=>{c.stopPropagation()});return A({ourProps:{ref:i,id:r,onClick:d},theirProps:l,slot:s,defaultTag:rr,name:"Dialog.Panel"})}let or="h2";function ar(e,t){let n=Z(),{id:r=`headlessui-dialog-title-${n}`,...l}=e,[{dialogState:a,setTitleId:o}]=ye("Dialog.Title"),i=N(t);u.useEffect(()=>(o(r),()=>o(null)),[r,o]);let s=u.useMemo(()=>({open:a===0}),[a]);return A({ourProps:{ref:i,id:r},theirProps:l,slot:s,defaultTag:or,name:"Dialog.Title"})}let ir=O(Jn),ur=O(nr),sr=O(lr),cr=O(er),dr=O(ar),tl=Object.assign(ir,{Backdrop:ur,Panel:sr,Overlay:cr,Title:dr,Description:qn});function fr(e=0){let[t,n]=u.useState(e),r=de(),l=u.useCallback(s=>{r.current&&n(d=>d|s)},[t,r]),a=u.useCallback(s=>!!(t&s),[t]),o=u.useCallback(s=>{r.current&&n(d=>d&~s)},[n,r]),i=u.useCallback(s=>{r.current&&n(d=>d^s)},[n]);return{flags:t,addFlag:l,hasFlag:a,removeFlag:o,toggleFlag:i}}function pr({onFocus:e}){let[t,n]=u.useState(!0),r=de();return t?b.createElement(be,{as:"button",type:"button",features:he.Focusable,onFocus:l=>{l.preventDefault();let a,o=50;function i(){if(o--<=0){a&&cancelAnimationFrame(a);return}if(e()){if(cancelAnimationFrame(a),!r.current)return;n(!1);return}a=requestAnimationFrame(i)}a=requestAnimationFrame(i)}}):null}const yt=u.createContext(null);function mr(){return{groups:new Map,get(e,t){var n;let r=this.groups.get(e);r||(r=new Map,this.groups.set(e,r));let l=(n=r.get(t))!=null?n:0;r.set(t,l+1);let a=Array.from(r.keys()).indexOf(t);function o(){let i=r.get(t);i>1?r.set(t,i-1):r.delete(t)}return[a,o]}}}function vr({children:e}){let t=u.useRef(mr());return u.createElement(yt.Provider,{value:t},e)}function Tt(e){let t=u.useContext(yt);if(!t)throw new Error("You must wrap your component in a <StableCollection>");let n=gr(),[r,l]=t.current.get(e,n);return u.useEffect(()=>l,[]),r}function gr(){var e,t,n;let r=(n=(t=(e=u.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED)==null?void 0:e.ReactCurrentOwner)==null?void 0:t.current)!=null?n:null;if(!r)return Symbol();let l=[],a=r;for(;a;)l.push(a.index),a=a.return;return"$."+l.join(".")}var hr=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(hr||{}),br=(e=>(e[e.Less=-1]="Less",e[e.Equal=0]="Equal",e[e.Greater=1]="Greater",e))(br||{}),Er=(e=>(e[e.SetSelectedIndex=0]="SetSelectedIndex",e[e.RegisterTab=1]="RegisterTab",e[e.UnregisterTab=2]="UnregisterTab",e[e.RegisterPanel=3]="RegisterPanel",e[e.UnregisterPanel=4]="UnregisterPanel",e))(Er||{});let wr={0(e,t){var n;let r=ie(e.tabs,c=>c.current),l=ie(e.panels,c=>c.current),a=r.filter(c=>{var f;return!((f=c.current)!=null&&f.hasAttribute("disabled"))}),o={...e,tabs:r,panels:l};if(t.index<0||t.index>r.length-1){let c=x(Math.sign(t.index-e.selectedIndex),{[-1]:()=>1,0:()=>x(Math.sign(t.index),{[-1]:()=>0,0:()=>0,1:()=>1}),1:()=>0});if(a.length===0)return o;let f=x(c,{0:()=>r.indexOf(a[0]),1:()=>r.indexOf(a[a.length-1])});return{...o,selectedIndex:f===-1?e.selectedIndex:f}}let i=r.slice(0,t.index),s=[...r.slice(t.index),...i].find(c=>a.includes(c));if(!s)return o;let d=(n=r.indexOf(s))!=null?n:e.selectedIndex;return d===-1&&(d=e.selectedIndex),{...o,selectedIndex:d}},1(e,t){if(e.tabs.includes(t.tab))return e;let n=e.tabs[e.selectedIndex],r=ie([...e.tabs,t.tab],a=>a.current),l=e.selectedIndex;return e.info.current.isControlled||(l=r.indexOf(n),l===-1&&(l=e.selectedIndex)),{...e,tabs:r,selectedIndex:l}},2(e,t){return{...e,tabs:e.tabs.filter(n=>n!==t.tab)}},3(e,t){return e.panels.includes(t.panel)?e:{...e,panels:ie([...e.panels,t.panel],n=>n.current)}},4(e,t){return{...e,panels:e.panels.filter(n=>n!==t.panel)}}},Je=u.createContext(null);Je.displayName="TabsDataContext";function se(e){let t=u.useContext(Je);if(t===null){let n=new Error(`<${e} /> is missing a parent <Tab.Group /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,se),n}return t}let Ze=u.createContext(null);Ze.displayName="TabsActionsContext";function et(e){let t=u.useContext(Ze);if(t===null){let n=new Error(`<${e} /> is missing a parent <Tab.Group /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,et),n}return t}function yr(e,t){return x(t.type,wr,e,t)}let Tr=u.Fragment;function $r(e,t){let{defaultIndex:n=0,vertical:r=!1,manual:l=!1,onChange:a,selectedIndex:o=null,...i}=e;const s=r?"vertical":"horizontal",d=l?"manual":"auto";let c=o!==null,f=k({isControlled:c}),h=N(t),[m,p]=u.useReducer(yr,{info:f,selectedIndex:o??n,tabs:[],panels:[]}),g=u.useMemo(()=>({selectedIndex:m.selectedIndex}),[m.selectedIndex]),v=k(a||(()=>{})),T=k(m.tabs),S=u.useMemo(()=>({orientation:s,activation:d,...m}),[s,d,m]),w=y($=>(p({type:1,tab:$}),()=>p({type:2,tab:$}))),C=y($=>(p({type:3,panel:$}),()=>p({type:4,panel:$}))),j=y($=>{L.current!==$&&v.current($),c||p({type:0,index:$})}),L=k(c?e.selectedIndex:m.selectedIndex),_=u.useMemo(()=>({registerTab:w,registerPanel:C,change:j}),[]);R(()=>{p({type:0,index:o??n})},[o]),R(()=>{if(L.current===void 0||m.tabs.length<=0)return;let $=ie(m.tabs,E=>E.current);$.some((E,F)=>m.tabs[F]!==E)&&j($.indexOf(m.tabs[L.current]))});let B={ref:h};return b.createElement(vr,null,b.createElement(Ze.Provider,{value:_},b.createElement(Je.Provider,{value:S},S.tabs.length<=0&&b.createElement(pr,{onFocus:()=>{var $,E;for(let F of T.current)if((($=F.current)==null?void 0:$.tabIndex)===0)return(E=F.current)==null||E.focus(),!0;return!1}}),A({ourProps:B,theirProps:i,slot:g,defaultTag:Tr,name:"Tabs"}))))}let Sr="div";function Pr(e,t){let{orientation:n,selectedIndex:r}=se("Tab.List"),l=N(t);return A({ourProps:{ref:l,role:"tablist","aria-orientation":n},theirProps:e,slot:{selectedIndex:r},defaultTag:Sr,name:"Tabs.List"})}let xr="button";function Lr(e,t){var n,r;let l=Z(),{id:a=`headlessui-tabs-tab-${l}`,...o}=e,{orientation:i,activation:s,selectedIndex:d,tabs:c,panels:f}=se("Tab"),h=et("Tab"),m=se("Tab"),p=u.useRef(null),g=N(p,t);R(()=>h.registerTab(p),[h,p]);let v=Tt("tabs"),T=c.indexOf(p);T===-1&&(T=v);let S=T===d,w=y(E=>{var F;let U=E();if(U===ae.Success&&s==="auto"){let ee=(F=Ve(p))==null?void 0:F.activeElement,K=m.tabs.findIndex(Y=>Y.current===ee);K!==-1&&h.change(K)}return U}),C=y(E=>{let F=c.map(U=>U.current).filter(Boolean);if(E.key===H.Space||E.key===H.Enter){E.preventDefault(),E.stopPropagation(),h.change(T);return}switch(E.key){case H.Home:case H.PageUp:return E.preventDefault(),E.stopPropagation(),w(()=>q(F,D.First));case H.End:case H.PageDown:return E.preventDefault(),E.stopPropagation(),w(()=>q(F,D.Last))}if(w(()=>x(i,{vertical(){return E.key===H.ArrowUp?q(F,D.Previous|D.WrapAround):E.key===H.ArrowDown?q(F,D.Next|D.WrapAround):ae.Error},horizontal(){return E.key===H.ArrowLeft?q(F,D.Previous|D.WrapAround):E.key===H.ArrowRight?q(F,D.Next|D.WrapAround):ae.Error}}))===ae.Success)return E.preventDefault()}),j=u.useRef(!1),L=y(()=>{var E;j.current||(j.current=!0,(E=p.current)==null||E.focus({preventScroll:!0}),h.change(T),Ee(()=>{j.current=!1}))}),_=y(E=>{E.preventDefault()}),B=u.useMemo(()=>{var E;return{selected:S,disabled:(E=e.disabled)!=null?E:!1}},[S,e.disabled]),$={ref:g,onKeyDown:C,onMouseDown:_,onClick:L,id:a,role:"tab",type:Yt(e,p),"aria-controls":(r=(n=f[T])==null?void 0:n.current)==null?void 0:r.id,"aria-selected":S,tabIndex:S?0:-1};return A({ourProps:$,theirProps:o,slot:B,defaultTag:xr,name:"Tabs.Tab"})}let Fr="div";function Cr(e,t){let{selectedIndex:n}=se("Tab.Panels"),r=N(t),l=u.useMemo(()=>({selectedIndex:n}),[n]);return A({ourProps:{ref:r},theirProps:e,slot:l,defaultTag:Fr,name:"Tabs.Panels"})}let Rr="div",Or=ue.RenderStrategy|ue.Static;function Dr(e,t){var n,r,l,a;let o=Z(),{id:i=`headlessui-tabs-panel-${o}`,tabIndex:s=0,...d}=e,{selectedIndex:c,tabs:f,panels:h}=se("Tab.Panel"),m=et("Tab.Panel"),p=u.useRef(null),g=N(p,t);R(()=>m.registerPanel(p),[m,p,i]);let v=Tt("panels"),T=h.indexOf(p);T===-1&&(T=v);let S=T===c,w=u.useMemo(()=>({selected:S}),[S]),C={ref:g,id:i,role:"tabpanel","aria-labelledby":(r=(n=f[T])==null?void 0:n.current)==null?void 0:r.id,tabIndex:S?s:-1};return!S&&((l=d.unmount)==null||l)&&!((a=d.static)!=null&&a)?b.createElement(be,{as:"span","aria-hidden":"true",...C}):A({ourProps:C,theirProps:d,slot:w,defaultTag:Rr,features:Or,visible:S,name:"Tabs.Panel"})}let Ar=O(Lr),Nr=O($r),Mr=O(Pr),kr=O(Cr),Ir=O(Dr),nl=Object.assign(Ar,{Group:Nr,List:Mr,Panels:kr,Panel:Ir});function Hr(e){let t={called:!1};return(...n)=>{if(!t.called)return t.called=!0,e(...n)}}function He(e,...t){e&&t.length>0&&e.classList.add(...t)}function je(e,...t){e&&t.length>0&&e.classList.remove(...t)}function jr(e,t){let n=le();if(!e)return n.dispose;let{transitionDuration:r,transitionDelay:l}=getComputedStyle(e),[a,o]=[r,l].map(s=>{let[d=0]=s.split(",").filter(Boolean).map(c=>c.includes("ms")?parseFloat(c):parseFloat(c)*1e3).sort((c,f)=>f-c);return d}),i=a+o;if(i!==0){n.group(d=>{d.setTimeout(()=>{t(),d.dispose()},i),d.addEventListener(e,"transitionrun",c=>{c.target===c.currentTarget&&d.dispose()})});let s=n.addEventListener(e,"transitionend",d=>{d.target===d.currentTarget&&(t(),s())})}else t();return n.add(()=>t()),n.dispose}function Br(e,t,n,r){let l=n?"enter":"leave",a=le(),o=r!==void 0?Hr(r):()=>{};l==="enter"&&(e.removeAttribute("hidden"),e.style.display="");let i=x(l,{enter:()=>t.enter,leave:()=>t.leave}),s=x(l,{enter:()=>t.enterTo,leave:()=>t.leaveTo}),d=x(l,{enter:()=>t.enterFrom,leave:()=>t.leaveFrom});return je(e,...t.base,...t.enter,...t.enterTo,...t.enterFrom,...t.leave,...t.leaveFrom,...t.leaveTo,...t.entered),He(e,...t.base,...i,...d),a.nextFrame(()=>{je(e,...t.base,...i,...d),He(e,...t.base,...i,...s),jr(e,()=>(je(e,...t.base,...i),He(e,...t.base,...t.entered),o()))}),a.dispose}function Ur({immediate:e,container:t,direction:n,classes:r,onStart:l,onStop:a}){let o=de(),i=Ge(),s=k(n);R(()=>{e&&(s.current="enter")},[e]),R(()=>{let d=le();i.add(d.dispose);let c=t.current;if(c&&s.current!=="idle"&&o.current)return d.dispose(),l.current(s.current),d.add(Br(c,r.current,s.current==="enter",()=>{d.dispose(),a.current(s.current)})),d.dispose},[n])}function X(e=""){return e.split(/\s+/).filter(t=>t.length>1)}let Fe=u.createContext(null);Fe.displayName="TransitionContext";var _r=(e=>(e.Visible="visible",e.Hidden="hidden",e))(_r||{});function Wr(){let e=u.useContext(Fe);if(e===null)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}function qr(){let e=u.useContext(Ce);if(e===null)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}let Ce=u.createContext(null);Ce.displayName="NestingContext";function Re(e){return"children"in e?Re(e.children):e.current.filter(({el:t})=>t.current!==null).filter(({state:t})=>t==="visible").length>0}function $t(e,t){let n=k(e),r=u.useRef([]),l=de(),a=Ge(),o=y((m,p=J.Hidden)=>{let g=r.current.findIndex(({el:v})=>v===m);g!==-1&&(x(p,{[J.Unmount](){r.current.splice(g,1)},[J.Hidden](){r.current[g].state="hidden"}}),a.microTask(()=>{var v;!Re(r)&&l.current&&((v=n.current)==null||v.call(n))}))}),i=y(m=>{let p=r.current.find(({el:g})=>g===m);return p?p.state!=="visible"&&(p.state="visible"):r.current.push({el:m,state:"visible"}),()=>o(m,J.Unmount)}),s=u.useRef([]),d=u.useRef(Promise.resolve()),c=u.useRef({enter:[],leave:[],idle:[]}),f=y((m,p,g)=>{s.current.splice(0),t&&(t.chains.current[p]=t.chains.current[p].filter(([v])=>v!==m)),t==null||t.chains.current[p].push([m,new Promise(v=>{s.current.push(v)})]),t==null||t.chains.current[p].push([m,new Promise(v=>{Promise.all(c.current[p].map(([T,S])=>S)).then(()=>v())})]),p==="enter"?d.current=d.current.then(()=>t==null?void 0:t.wait.current).then(()=>g(p)):g(p)}),h=y((m,p,g)=>{Promise.all(c.current[p].splice(0).map(([v,T])=>T)).then(()=>{var v;(v=s.current.shift())==null||v()}).then(()=>g(p))});return u.useMemo(()=>({children:r,register:i,unregister:o,onStart:f,onStop:h,wait:d,chains:c}),[i,o,r,f,h,c,d])}function Yr(){}let Gr=["beforeEnter","afterEnter","beforeLeave","afterLeave"];function at(e){var t;let n={};for(let r of Gr)n[r]=(t=e[r])!=null?t:Yr;return n}function Vr(e){let t=u.useRef(at(e));return u.useEffect(()=>{t.current=at(e)},[e]),t}let Kr="div",St=ue.RenderStrategy;function zr(e,t){var n,r;let{beforeEnter:l,afterEnter:a,beforeLeave:o,afterLeave:i,enter:s,enterFrom:d,enterTo:c,entered:f,leave:h,leaveFrom:m,leaveTo:p,...g}=e,v=u.useRef(null),T=N(v,t),S=(n=g.unmount)==null||n?J.Unmount:J.Hidden,{show:w,appear:C,initial:j}=Wr(),[L,_]=u.useState(w?"visible":"hidden"),B=qr(),{register:$,unregister:E}=B;u.useEffect(()=>$(v),[$,v]),u.useEffect(()=>{if(S===J.Hidden&&v.current){if(w&&L!=="visible"){_("visible");return}return x(L,{hidden:()=>E(v),visible:()=>$(v)})}},[L,v,$,E,w,S]);let F=k({base:X(g.className),enter:X(s),enterFrom:X(d),enterTo:X(c),entered:X(f),leave:X(h),leaveFrom:X(m),leaveTo:X(p)}),U=Vr({beforeEnter:l,afterEnter:a,beforeLeave:o,afterLeave:i}),ee=ce();u.useEffect(()=>{if(ee&&L==="visible"&&v.current===null)throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")},[v,L,ee]);let K=j&&!C,Y=C&&w&&j,Oe=(()=>!ee||K?"idle":w?"enter":"leave")(),te=fr(0),Te=y(W=>x(W,{enter:()=>{te.addFlag(M.Opening),U.current.beforeEnter()},leave:()=>{te.addFlag(M.Closing),U.current.beforeLeave()},idle:()=>{}})),De=y(W=>x(W,{enter:()=>{te.removeFlag(M.Opening),U.current.afterEnter()},leave:()=>{te.removeFlag(M.Closing),U.current.afterLeave()},idle:()=>{}})),oe=$t(()=>{_("hidden"),E(v)},B),fe=u.useRef(!1);Ur({immediate:Y,container:v,classes:F,direction:Oe,onStart:k(W=>{fe.current=!0,oe.onStart(v,W,Te)}),onStop:k(W=>{fe.current=!1,oe.onStop(v,W,De),W==="leave"&&!Re(oe)&&(_("hidden"),E(v))})});let z=g,Ae={ref:T};return Y?z={...z,className:xe(g.className,...F.current.enter,...F.current.enterFrom)}:fe.current&&(z.className=xe(g.className,(r=v.current)==null?void 0:r.className),z.className===""&&delete z.className),b.createElement(Ce.Provider,{value:oe},b.createElement(Xt,{value:x(L,{visible:M.Open,hidden:M.Closed})|te.flags},A({ourProps:Ae,theirProps:z,defaultTag:Kr,features:St,visible:L==="visible",name:"Transition.Child"})))}function Xr(e,t){let{show:n,appear:r=!1,unmount:l=!0,...a}=e,o=u.useRef(null),i=N(o,t);ce();let s=Xe();if(n===void 0&&s!==null&&(n=(s&M.Open)===M.Open),![!0,!1].includes(n))throw new Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[d,c]=u.useState(n?"visible":"hidden"),f=$t(()=>{c("hidden")}),[h,m]=u.useState(!0),p=u.useRef([n]);R(()=>{h!==!1&&p.current[p.current.length-1]!==n&&(p.current.push(n),m(!1))},[p,n]);let g=u.useMemo(()=>({show:n,appear:r,initial:h}),[n,r,h]);u.useEffect(()=>{if(n)c("visible");else if(!Re(f))c("hidden");else{let w=o.current;if(!w)return;let C=w.getBoundingClientRect();C.x===0&&C.y===0&&C.width===0&&C.height===0&&c("hidden")}},[n,f]);let v={unmount:l},T=y(()=>{var w;h&&m(!1),(w=e.beforeEnter)==null||w.call(e)}),S=y(()=>{var w;h&&m(!1),(w=e.beforeLeave)==null||w.call(e)});return b.createElement(Ce.Provider,{value:f},b.createElement(Fe.Provider,{value:g},A({ourProps:{...v,as:u.Fragment,children:b.createElement(Pt,{ref:i,...v,...a,beforeEnter:T,beforeLeave:S})},theirProps:{},defaultTag:u.Fragment,features:St,visible:d==="visible",name:"Transition"})))}function Qr(e,t){let n=u.useContext(Fe)!==null,r=Xe()!==null;return b.createElement(b.Fragment,null,!n&&r?b.createElement(Ye,{ref:t,...e}):b.createElement(Pt,{ref:t,...e}))}let Ye=O(Xr),Pt=O(zr),Jr=O(Qr),rl=Object.assign(Ye,{Child:Jr,Root:Ye});export{nl as $,tl as _,rl as q};
