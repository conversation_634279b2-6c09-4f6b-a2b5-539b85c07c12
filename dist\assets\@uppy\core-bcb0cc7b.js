import{g}from"../vendor-94843817.js";import{B as h}from"./aws-s3-4ad7e04c.js";import{Y as d}from"../@fullcalendar/core-ff88745d.js";function x(e){const t=e.lastIndexOf(".");return t===-1||t===e.length-1?{name:e,extension:void 0}:{name:e.slice(0,t),extension:e.slice(t+1)}}const c={__proto__:null,md:"text/markdown",markdown:"text/markdown",mp4:"video/mp4",mp3:"audio/mp3",svg:"image/svg+xml",jpg:"image/jpeg",png:"image/png",webp:"image/webp",gif:"image/gif",heic:"image/heic",heif:"image/heif",yaml:"text/yaml",yml:"text/yaml",csv:"text/csv",tsv:"text/tab-separated-values",tab:"text/tab-separated-values",avi:"video/x-msvideo",mks:"video/x-matroska",mkv:"video/x-matroska",mov:"video/quicktime",dicom:"application/dicom",doc:"application/msword",docm:"application/vnd.ms-word.document.macroenabled.12",docx:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",dot:"application/msword",dotm:"application/vnd.ms-word.template.macroenabled.12",dotx:"application/vnd.openxmlformats-officedocument.wordprocessingml.template",xla:"application/vnd.ms-excel",xlam:"application/vnd.ms-excel.addin.macroenabled.12",xlc:"application/vnd.ms-excel",xlf:"application/x-xliff+xml",xlm:"application/vnd.ms-excel",xls:"application/vnd.ms-excel",xlsb:"application/vnd.ms-excel.sheet.binary.macroenabled.12",xlsm:"application/vnd.ms-excel.sheet.macroenabled.12",xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",xlt:"application/vnd.ms-excel",xltm:"application/vnd.ms-excel.template.macroenabled.12",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template",xlw:"application/vnd.ms-excel",txt:"text/plain",text:"text/plain",conf:"text/plain",log:"text/plain",pdf:"application/pdf",zip:"application/zip","7z":"application/x-7z-compressed",rar:"application/x-rar-compressed",tar:"application/x-tar",gz:"application/gzip",dmg:"application/x-apple-diskimage"};function v(e){var t;if(e.type)return e.type;const n=e.name?(t=x(e.name).extension)==null?void 0:t.toLowerCase():null;return n&&n in c?c[n]:"application/octet-stream"}function y(e){return e.charCodeAt(0).toString(32)}function u(e){let t="";return e.replace(/[^A-Z0-9]/gi,n=>(t+=`-${y(n)}`,"/"))+t}function b(e,t){let n=t||"uppy";return typeof e.name=="string"&&(n+=`-${u(e.name.toLowerCase())}`),e.type!==void 0&&(n+=`-${e.type}`),e.meta&&typeof e.meta.relativePath=="string"&&(n+=`-${u(e.meta.relativePath.toLowerCase())}`),e.data.size!==void 0&&(n+=`-${e.data.size}`),e.data.lastModified!==void 0&&(n+=`-${e.data.lastModified}`),n}function w(e){return!e.isRemote||!e.remote?!1:new Set(["box","dropbox","drive","facebook","unsplash"]).has(e.remote.provider)}function C(e,t){if(w(e))return e.id;const n=v(e);return b({...e,type:n},t)}var E=function(t){if(typeof t!="number"||Number.isNaN(t))throw new TypeError(`Expected a number, got ${typeof t}`);const n=t<0;let a=Math.abs(t);if(n&&(a=-a),a===0)return"0 B";const i=["B","KB","MB","GB","TB","PB","EB","ZB","YB"],o=Math.min(Math.floor(Math.log(a)/Math.log(1024)),i.length-1),s=Number(a/1024**o),r=i[o];return`${s>=10||s%1===0?Math.round(s):s.toFixed(1)} ${r}`};const z=g(E);function T(e){return typeof e!="object"||e===null||!("nodeType"in e)?!1:e.nodeType===Node.ELEMENT_NODE}function M(e,t){return t===void 0&&(t=document),typeof e=="string"?t.querySelector(e):T(e)?e:null}function P(e){for(var t;e&&!e.dir;)e=e.parentNode;return(t=e)==null?void 0:t.dir}function m(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw new TypeError("attempted to use private field on non-instance");return e}var B=0;function $(e){return"__private_"+B+++"_"+e}function I(e){let t=null,n;return function(){for(var a=arguments.length,i=new Array(a),o=0;o<a;o++)i[o]=arguments[o];return n=i,t||(t=Promise.resolve().then(()=>(t=null,e(...n)))),t}}var l=$("updateUI");class p extends h{constructor(){super(...arguments),Object.defineProperty(this,l,{writable:!0,value:void 0})}getTargetPlugin(t){let n;if(typeof(t==null?void 0:t.addTarget)=="function")n=t,n instanceof p||console.warn(new Error("The provided plugin is not an instance of UIPlugin. This is an indication of a bug with the way Uppy is bundled.",{cause:{targetPlugin:n,UIPlugin:p}}));else if(typeof t=="function"){const a=t;this.uppy.iteratePlugins(i=>{i instanceof a&&(n=i)})}return n}mount(t,n){const a=n.id,i=M(t);if(i){this.isTargetDOMEl=!0;const r=document.createElement("div");return r.classList.add("uppy-Root"),m(this,l)[l]=I(f=>{this.uppy.getPlugin(this.id)&&(d(this.render(f),r),this.afterUpdate())}),this.uppy.log(`Installing ${a} to a DOM element '${t}'`),this.opts.replaceTargetContent&&(i.innerHTML=""),d(this.render(this.uppy.getState()),r),this.el=r,i.appendChild(r),r.dir=this.opts.direction||P(r)||"ltr",this.onMount(),this.el}const o=this.getTargetPlugin(t);if(o)return this.uppy.log(`Installing ${a} to ${o.id}`),this.parent=o,this.el=o.addTarget(n),this.onMount(),this.el;this.uppy.log(`Not installing ${a}`);let s=`Invalid target option given to ${a}.`;throw typeof t=="function"?s+=" The given target is not a Plugin class. Please check that you're not specifying a React Component instead of a plugin. If you are using @uppy/* packages directly, make sure you have only 1 version of @uppy/core installed: run `npm ls @uppy/core` on the command line and verify that all the versions match and are deduped correctly.":s+="If you meant to target an HTML element, please make sure that the element exists. Check that the <script> tag initializing Uppy is right before the closing </body> tag at the end of the page. (see https://github.com/transloadit/uppy/issues/1042)\n\nIf you meant to target a plugin, please confirm that your `import` statements or `require` calls are correct.",new Error(s)}render(t){throw new Error("Extend the render method to add your plugin to a DOM element")}update(t){if(this.el!=null){var n,a;(n=(a=m(this,l))[l])==null||n.call(a,t)}}unmount(){if(this.isTargetDOMEl){var t;(t=this.el)==null||t.remove()}this.onUnmount()}onMount(){}onUnmount(){}}const U=p;export{U,C as a,P as b,M as f,x as g,T as i,c as m,z as p};
