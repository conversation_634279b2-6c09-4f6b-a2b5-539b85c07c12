import ConfirmModal from "Components/Modal/ConfirmModal";
import DuoConfirmModal from "Components/Modal/DuoConfirmModal";
import React, { useEffect, useState } from "react";
import { useLocation, useNavigate, useParams } from "react-router";
import { AuthContext } from "Src/authContext";
import { GlobalContext, showToast } from "Src/globalContext";
import {
  UpdateEditTypeAPI,
  deleteEditTypeAPI,
  viewEditTypesDetails,
} from "Src/services/editService";

const ViewMemberEditTypePage = () => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [notes, setNotes] = useState("");

  const [viewModelEdit, setViewModelEdit] = React.useState({});

  const params = useParams();

  const [showRealDeleteEditModal, setShowRealDeleteEditModal] =
    React.useState(false);
  const [showDeleteEditModal, setShowDeleteEditModal] = React.useState(false);
  const location = useLocation();

  useEffect(() => {
    document
      .getElementById("mainContainer")
      .scrollTo({ top: 0, behavior: "smooth" });

    window.scrollTo({ top: 0 });
  }, [location.pathname]);

  const handleRealDeleteEditModalClose = () => {
    setShowRealDeleteEditModal(false);
  };

  const openSecondDelete = () => {
    setShowRealDeleteEditModal(false);

    setTimeout(() => {
      setShowDeleteEditModal(true);
    }, 500);
  };

  const navigate = useNavigate();

  const handleDeleteEdit = async (id) => {
    console.log(id);
    // console.log(video);
    await deleteEditTypeAPI(id);
    setShowDeleteEditModal(false);
    navigate("/member/edits");
    // await getData();
    showToast(globalDispatch, `Special Edit Type Deleted`, 5000);
  };

  const getEditDetails = async () => {
    const res = await viewEditTypesDetails(params.id);

    setNotes(res.model.note_keys);

    setViewModelEdit(res.model);
  };

  useEffect(() => {
    getEditDetails();
  }, []);

  console.log(viewModelEdit);

  const updateNote = async () => {
    try {
      const res = await UpdateEditTypeAPI({ note_keys: notes }, params?.id);

      showToast(globalDispatch, "Special Edits Updated", 5000, "success");
    } catch (error) {
      showToast(globalDispatch, "Special Edits update failed", 5000, "error");
      throw error;
    }
  };

  return (
    <>
      {" "}
      {showRealDeleteEditModal ? (
        <ConfirmModal
          confirmText={`Are you sure you want to delete this special edit?`}
          setModalClose={handleRealDeleteEditModalClose}
          setFormYes={openSecondDelete}
        />
      ) : null}
      {showDeleteEditModal ? (
        <DuoConfirmModal
          confirmText={`Are you sure you want to delete this special edit? This action cannot be undone.`}
          setModalClose={setShowDeleteEditModal}
          setFormYes={() => {
            handleDeleteEdit(params?.id);
          }}
        />
      ) : null}
      <div
        id="mainContainer"
        className="flex min-h-screen w-full flex-col rounded-md bg-gray-800 p-5 text-white"
      >
        <div className="flex items-center gap-1">
          <h6 className="whitespace-normal text-xl font-normal">
            Special Edit Type:
          </h6>{" "}
          <h6 className="whitespace-normal text-xl font-normal">
            {viewModelEdit?.edit_type}
          </h6>
        </div>

        <div className="mt-14 flex justify-between gap-4">
          <h6 className="basis-[20%] text-base font-normal">
            Special Edits Note:
          </h6>{" "}
          <div className=" flex w-[70%] flex-col ">
            <textarea
              className="h-[150px]  bg-white text-black"
              name=""
              id=""
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
            ></textarea>
            <button
              onClick={updateNote}
              className=" mt-5 flex w-[80px] items-center justify-center gap-1 self-end rounded-md border border-black/70 bg-blue-800 py-[5px]"
            >
              <span className="  text-[10px] font-medium">Update</span>
            </button>
          </div>
        </div>

        <div className="mt-8 flex w-full items-center justify-center">
          <button
            onClick={() => setShowRealDeleteEditModal(true)}
            className=" mt-10 flex w-[150px] items-center justify-center gap-1 rounded-md border border-black/70 bg-primary py-[12px]"
          >
            <span className="  text-[14px] font-medium">Delete Edit</span>
          </button>
        </div>
      </div>
    </>
  );
};

export default ViewMemberEditTypePage;
