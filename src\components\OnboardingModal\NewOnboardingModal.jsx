import React, { useState, useEffect, useMemo, useCallback } from "react";
import { GlobalContext, showToast } from "../../globalContext";
import { ClipLoader } from "react-spinners";
import TermsConditionsStep from "./OnboardingSteps/TermsConditionsStep";
import BusinessInfoStep from "./OnboardingSteps/BusinessInfoStep";
import ClientServiceAgreementStep from "./OnboardingSteps/ClientServiceAgreementStep";
import EditPolicyStep from "./OnboardingSteps/EditPolicyStep";
import PaymentBillingStep from "./OnboardingSteps/PaymentBillingStep";
import ProjectManagementStep from "./OnboardingSteps/ProjectManagementStep";
import ProjectTimelinesStep from "./OnboardingSteps/ProjectTimelinesStep";
import FullContactDetailsStep from "./OnboardingSteps/FullContactDetailsStep";
import SubscriptionStep from "./OnboardingSteps/SubscriptionStep";
import ExploreCheerEQStep from "./OnboardingSteps/ExploreCheerEQStep";
import CompletionOfMandatoryStep from "./OnboardingSteps/figma-ui/CompletionOfMandatoryStep";
import FinalCompletionStep from "./OnboardingSteps/figma-ui/FinalCompletionStep";
import {
  getUserDetailsByIdAPI,
  updateUserDetailsAPI,
} from "../../services/userService";
import { useNavigate } from "react-router-dom";
import { AuthContext } from "../../authContext";

const NewOnboardingModal = ({
  userDetails: initialUserDetails,
  onComplete,
  onMinimize,
  onStepDataUpdate = null, // Optional callback for step data updates
  isMinimized = false,
}) => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { dispatch: authDispatch } = React.useContext(AuthContext);
  const [loadFresh, setLoadFresh] = useState(true);
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [stepData, setStepData] = useState({});
  const [canMinimize, setCanMinimize] = useState(false);
  const [userDetails, setUserDetails] = useState(initialUserDetails);

  // Figma-based steps (pixel-perfect, no account activation)
  const visualPreSteps = [
    {
      id: "pre-1",
      title: "Create Your Account",
      icon: (
        <svg
          width="36"
          height="36"
          viewBox="0 0 36 36"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect x="2" y="2" width="32" height="32" rx="16" fill="#E0E6FC" />
          <rect
            x="2"
            y="2"
            width="32"
            height="32"
            rx="16"
            stroke="#F5F7FF"
            stroke-width="4"
          />
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M23.014 23.1761C24.4291 21.8426 25.3125 19.9511 25.3125 17.8533C25.3125 13.8147 22.0386 10.5408 18 10.5408C13.9614 10.5408 10.6875 13.8147 10.6875 17.8533C10.6875 19.9511 11.5709 21.8426 12.986 23.1761C14.2951 24.4097 16.0593 25.1658 18 25.1658C19.9407 25.1658 21.7049 24.4097 23.014 23.1761ZM13.6088 22.2125C14.6397 20.9266 16.2237 20.1033 18 20.1033C19.7763 20.1033 21.3603 20.9266 22.3912 22.2125C21.2702 23.3416 19.7168 24.0408 18 24.0408C16.2832 24.0408 14.7298 23.3416 13.6088 22.2125ZM20.8125 15.6033C20.8125 17.1566 19.5533 18.4158 18 18.4158C16.4467 18.4158 15.1875 17.1566 15.1875 15.6033C15.1875 14.05 16.4467 12.7908 18 12.7908C19.5533 12.7908 20.8125 14.05 20.8125 15.6033Z"
            fill="#3C50E0"
          />
        </svg>
      ),
      completed: true,
    },
    {
      id: "pre-2",
      title: "Verify Your Email",
      icon: (
        <div className="flex h-7 w-7 flex-shrink-0 items-center justify-center rounded-full bg-[#E0E6FC]">
          <svg
            className="h-[18px] w-[18px] text-[#3C50E0]"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
            />
          </svg>
        </div>
      ),
      completed: true,
    },
  ];

  // Mandatory steps (Figma order, skip account activation)
  const mandatorySteps = useMemo(
    () => [
      {
        id: 1,
        title: "Choose Your Plan",
        subtitle: "Select your subscription",
        icon: (
          <svg
            width="18"
            height="19"
            viewBox="0 0 18 19"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M1.6875 7.04077H16.3125M1.6875 7.60327H16.3125M3.9375 11.5408H8.4375M3.9375 13.2283H6.1875M3.375 15.4783H14.625C15.557 15.4783 16.3125 14.7228 16.3125 13.7908V5.91577C16.3125 4.98379 15.557 4.22827 14.625 4.22827H3.375C2.44302 4.22827 1.6875 4.98379 1.6875 5.91577V13.7908C1.6875 14.7228 2.44302 15.4783 3.375 15.4783Z"
              stroke="#3C50E0"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        ),
        component: SubscriptionStep,
        required: true,
        key: "plan_chosen",
      },
      {
        id: 2,
        title: "Legal Agreements",
        subtitle: "Review our terms & policies.",
        icon: (
          <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g clip-path="url(#clip0_3181_5114)">
              <path
                d="M6 7C5.86739 7 5.74021 7.05268 5.64645 7.14645C5.55268 7.24021 5.5 7.36739 5.5 7.5C5.5 7.63261 5.55268 7.75979 5.64645 7.85355C5.74021 7.94732 5.86739 8 6 8H11C11.1326 8 11.2598 7.94732 11.3536 7.85355C11.4473 7.75979 11.5 7.63261 11.5 7.5C11.5 7.36739 11.4473 7.24021 11.3536 7.14645C11.2598 7.05268 11.1326 7 11 7H6ZM5.5 9.5C5.5 9.36739 5.55268 9.24021 5.64645 9.14645C5.74021 9.05268 5.86739 9 6 9H11C11.1326 9 11.2598 9.05268 11.3536 9.14645C11.4473 9.24021 11.5 9.36739 11.5 9.5C11.5 9.63261 11.4473 9.75979 11.3536 9.85355C11.2598 9.94732 11.1326 10 11 10H6C5.86739 10 5.74021 9.94732 5.64645 9.85355C5.55268 9.75979 5.5 9.63261 5.5 9.5ZM5.5 11.5C5.5 11.3674 5.55268 11.2402 5.64645 11.1464C5.74021 11.0527 5.86739 11 6 11H8C8.13261 11 8.25979 11.0527 8.35355 11.1464C8.44732 11.2402 8.5 11.3674 8.5 11.5C8.5 11.6326 8.44732 11.7598 8.35355 11.8536C8.25979 11.9473 8.13261 12 8 12H6C5.86739 12 5.74021 11.9473 5.64645 11.8536C5.55268 11.7598 5.5 11.6326 5.5 11.5Z"
                fill="#3C50E0"
              />
              <path
                d="M10 0H4.5C3.96957 0 3.46086 0.210714 3.08579 0.585786C2.71071 0.960859 2.5 1.46957 2.5 2V14C2.5 14.5304 2.71071 15.0391 3.08579 15.4142C3.46086 15.7893 3.96957 16 4.5 16H12.5C13.0304 16 13.5391 15.7893 13.9142 15.4142C14.2893 15.0391 14.5 14.5304 14.5 14V4.5L10 0ZM10 1V3C10 3.39782 10.158 3.77936 10.4393 4.06066C10.7206 4.34196 11.1022 4.5 11.5 4.5H13.5V14C13.5 14.2652 13.3946 14.5196 13.2071 14.7071C13.0196 14.8946 12.7652 15 12.5 15H4.5C4.23478 15 3.98043 14.8946 3.79289 14.7071C3.60536 14.5196 3.5 14.2652 3.5 14V2C3.5 1.73478 3.60536 1.48043 3.79289 1.29289C3.98043 1.10536 4.23478 1 4.5 1H10Z"
                fill="#3C50E0"
              />
            </g>
            <defs>
              <clipPath id="clip0_3181_5114">
                <rect width="16" height="16" fill="white" />
              </clipPath>
            </defs>
          </svg>
        ),
        component: TermsConditionsStep,
        required: true,
        key: "legal_agreements_complete",
      },
      {
        id: 3,
        title: "Explore CheerEQ Features",
        subtitle: "Discover powerful tools.",
        icon: (
          <svg
            width="36"
            height="36"
            viewBox="0 0 36 36"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <rect x="2" y="2" width="32" height="32" rx="16" fill="#E0E6FC" />
            <rect
              x="2"
              y="2"
              width="32"
              height="32"
              rx="16"
              stroke="#F6F7F8"
              stroke-width="4"
            />
            <path
              d="M16.3599 20.7811L15.75 22.9158L15.1401 20.7811C14.8198 19.6599 13.9433 18.7835 12.8221 18.4632L10.6875 17.8533L12.8221 17.2434C13.9433 16.923 14.8198 16.0466 15.1401 14.9254L15.75 12.7908L16.3599 14.9254C16.6802 16.0466 17.5567 16.923 18.6779 17.2434L20.8125 17.8533L18.6779 18.4632C17.5567 18.7835 16.6802 19.6599 16.3599 20.7811Z"
              stroke="#3C50E0"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M22.6941 15.3892L22.5 16.1658L22.3059 15.3892C22.0791 14.4823 21.371 13.7741 20.4641 13.5474L19.6875 13.3533L20.4641 13.1591C21.371 12.9324 22.0791 12.2243 22.3059 11.3174L22.5 10.5408L22.6941 11.3174C22.9209 12.2243 23.629 12.9324 24.5359 13.1591L25.3125 13.3533L24.5359 13.5474C23.629 13.7741 22.9209 14.4823 22.6941 15.3892Z"
              stroke="#3C50E0"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M21.6707 24.2787L21.375 25.1658L21.0793 24.2787C20.9114 23.7748 20.5159 23.3794 20.012 23.2115L19.125 22.9158L20.012 22.6201C20.5159 22.4521 20.9114 22.0567 21.0793 21.5528L21.375 20.6658L21.6707 21.5528C21.8386 22.0567 22.2341 22.4521 22.738 22.6201L23.625 22.9158L22.738 23.2115C22.2341 23.3794 21.8386 23.7748 21.6707 24.2787Z"
              stroke="#3C50E0"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        ),
        component: ExploreCheerEQStep,
        required: true,
        key: "feature_tour_complete",
      },
      {
        id: 4,
        title: "Your Business Identity",
        subtitle: "Personalize your account.",
        icon: (
          <svg
            width="18"
            height="19"
            viewBox="0 0 18 19"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M1.6875 16.6033H16.3125M2.8125 3.10327V16.6033M10.6875 3.10327V16.6033M15.1875 6.47827V16.6033M5.0625 5.91577H5.625M5.0625 8.16577H5.625M5.0625 10.4158H5.625M7.875 5.91577H8.4375M7.875 8.16577H8.4375M7.875 10.4158H8.4375M5.0625 16.6033V14.072C5.0625 13.606 5.44026 13.2283 5.90625 13.2283H7.59375C8.05974 13.2283 8.4375 13.606 8.4375 14.072V16.6033M2.25 3.10327H11.25M10.6875 6.47827H15.75M12.9375 9.29077H12.9431V9.2964H12.9375V9.29077ZM12.9375 11.5408H12.9431V11.5464H12.9375V11.5408ZM12.9375 13.7908H12.9431V13.7964H12.9375V13.7908Z"
              stroke="#3C50E0"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        ),
        component: BusinessInfoStep,
        required: true,
        key: "business_identity_complete",
      },
    ],
    []
  );

  const optionalSteps = useMemo(
    () => [
      {
        id: 5,
        title: "Secure Your Financials",
        subtitle:
          "Integrate Stripe for direct payments and streamlined billing.",
        icon: (
          <svg
            width="19"
            height="19"
            viewBox="0 0 19 19"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M2.1875 6.65332H16.8125M2.1875 7.21582H16.8125M4.4375 11.1533H8.9375M4.4375 12.8408H6.6875M3.875 15.0908H15.125C16.057 15.0908 16.8125 14.3353 16.8125 13.4033V5.52832C16.8125 4.59634 16.057 3.84082 15.125 3.84082H3.875C2.94302 3.84082 2.1875 4.59634 2.1875 5.52832V13.4033C2.1875 14.3353 2.94302 15.0908 3.875 15.0908Z"
              stroke="#3C50E0"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        ),
        component: PaymentBillingStep,
        required: false,
        key: "payment_billing_complete",
      },
      {
        id: 6,
        title: "Client Service Agreement",
        subtitle: "Add your client agreement",
        icon: (
          <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M6 7C5.86739 7 5.74021 7.05268 5.64645 7.14645C5.55268 7.24021 5.5 7.36739 5.5 7.5C5.5 7.63261 5.55268 7.75979 5.64645 7.85355C5.74021 7.94732 5.86739 8 6 8H11C11.1326 8 11.2598 7.94732 11.3536 7.85355C11.4473 7.75979 11.5 7.63261 11.5 7.5C11.5 7.36739 11.4473 7.24021 11.3536 7.14645C11.2598 7.05268 11.1326 7 11 7H6ZM5.5 9.5C5.5 9.36739 5.55268 9.24021 5.64645 9.14645C5.74021 9.05268 5.86739 9 6 9H11C11.1326 9 11.2598 9.05268 11.3536 9.14645C11.4473 9.24021 11.5 9.36739 11.5 9.5C11.5 9.63261 11.4473 9.75979 11.3536 9.85355C11.2598 9.94732 11.1326 10 11 10H6C5.86739 10 5.74021 9.94732 5.64645 9.85355C5.55268 9.75979 5.5 9.63261 5.5 9.5ZM5.5 11.5C5.5 11.3674 5.55268 11.2402 5.64645 11.1464C5.74021 11.0527 5.86739 11 6 11H8C8.13261 11 8.25979 11.0527 8.35355 11.1464C8.44732 11.2402 8.5 11.3674 8.5 11.5C8.5 11.6326 8.44732 11.7598 8.35355 11.8536C8.25979 11.9473 8.13261 12 8 12H6C5.86739 12 5.74021 11.9473 5.64645 11.8536C5.55268 11.7598 5.5 11.6326 5.5 11.5Z"
              fill="#3C50E0"
            />
            <path
              d="M10 0H4.5C3.96957 0 3.46086 0.210714 3.08579 0.585786C2.71071 0.960859 2.5 1.46957 2.5 2V14C2.5 14.5304 2.71071 15.0391 3.08579 15.4142C3.46086 15.7893 3.96957 16 4.5 16H12.5C13.0304 16 13.5391 15.7893 13.9142 15.4142C14.2893 15.0391 14.5 14.5304 14.5 14V4.5L10 0ZM10 1V3C10 3.39782 10.158 3.77936 10.4393 4.06066C10.7206 4.34196 11.1022 4.5 11.5 4.5H13.5V14C13.5 14.2652 13.3946 14.5196 13.2071 14.7071C13.0196 14.8946 12.7652 15 12.5 15H4.5C4.23478 15 3.98043 14.8946 3.79289 14.7071C3.60536 14.5196 3.5 14.2652 3.5 14V2C3.5 1.73478 3.60536 1.48043 3.79289 1.29289C3.98043 1.10536 4.23478 1 4.5 1H10Z"
              fill="#3C50E0"
            />
          </svg>
        ),
        component: ClientServiceAgreementStep,
        required: false,
        key: "client_service_agreement_complete",
      },
      {
        id: 7,
        title: "Edit Policy Upload",
        subtitle: "Upload your PDF policy for music revisions.",
        icon: (
          <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M9.5 0C9.63261 0 9.75979 0.0526784 9.85355 0.146447C9.94732 0.240215 10 0.367392 10 0.5C10 0.632608 10.0527 0.759785 10.1464 0.853553C10.2402 0.947322 10.3674 1 10.5 1C10.6326 1 10.7598 1.05268 10.8536 1.14645C10.9473 1.24021 11 1.36739 11 1.5V2C11 2.13261 10.9473 2.25979 10.8536 2.35355C10.7598 2.44732 10.6326 2.5 10.5 2.5H5.5C5.36739 2.5 5.24021 2.44732 5.14645 2.35355C5.05268 2.25979 5 2.13261 5 2V1.5C5 1.36739 5.05268 1.24021 5.14645 1.14645C5.24021 1.05268 5.36739 1 5.5 1C5.63261 1 5.75979 0.947322 5.85355 0.853553C5.94732 0.759785 6 0.632608 6 0.5C6 0.367392 6.05268 0.240215 6.14645 0.146447C6.24021 0.0526784 6.36739 0 6.5 0H9.5Z"
              fill="#3C50E0"
            />
            <path
              d="M3 2.5C3 2.36739 3.05268 2.24021 3.14645 2.14645C3.24021 2.05268 3.36739 2 3.5 2H4C4.13261 2 4.25979 1.94732 4.35355 1.85355C4.44732 1.75979 4.5 1.63261 4.5 1.5C4.5 1.36739 4.44732 1.24021 4.35355 1.14645C4.25979 1.05268 4.13261 1 4 1H3.5C3.10218 1 2.72064 1.15804 2.43934 1.43934C2.15804 1.72064 2 2.10218 2 2.5V14.5C2 14.8978 2.15804 15.2794 2.43934 15.5607C2.72064 15.842 3.10218 16 3.5 16H12.5C12.8978 16 13.2794 15.842 13.5607 15.5607C13.842 15.2794 14 14.8978 14 14.5V2.5C14 2.10218 13.842 1.72064 13.5607 1.43934C13.2794 1.15804 12.8978 1 12.5 1H12C11.8674 1 11.7402 1.05268 11.6464 1.14645C11.5527 1.24021 11.5 1.36739 11.5 1.5C11.5 1.63261 11.5527 1.75979 11.6464 1.85355C11.7402 1.94732 11.8674 2 12 2H12.5C12.6326 2 12.7598 2.05268 12.8536 2.14645C12.9473 2.24021 13 2.36739 13 2.5V14.5C13 14.6326 12.9473 14.7598 12.8536 14.8536C12.7598 14.9473 12.6326 15 12.5 15H3.5C3.36739 15 3.24021 14.9473 3.14645 14.8536C3.05268 14.7598 3 14.6326 3 14.5V2.5Z"
              fill="#3C50E0"
            />
            <path
              d="M11.0006 7.50014C11.0006 7.63292 10.9479 7.76026 10.854 7.85414L7.85401 10.8541C7.80756 10.9007 7.75239 10.9376 7.69164 10.9629C7.63089 10.9881 7.56577 11.001 7.50001 11.001C7.43424 11.001 7.36912 10.9881 7.30837 10.9629C7.24763 10.9376 7.19245 10.9007 7.14601 10.8541L5.64601 9.35414C5.55212 9.26026 5.49937 9.13292 5.49937 9.00014C5.49937 8.86737 5.55212 8.74003 5.64601 8.64614C5.73989 8.55226 5.86723 8.49951 6.00001 8.49951C6.13278 8.49951 6.26012 8.55226 6.35401 8.64614L7.50001 9.79314L10.146 7.14614C10.2399 7.05226 10.3672 6.99951 10.5 6.99951C10.6328 6.99951 10.7601 7.05226 10.854 7.14614C10.9479 7.24003 11.0006 7.36737 11.0006 7.50014Z"
              fill="#3C50E0"
            />
          </svg>
        ),
        component: EditPolicyStep,
        required: false,
        key: "edit_policy_complete",
      },
      {
        id: 8,
        title: "Project Management Settings",
        subtitle: "Define default project workflows and vocal order deadlines.",
        icon: (
          <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M9.5 0C9.63261 0 9.75979 0.0526784 9.85355 0.146447C9.94732 0.240215 10 0.367392 10 0.5C10 0.632608 10.0527 0.759785 10.1464 0.853553C10.2402 0.947322 10.3674 1 10.5 1C10.6326 1 10.7598 1.05268 10.8536 1.14645C10.9473 1.24021 11 1.36739 11 1.5V2C11 2.13261 10.9473 2.25979 10.8536 2.35355C10.7598 2.44732 10.6326 2.5 10.5 2.5H5.5C5.36739 2.5 5.24021 2.44732 5.14645 2.35355C5.05268 2.25979 5 2.13261 5 2V1.5C5 1.36739 5.05268 1.24021 5.14645 1.14645C5.24021 1.05268 5.36739 1 5.5 1C5.63261 1 5.75979 0.947322 5.85355 0.853553C5.94732 0.759785 6 0.632608 6 0.5C6 0.367392 6.05268 0.240215 6.14645 0.146447C6.24021 0.0526784 6.36739 0 6.5 0H9.5Z"
              fill="#3C50E0"
            />
            <path
              d="M3 2.5C3 2.36739 3.05268 2.24021 3.14645 2.14645C3.24021 2.05268 3.36739 2 3.5 2H4C4.13261 2 4.25979 1.94732 4.35355 1.85355C4.44732 1.75979 4.5 1.63261 4.5 1.5C4.5 1.36739 4.44732 1.24021 4.35355 1.14645C4.25979 1.05268 4.13261 1 4 1H3.5C3.10218 1 2.72064 1.15804 2.43934 1.43934C2.15804 1.72064 2 2.10218 2 2.5V14.5C2 14.8978 2.15804 15.2794 2.43934 15.5607C2.72064 15.842 3.10218 16 3.5 16H12.5C12.8978 16 13.2794 15.842 13.5607 15.5607C13.842 15.2794 14 14.8978 14 14.5V2.5C14 2.10218 13.842 1.72064 13.5607 1.43934C13.2794 1.15804 12.8978 1 12.5 1H12C11.8674 1 11.7402 1.05268 11.6464 1.14645C11.5527 1.24021 11.5 1.36739 11.5 1.5C11.5 1.63261 11.5527 1.75979 11.6464 1.85355C11.7402 1.94732 11.8674 2 12 2H12.5C12.6326 2 12.7598 2.05268 12.8536 2.14645C12.9473 2.24021 13 2.36739 13 2.5V14.5C13 14.6326 12.9473 14.7598 12.8536 14.8536C12.7598 14.9473 12.6326 15 12.5 15H3.5C3.36739 15 3.24021 14.9473 3.14645 14.8536C3.05268 14.7598 3 14.6326 3 14.5V2.5Z"
              fill="#3C50E0"
            />
            <path
              d="M11.0006 7.50014C11.0006 7.63292 10.9479 7.76026 10.854 7.85414L7.85401 10.8541C7.80756 10.9007 7.75239 10.9376 7.69164 10.9629C7.63089 10.9881 7.56577 11.001 7.50001 11.001C7.43424 11.001 7.36912 10.9881 7.30837 10.9629C7.24763 10.9376 7.19245 10.9007 7.14601 10.8541L5.64601 9.35414C5.55212 9.26026 5.49937 9.13292 5.49937 9.00014C5.49937 8.86737 5.55212 8.74003 5.64601 8.64614C5.73989 8.55226 5.86723 8.49951 6.00001 8.49951C6.13278 8.49951 6.26012 8.55226 6.35401 8.64614L7.50001 9.79314L10.146 7.14614C10.2399 7.05226 10.3672 6.99951 10.5 6.99951C10.6328 6.99951 10.7601 7.05226 10.854 7.14614C10.9479 7.24003 11.0006 7.36737 11.0006 7.50014Z"
              fill="#3C50E0"
            />
          </svg>
        ),
        component: ProjectManagementStep,
        required: false,
        key: "project_management_complete",
      },
      {
        id: 9,
        title: "Project Timelines & Defaults",
        subtitle: "Set default project schedules & financial terms.",
        icon: (
          <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g clip-path="url(#clip0_3162_4569)">
              <path
                d="M3.5 0C3.63261 0 3.75979 0.0526784 3.85355 0.146447C3.94732 0.240215 4 0.367392 4 0.5V1H12V0.5C12 0.367392 12.0527 0.240215 12.1464 0.146447C12.2402 0.0526784 12.3674 0 12.5 0C12.6326 0 12.7598 0.0526784 12.8536 0.146447C12.9473 0.240215 13 0.367392 13 0.5V1H14C14.5304 1 15.0391 1.21071 15.4142 1.58579C15.7893 1.96086 16 2.46957 16 3V14C16 14.5304 15.7893 15.0391 15.4142 15.4142C15.0391 15.7893 14.5304 16 14 16H2C1.46957 16 0.960859 15.7893 0.585786 15.4142C0.210714 15.0391 0 14.5304 0 14V3C0 2.46957 0.210714 1.96086 0.585786 1.58579C0.960859 1.21071 1.46957 1 2 1H3V0.5C3 0.367392 3.05268 0.240215 3.14645 0.146447C3.24021 0.0526784 3.36739 0 3.5 0ZM2 2C1.73478 2 1.48043 2.10536 1.29289 2.29289C1.10536 2.48043 1 2.73478 1 3V14C1 14.2652 1.10536 14.5196 1.29289 14.7071C1.48043 14.8946 1.73478 15 2 15H14C14.2652 15 14.5196 14.8946 14.7071 14.7071C14.8946 14.5196 15 14.2652 15 14V3C15 2.73478 14.8946 2.48043 14.7071 2.29289C14.5196 2.10536 14.2652 2 14 2H2Z"
                fill="#3C50E0"
              />
              <path
                d="M2.5 4C2.5 3.86739 2.55268 3.74021 2.64645 3.64645C2.74021 3.55268 2.86739 3.5 3 3.5H13C13.1326 3.5 13.2598 3.55268 13.3536 3.64645C13.4473 3.74021 13.5 3.86739 13.5 4V5C13.5 5.13261 13.4473 5.25979 13.3536 5.35355C13.2598 5.44732 13.1326 5.5 13 5.5H3C2.86739 5.5 2.74021 5.44732 2.64645 5.35355C2.55268 5.25979 2.5 5.13261 2.5 5V4ZM9 8C9 7.73478 9.10536 7.48043 9.29289 7.29289C9.48043 7.10536 9.73478 7 10 7H15V9H10C9.73478 9 9.48043 8.89464 9.29289 8.70711C9.10536 8.51957 9 8.26522 9 8ZM1 10H5C5.26522 10 5.51957 10.1054 5.70711 10.2929C5.89464 10.4804 6 10.7348 6 11C6 11.2652 5.89464 11.5196 5.70711 11.7071C5.51957 11.8946 5.26522 12 5 12H1V10Z"
                fill="#3C50E0"
              />
            </g>
            <defs>
              <clipPath id="clip0_3162_4569">
                <rect width="16" height="16" fill="white" />
              </clipPath>
            </defs>
          </svg>
        ),
        component: ProjectTimelinesStep,
        required: false,
        key: "project_timelines_complete",
      },
      {
        id: 10,
        title: "Full Contact Details",
        subtitle: "Complete your business address and contact info.",
        icon: (
          <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g clip-path="url(#clip0_3162_4578)">
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M3.1 11.2001C3.14657 11.138 3.20697 11.0876 3.27639 11.0529C3.34582 11.0182 3.42238 11.0001 3.5 11.0001H6C6.13261 11.0001 6.25979 11.0528 6.35355 11.1466C6.44732 11.2403 6.5 11.3675 6.5 11.5001C6.5 11.6327 6.44732 11.7599 6.35355 11.8537C6.25979 11.9474 6.13261 12.0001 6 12.0001H3.75L1.5 15.0001H14.5L12.25 12.0001H10C9.86739 12.0001 9.74021 11.9474 9.64645 11.8537C9.55268 11.7599 9.5 11.6327 9.5 11.5001C9.5 11.3675 9.55268 11.2403 9.64645 11.1466C9.74021 11.0528 9.86739 11.0001 10 11.0001H12.5C12.5776 11.0001 12.6542 11.0182 12.7236 11.0529C12.793 11.0876 12.8534 11.138 12.9 11.2001L15.9 15.2001C15.9557 15.2744 15.9896 15.3627 15.998 15.4552C16.0063 15.5477 15.9887 15.6407 15.9472 15.7237C15.9057 15.8068 15.8419 15.8766 15.7629 15.9254C15.6839 15.9743 15.5929 16.0001 15.5 16.0001H0.5C0.407144 16.0001 0.316123 15.9743 0.237135 15.9254C0.158147 15.8766 0.0943131 15.8068 0.0527866 15.7237C0.0112602 15.6407 -0.00631841 15.5477 0.00202058 15.4552C0.0103596 15.3627 0.0442867 15.2744 0.1 15.2001L3.1 11.2001Z"
                fill="#3C50E0"
              />
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M8 1.00012C7.60603 1.00012 7.21593 1.07771 6.85195 1.22848C6.48797 1.37924 6.15726 1.60022 5.87868 1.8788C5.6001 2.15737 5.37913 2.48809 5.22836 2.85207C5.0776 3.21604 5 3.60615 5 4.00012C5 4.39408 5.0776 4.78419 5.22836 5.14817C5.37913 5.51215 5.6001 5.84286 5.87868 6.12144C6.15726 6.40001 6.48797 6.62099 6.85195 6.77176C7.21593 6.92252 7.60603 7.00012 8 7.00012C8.79565 7.00012 9.55871 6.68405 10.1213 6.12144C10.6839 5.55883 11 4.79577 11 4.00012C11 3.20447 10.6839 2.44141 10.1213 1.8788C9.55871 1.31619 8.79565 1.00012 8 1.00012ZM4 4.00012C4.00007 3.23034 4.22226 2.47694 4.63989 1.83031C5.05753 1.18368 5.65288 0.671289 6.3545 0.354624C7.05613 0.0379591 7.83422 -0.0695301 8.59542 0.0450543C9.35662 0.159639 10.0686 0.491429 10.6459 1.00061C11.2232 1.5098 11.6413 2.17475 11.8501 2.91567C12.0589 3.6566 12.0494 4.44202 11.8228 5.1777C11.5963 5.91339 11.1623 6.56807 10.5729 7.06321C9.98349 7.55835 9.26374 7.8729 8.5 7.96912V13.5001C8.5 13.6327 8.44732 13.7599 8.35355 13.8537C8.25979 13.9474 8.13261 14.0001 8 14.0001C7.86739 14.0001 7.74021 13.9474 7.64645 13.8537C7.55268 13.7599 7.5 13.6327 7.5 13.5001V7.97012C6.53296 7.84828 5.64369 7.37755 4.99921 6.64635C4.35474 5.91515 3.99942 4.9748 4 4.00012Z"
                fill="#3C50E0"
              />
            </g>
            <defs>
              <clipPath id="clip0_3162_4578">
                <rect width="16" height="16" fill="white" />
              </clipPath>
            </defs>
          </svg>
        ),
        component: FullContactDetailsStep,
        required: false,
        key: "contact_details_complete",
      },
    ],
    []
  );

  // Helper to determine if all mandatory steps are complete
  const allMandatoryComplete = React.useCallback(
    (stepData) => mandatorySteps.every((step) => stepData[step.key]),
    [mandatorySteps]
  );

  // Helper to determine if all optional steps are complete
  const allOptionalComplete = React.useCallback(
    (stepData) => optionalSteps.every((step) => stepData[step.key]),
    [optionalSteps]
  );

  // Helper to get completed steps count (for progress bar)
  const getCompletedStepsCount = () => {
    // Count completed mandatory steps
    const completedMandatory = mandatorySteps.filter(
      (step) => stepData[step.key]
    ).length;

    if (allMandatoryComplete(stepData)) {
      // When all mandatory complete, check if user has started optional steps
      const completedOptional = optionalSteps.filter(
        (step) => stepData[step.key]
      ).length;

      if (completedOptional > 0) {
        // User has started optional steps - count all completed steps
        return completedMandatory + completedOptional;
      } else {
        // User hasn't started optional steps - show mandatory completion (2 pre-steps + 4 mandatory = 6)
        return 2 + completedMandatory;
      }
    } else {
      // When in mandatory phase, count pre-steps (always 2) + completed mandatory steps
      return 2 + completedMandatory; // 2 pre-steps + mandatory steps
    }
  };

  // Helper to get progress percent (for progress bar)
  const getProgressPercent = () => {
    if (allMandatoryComplete(stepData)) {
      // When all mandatory complete, check if user has moved to optional steps
      if (stepData.mandatory_completion_screen_viewed) {
        // User has moved to optional steps - calculate based only on optional steps
        const completedOptional = optionalSteps.filter(
          (step) => stepData[step.key]
        ).length;
        const totalOptionalSteps = optionalSteps.length;
        return Math.round((completedOptional / totalOptionalSteps) * 100);
      } else {
        // User hasn't moved to optional steps yet - show 100% for mandatory completion
        return 100;
      }
    } else {
      // When in mandatory phase, total = 2 pre-steps + 4 mandatory = 6
      const totalMandatorySteps = 2 + mandatorySteps.length; // 2 pre-steps + 4 mandatory
      return Math.round((getCompletedStepsCount() / totalMandatorySteps) * 100);
    }
  };

  const steps = useMemo(
    () => [...mandatorySteps, ...optionalSteps],
    [mandatorySteps, optionalSteps]
  );

  // Function to determine if user can minimize modal
  const canUserMinimize = useCallback(
    (userDetails, stepData) => {
      // Check if we're in the optional phase
      const isOptionalPhase =
        allMandatoryComplete(stepData) &&
        stepData.mandatory_completion_screen_viewed;

      // If we're in the optional phase, anyone can minimize - no matter who you are
      if (isOptionalPhase) {
        return true;
      }

      // If we're in the mandatory phase, minimize should be hidden for everyone
      // (Sub-members skip onboarding entirely, so they won't reach this modal)
      return false;
    },
    [allMandatoryComplete]
  );

  // Handle logout
  const handleLogout = () => {
    globalDispatch({
      type: "TOGGLE_PROFILE",
      payload: { showProfile: false },
    });
    authDispatch({
      type: "LOGOUT",
    });
    navigate("/member/login");
  };

  // Load fresh user details
  const loadUserDetails = async () => {
    try {
      setLoadFresh(true);
      const userId = localStorage.getItem("user");
      if (userId) {
        const result = await getUserDetailsByIdAPI(userId);
        if (!result.error) {
          setUserDetails(result.model);
          return result.model;
        }
      }
    } catch (error) {
      console.error("Error loading user details:", error);
    } finally {
      setLoadFresh(false);
    }
    return userDetails;
  };

  // Load fresh user details on mount
  useEffect(() => {
    loadUserDetails();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (userDetails?.steps) {
      try {
        const parsedSteps = JSON.parse(userDetails.steps);
        setStepData(parsedSteps);

        // Only set current step if it's not already set or if we need to find the first incomplete step
        if (currentStep === 0 || currentStep === 1) {
          // Check if we're in optional phase
          const isOptionalPhase =
            allMandatoryComplete(parsedSteps) &&
            parsedSteps.mandatory_completion_screen_viewed;

          if (isOptionalPhase) {
            // Find the first incomplete optional step
            const firstIncompleteOptional = optionalSteps.find(
              (step) => !parsedSteps[step.key]
            );
            if (firstIncompleteOptional) {
              setCurrentStep(firstIncompleteOptional.id);
            } else {
              // All optional steps complete, set to first optional step
              setCurrentStep(optionalSteps[0].id);
            }
          } else if (allMandatoryComplete(parsedSteps)) {
            // All mandatory complete but completion screen not viewed - stay on completion screen
            setCurrentStep(1); // This will show completion screen
          } else {
            // Find the first incomplete mandatory step
            const firstIncompleteStep = mandatorySteps.find(
              (step) => !parsedSteps[step.key]
            );
            if (firstIncompleteStep) {
              setCurrentStep(firstIncompleteStep.id);
            }
          }
        }
      } catch (error) {
        console.error("Error parsing step data:", error);
      }
    }
  }, [
    userDetails?.steps,
    currentStep,
    allMandatoryComplete,
    mandatorySteps,
    optionalSteps,
  ]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    // Check if user can minimize based on business info completion and member status
    const canMinimizeModal = canUserMinimize(userDetails, stepData);
    setCanMinimize(canMinimizeModal);
  }, [userDetails, stepData, canUserMinimize]);

  const handleStepComplete = async (stepId, data) => {
    console.log(stepId, data, "lopepe");
    try {
      setIsLoading(true);

      // First, fetch current user details to get all fields
      const userId = localStorage.getItem("user");
      const currentUserDetails = await getUserDetailsByIdAPI(userId);

      if (currentUserDetails.error) {
        throw new Error("Failed to fetch current user details");
      }

      // Find step in mandatory or optional
      let stepList = mandatorySteps;
      if (allMandatoryComplete(stepData)) {
        stepList = [...mandatorySteps, ...optionalSteps];
      }
      const step = stepList.find((s) => s.id === stepId);
      const updatedStepData = {
        ...stepData,
        ...data,
        [step.key]: true,
      };

      // If this is step 2 (business info), mark business profile as complete and reload user details
      if (stepId === 2) {
        updatedStepData.business_profile_complete = true;
      }

      // Prepare payload with all current user fields plus updated steps
      const payload = {
        id: parseInt(userId),
        // Include all existing user fields
        first_name: currentUserDetails.model.first_name || "",
        last_name: currentUserDetails.model.last_name || "",
        email: currentUserDetails.model.email || "",
        // For step 2 (business info), use the data from the step, otherwise use current data
        company_name:
          (stepId === 2 || stepId == 10) && data.company_name
            ? data.company_name
            : currentUserDetails.model.company_name || "",
        company_address:
          (stepId === 2 || stepId == 10) && data.company_address
            ? data.company_address
            : currentUserDetails.model.company_address || "",
        office_email:
          stepId === 2 && data.office_email
            ? data.office_email
            : currentUserDetails.model.office_email || "",
        phone:
          (stepId === 2 || stepId == 10) && data.phone
            ? data.phone
            : currentUserDetails.model.phone || "",
        company_logo:
          stepId === 2 && data.company_logo
            ? data.company_logo
            : currentUserDetails.model.company_logo || "",
        license_company_logo:
          stepId === 2 && data.license_company_logo
            ? data.license_company_logo
            : currentUserDetails.model.license_company_logo || "",
        deposit_percent:
          stepId == 9
            ? data.deposit_percent
            : currentUserDetails.model.deposit_percent || 0,
        // For step 3 (service agreement), use the data from the step, otherwise use current data
        contract_agreement:
          stepId === 3 && data.contract_agreement
            ? data.contract_agreement
            : currentUserDetails.model.contract_agreement || "",
        // For step 6 (project management), use the data from the step, otherwise use current data
        survey:
          stepId == 9 && data?.survey_week
            ? JSON.stringify({
                weeks: data.survey_week,
                day: data?.survey_day,
              })
            : JSON.stringify(currentUserDetails.model.survey) || "",
        routine_submission_date:
          stepId == 9 && data?.routine_submission_weeks
            ? JSON.stringify({
                weeks: data.routine_submission_weeks,
                day: data?.routine_submission_day,
              })
            : JSON.stringify(
                currentUserDetails.model.routine_submission_date
              ) || "",
        estimated_delivery:
          stepId == 9 && data?.estimated_delivery_day
            ? JSON.stringify({
                weeks: data.estimated_delivery_weeks,
                day: data?.estimated_delivery_day,
              })
            : JSON.stringify(currentUserDetails.model.estimated_delivery) || "",
        // For step 4 (edit policy), use the data from the step, otherwise use current data
        edit_policy_link:
          stepId == 7 && data.edit_policy_link
            ? data.edit_policy_link
            : currentUserDetails.model.edit_policy_link || "",
        // Update the steps field
        steps: JSON.stringify(updatedStepData),
      };

      // Update user with all fields including updated steps
      const updateResult = await updateUserDetailsAPI(payload);

      if (!updateResult.error) {
        setStepData(updatedStepData);
        showToast(
          globalDispatch,
          `Step completed successfully!`,
          3000,
          "success"
        );

        // Reload user details to get updated info for minimize check
        await loadUserDetails();

        // Notify parent component of step data update AFTER all state updates
        console.log(
          "🔄 NewOnboardingModal: Notifying parent of step data update:",
          updatedStepData
        );
        if (onStepDataUpdate) {
          onStepDataUpdate(updatedStepData);
        }

        // Move to next step or complete onboarding
        // Check if we just completed the last mandatory step
        const isLastMandatoryStep = stepId === 4; // Business Identity step
        const allMandatoryNowComplete = mandatorySteps.every(
          (step) => updatedStepData[step.key]
        );

        console.log("hello");

        if (
          isLastMandatoryStep &&
          allMandatoryNowComplete &&
          !updatedStepData.mandatory_completion_screen_viewed
        ) {
          // Show completion screen after last mandatory step
          // Don't set currentStep here, let the render logic handle it
          setStepData(updatedStepData);
          console.log("hello1");
        } else {
          console.log("hello4");
          // Normal flow - find next step
          const nextStep = stepList.find(
            (s) => s.id > stepId && !updatedStepData[s.key]
          );
          const nextIncompleteStep = !nextStep
            ? stepList.find((s) => !updatedStepData[s.key])
            : null;
          if (nextStep || nextIncompleteStep) {
            setCurrentStep((nextStep || nextIncompleteStep)?.id);
          } else {
            // Check if we're in optional phase and all optional steps are complete
            const isOptionalPhase =
              allMandatoryComplete(updatedStepData) &&
              updatedStepData.mandatory_completion_screen_viewed;
            if (isOptionalPhase && allOptionalComplete(updatedStepData)) {
              // All optional steps complete - show final completion screen
              setCurrentStep("final_completion");
            } else {
              // All steps complete
              await handleCompleteOnboarding(updatedStepData);
            }
          }
          console.log("hello2");
        }
      } else {
        throw new Error(updateResult.message || "Failed to save step data");
      }
    } catch (error) {
      console.error("Error completing step:", error);
      showToast(
        globalDispatch,
        "Failed to save step data. Please try again.",
        4000,
        "error"
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleCompleteOnboarding = async (finalStepData) => {
    try {
      // First, fetch current user details to get all fields
      const userId = localStorage.getItem("user");
      const currentUserDetails = await getUserDetailsByIdAPI(userId);

      if (currentUserDetails.error) {
        throw new Error("Failed to fetch current user details");
      }

      const completeData = {
        ...finalStepData,
        onboarding_complete: true,
        completed_at: new Date().toISOString(),
      };

      // Prepare payload with all current user fields plus completed steps
      const payload = {
        id: parseInt(userId),
        first_name: currentUserDetails.model.first_name || "",
        last_name: currentUserDetails.model.last_name || "",
        email: currentUserDetails.model.email || "",
        company_name: currentUserDetails.model.company_name || "",
        company_address: currentUserDetails.model.company_address || "",
        office_email: currentUserDetails.model.office_email || "",
        phone: currentUserDetails.model.phone || "",
        company_logo: currentUserDetails.model.company_logo || "",
        license_company_logo:
          currentUserDetails.model.license_company_logo || "",
        deposit_percent: currentUserDetails.model.deposit_percent || 0,
        contract_agreement: currentUserDetails.model.contract_agreement || "",
        survey: JSON.stringify(currentUserDetails.model.survey) || "",
        routine_submission_date:
          JSON.stringify(currentUserDetails.model.routine_submission_date) ||
          "",
        estimated_delivery:
          JSON.stringify(currentUserDetails.model.estimated_delivery) || "",
        edit_policy_link: currentUserDetails.model.edit_policy_link || "",
        // Update the steps field with completion data
        steps: JSON.stringify(completeData),
      };

      const updateResult = await updateUserDetailsAPI(payload);

      if (!updateResult.error) {
        showToast(
          globalDispatch,
          "Onboarding completed successfully!",
          4000,
          "success"
        );
        onComplete();
      } else {
        throw new Error(
          updateResult.message || "Failed to complete onboarding"
        );
      }
    } catch (error) {
      console.error("Error completing onboarding:", error);
      showToast(
        globalDispatch,
        "Failed to complete onboarding. Please try again.",
        4000,
        "error"
      );
    }
  };

  const handleStepClick = (stepId) => {
    console.log("handleStepClick called with stepId:", stepId);
    console.log("Current stepData:", stepData);
    console.log(
      "allMandatoryComplete(stepData):",
      allMandatoryComplete(stepData)
    );
    console.log(
      "stepData.mandatory_completion_screen_viewed:",
      stepData.mandatory_completion_screen_viewed
    );

    // Check if we're in optional phase
    const isOptionalPhase =
      allMandatoryComplete(stepData) &&
      stepData.mandatory_completion_screen_viewed;

    console.log("isOptionalPhase:", isOptionalPhase);

    if (isOptionalPhase) {
      // During optional phase: only allow clicking on optional steps
      // Prevent navigation back to completed mandatory steps
      const isOptionalStep = optionalSteps.some((step) => step.id === stepId);
      const isMandatoryStep = mandatorySteps.some((step) => step.id === stepId);

      console.log("isOptionalStep:", isOptionalStep);
      console.log("isMandatoryStep:", isMandatoryStep);

      if (isOptionalStep) {
        console.log("Setting current step to:", stepId);
        setCurrentStep(stepId);
        return;
      }

      // Block navigation to mandatory steps during optional phase
      if (isMandatoryStep) {
        console.log("Blocked navigation to completed mandatory step:", stepId);
        return;
      }
    }

    // During mandatory phase: only allow navigation to completed steps or the next incomplete step
    // Find the step to get its key
    const step = [...mandatorySteps, ...optionalSteps].find(
      (s) => s.id === stepId
    );
    const isCompleted = step ? stepData[step.key] : false;
    const isNext = stepId === currentStep;

    console.log("step:", step);
    console.log("isCompleted:", isCompleted);
    console.log("isNext:", isNext);

    if (isCompleted || isNext) {
      console.log("Setting current step to:", stepId);
      setCurrentStep(stepId);
    }
  };

  const handleContinueSetup = async () => {
    try {
      setIsLoading(true);

      // First, fetch current user details to get all fields
      const userId = localStorage.getItem("user");
      const currentUserDetails = await getUserDetailsByIdAPI(userId);

      if (currentUserDetails.error) {
        throw new Error("Failed to fetch current user details");
      }

      // Mark completion screen as viewed
      const updatedStepData = {
        ...stepData,
        mandatory_completion_screen_viewed: true,
      };

      // Prepare payload with all current user fields plus updated steps
      const payload = {
        id: parseInt(userId),
        // Keep all existing user fields
        first_name: currentUserDetails.model.first_name || "",
        last_name: currentUserDetails.model.last_name || "",
        email: currentUserDetails.model.email || "",
        company_name: currentUserDetails.model.company_name || "",
        company_address: currentUserDetails.model.company_address || "",
        office_email: currentUserDetails.model.office_email || "",
        phone: currentUserDetails.model.phone || "",
        company_logo: currentUserDetails.model.company_logo || "",
        license_company_logo:
          currentUserDetails.model.license_company_logo || "",
        deposit_percent: currentUserDetails.model.deposit_percent || 0,
        contract_agreement: currentUserDetails.model.contract_agreement || "",
        survey: currentUserDetails.model.survey
          ? JSON.stringify(currentUserDetails.model.survey)
          : "",
        routine_submission_date: currentUserDetails.model
          .routine_submission_date
          ? JSON.stringify(currentUserDetails.model.routine_submission_date)
          : "",
        estimated_delivery: currentUserDetails.model.estimated_delivery
          ? JSON.stringify(currentUserDetails.model.estimated_delivery)
          : "",
        edit_policy_link: currentUserDetails.model.edit_policy_link || "",
        // Update the steps field with mandatory_completion_screen_viewed
        steps: JSON.stringify(updatedStepData),
      };

      const updateResult = await updateUserDetailsAPI(payload);

      if (!updateResult.error) {
        setStepData(updatedStepData);
        // Notify parent component of step data update
        console.log(
          "🔄 NewOnboardingModal: Notifying parent of completion screen update:",
          updatedStepData
        );
        if (onStepDataUpdate) {
          onStepDataUpdate(updatedStepData);
        }

        // Find the first incomplete optional step
        const firstIncompleteOptional = optionalSteps.find(
          (step) => !updatedStepData[step.key]
        );
        if (firstIncompleteOptional) {
          setCurrentStep(firstIncompleteOptional.id);
        }
      } else {
        throw new Error(updateResult.message || "Failed to update step data");
      }
    } catch (error) {
      console.error("Error continuing setup:", error);
      showToast(
        globalDispatch,
        "Failed to continue setup. Please try again.",
        4000,
        "error"
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoToDashboard = async () => {
    try {
      setIsLoading(true);

      // Mark completion screen as viewed but keep onboarding flag for optional steps
      const updatedStepData = {
        ...stepData,
        mandatory_completion_screen_viewed: true,
      };

      // Update steps in user details
      const userId = localStorage.getItem("user");
      const currentUserDetails = await getUserDetailsByIdAPI(userId);

      if (!currentUserDetails.error) {
        const updateResult = await updateUserDetailsAPI({
          id: parseInt(userId),

          // Keep all existing user fields
          first_name: currentUserDetails.model.first_name || "",
          last_name: currentUserDetails.model.last_name || "",
          email: currentUserDetails.model.email || "",
          company_name: currentUserDetails.model.company_name || "",
          company_address: currentUserDetails.model.company_address || "",
          office_email: currentUserDetails.model.office_email || "",
          phone: currentUserDetails.model.phone || "",
          company_logo: currentUserDetails.model.company_logo || "",
          license_company_logo:
            currentUserDetails.model.license_company_logo || "",
          deposit_percent: currentUserDetails.model.deposit_percent || 0,
          contract_agreement: currentUserDetails.model.contract_agreement || "",
          survey: currentUserDetails.model.survey
            ? JSON.stringify(currentUserDetails.model.survey)
            : "",
          routine_submission_date: currentUserDetails.model
            .routine_submission_date
            ? JSON.stringify(currentUserDetails.model.routine_submission_date)
            : "",
          estimated_delivery: currentUserDetails.model.estimated_delivery
            ? JSON.stringify(currentUserDetails.model.estimated_delivery)
            : "",
          edit_policy_link: currentUserDetails.model.edit_policy_link || "",
          // Keep all existing user fields

          steps: JSON.stringify(updatedStepData),
        });

        if (!updateResult.error) {
          setStepData(updatedStepData);
          // Notify parent component of step data update
          console.log(
            "🔄 NewOnboardingModal: Notifying parent of dashboard navigation update:",
            updatedStepData
          );
          if (onStepDataUpdate) {
            onStepDataUpdate(updatedStepData);
          }

          // Close modal but keep needs_onboarding flag for optional steps tracker
          onMinimize(); // This will show the tracker instead of removing onboarding entirely
        }
      }
    } catch (error) {
      console.error("Error going to dashboard:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Determine which step list to use and find current step
  const isOptionalPhase =
    allMandatoryComplete(stepData) &&
    stepData.mandatory_completion_screen_viewed;
  let stepList = mandatorySteps;
  if (allMandatoryComplete(stepData)) {
    stepList = [...mandatorySteps, ...optionalSteps];
  }

  // Find current step data from the appropriate list
  let currentStepData;
  if (isOptionalPhase) {
    // During optional phase, look in optional steps first
    currentStepData = optionalSteps.find((step) => step.id === currentStep);
    if (!currentStepData) {
      // Fallback to full step list
      currentStepData = stepList.find((step) => step.id === currentStep);
    }
  } else {
    // During mandatory phase, use full step list
    currentStepData = stepList.find((step) => step.id === currentStep);
  }

  const CurrentStepComponent = currentStepData?.component;

  if (isMinimized) {
    return null; // The minimized view will be handled by OnboardingProgressTracker
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="mx-4 h-[90vh] w-full max-w-7xl">
        <div className="relative flex h-full flex-col rounded-lg bg-white shadow-xl">
          {/* Header */}
          <div className="absolute right-3 top-3 z-[50] flex items-center gap-2">
            {canMinimize && (
              <button
                onClick={onMinimize}
                className="rounded-full p-2 hover:bg-gray-400 hover:text-gray-700"
                title="Minimize"
              >
                <svg
                  className="h-5 w-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M20 12H4"
                  />
                </svg>
              </button>
            )}
            <button
              onClick={handleLogout}
              className="rounded-full p-2 text-[#CD4631] transition-colors hover:bg-gray-100"
              title="Logout from your account"
            >
              <svg
                className="h-6 w-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                />
              </svg>
            </button>
          </div>

          {loadFresh ? (
            <div className="flex h-full flex-col items-center justify-center">
              <ClipLoader color="#3C50E0" size={50} />
            </div>
          ) : (
            <div className="relative flex flex-1 overflow-hidden">
              {/* Left Sidebar - Steps */}
              <div className="flex h-full w-[424px] flex-col justify-between gap-[24px] overflow-y-auto rounded-l-[16px] border-r border-r-[#e7e7e7] bg-white p-[40px]  shadow-[0_0_0_4px_#f4ebff,0_1px_2px_0px_rgba(10,13,18,0.16),0_1px_3px_0px_rgba(10,13,18,0.3)] ">
                {/* Modal Header */}
                {!allMandatoryComplete(stepData) &&
                  !stepData.mandatory_completion_screen_viewed && (
                    <div className="mb-4 flex flex-col items-center gap-2">
                      <h2 className="text-center text-[18px] font-semibold leading-[28px] text-[#131E2B]">
                        Your Setup Roadmap
                      </h2>
                      <p className="text-center text-[14px] leading-[20px] text-[#667484]">
                        See what's next to unlock your app's full power.
                      </p>
                    </div>
                  )}
                {/* SubHeader */}
                <div className="mb-4 flex flex-col gap-3">
                  <span className="text-[18px] font-semibold leading-[28px] text-[#131E2B]">
                    {allMandatoryComplete(stepData) &&
                    stepData.mandatory_completion_screen_viewed
                      ? "Empower Your Business Operations"
                      : "Instant Kick-Off"}
                  </span>
                  {allMandatoryComplete(stepData) &&
                  stepData.mandatory_completion_screen_viewed ? (
                    <p className="text-[14px] leading-[20px] text-[#667484]">
                      Small steps, monumental impact.
                    </p>
                  ) : null}
                  <div className="h-2 w-full rounded-full bg-[#E9EBEF]">
                    <div
                      className="h-full rounded-full bg-[#3C50E0] transition-all duration-300"
                      style={{ width: `${getProgressPercent()}%` }}
                    ></div>
                  </div>
                </div>
                {/* Steps Checklist */}
                <div className="flex flex-1 flex-col justify-start gap-4">
                  {/* Pre-steps (only show during mandatory phase) */}
                  {!(
                    allMandatoryComplete(stepData) &&
                    stepData.mandatory_completion_screen_viewed
                  ) &&
                    visualPreSteps.map((step) => (
                      <div
                        key={step.id}
                        className={`flex h-[42px] items-center justify-between gap-4 rounded-lg border border-[#A0B0F3] bg-[#F5F7FF] px-4 py-2`}
                      >
                        <div className="flex items-center gap-3">
                          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-[white]">
                            {step.icon}
                          </div>
                          <span className="font-medium text-[#1F2E8A]">
                            {step.title}
                          </span>
                        </div>
                        <img
                          className="h-6 w-6"
                          src="/checkbox_base.svg"
                          alt=""
                        />
                      </div>
                    ))}
                  {/* Main steps - show mandatory steps during mandatory phase, only optional steps during optional phase */}
                  {(allMandatoryComplete(stepData) &&
                  stepData.mandatory_completion_screen_viewed
                    ? optionalSteps
                    : mandatorySteps
                  ).map((step) => {
                    const completed = stepData[step.key];
                    const isCurrent = step.id === currentStep;
                    const isOptionalPhase =
                      allMandatoryComplete(stepData) &&
                      stepData.mandatory_completion_screen_viewed;
                    const isMandatoryStep = mandatorySteps.some(
                      (s) => s.id === step.id
                    );

                    // Check if this is a completed mandatory step during optional phase (should be disabled)
                    const isDisabledMandatory =
                      isOptionalPhase && isMandatoryStep && completed;

                    // Styling logic: completed = light blue bg, current = blue border + white bg, others = white bg
                    const bg = completed
                      ? "bg-[#F5F7FF]"
                      : isCurrent
                      ? "bg-white"
                      : "bg-white";
                    const border = completed
                      ? "border-[#A0B0F3]"
                      : isCurrent
                      ? "border-[#2B3EB4]"
                      : "border-[#D1D6DE]";

                    // Use cursor-not-allowed for disabled mandatory steps
                    const cursorStyle = isDisabledMandatory
                      ? "cursor-not-allowed"
                      : "cursor-pointer";

                    return (
                      <div
                        key={step.id}
                        className={`flex items-center gap-4 rounded-lg border px-4 py-2 ${bg} ${border} ${cursorStyle} ${
                          completed && !step.subtitle ? "h-[42px]" : ""
                        }`}
                        onClick={() => handleStepClick(step.id)}
                      >
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-[#E0E6FC]">
                          {step.icon}
                        </div>
                        <div className="flex-1">
                          <span
                            className={`font-medium ${
                              completed ? "text-[#2B3EB4]" : "text-[#667484]"
                            }`}
                          >
                            {step.title}
                          </span>
                          {/* Hide subtitle if completed, otherwise show it */}
                          {!completed && step.subtitle && (
                            <div className="text-xs text-[#667484]">
                              {step.subtitle}
                            </div>
                          )}
                        </div>
                        {completed ? (
                          <img
                            className="h-6 w-6"
                            src="/checkbox_base.svg"
                            alt=""
                          />
                        ) : (
                          <div className="h-4 w-4 rounded-[50%] border border-[#667484] "></div>
                        )}
                      </div>
                    );
                  })}
                </div>
                {allMandatoryComplete(stepData) &&
                  stepData.mandatory_completion_screen_viewed && (
                    <div className="relative bottom-1">
                      <p className="max-w-[323px] text-[13px] font-normal leading-[20px] text-gray-500">
                        Your progress is saved, so feel free to return to this
                        optional setup via the 'Onboarding Checklist' widget on
                        your dashboard.
                      </p>
                    </div>
                  )}
              </div>
              {/* Right Content Area */}
              <div className="flex flex-1 flex-col justify-start overflow-y-auto rounded-r-2xl bg-white p-10">
                {isLoading ? (
                  <div className="flex h-full flex-col items-center justify-center">
                    <h3 className="mb-3 text-xl font-medium text-primary">
                      Processing
                    </h3>
                    <ClipLoader color="#3C50E0" size={50} />
                  </div>
                ) : allMandatoryComplete(stepData) &&
                  !stepData.mandatory_completion_screen_viewed ? (
                  // Show completion step when all mandatory steps are complete but completion screen not viewed
                  <CompletionOfMandatoryStep
                    isLoading={isLoading}
                    onContinueSetup={handleContinueSetup}
                    onGoToDashboard={handleGoToDashboard}
                  />
                ) : currentStep === "final_completion" ? (
                  // Show final completion screen when all optional steps are complete
                  <FinalCompletionStep onGoToDashboard={handleGoToDashboard} />
                ) : currentStep === 1 ? (
                  <SubscriptionStep
                    userDetails={userDetails}
                    onComplete={async (data) => {
                      await handleStepComplete(1, data);
                    }}
                    setIsLoading={setIsLoading}
                    globalDispatch={globalDispatch}
                  />
                ) : CurrentStepComponent ? (
                  <CurrentStepComponent
                    stepData={stepData}
                    userDetails={userDetails}
                    onComplete={(data) => handleStepComplete(currentStep, data)}
                    onSkip={() => {
                      // Skip to next step without marking as completed
                      const isOptionalPhase =
                        allMandatoryComplete(stepData) &&
                        stepData.mandatory_completion_screen_viewed;
                      if (isOptionalPhase) {
                        const currentStepList = optionalSteps;
                        const currentIndex = currentStepList.findIndex(
                          (step) => step.id === currentStep
                        );
                        const nextStep = currentStepList[currentIndex + 1];
                        if (nextStep) {
                          setCurrentStep(nextStep.id);
                        }
                      }
                    }}
                    onNext={() => {
                      const nextStep = steps.find(
                        (step) => step.id > currentStep
                      );
                      if (nextStep) setCurrentStep(nextStep.id);
                    }}
                    setIsLoading={setIsLoading}
                    globalDispatch={globalDispatch}
                  />
                ) : (
                  <div className="flex h-full items-center justify-center">
                    <div className="text-center">
                      <div className="mb-4 text-6xl">
                        {currentStepData?.icon}
                      </div>
                      <h3 className="text-xl font-semibold text-[#131E2B]">
                        {currentStepData?.title}
                      </h3>
                      <p className="mt-2 text-sm text-[#667484]">
                        This step is coming soon. Click continue to proceed.
                      </p>
                      <button
                        onClick={() => handleStepComplete(currentStep, {})}
                        className="mt-4 rounded-lg bg-[#3C50E0] px-6 py-2 text-white transition hover:bg-opacity-90"
                      >
                        Continue
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default NewOnboardingModal;
