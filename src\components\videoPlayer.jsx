import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import React from "react";

const VideoPlayer = ({ fileSource, setModalClose }) => {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/50 backdrop-blur-sm"
        onClick={() => setModalClose(false)}
      />

      {/* Modal Container */}
      <div className="shadow-default w-full max-w-md transform rounded border border-strokedark bg-boxdark transition-all">
        {/* Modal Header */}
        <div className="flex items-center justify-between border-b border-stroke px-6 py-4">
          <div className="flex items-center gap-3">
            <FontAwesomeIcon
              icon="fa-solid fa-video"
              className="text-xl text-primary"
            />
            <h3 className="text-xl font-medium text-white">Video Player</h3>
          </div>
          <button
            onClick={() => setModalClose(false)}
            className="hover:text-primary"
          >
            <FontAwesomeIcon icon="fa-solid fa-xmark" className="text-2xl" />
          </button>
        </div>

        {/* Video Content */}
        <div className="p-6">
          <div className="relative aspect-video w-full overflow-hidden rounded border border-stroke bg-boxdark-2">
            <video controls className="h-full w-full" controlsList="nodownload">
              <source src={fileSource} type="video/mp4" />
              <div className="flex flex-col items-center justify-center py-8">
                <div className="mb-3 rounded-full bg-boxdark p-3">
                  <FontAwesomeIcon
                    icon="fa-solid fa-circle-exclamation"
                    className="h-6 w-6 text-bodydark2"
                  />
                </div>
                <p className="text-sm text-bodydark2">
                  Your browser does not support the video player
                </p>
              </div>
            </video>
          </div>
        </div>

        {/* Footer */}
        <div className="border-t border-stroke px-6 py-4">
          <button
            onClick={() => setModalClose(false)}
            className="flex w-full items-center justify-center rounded-sm bg-primary px-6 py-2 text-sm font-medium text-white hover:bg-opacity-90"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default VideoPlayer;
