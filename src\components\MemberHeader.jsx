import {
  AudioLines,
  Boxes,
  Briefcase,
  Calendar,
  ChevronDown,
  Disc3,
  Edit3,
  FolderKanban,
  Layers,
  LayoutDashboard,
  LogOut,
  Settings,
  UserCog2,
  Users,
  CreditCard,
  Receipt,
} from "lucide-react";
import React, { useEffect } from "react";
import { NavLink, useLocation } from "react-router-dom";
import { getAllEditAPI } from "Src/services/editService";
import { AuthContext } from "../authContext";
import { GlobalContext } from "../globalContext";
import { useSubscription } from "../hooks/useSubscription";
import { useUserAndSubscription } from "../hooks/useUserAndSubscription";

export const MemberHeader = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch, state } = React.useContext(GlobalContext);

  // Use the comprehensive hook to ensure prices are loaded
  const { subscription: subscriptionFromHook } = useUserAndSubscription({
    autoFetch: true,
    shouldFetchSubscriptionData: true,
  });

  const { hasAdvanced, hasStudioAccess } = useSubscription();
  const location = useLocation();
  const [isSettingsOpen, setIsSettingsOpen] = React.useState(false);
  console.log("=== MemberHeader Debug ===");
  console.log("hasAdvanced:", hasAdvanced);
  console.log("hasStudioAccess:", hasStudioAccess);
  console.log("useSubscription full data:", { hasAdvanced, hasStudioAccess });
  console.log("subscriptionFromHook:", subscriptionFromHook);

  // Get the full subscription hook data for debugging
  const subscriptionData = useSubscription();
  console.log("Full subscription data:", subscriptionData);
  console.log("subscriptionProductId:", subscriptionData.subscriptionProductId);
  console.log("userDetails:", subscriptionData.userDetails);
  console.log("globalSubscription:", subscriptionData.globalSubscription);

  // Debug the prices data
  console.log("Global prices:", state.subscription?.prices);
  console.log("Prices length:", state.subscription?.prices?.length);
  if (subscriptionData.userDetails?.plan_id) {
    console.log("User plan_id:", subscriptionData.userDetails.plan_id);
    const matchingPrice = state.subscription?.prices?.find(
      (p) => p.id === subscriptionData.userDetails.plan_id
    );
    console.log("Matching price:", matchingPrice);
    if (matchingPrice) {
      console.log("Product ID from price:", matchingPrice.product_id);
    }
  }
  console.log("========================");
  const getPendingEdits = async () => {
    const res = await getAllEditAPI({
      producer_id: localStorage.getItem("user"),
      page: 1,
      limit: 50000,
      edit_status: 2,
    });

    globalDispatch({
      type: "SET_CURRENT_PENDING_LENGTH",
      payload: { pendingLength: res?.list?.length || 0 },
    });
  };

  useEffect(() => {
    getPendingEdits();
  }, [location.pathname]);

  const handleLogout = () => {
    globalDispatch({
      type: "TOGGLE_PROFILE",
      payload: { showProfile: false },
    });
    dispatch({
      type: "LOGOUT",
    });
  };

  // Subscription data is now handled by the useSubscription hook

  console.log(state.isOpen, "xnx");

  return (
    <aside
      className={`absolute left-0 top-0 z-[9] flex h-screen w-[220px] flex-col overflow-y-hidden border-r border-strokedark bg-boxdark text-bodydark1 duration-300 ease-linear  dark:bg-boxdark ${
        state.isOpen
          ? "lg:static lg:translate-x-0"
          : "-translate-x-full lg:-translate-x-full"
      }`}
    >
      {/* Sidebar Header */}
      <div className="py-5.5 lg:py-6.5 flex items-center justify-between gap-2 px-3 pl-6 lg:pt-[20px]">
        <div className="flex items-center justify-center lg:flex">
          <img
            crossOrigin="anonymous"
            src={
              state.siteLogo ??
              `${window.location.origin}/new/cheerEQ-2-Ed2.png`
            }
            className="h-auto w-[170px]"
            alt="logo"
          />
        </div>
        {/* <button
          onClick={() => toggleOpen(!state.isOpen)}
          className="relative left-[30px] lg:block"
        >
          <PanelLeftClose className="text-bodydark" />
        </button> */}
      </div>

      {/* Sidebar Menu */}
      <div className="no-scrollbar custom-overflow flex flex-col overflow-y-auto duration-300 ease-linear">
        <nav className="mt-2 px-2 py-2 lg:mt-3 lg:px-3">
          <div>
            <h3 className="mb-4 ml-4 text-sm font-semibold text-bodydark2">
              MENU
            </h3>

            <ul className="mb-6 flex flex-col gap-1.5">
              {/* Dashboard */}
              {hasStudioAccess && (
                <li>
                  <NavLink
                    to="/member/dashboard"
                    className={`group relative flex items-center gap-2.5 rounded-sm px-4 py-2 font-medium duration-300 ease-in-out hover:bg-graydark dark:hover:bg-meta-4 ${
                      state.path === "dashboard"
                        ? "bg-graydark dark:bg-meta-4"
                        : ""
                    }`}
                  >
                    <LayoutDashboard className="h-4 w-4" />
                    <span className="text-[15px]">Dashboard</span>
                  </NavLink>
                </li>
              )}

              {/* Project Calendar */}
              {hasStudioAccess && (
                <li>
                  <NavLink
                    to="/member/project-calendar"
                    className={`group relative flex items-center gap-2.5 rounded-sm px-4 py-2 font-medium duration-300 ease-in-out hover:bg-meta-4 dark:hover:bg-meta-4 ${
                      state.path === "project-calendar"
                        ? "bg-graydark dark:bg-meta-4"
                        : ""
                    }`}
                  >
                    <Calendar className="h-4 w-4" />
                    <span className="text-[15px]">Project Calendar</span>
                  </NavLink>
                </li>
              )}

              {/* Projects */}
              <li>
                <NavLink
                  to="/member/projects"
                  className={`group relative flex items-center gap-2.5 rounded-sm px-4 py-2 font-medium duration-300 ease-in-out hover:bg-graydark dark:hover:bg-meta-4 ${
                    state.path === "projects"
                      ? "bg-graydark dark:bg-meta-4"
                      : ""
                  }`}
                >
                  <FolderKanban className="h-4 w-4" />
                  <span className="text-[15px]">Projects</span>
                </NavLink>
              </li>

              {/* Edits */}
              {hasAdvanced && (
                <li>
                  <NavLink
                    to="/member/edits"
                    className={`group relative flex items-center gap-2.5 rounded-sm px-4 py-2 font-medium duration-300 ease-in-out hover:bg-graydark dark:hover:bg-meta-4 ${
                      state.path === "edits" ? "bg-graydark dark:bg-meta-4" : ""
                    }`}
                  >
                    <Edit3 className="h-4 w-4" />
                    <span className="text-[15px]">Edits</span>
                    {state?.pendingLength?.pendingLength > 0 && (
                      <span className="absolute right-4 top-1/2 flex h-5 w-5 -translate-y-1/2 items-center justify-center rounded-[2px] bg-red text-xs text-white">
                        {state?.pendingLength?.pendingLength}
                      </span>
                    )}
                  </NavLink>
                </li>
              )}

              {/* Work Orders */}
              {hasStudioAccess && (
                <li>
                  <NavLink
                    to="/member/work-orders"
                    className={`group relative flex items-center gap-2.5 rounded-sm px-4 py-2 font-medium duration-300 ease-in-out hover:bg-graydark dark:hover:bg-meta-4 ${
                      state.path === "work-orders"
                        ? "bg-graydark dark:bg-meta-4"
                        : ""
                    }`}
                  >
                    <Briefcase className="h-4 w-4" />
                    <span className="text-[15px]">Work Orders</span>
                  </NavLink>
                </li>
              )}

              {/* Songs */}
              {hasStudioAccess && (
                <li>
                  <NavLink
                    to="/member/unassigned-songs"
                    className={`group relative flex items-center gap-2.5 rounded-sm px-4 py-2 font-medium duration-300 ease-in-out hover:bg-graydark dark:hover:bg-meta-4 ${
                      state.path === "unassigned-songs"
                        ? "bg-graydark dark:bg-meta-4"
                        : ""
                    }`}
                  >
                    <AudioLines className="h-4 w-4" />
                    <span className="text-[15px]">Songs</span>
                  </NavLink>
                </li>
              )}

              {/* Mix Types */}

              {/* Count Tracks */}
              {hasAdvanced && (
                <li>
                  <NavLink
                    to="/member/count-tracks"
                    className={`group relative flex items-center gap-2.5 rounded-sm px-4 py-2 font-medium duration-300 ease-in-out hover:bg-graydark dark:hover:bg-meta-4 ${
                      state.path === "count-tracks"
                        ? "bg-graydark dark:bg-meta-4"
                        : ""
                    }`}
                  >
                    <Disc3 className="h-4 w-4" />
                    <span className="text-[15px]">Count Tracks</span>
                  </NavLink>
                </li>
              )}

              {/* Add Plans Menu Item */}

              <li>
                <NavLink
                  to="/member/invoices"
                  className={`group relative flex items-center gap-2.5 rounded-sm px-4 py-2 font-medium duration-300 ease-in-out hover:bg-graydark dark:hover:bg-meta-4 ${
                    state.path === "invoices"
                      ? "bg-graydark dark:bg-meta-4"
                      : ""
                  }`}
                >
                  <Receipt className="h-4 w-4" />
                  <span className="text-[15px]">Invoices</span>
                </NavLink>
              </li>

              {/* Settings Dropdown */}
              <li>
                <button
                  onClick={() => setIsSettingsOpen(!isSettingsOpen)}
                  className={`group relative flex w-full items-center justify-between gap-2.5 rounded-sm px-4 py-2 font-medium duration-300 ease-in-out hover:bg-graydark dark:hover:bg-meta-4 ${
                    state.path.includes("setting")
                      ? "bg-graydark dark:bg-meta-4"
                      : ""
                  }`}
                >
                  <div className="flex items-center">
                    <Settings className="h-4 w-4" />
                    <span className="ml-2 text-[15px]">Setting</span>
                  </div>
                  <ChevronDown
                    className={`h-4 w-4 transition-transform duration-200 ${
                      isSettingsOpen ? "rotate-180" : ""
                    }`}
                  />
                </button>

                {/* Dropdown Menu */}
                <div
                  className={`mt-2 space-y-1 px-4 ${
                    isSettingsOpen ? "block" : "hidden"
                  }`}
                >
                  <NavLink
                    to="/member/setting"
                    className={`group flex items-center gap-2.5 rounded-sm px-4 py-2 font-medium duration-300 ease-in-out hover:bg-graydark dark:hover:bg-meta-4 ${
                      location.pathname === "/member/setting"
                        ? "text-primary"
                        : "text-bodydark1"
                    }`}
                  >
                    <Settings className="h-4 w-4" />
                    <span className="text-sm">General</span>
                  </NavLink>

                  <li>
                    <NavLink
                      to="/member/subscription"
                      className={`group relative flex items-center gap-2.5 rounded-sm px-4 py-2 font-medium duration-300 ease-in-out hover:bg-graydark dark:hover:bg-meta-4 ${
                        state.path === "subscription"
                          ? "bg-graydark dark:bg-meta-4"
                          : ""
                      }`}
                    >
                      <CreditCard className="h-4 w-4" />
                      <span className="text-[15px]">Subscription</span>
                    </NavLink>
                  </li>

                  <NavLink
                    to="/member/clients"
                    className={`group flex items-center gap-2.5 rounded-sm px-4 py-2 font-medium duration-300 ease-in-out hover:bg-graydark dark:hover:bg-meta-4 ${
                      location.pathname === "/member/clients"
                        ? "text-primary"
                        : "text-bodydark1"
                    }`}
                  >
                    <Briefcase className="h-4 w-4" />
                    <span className="text-sm">Clients</span>
                  </NavLink>

                  <NavLink
                    to="/member/mix-types"
                    className={`group flex items-center gap-2.5 rounded-sm px-4 py-2 font-medium duration-300 ease-in-out hover:bg-graydark dark:hover:bg-meta-4 ${
                      location.pathname === "/member/mix-types"
                        ? "text-primary"
                        : "text-bodydark1"
                    }`}
                  >
                    <Boxes className="h-4 w-4" />
                    <span className="text-sm">Mix Types</span>
                  </NavLink>

                  <NavLink
                    to="/member/mix-seasons"
                    className={`group flex items-center gap-2.5 rounded-sm px-4 py-2 font-medium duration-300 ease-in-out hover:bg-graydark dark:hover:bg-meta-4 ${
                      location.pathname === "/member/mix-seasons"
                        ? "text-primary"
                        : "text-bodydark1"
                    }`}
                  >
                    <Layers className="h-4 w-4" />
                    <span className="text-sm">Mix Seasons</span>
                  </NavLink>

                  <NavLink
                    to="/member/employees"
                    className={`group flex items-center gap-2.5 rounded-sm px-4 py-2 font-medium duration-300 ease-in-out hover:bg-graydark dark:hover:bg-meta-4 ${
                      location.pathname === "/member/employees"
                        ? "text-primary"
                        : "text-bodydark1"
                    }`}
                  >
                    <Users className="h-4 w-4" />
                    <span className="text-sm">Employees</span>
                  </NavLink>
                  <NavLink
                    to="/member/profile"
                    className={`group relative flex items-center gap-2.5 rounded-sm px-4 py-2 font-medium duration-300 ease-in-out hover:bg-graydark dark:hover:bg-meta-4 ${
                      state.path === "profile"
                        ? "text-primary"
                        : "text-bodydark1"
                    }`}
                  >
                    <UserCog2 className="h-4 w-4" />
                    <span className="text-[15px]">Profile</span>
                  </NavLink>
                </div>
              </li>
              <li></li>

              {/* Profile */}

              {/* Logout */}
              <li>
                <NavLink
                  to="/member/login"
                  onClick={handleLogout}
                  className="group relative flex items-center gap-2.5 rounded-sm px-4 py-2 font-medium duration-300 ease-in-out hover:bg-graydark dark:hover:bg-meta-4"
                >
                  <LogOut className="h-4 w-4 text-[#CD4631]" />
                  <span className="text-[#CD4631]">Logout</span>
                </NavLink>
              </li>
            </ul>
          </div>
        </nav>
      </div>
    </aside>
  );
};

export default MemberHeader;
