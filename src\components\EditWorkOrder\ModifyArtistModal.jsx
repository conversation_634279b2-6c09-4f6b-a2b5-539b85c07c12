import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import CustomSelect2 from "Components/CustomSelect2";

const ModifyArtistModal = ({
  artists,
  selectedArtistId,
  setModalClose,
  setFormSubmit,
}) => {
  const [artistCost, setArtistCost] = React.useState(0);
  const [localSelectedArtistId, setLocalSelectedArtistId] =
    React.useState(selectedArtistId);

  const handleArtistChange = (value) => {
    if (value) {
      setLocalSelectedArtistId(value);
      if (artists) {
        const selectedArtist = artists.filter(
          (artist) => artist.id === Number(value)
        );
        setArtistCost(selectedArtist[0].artist_cost);
      }
    } else {
      setLocalSelectedArtistId(null);
      setArtistCost(0);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const payload = {
      artist_id: localSelectedArtistId,
      artist_cost: artistCost,
    };
    setFormSubmit(payload);
    setModalClose(false);
  };

  React.useEffect(() => {
    if (artists && artists.length > 0) {
      const selectedArtist = artists.filter(
        (artist) => artist.id === Number(selectedArtistId)
      );
      setArtistCost(selectedArtist[0].artist_cost);
    }
  }, [artists]);

  return (
    <div className="flex fixed inset-0 z-50 justify-center items-center">
      <div
        className="fixed inset-0 backdrop-blur-sm bg-black/50"
        onClick={() => setModalClose(false)}
      />
      <div className="w-full max-w-xl rounded border transition-all transform shadow-default border-strokedark bg-boxdark">
        {/* Modal Header */}
        <div className="flex justify-between items-center px-6 py-4 border-b border-form-strokedark">
          <div className="flex gap-3 items-center">
            <FontAwesomeIcon
              icon="fa-solid fa-music"
              className="text-xl text-primary"
            />
            <h3 className="text-xl font-medium text-white">Update Artist</h3>
          </div>
          <button
            onClick={() => setModalClose(false)}
            className="hover:text-primary"
          >
            <FontAwesomeIcon icon="fa-solid fa-xmark" className="text-2xl" />
          </button>
        </div>

        <div className="px-6 py-4">
          <form className="space-y-4">
            <div className="flex flex-col">
              <label
                className="mb-2.5 block text-sm font-medium text-white"
                htmlFor="artist"
              >
                Artist
              </label>
              <CustomSelect2
                className="w-full rounded border border-form-strokedark bg-boxdark-2 px-4 py-2.5 text-sm text-bodydark2 focus:border-primary focus-visible:outline-none"
                name="artist"
                id="artist"
                value={localSelectedArtistId}
                defaultValue={selectedArtistId}
                onChange={(value) => {
                  handleArtistChange(value);
                }}
              >
                <option value="">Select Artist</option>
                {artists &&
                  artists.length > 0 &&
                  artists?.map((artist) => (
                    <option key={artist.id} value={artist.id}>
                      {artist.name}
                    </option>
                  ))}
              </CustomSelect2>
            </div>

            <div className="flex flex-col">
              <label
                className="mb-2.5 block text-sm font-medium text-white"
                htmlFor="artist_cost"
              >
                Cost (default)
              </label>
              <input
                type="number"
                id="artist_cost"
                className="w-full rounded border border-form-strokedark bg-form-input px-4 py-2.5 text-sm text-bodydark2 focus:border-primary focus-visible:outline-none"
                placeholder="Enter cost"
                value={artistCost}
                min="0"
                onChange={(e) => setArtistCost(e.target.value)}
              />
            </div>
          </form>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-form-strokedark">
          <div className="flex gap-2">
            <button
              onClick={(e) => handleSubmit(e)}
              className="flex justify-center items-center px-6 py-2 w-full text-sm font-medium text-white rounded-sm bg-primary hover:bg-opacity-90"
            >
              Save Changes
            </button>
            <button
              onClick={() => setModalClose(false)}
              className="flex justify-center items-center px-6 py-2 w-full text-sm font-medium text-white rounded-sm bg-danger hover:bg-opacity-90"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModifyArtistModal;
