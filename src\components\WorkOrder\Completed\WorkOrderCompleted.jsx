import React from "react";
import SubProject from "./SubProject";
import { GlobalContext, showToast } from "Src/globalContext";
import SessionFiles from "./SessionFiles";
import { uploadFilesDataAPI } from "Src/services/workOrderService";
import { updateSubProjectDetailsAPI } from "Src/services/projectService";
import { useS3Upload } from "Src/libs/uploads3Hook";

const WorkOrderCompleted = ({
  sessions,
  subProjects,
  workOrderDetails,
  setLyrics,
  setDeleteFileId,
}) => {
  const {
    dispatch: globalDispatch,
    state: { subproject_update },
  } = React.useContext(GlobalContext);

  const { uploadS3FilesAPI, progress, error, isUploading } = useS3Upload();

  const handleSessionUploads = async (formData) => {
    try {
      const result = await uploadS3FilesAPI(formData);
      if (!result.error) {
        const payload = {
          project_id: workOrderDetails.project_id
            ? Number(workOrderDetails.project_id)
            : null,
          subproject_id: workOrderDetails.subproject_id
            ? Number(workOrderDetails.subproject_id)
            : null,
          workorder_id: workOrderDetails.id
            ? Number(workOrderDetails.id)
            : null,
          employee_id: Number(workOrderDetails.artist_id),
          employee_type: "artist",
          type: "session",
          attachments: result.attachments,
        };

        const fileUploadDataResult = await uploadFilesDataAPI(payload);
        if (!fileUploadDataResult.error) {
          showToast(globalDispatch, "Files updated successfully", 5000);
          globalDispatch({
            type: "SET_SUBPROJECT_UPDATE",
            payload: !subproject_update,
          });
        } else {
          showToast(
            globalDispatch,
            fileUploadDataResult.message,
            5000,
            "error"
          );
        }
        // setIsLoading(false);
      } else {
        showToast(globalDispatch, result.message, 5000, "error");
        // setIsLoading(false);
      }
    } catch (error) {
      showToast(globalDispatch, error.message, 5000, "error");
    }
  };

  const handleUpdateSubProjectDetails = async (payload) => {
    try {
      const result = await updateSubProjectDetailsAPI(payload);
      if (!result.error) {
        showToast(
          globalDispatch,
          "Sub-project details updated successfully",
          5000
        );
        globalDispatch({
          type: "SET_SUBPROJECT_UPDATE",
          payload: !subproject_update,
        });
      } else {
        showToast(globalDispatch, result.message, 5000, "error");
      }
    } catch (error) {
      showToast(globalDispatch, error.message, 5000, "error");
    }
  };

  return (
    <>
      {window.location.pathname.includes("edit-work-order") ? null : (
        <SessionFiles
          uploadedFilesProgressData={{ progress, error, isUploading }}
          uploadedFiles={sessions}
          setDeleteFileId={setDeleteFileId}
          setFormData={handleSessionUploads}
        />
      )}

      {subProjects &&
        subProjects.length > 0 &&
        subProjects.map((subProject, index) => {
          return (
            <SubProject
              key={index}
              workOrderDetails={workOrderDetails}
              subProject={subProject}
              uploadedDemoFiles={subProject.demos}
              uploadedMasterFiles={subProject.masters}
              setLyrics={setLyrics}
              setDeleteFileId={setDeleteFileId}
              setSubProjectDetails={handleUpdateSubProjectDetails}
            />
          );
        })}
    </>
  );
};

export default WorkOrderCompleted;
