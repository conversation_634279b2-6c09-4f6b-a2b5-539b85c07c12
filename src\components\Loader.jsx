import { ClipLoader } from "react-spinners";
import Indicator from "./LoadingIndicator";

const Loader = ({ style }) => {
  return (
    <div
      style={{
        display: "flex",
        width: "100vw",
        height: "100vh",
        justifyContent: "center",
        alignItems: "center",
        ...style,
      }}
    >
      <ClipLoader color="#fff" size={40} />
    </div>
  );
};

export default Loader;
