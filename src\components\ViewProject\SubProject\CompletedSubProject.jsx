import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import AudioPlayer from "Components/AudioPlayer";
import ConfirmModal from "Components/Modal/ConfirmModal";

const CompletedSubProject = ({
  ideas,
  typeName,
  writers,
  artists,
  engineers,
  producers,
  writer,
  artist,
  engineer,
  producer,
  handleAudioUpload,
  instrumentalFiles,
  subProject,
  setInCompleteSubProjectId,
}) => {
  const [localSelectedSubProjectId, setLocalSelectedSubProjectId] =
    React.useState(null);

  const [showIncompleteSubProjectModal, setShowIncompleteSubProjectModal] =
    React.useState(false);

  const writerCost = writer[0]?.writer_cost
    ? Number(writer[0]?.writer_cost)
    : 0;
  const artistCost = artist[0]?.artist_cost
    ? Number(artist[0]?.artist_cost)
    : 0;
  const engineerCost = engineer[0]?.engineer_cost
    ? Number(engineer[0]?.engineer_cost)
    : 0;

  const totalCost = writerCost + artistCost + engineerCost;

  const handleSelectedSubProjectId = (id) => {
    setLocalSelectedSubProjectId(null);
    if (localSelectedSubProjectId === id) {
      setLocalSelectedSubProjectId(null);
    } else {
      setLocalSelectedSubProjectId(id);
    }
  };

  const handleIncompleteSubProject = async () => {
    setInCompleteSubProjectId(subProject.id);
    setShowIncompleteSubProjectModal(false);
  };

  const handleIncompleteSubProjectModalClose = () => {
    setShowIncompleteSubProjectModal(false);
  };

  return (
    <>
      <div className="mb-4 w-full rounded-md border border-slate-500 bg-orange-400 p-5 shadow">
        <div className="flex w-full flex-row flex-wrap items-end justify-start gap-2 xl:justify-between">
          <span
            className="cursor-pointer"
            style={{
              position: "relative",
              top: "-10px",
              // right: '10px',
            }}
            onClick={() => {
              handleSelectedSubProjectId(subProject.id);
            }}
          >
            {localSelectedSubProjectId === subProject.id ? (
              <FontAwesomeIcon
                icon="fa-solid fa-chevron-down"
                width={12}
                height={12}
              />
            ) : (
              <FontAwesomeIcon
                icon="fa-solid fa-chevron-up"
                width={12}
                height={12}
              />
            )}
          </span>

          <div className="relative mt-6">
            {/* <label
                className='block mb-1 text-sm font-normal text-gray-100'
                htmlFor='name'
              >
                Type
              </label> */}
            <input
              type="text"
              className={`w-24 cursor-default rounded-lg border border-gray-500 bg-gray-700 p-2.5 text-sm text-white placeholder-gray-300 focus:border-blue-500 focus:ring-blue-500`}
              placeholder="Type"
              value={subProject.type}
              readOnly
              disabled
            />
          </div>

          {/* <div className='flex flex-col'>
            <label
              className='block mb-1 text-sm font-normal text-gray-100'
              htmlFor='name'
            >
              Type
            </label>
            <input
              type='text'
              className='w-24 cursor-default rounded-lg border border-gray-500 bg-gray-700 p-2.5 text-sm text-white placeholder-gray-300 focus:border-blue-500 focus:ring-blue-500'
              placeholder='Type'
              value={subProject.type}
              disabled
            />
          </div> */}

          <div class="relative mt-6">
            <select
              class="border-1 peer block h-[42px] w-28 appearance-none text-ellipsis rounded-lg  border-gray-600 bg-transparent p-2.5 pr-5 text-sm text-white focus:border-blue-500  focus:outline-none focus:ring-0 dark:border-gray-600 dark:text-white dark:focus:border-blue-500"
              name="writer"
              id="writer"
              value={writer[0]?.id}
              disabled
            >
              <option className="bg-gray-800 text-white" value="">
                Select
              </option>
              {writers?.map((writer) => (
                <option
                  className="bg-gray-800 text-white"
                  key={writer.id}
                  value={writer.id}
                >
                  {writer.name}
                </option>
              ))}
            </select>
            <label
              htmlFor="writer"
              class="absolute start-1 top-2  z-[4] origin-[0] -translate-y-4 scale-75 transform  bg-gray-800 px-2  text-sm text-white  duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75  peer-focus:px-2 peer-focus:text-blue-500  rtl:peer-focus:left-auto rtl:peer-focus:translate-x-1/4 dark:bg-gray-800  dark:text-white"
            >
              Writer
            </label>
          </div>

          {/* <div className='flex flex-col'>
            <label
              className='block mb-1 text-sm font-normal text-gray-100'
              htmlFor='writer'
            >
              Writer
            </label>
            <select
              className='w-28 rounded-lg border border-stone-500 bg-stone-700 p-2.5 text-sm text-white placeholder-stone-300 focus:border-blue-500 focus:ring-blue-500'
              name='writer'
              id='writer'
              value={writer[0]?.id}
              disabled
            >
              <option value=''>Select</option>
              {writers?.map((writer) => (
                <option key={writer.id} value={writer.id}>
                  {writer.name}
                </option>
              ))}
            </select>
          </div> */}

          <div class="relative mt-6">
            <input
              type="number"
              id="writer_cost"
              class="border-1 peer block h-[42px] w-20 appearance-none rounded-lg border-gray-600  bg-transparent px-2.5 pb-2.5 pt-4 text-sm text-white  focus:border-blue-500 focus:outline-none focus:ring-0 dark:text-white dark:focus:border-blue-500 "
              placeholder="Cost"
              value={writer[0]?.writer_cost}
              disabled
            />
            <label
              for="writer_cost"
              class="absolute start-1 top-2  z-[4] origin-[0] -translate-y-4 scale-75 transform  bg-gray-800 px-2  text-sm text-white  duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75  peer-focus:px-2 peer-focus:text-blue-500  rtl:peer-focus:left-auto rtl:peer-focus:translate-x-1/4 dark:bg-gray-800  dark:text-white"
            >
              Writer Cost
            </label>
          </div>

          {/* <div className='flex flex-col'>
            <label
              className='block mb-1 text-sm font-normal text-gray-100'
              htmlFor='writer_cost'
            >
              Writer Cost
            </label>
            <input
              type='text'
              className='w-16 rounded-lg border border-stone-500 bg-stone-700 p-2.5 text-sm text-white placeholder-stone-300 focus:border-blue-500 focus:ring-blue-500'
              placeholder='Writer Cost'
              value={writer[0]?.writer_cost}
              disabled
            />
          </div> */}

          <div class="relative mt-6">
            <select
              class="border-1 peer block h-[42px] w-28 appearance-none text-ellipsis rounded-lg  border-gray-600 bg-transparent p-2.5 pr-5   text-sm text-white focus:border-blue-500 focus:outline-none focus:ring-0 dark:border-gray-600 dark:text-white dark:focus:border-blue-500"
              name="artist"
              id="artist"
              value={artist[0]?.id}
              disabled
            >
              <option className="bg-gray-800 text-white" value="">
                Select
              </option>
              {artists?.map((artist) => (
                <option
                  className="bg-gray-800 text-white"
                  key={artist.id}
                  value={artist.id}
                >
                  {artist.name}
                </option>
              ))}
            </select>
            <label
              htmlFor="artist"
              class="absolute start-1 top-2  z-[4] origin-[0] -translate-y-4 scale-75 transform  bg-gray-800 px-2  text-sm text-white  duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75  peer-focus:px-2 peer-focus:text-blue-500  rtl:peer-focus:left-auto rtl:peer-focus:translate-x-1/4 dark:bg-gray-800  dark:text-white"
            >
              Artist
            </label>
          </div>

          {/* <div className='flex flex-col'>
            <label
              className='block mb-1 text-sm font-normal text-gray-100'
              htmlFor='artist'
            >
              Artist
            </label>
            <select
              className='w-28 rounded-lg border border-slate-500 bg-slate-700 p-2.5 text-sm text-white placeholder-slate-300 focus:border-blue-500 focus:ring-blue-500'
              name='artist'
              id='artist'
              value={artist[0]?.id}
              disabled
            >
              <option value=''>Select</option>
              {artists?.map((artist) => (
                <option key={artist.id} value={artist.id}>
                  {artist.name}
                </option>
              ))}
            </select>
          </div> */}

          <div class="relative mt-6">
            <input
              type="number"
              id="artist_cost"
              class="border-1 peer block h-[42px] w-20 appearance-none rounded-lg border-gray-600  bg-transparent px-2.5 pb-2.5 pt-4 text-sm text-white  focus:border-blue-500 focus:outline-none focus:ring-0 dark:text-white dark:focus:border-blue-500 "
              placeholder="Cost"
              value={artist[0]?.artist_cost}
              disabled
            />
            <label
              for="artist_cost"
              class="absolute start-1 top-2  z-[4] origin-[0] -translate-y-4 scale-75 transform  bg-gray-800 px-2  text-sm text-white  duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75  peer-focus:px-2 peer-focus:text-blue-500  rtl:peer-focus:left-auto rtl:peer-focus:translate-x-1/4 dark:bg-gray-800  dark:text-white"
            >
              Artist Cost
            </label>
          </div>

          {/* <div className='flex flex-col'>
            <label
              className='block mb-1 text-sm font-normal text-gray-100'
              htmlFor='artist_cost'
            >
              Artist Cost
            </label>
            <input
              type='text'
              className='w-16 rounded-lg border border-slate-500 bg-slate-700 p-2.5 text-sm text-white placeholder-slate-300 focus:border-blue-500 focus:ring-blue-500'
              placeholder='Artist Cost'
              value={artist[0]?.artist_cost}
              disabled
            />
          </div> */}

          <div class="relative mt-6">
            <select
              class="border-1 dark:focus:border-blue-5000 peer block h-[42px] w-28 appearance-none text-ellipsis rounded-lg border-zinc-500 bg-zinc-700  p-2.5 pr-5 text-sm text-white focus:border-blue-500 focus:outline-none focus:ring-0 dark:border-zinc-500 dark:text-white"
              name="engineer"
              id="engineer"
              value={engineer[0]?.id}
              disabled
            >
              <option className="bg-gray-800 text-white" value="">
                Select
              </option>
              {engineers?.map((engineer) => (
                <option
                  className="bg-gray-800 text-white"
                  key={engineer.id}
                  value={engineer.id}
                >
                  {engineer.name}
                </option>
              ))}
            </select>
            <label
              htmlFor="engineer"
              class="absolute start-1 top-2 z-[4]  origin-[0] -translate-y-4 scale-75 transform rounded-[40px] bg-gray-800 px-2 text-sm font-medium text-white duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:px-2  peer-focus:text-blue-500 rtl:peer-focus:left-auto rtl:peer-focus:translate-x-1/4 dark:text-white peer-focus:dark:text-blue-500"
            >
              Engineer
            </label>
          </div>

          {/* <div className='flex flex-col'>
            <label
              className='block mb-1 text-sm font-normal text-gray-100'
              htmlFor='engineer'
            >
              Engineer
            </label>
            <select
              className='w-28 rounded-lg border border-zinc-500 bg-zinc-700 p-2.5 text-sm text-white placeholder-zinc-300 focus:border-blue-500 focus:ring-blue-500'
              name='engineer'
              id='engineer'
              value={engineer[0]?.id}
              disabled
            >
              <option value=''>Select</option>
              {engineers?.map((engineer) => (
                <option key={engineer.id} value={engineer.id}>
                  {engineer.name}
                </option>
              ))}
            </select>
          </div> */}

          <div class="relative mt-6">
            <input
              type="text"
              id="engineer_cost"
              class="border-1 peer block h-[42px] w-20 appearance-none rounded-lg border-zinc-500 bg-zinc-700 bg-zinc-700 px-2.5 pb-2.5 pt-4 text-sm text-white focus:border-blue-600 focus:outline-none focus:ring-0 dark:border-zinc-500 dark:text-white dark:focus:border-blue-500   "
              placeholder="Cost"
              value={engineer[0]?.engineer_cost}
              disabled
            />
            <label
              for="engineer_cost"
              class="absolute start-1 top-2 z-[4]  origin-[0] -translate-y-4 scale-75 transform rounded-[40px] bg-gray-800 px-2 text-sm font-medium text-white duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:px-2  peer-focus:text-blue-500 rtl:peer-focus:left-auto rtl:peer-focus:translate-x-1/4 dark:text-white peer-focus:dark:text-blue-500"
            >
              Engineer Cost
            </label>
          </div>

          {/* <div className='flex flex-col'>
            <label
              className='block mb-1 w-16 text-sm font-normal text-gray-100'
              htmlFor='engineer_cost'
            >
              Engineer Cost
            </label>
            <input
              type='text'
              className='w-16 rounded-lg border border-zinc-500 bg-zinc-700 p-2.5 text-sm text-white placeholder-zinc-300 focus:border-blue-500 focus:ring-blue-500'
              placeholder='Engineer Cost'
              value={engineer[0]?.engineer_cost}
              disabled
            />
          </div> */}

          <div class="relative mt-6">
            <input
              type="text"
              id="total"
              class="border-1 peer block h-[42px] w-20 appearance-none rounded-lg border-zinc-500 bg-zinc-700 bg-zinc-700 px-2.5 pb-2.5 pt-4 text-sm text-white focus:border-blue-600 focus:outline-none focus:ring-0 dark:border-zinc-500 dark:text-white dark:focus:border-blue-500   "
              value={totalCost ?? 0}
              disabled
            />
            <label
              for="total"
              class="absolute start-1 top-2 z-[4]  origin-[0] -translate-y-4 scale-75 transform rounded-[40px] bg-gray-800 px-2 text-sm font-medium text-white duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:px-2  peer-focus:text-blue-500 rtl:peer-focus:left-auto rtl:peer-focus:translate-x-1/4 dark:text-white peer-focus:dark:text-blue-500"
            >
              Total
            </label>
          </div>

          {/* <div className='flex flex-col'>
            <label
              className='block mb-1 text-sm font-normal text-gray-100'
              htmlFor='total'
            >
              Total
            </label>
            <input
              type='text'
              className='w-20 rounded-lg border border-neutral-500 bg-neutral-700 p-2.5 text-sm text-white placeholder-neutral-300 focus:border-blue-500 focus:ring-blue-500'
              placeholder='Total'
              value={totalCost ?? 0}
              disabled
            />
          </div> */}

          <div className="flex flex-col">
            <button
              type="button"
              className="rounded-lg border border-red-600 bg-red-700 px-4 py-2 font-bold text-white hover:bg-red-700"
              onClick={() => {
                setShowIncompleteSubProjectModal(true);
              }}
            >
              Incomplete
            </button>
          </div>
        </div>
      </div>

      {localSelectedSubProjectId === subProject.id && (
        <div className="flex w-full flex-col items-start justify-start gap-4 rounded border border-gray-500 bg-slate-500 p-5 shadow">
          <div className="my-1 flex w-full flex-col items-start justify-start gap-2 border-b-2 pb-2 md:flex-row">
            <div className="w-1/2">
              <div className="flex flex-col">
                <div className="mb-2 text-xl font-semibold">
                  Instrumental Uploads
                </div>
                <div>
                  <label
                    className="mb-2 block cursor-pointer text-sm font-bold"
                    htmlFor="instrumental"
                  >
                    <span className="underline">Upload Instrumental</span>
                    <input
                      id="instrumental"
                      type="file"
                      accept="audio/*"
                      onChange={(e) => handleAudioUpload(e, "instrumental")}
                      className={`focus:shadow-outline hidden w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none`}
                    />
                  </label>
                </div>
                {instrumentalFiles &&
                  instrumentalFiles.length > 0 &&
                  instrumentalFiles.map((file, i) => (
                    <div key={i} className="flex flex-row items-center gap-2">
                      <AudioPlayer fileSource={file.url} />
                      {/* <span>
                        <FontAwesomeIcon
                          icon='fa-solid fa-trash'
                          color='orange'
                          width={24}
                          height={24}
                        />
                      </span> */}
                    </div>
                  ))}
              </div>
            </div>
            <div className="flex w-1/2 flex-col gap-1">
              <div className="mb-2 text-xl font-semibold">Ideas</div>
              <div className="flex flex-row flex-wrap justify-between gap-2">
                <div className="mb-1 flex flex-row gap-4">
                  <span>Idea #1</span>
                  {/* <span>
                    <FontAwesomeIcon icon='fa-solid fa-trash' color='orange' />
                  </span> */}
                </div>
                <div className="mb-1 flex flex-row gap-4">
                  <span>Idea #2</span>
                  {/* <span>
                    <FontAwesomeIcon icon='fa-solid fa-trash' color='orange' />
                  </span> */}
                </div>
                <div className="mb-1 flex flex-row gap-4">
                  <span>Idea #3</span>
                  {/* <span>
                    <FontAwesomeIcon icon='fa-solid fa-trash' color='orange' />
                  </span> */}
                </div>
              </div>
              {/* <button
                className='px-2 py-1 mb-1 w-max text-base font-semibold text-white bg-blue-500 rounded hover:bg-blue-700'
                type='button'
                onClick={() => {
                  setShowAssignIdeaModal(true);
                }}
              >
                Assign New Idea
              </button> */}
            </div>
          </div>

          <div className="my-1 flex w-full flex-col items-start justify-start gap-2 border-b-2 pb-2 md:flex-row">
            <div className="w-1/2">
              <div className="flex flex-col">
                <div className="mb-2 text-xl font-semibold">Demo Files</div>
                <div className="flex flex-row items-center gap-2">
                  <AudioPlayer />
                  {/* <span>
                    <FontAwesomeIcon
                      icon='fa-solid fa-trash'
                      color='orange'
                      width={24}
                      height={24}
                    />
                  </span> */}
                </div>
              </div>
            </div>
            <div className="w-1/2">
              <div className="flex flex-col">
                <div className="mb-2 text-xl font-semibold">Lyrics</div>
                <textarea
                  className="w-full rounded-md border border-gray-500 bg-gray-700 p-2.5 text-sm text-white placeholder-gray-300 focus:border-blue-500 focus:ring-blue-500"
                  placeholder="Lyrics"
                  defaultValue={"test"}
                  disabled
                ></textarea>
                {/* <button
                  type='button'
                  className='px-2 py-1 mt-1 w-max font-medium text-white bg-green-600 rounded hover:bg-green-700'
                >
                  Save
                </button> */}
              </div>
            </div>
          </div>

          <div className="my-1 flex w-full flex-col items-start justify-start gap-2 py-1 md:flex-row">
            <div className="w-1/2">
              <div className="flex flex-col">
                <div className="mb-2 text-xl font-semibold">Session Files</div>
                <div className="flex flex-row items-center gap-2">
                  <AudioPlayer />
                  {/* <span>
                    <FontAwesomeIcon
                      icon='fa-solid fa-trash'
                      color='orange'
                      width={24}
                      height={24}
                    />
                  </span> */}
                </div>
              </div>
            </div>
            <div className="w-1/2">
              <div className="flex flex-col">
                <div className="mb-2 text-xl font-semibold">Master Files</div>
                <div className="flex flex-row items-center gap-2">
                  <AudioPlayer />
                  {/* <span>
                    <FontAwesomeIcon
                      icon='fa-solid fa-trash'
                      color='orange'
                      width={24}
                      height={24}
                    />
                  </span> */}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {showIncompleteSubProjectModal ? (
        <ConfirmModal
          confirmText={`Are you sure you want to incomplete this sub-project?`}
          setModalClose={handleIncompleteSubProjectModalClose}
          setFormYes={handleIncompleteSubProject}
        />
      ) : null}
    </>
  );
};

export default CompletedSubProject;
