import { AuthContext } from "Src/authContext";
import { GlobalContext } from "Src/globalContext";
import { getUserDetailsByIdAPI } from "Src/services/userService";
import React, { useState } from "react";
import { useNavigate } from "react-router";

const ManagerListClientRow = ({ row, columns, i, selectedProducers }) => {
  const { dispatch, state: authState } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [name1, setName1] = useState("");
  const [name2, setName2] = useState("");
  const navigate = useNavigate();

  const member =
    row.members.find((elem) => elem.id == selectedProducers) ||
    row.members?.[0];

  return (
    <tr
      key={i}
      className="hover:bg-gray-800"
      onClick={() => {
        navigate(`/${authState.role}/view-client/` + row.id, {
          state: row,
        });
      }}
    >
      {columns.map((cell, index) => {
        if (cell.mappingExist) {
          return (
            <td key={index} className="whitespace-nowrap px-6 py-4">
              {cell.mappings[row[cell.accessor]]}
            </td>
          );
        }
        if (cell.accessor === "member_name") {
          return (
            <td key={index} className="whitespace-nowrap px-6 py-4">
              <div className="flex flex-col gap-[2px]">
                <span>{member?.name}</span>
              </div>
            </td>
          );
        }
        return (
          <td
            key={index}
            className={`whitespace-nowrap px-6 py-4 ${
              index == 0 && "2xl:pl-9"
            }`}
          >
            {row[cell.accessor]}
          </td>
        );
      })}
    </tr>
  );
};

export default ManagerListClientRow;
