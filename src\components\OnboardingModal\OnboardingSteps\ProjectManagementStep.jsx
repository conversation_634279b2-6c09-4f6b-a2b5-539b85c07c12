import React, { useState, useEffect, useContext } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { ClipLoader } from "react-spinners";
import { GlobalContext, showToast } from "../../../globalContext";
import {
  retrieveAllSettingsAPI,
  createOrUpdateAllSettingsAPI,
} from "Src/services/settingService";
import { AuthContext, tokenExpireError } from "Src/authContext";

// Validation schema
const schema = yup.object().shape({
  management_fee_applicable: yup.boolean(),
  management_value: yup
    .number()
    .min(0, "Management value must be at least 0")
    .when("management_fee_applicable", {
      is: true,
      then: (schema) =>
        schema.required(
          "Management value is required when management fee is applicable"
        ),
      otherwise: (schema) => schema.notRequired(),
    }),
  management_value_type: yup
    .string()
    .oneOf(["%", "$"], "Management value type must be % or $")
    .when("management_fee_applicable", {
      is: true,
      then: (schema) =>
        schema.required(
          "Management value type is required when management fee is applicable"
        ),
      otherwise: (schema) => schema.notRequired(),
    }),
  artist_deadline_days: yup
    .number()
    .min(1, "Must be at least 1 day")
    .max(30, "Cannot exceed 30 days")
    .required("Artist deadline is required"),
  engineer_deadline_days: yup
    .number()
    .min(1, "Must be at least 1 day")
    .max(30, "Cannot exceed 30 days")
    .required("Engineer deadline is required"),
  artist_engineer_deadline_days: yup
    .number()
    .min(1, "Must be at least 1 day")
    .max(30, "Cannot exceed 30 days")
    .required("Artist/Engineer deadline is required"),
  auto_approve_writer_submissions: yup.boolean(),
});

const ProjectManagementStep = ({
  stepData,
  userDetails,
  onComplete,
  onSkip,
}) => {
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const { dispatch } = useContext(AuthContext);
  const [settings, setSettings] = React.useState([]);
  const [managementValue, setManagementValue] = React.useState("");
  const [managementValueType, setManagementValueType] = React.useState("");
  const [artistDeadline, setArtistDeadline] = React.useState("");
  const [engineerDeadline, setEngineerDeadline] = React.useState("");
  const [artistEngineerDeadline, setArtistEngineerDeadline] =
    React.useState("");
  const [autoApprove, setAutoApprove] = React.useState(0);
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      management_fee_applicable: false,
      management_value: 0,
      management_value_type: "%",
      artist_deadline_days: 3,
      engineer_deadline_days: 3,
      artist_engineer_deadline_days: 3,
      auto_approve_writer_submissions: false,
    },
  });

  // Watch management fee applicable to show/hide management fee fields
  const managementFeeApplicable = watch("management_fee_applicable");

  useEffect(() => {
    // Set initial values from userDetails if they exist
    if (userDetails) {
      (async () => {
        try {
          const result = await retrieveAllSettingsAPI();
          if (!result.error) {
            setSettings(result.list);
            // set all settings that matches with SETTING_KEYS
            if (result.list.length > 0) {
              let retrievedManagementValue = "";
              let retrievedManagementValueType = "%";
              let retrievedArtistDeadline = 3;
              let retrievedEngineerDeadline = 3;
              let retrievedArtistEngineerDeadline = 3;
              let retrievedAutoApprove = false;

              result.list.forEach((row) => {
                if (row.setting_key === "management_value") {
                  setManagementValue(row.setting_value);
                  retrievedManagementValue = row.setting_value;
                }
                if (row.setting_key === "management_value_type") {
                  setManagementValueType(row.setting_value);
                  retrievedManagementValueType = row.setting_value;
                }
                if (row.setting_key === "artist_deadline") {
                  setArtistDeadline(row.setting_value);
                  retrievedArtistDeadline = parseInt(row.setting_value) || 3;
                }
                if (row.setting_key === "engineer_deadline") {
                  setEngineerDeadline(row.setting_value);
                  retrievedEngineerDeadline = parseInt(row.setting_value) || 3;
                }
                if (row.setting_key === "artist_engineer_deadline") {
                  setArtistEngineerDeadline(row.setting_value);
                  retrievedArtistEngineerDeadline =
                    parseInt(row.setting_value) || 3;
                }
                if (row.setting_key === "auto_approve") {
                  setAutoApprove(row.setting_value);
                  retrievedAutoApprove =
                    row.setting_value === "1" || row.setting_value === 1;
                }
              });

              // Set form values from retrieved settings
              setValue(
                "management_fee_applicable",
                retrievedManagementValue ? true : false
              );
              setValue(
                "management_value",
                parseFloat(retrievedManagementValue) || 0
              );
              setValue("management_value_type", retrievedManagementValueType);
              setValue("artist_deadline_days", retrievedArtistDeadline);
              setValue("engineer_deadline_days", retrievedEngineerDeadline);
              setValue(
                "artist_engineer_deadline_days",
                retrievedArtistEngineerDeadline
              );
              setValue("auto_approve_writer_submissions", retrievedAutoApprove);
            }
          }
        } catch (error) {
          console.error("Error retrieving settings:", error);
          tokenExpireError(dispatch, error.message);
        }
      })();
    }
  }, [userDetails, setValue, dispatch]);

  const onSubmit = async (data) => {
    try {
      setIsLoading(true);

      // Prepare settings payload for the API
      const settingsPayload = {
        settings: [
          {
            setting_key: "management_value",
            setting_value: data.management_value?.toString() ?? "0",
          },
          {
            setting_key: "management_value_type",
            setting_value: data.management_value_type ?? "%",
          },
          {
            setting_key: "artist_deadline",
            setting_value: data.artist_deadline_days?.toString() ?? "3",
          },
          {
            setting_key: "engineer_deadline",
            setting_value: data.engineer_deadline_days?.toString() ?? "3",
          },
          {
            setting_key: "artist_engineer_deadline",
            setting_value:
              data.artist_engineer_deadline_days?.toString() ?? "3",
          },
          {
            setting_key: "voiceover_eight_count",
            setting_value: "4", // Default value
          },
          {
            setting_key: "song_eight_count",
            setting_value: "8", // Default value
          },
          {
            setting_key: "tracking_eight_count",
            setting_value: "4", // Default value
          },
          {
            setting_key: "auto_approve",
            setting_value: data.auto_approve_writer_submissions ? "1" : "0",
          },
        ],
      };

      // Save settings using the API
      const result = await createOrUpdateAllSettingsAPI(settingsPayload);
      if (!result.error) {
        showToast(
          globalDispatch,
          result.message || "Settings saved successfully",
          4000
        );

        // Prepare project management data for the onboarding flow
        const projectManagementData = {
          survey: JSON.stringify({
            management_fee_applicable: data.management_fee_applicable,
            management_value: data.management_value,
            management_value_type: data.management_value_type,
            artist_deadline_days: parseInt(data.artist_deadline_days),
            engineer_deadline_days: parseInt(data.engineer_deadline_days),
            artist_engineer_deadline_days: parseInt(
              data.artist_engineer_deadline_days
            ),
            auto_approve_writer_submissions:
              data.auto_approve_writer_submissions,
          }),
        };

        onComplete(projectManagementData);
      } else {
        showToast(
          globalDispatch,
          result.message || "Failed to save project management settings",
          4000,
          "error"
        );
      }
    } catch (error) {
      console.error("Error saving project management settings:", error);
      tokenExpireError(dispatch, error.message);
      showToast(
        globalDispatch,
        "Failed to save project management settings",
        4000,
        "error"
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="mx-auto max-w-[420px] space-y-6">
      {/* Header */}
      <div className="text-center">
        <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-lg bg-blue-50">
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M9 12H15M9 16H15M17 21H7C5.89543 21 5 20.1046 5 19V5C5 3.89543 5.89543 3 7 3H12.5858C12.851 3 13.1054 3.10536 13.2929 3.29289L18.7071 8.70711C18.8946 8.89464 19 9.149 19 9.41421V19C19 20.1046 18.1046 21 17 21Z"
              stroke="#3B82F6"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>
        <h2 className="text-xl font-semibold text-gray-900">
          Project Management Settings
        </h2>
        <p className="mt-1 text-sm text-[#667484]">
          Streamline your project workflow.
        </p>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Management Fee Configuration */}
        <div className="space-y-3">
          <div>
            <h3 className="text-base font-semibold text-[#3C50E0]">
              Management Fee Configuration
            </h3>
          </div>

          <div className="flex items-start gap-3">
            <input
              type="checkbox"
              id="management_fee_applicable"
              {...register("management_fee_applicable")}
              className="mt-1 h-4 w-4 rounded border-[#B7BEC8] text-[#3C50E0] focus:ring-blue-500"
            />
            <label
              htmlFor="management_fee_applicable"
              className="text-sm text-gray-900"
            >
              Is a management fee applicable to your projects?
            </label>
          </div>

          {/* Management Fee Value and Type - Show only when applicable */}
          {managementFeeApplicable && (
            <div className="space-y-3 pl-7">
              <div className="rounded-lg border border-gray-200 bg-white p-4">
                <div className="space-y-3">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">
                      Management Fee Details
                    </h4>
                    <p className="text-sm text-[#667484]">
                      Specify the management fee value and type.
                    </p>
                  </div>

                  <div className="flex gap-3">
                    {/* Management Value */}
                    <div className="flex-1">
                      <label className="mb-1 block text-sm font-medium text-gray-700">
                        Value
                      </label>
                      <input
                        type="number"
                        min="0"
                        step="0.01"
                        {...register("management_value")}
                        className="w-full rounded-[8px] border border-[#D5D7DA] px-3 py-2 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500"
                        placeholder="Enter value"
                      />
                      {errors.management_value && (
                        <p className="mt-1 text-sm text-red-500">
                          {errors.management_value.message}
                        </p>
                      )}
                    </div>

                    {/* Management Value Type */}
                    <div className="w-20">
                      <label className="mb-1 block text-sm font-medium text-gray-700">
                        Type
                      </label>
                      <select
                        {...register("management_value_type")}
                        className="black-select w-full rounded-[8px] border border-[#D5D7DA] px-3 py-2 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500"
                      >
                        <option value="%">%</option>
                        <option value="$">$</option>
                      </select>
                      {errors.management_value_type && (
                        <p className="mt-1 text-sm text-red-500">
                          {errors.management_value_type.message}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Team Deadline Defaults */}
        <div className="space-y-4">
          <div>
            <h3 className="text-base font-semibold text-[#3C50E0]">
              Team Deadline Defaults (Days)
            </h3>
            <p className="text-sm text-[#667484]">
              Automate default creative deadlines for new work orders, based on
              Writer's submission.
            </p>
          </div>

          {/* Artist Deadline */}
          <div className="rounded-lg border border-gray-200 bg-white p-4 py-2">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <h4 className="text-base font-medium text-gray-900">Artist</h4>
                <p className="text-sm text-[#667484]">
                  Number of days for the task to be due.
                </p>
              </div>
              <div className="relative flex h-[42px] w-[90px] items-center gap-3 rounded-[8px] border border-[#D5D7DA] bg-white">
                <input
                  type="number"
                  min="1"
                  max="30"
                  {...register("artist_deadline_days")}
                  className="h-full w-full rounded-[8px]  border border-transparent px-1 py-1 pl-5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500"
                />

                <div className="group absolute right-2">
                  <svg
                    className="cursor-help"
                    width="16"
                    height="16"
                    viewBox="0 0 16 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g clipPath="url(#clip0_3161_3327)">
                      <path
                        d="M6.06004 6.00016C6.21678 5.55461 6.52614 5.1789 6.93334 4.93958C7.34055 4.70027 7.8193 4.61279 8.28483 4.69264C8.75035 4.77249 9.17259 5.01451 9.47676 5.37585C9.78093 5.73718 9.94741 6.19451 9.94671 6.66683C9.94671 8.00016 7.94671 8.66683 7.94671 8.66683M8.00004 11.3335H8.00671M14.6667 8.00016C14.6667 11.6821 11.6819 14.6668 8.00004 14.6668C4.31814 14.6668 1.33337 11.6821 1.33337 8.00016C1.33337 4.31826 4.31814 1.3335 8.00004 1.3335C11.6819 1.3335 14.6667 4.31826 14.6667 8.00016Z"
                        stroke="#9BA5B4"
                        strokeWidth="1.33333"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </g>
                    <defs>
                      <clipPath id="clip0_3161_3327">
                        <rect width="16" height="16" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>
                  {/* Tooltip */}
                  <div className="absolute bottom-full right-0 z-50 mb-2 hidden w-64 rounded-lg bg-gray-900 px-3 py-2 text-xs text-white shadow-lg group-hover:block">
                    Days given to the artist to complete recording tasks
                    (vocals, instruments, etc.) after writer submission is
                    approved. This deadline is used when the artist and engineer
                    are different people.
                    <div className="absolute right-4 top-full h-0 w-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
                  </div>
                </div>
              </div>
            </div>
            {errors.artist_deadline_days && (
              <p className="mt-2 text-sm text-red-500">
                {errors.artist_deadline_days.message}
              </p>
            )}
          </div>

          {/* Engineer Deadline */}
          <div className="rounded-lg border border-gray-200 bg-white p-4 py-2">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <h4 className="text-base font-medium text-gray-900">
                  Engineer
                </h4>
                <p className="text-sm text-[#667484]">
                  Number of days for tasks to be due.
                </p>
              </div>
              <div className="relative flex h-[42px] w-[90px] items-center gap-3 rounded-[8px] border border-[#D5D7DA] bg-white">
                <input
                  type="number"
                  min="1"
                  max="30"
                  {...register("engineer_deadline_days")}
                  className="h-full w-full rounded-[8px]  border border-transparent px-1 py-1 pl-5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500"
                />
                <div className="group absolute right-2">
                  <svg
                    className="cursor-help"
                    width="16"
                    height="16"
                    viewBox="0 0 16 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g clipPath="url(#clip0_3161_3327_engineer)">
                      <path
                        d="M6.06004 6.00016C6.21678 5.55461 6.52614 5.1789 6.93334 4.93958C7.34055 4.70027 7.8193 4.61279 8.28483 4.69264C8.75035 4.77249 9.17259 5.01451 9.47676 5.37585C9.78093 5.73718 9.94741 6.19451 9.94671 6.66683C9.94671 8.00016 7.94671 8.66683 7.94671 8.66683M8.00004 11.3335H8.00671M14.6667 8.00016C14.6667 11.6821 11.6819 14.6668 8.00004 14.6668C4.31814 14.6668 1.33337 11.6821 1.33337 8.00016C1.33337 4.31826 4.31814 1.3335 8.00004 1.3335C11.6819 1.3335 14.6667 4.31826 14.6667 8.00016Z"
                        stroke="#9BA5B4"
                        strokeWidth="1.33333"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </g>
                    <defs>
                      <clipPath id="clip0_3161_3327_engineer">
                        <rect width="16" height="16" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>
                  {/* Tooltip */}
                  <div className="absolute bottom-full right-0 z-50 mb-2 hidden w-64 rounded-lg bg-gray-900 px-3 py-2 text-xs text-white shadow-lg group-hover:block">
                    Days given to the engineer to complete mixing, mastering,
                    and post-production tasks after the artist has finished
                    recording. This applies when artist and engineer roles are
                    separate.
                    <div className="absolute right-4 top-full h-0 w-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
                  </div>
                </div>
              </div>
            </div>
            {errors.engineer_deadline_days && (
              <p className="mt-2 text-sm text-red-500">
                {errors.engineer_deadline_days.message}
              </p>
            )}
          </div>

          {/* Artist/Engineer Deadline */}
          <div className="rounded-lg border border-gray-200 bg-white p-4 py-2">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <h4 className="text-base font-medium text-gray-900">
                  Artist/Engineer
                </h4>
                <p className="text-sm text-[#667484]">
                  Number of days for tasks to be due.
                </p>
              </div>
              <div className="relative flex h-[42px] w-[90px] items-center gap-3 rounded-[8px] border border-[#D5D7DA] bg-white">
                <input
                  type="number"
                  min="1"
                  max="30"
                  {...register("artist_engineer_deadline_days")}
                  className="h-full w-full rounded-[8px]  border border-transparent px-1 py-1 pl-5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500"
                />
                <div className="group absolute right-2">
                  <svg
                    className="cursor-help"
                    width="16"
                    height="16"
                    viewBox="0 0 16 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g clipPath="url(#clip0_3161_3327_artist_engineer)">
                      <path
                        d="M6.06004 6.00016C6.21678 5.55461 6.52614 5.1789 6.93334 4.93958C7.34055 4.70027 7.8193 4.61279 8.28483 4.69264C8.75035 4.77249 9.17259 5.01451 9.47676 5.37585C9.78093 5.73718 9.94741 6.19451 9.94671 6.66683C9.94671 8.00016 7.94671 8.66683 7.94671 8.66683M8.00004 11.3335H8.00671M14.6667 8.00016C14.6667 11.6821 11.6819 14.6668 8.00004 14.6668C4.31814 14.6668 1.33337 11.6821 1.33337 8.00016C1.33337 4.31826 4.31814 1.3335 8.00004 1.3335C11.6819 1.3335 14.6667 4.31826 14.6667 8.00016Z"
                        stroke="#9BA5B4"
                        strokeWidth="1.33333"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </g>
                    <defs>
                      <clipPath id="clip0_3161_3327_artist_engineer">
                        <rect width="16" height="16" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>
                  {/* Tooltip */}
                  <div className="absolute bottom-full right-0 z-50 mb-2 hidden w-64 rounded-lg bg-gray-900 px-3 py-2 text-xs text-white shadow-lg group-hover:block">
                    Days given to complete both recording AND engineering tasks
                    when the same person handles both roles. This covers the
                    entire production process from recording to final master.
                    <div className="absolute right-4 top-full h-0 w-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
                  </div>
                </div>
              </div>
            </div>
            {errors.artist_engineer_deadline_days && (
              <p className="mt-2 text-sm text-red-500">
                {errors.artist_engineer_deadline_days.message}
              </p>
            )}
          </div>
        </div>

        {/* Writer Submission Approval */}
        <div className="space-y-3">
          <div>
            <h3 className="text-base font-semibold text-[#3C50E0]">
              Writer Submission Approval
            </h3>
          </div>

          <div className="space-y-2">
            <div className="flex items-start gap-3">
              <input
                type="checkbox"
                id="auto_approve_writer_submissions"
                {...register("auto_approve_writer_submissions")}
                className="mt-1 h-4 w-4 rounded border-[#B7BEC8] text-[#3C50E0] focus:ring-blue-500"
              />
              <div>
                <label
                  htmlFor="auto_approve_writer_submissions"
                  className="text-sm font-medium text-gray-900"
                >
                  Auto-Approve Writer Submissions
                </label>
                <p className="text-sm text-[#667484]">
                  Enable to automatically approve Writer submissions, bypassing
                  manual review.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons - cesntered at bottom */}
        <div className="mt-auto flex justify-center gap-4 pt-8">
          <button
            type="button"
            onClick={onSkip}
            disabled={isLoading}
            className="ite flex h-[44px] w-[200px] justify-center rounded-lg border border-[#D5D7DA] px-8 py-3 font-medium text-[#3f4d5d] transition-colors hover:bg-white/80 disabled:cursor-not-allowed disabled:opacity-50"
          >
            Skip for Now
          </button>
          <button
            type="submit"
            disabled={isLoading}
            className="flex h-[44px] w-[200px] items-center justify-center gap-2 rounded-lg bg-[#3C50E0] px-8 py-3 font-medium text-white transition-colors hover:bg-primary/90 disabled:cursor-not-allowed disabled:opacity-50"
          >
            {isLoading ? (
              <>
                <ClipLoader size={16} color="#ffffff" />
                Saving...
              </>
            ) : (
              "Continue"
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ProjectManagementStep;
