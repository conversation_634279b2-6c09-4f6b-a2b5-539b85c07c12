import{g as Le,d as Se}from"./vendor-94843817.js";const we={rtl:{italic:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10.5 15.8" xml:space="preserve"><g><path d="M0.3,0.1c0.3,0,0.5,0,0.7,0c1,0.1,1.7,0.1,2.2,0.1H4L7.2,0l0.2,1.1H7c-0.5,0-1,0.1-1.5,0.3v0.4l0.3,1.9L6,4.4L6.3,6 l0.1,0.4l0.1,0.5c0.1,0.2,0.1,0.4,0.2,0.7s0.1,0.6,0.2,0.9L7,9.1l0.6,2.8l0.3,1.4c0.1,0.4,0.2,0.7,0.4,1c0.4,0.2,0.8,0.3,1.2,0.4 l0.8,0.2l0.2,0.9l-1.1,0c-0.9-0.1-1.5-0.1-1.8-0.1h-2c-0.9,0.1-1.4,0.2-1.5,0.2c-0.1,0-0.2,0-0.3,0H3.4c-0.1,0-0.2,0-0.2,0 l-0.1-0.4c0-0.2-0.1-0.4-0.1-0.6l0.7-0.1c0.4,0,0.8-0.1,1.2-0.2c0-0.1,0-0.2,0-0.3l-0.1-0.5l-0.4-2.4L4,9.6L3.4,6.4 C3.2,5.7,3,4.7,2.7,3.3c0-0.3-0.1-0.5-0.1-0.8C2.5,2.1,2.4,1.9,2.3,1.6C2,1.4,1.6,1.3,1.3,1.2C0.9,1.2,0.5,1.1,0.2,0.9L0,0.4L0,0 L0.3,0.1L0.3,0.1z"/></g></svg>',indent:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.7 12.4" xml:space="preserve"><g><g><path d="M15.5,10.1L15.5,10.1c0.1,0,0.3,0.1,0.3,0.3v1.7c0,0.1,0,0.1-0.1,0.2c-0.1,0.1-0.1,0.1-0.2,0.1l-15.2,0 c-0.1,0-0.1,0-0.2-0.1C0,12.2,0,12.2,0,12.1l0-1.7c0-0.1,0-0.1,0.1-0.2c0.1-0.1,0.1-0.1,0.2-0.1C0.3,10.1,15.5,10.1,15.5,10.1z M9.8,6.7c0.1,0,0.1,0,0.2,0.1C10.1,6.9,10.1,7,10.1,7v1.7c0,0.1,0,0.2-0.1,0.2C10,9,9.9,9,9.8,9L0.3,9C0.2,9,0.1,9,0.1,8.9 C0,8.9,0,8.8,0,8.7V7C0,7,0,6.9,0.1,6.8c0.1-0.1,0.1-0.1,0.2-0.1C0.3,6.7,9.8,6.7,9.8,6.7z M0.3,3.4h9.6h0c0.1,0,0.3,0.1,0.3,0.3 v1.7v0c0,0.1-0.1,0.3-0.3,0.3H0.3c-0.1,0-0.1,0-0.2-0.1C0,5.5,0,5.4,0,5.3V3.6c0-0.1,0-0.1,0.1-0.2C0.1,3.4,0.2,3.4,0.3,3.4 L0.3,3.4z M0.3,0l15.2,0c0.1,0,0.1,0,0.2,0.1c0.1,0.1,0.1,0.1,0.1,0.2V2c0,0.1,0,0.2-0.1,0.2c-0.1,0.1-0.1,0.1-0.2,0.1H0.3 c-0.1,0-0.1,0-0.2-0.1C0,2.1,0,2,0,2l0-1.7c0-0.1,0-0.1,0.1-0.2C0.1,0,0.2,0,0.3,0z"/></g><path d="M13.1,3.5L15.7,6c0.1,0.1,0.1,0.3,0,0.4l-2.5,2.5C13.1,9,13,9,12.9,9c-0.1,0-0.1,0-0.2-0.1c-0.1-0.1-0.1-0.1-0.1-0.2V3.7 c0-0.1,0-0.2,0.1-0.2c0.1-0.1,0.1-0.1,0.2-0.1C13,3.4,13.1,3.4,13.1,3.5z"/></g></svg>',outdent:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.7 12.4" xml:space="preserve"><g><g><path d="M15.5,10.1L15.5,10.1c0.1,0,0.3,0.1,0.3,0.3v1.7c0,0.1,0,0.1-0.1,0.2c-0.1,0.1-0.1,0.1-0.2,0.1l-15.2,0 c-0.1,0-0.1,0-0.2-0.1C0,12.2,0,12.2,0,12.1l0-1.7c0-0.1,0-0.1,0.1-0.2c0.1-0.1,0.1-0.1,0.2-0.1C0.3,10.1,15.5,10.1,15.5,10.1z M9.8,6.7c0.1,0,0.1,0,0.2,0.1C10.1,6.9,10.1,7,10.1,7v1.7c0,0.1,0,0.2-0.1,0.2C10,9,9.9,9,9.8,9L0.3,9C0.2,9,0.1,9,0.1,8.9 C0,8.9,0,8.8,0,8.7V7C0,7,0,6.9,0.1,6.8c0.1-0.1,0.1-0.1,0.2-0.1C0.3,6.7,9.8,6.7,9.8,6.7z M0.3,3.4h9.6h0c0.1,0,0.3,0.1,0.3,0.3 v1.7v0c0,0.1-0.1,0.3-0.3,0.3H0.3c-0.1,0-0.1,0-0.2-0.1C0,5.5,0,5.4,0,5.3V3.6c0-0.1,0-0.1,0.1-0.2C0.1,3.4,0.2,3.4,0.3,3.4 L0.3,3.4z M0.3,0l15.2,0c0.1,0,0.1,0,0.2,0.1c0.1,0.1,0.1,0.1,0.1,0.2V2c0,0.1,0,0.2-0.1,0.2c-0.1,0.1-0.1,0.1-0.2,0.1H0.3 c-0.1,0-0.1,0-0.2-0.1C0,2.1,0,2,0,2l0-1.7c0-0.1,0-0.1,0.1-0.2C0.1,0,0.2,0,0.3,0z"/></g><path d="M15.5,3.4c0.1,0,0.1,0,0.2,0.1c0.1,0.1,0.1,0.1,0.1,0.2v5.1c0,0.1,0,0.1-0.1,0.2C15.6,9,15.5,9,15.5,9 c-0.1,0-0.1,0-0.2-0.1l-2.5-2.5c-0.1-0.1-0.1-0.3,0-0.4l2.5-2.5C15.3,3.4,15.4,3.4,15.5,3.4z"/></g></svg>',list_bullets:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.7 12.4" xml:space="preserve"><g><path d="M12.4,10.7c0,0.9,0.8,1.7,1.7,1.7c0.9,0,1.7-0.8,1.7-1.7C15.7,9.8,15,9,14.1,9c-0.4,0-0.9,0.2-1.2,0.5 C12.5,9.8,12.4,10.2,12.4,10.7C12.4,10.7,12.4,10.7,12.4,10.7z M12.4,6.2c0,0.9,0.8,1.7,1.7,1.7c0.4,0,0.9-0.2,1.2-0.5 c0.3-0.3,0.4-0.7,0.4-1.1c0-0.9-0.7-1.7-1.6-1.7C13.1,4.6,12.4,5.3,12.4,6.2C12.4,6.2,12.4,6.2,12.4,6.2z M0,9.8v1.7 c0,0.1,0,0.1,0.1,0.2c0.1,0.1,0.1,0.1,0.2,0.1l10.7,0c0,0,0,0,0,0c0.1,0,0.3-0.1,0.3-0.3V9.8c0-0.1,0-0.1-0.1-0.2 C11.1,9.6,11,9.6,11,9.6l-10.7,0c-0.1,0-0.1,0-0.2,0.1C0,9.7,0,9.8,0,9.8L0,9.8z M12.9,2.9c0.3,0.3,0.7,0.5,1.2,0.5 c0.4,0,0.9-0.2,1.2-0.5c0.7-0.7,0.7-1.7,0-2.4C14.9,0.2,14.5,0,14.1,0c-0.4,0-0.9,0.2-1.2,0.5c-0.3,0.3-0.5,0.7-0.5,1.2 C12.4,2.1,12.5,2.6,12.9,2.9z M0,5.3V7c0,0.1,0,0.1,0.1,0.2c0.1,0.1,0.1,0.1,0.2,0.1H11c0.1,0,0.1,0,0.2-0.1 c0.1-0.1,0.1-0.1,0.1-0.2V5.3c0,0,0,0,0,0c0-0.1-0.1-0.3-0.3-0.3H0.3c-0.1,0-0.1,0-0.2,0.1C0,5.2,0,5.3,0,5.3L0,5.3z M0,0.8v1.7 c0,0.1,0,0.1,0.1,0.2c0.1,0.1,0.1,0.1,0.2,0.1h10.7c0.1,0,0.1,0,0.2-0.1c0,0,0.1-0.1,0.1-0.2V0.8c0-0.1,0-0.1-0.1-0.2 c0-0.1-0.1-0.1-0.2-0.1H0.3c-0.1,0-0.1,0-0.2,0.1C0,0.7,0,0.8,0,0.8L0,0.8z"/></g></svg>',list_number:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.7 15.7" xml:space="preserve"><g><path d="M0,11.5l0,1.7c0,0.1,0,0.1,0.1,0.2c0.1,0.1,0.1,0.1,0.2,0.1H11c0.1,0,0.2,0,0.2-0.1c0.1-0.1,0.1-0.1,0.1-0.2v-1.7 c0-0.1,0-0.1-0.1-0.2c-0.1-0.1-0.1-0.1-0.2-0.1H0.3c-0.1,0-0.2,0-0.2,0.1C0,11.4,0,11.4,0,11.5L0,11.5z M0,8.7c0,0.1,0,0.1,0.1,0.2 C0.1,8.9,0.2,9,0.3,9H11c0.1,0,0.2,0,0.2-0.1c0.1-0.1,0.1-0.1,0.1-0.2V7c0-0.1,0-0.1-0.1-0.2c-0.1-0.1-0.1-0.1-0.2-0.1l-10.7,0 c-0.1,0-0.2,0-0.2,0.1C0,6.8,0,6.9,0,7C0,7,0,8.7,0,8.7z M0,2.5v1.7c0,0.1,0,0.1,0.1,0.2c0,0,0.1,0.1,0.2,0.1l10.7,0 c0.1,0,0.2,0,0.2-0.1c0.1-0.1,0.1-0.1,0.1-0.2V2.4c0-0.1,0-0.1-0.1-0.2c-0.1,0-0.1,0-0.2,0H0.3c-0.1,0-0.1,0-0.2,0 C0,2.3,0,2.4,0,2.5L0,2.5z"/></g><path d="M15.6,14.2c0-0.3-0.1-0.6-0.3-0.8c-0.2-0.2-0.4-0.4-0.7-0.4l0.9-1v-0.8h-2.9v1.3h0.9v-0.5h0.9l0,0c-0.1,0.1-0.2,0.2-0.3,0.3 s-0.2,0.3-0.4,0.5l-0.3,0.3l0.2,0.5c0.6,0,0.9,0.1,0.9,0.5c0,0.1-0.1,0.3-0.2,0.4c-0.1,0.1-0.3,0.1-0.4,0.1c-0.3,0-0.7-0.1-0.9-0.3 l-0.5,0.8c0.4,0.4,0.9,0.6,1.5,0.6c0.4,0,0.9-0.1,1.2-0.4C15.5,15.1,15.6,14.7,15.6,14.2z"/><path d="M15.6,8.7h-0.9v0.5h-1.1c0-0.2,0.2-0.4,0.4-0.5c0.2-0.2,0.4-0.3,0.7-0.4c0.3-0.2,0.5-0.3,0.7-0.6c0.2-0.2,0.3-0.5,0.3-0.8 c0-0.4-0.2-0.8-0.5-1c-0.6-0.4-1.4-0.5-2-0.1c-0.3,0.2-0.5,0.4-0.6,0.7L13.3,7c0.1-0.3,0.4-0.5,0.7-0.5c0.1,0,0.3,0,0.3,0.1 c0.1,0.1,0.1,0.2,0.1,0.3c0,0.2-0.1,0.3-0.2,0.4c-0.2,0.1-0.3,0.3-0.5,0.4c-0.2,0.1-0.4,0.3-0.6,0.4c-0.2,0.2-0.4,0.4-0.5,0.6 c-0.1,0.2-0.2,0.5-0.2,0.8c0,0.2,0,0.3,0,0.5h3.2L15.6,8.7L15.6,8.7z"/><path d="M15.6,3.6h-1V0h-0.9l-1.2,1.1l0.6,0.7c0.2-0.1,0.3-0.3,0.4-0.5l0,0v2.2h-0.9v0.9h3L15.6,3.6L15.6,3.6z"/></svg>',link:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.7 15.7" xml:space="preserve"><g><path d="M7.4,9.9l3.1,3.1c0.3,0.3,0.8,0.5,1.3,0.5c0.5,0,0.9-0.2,1.3-0.5c0,0,0,0,0,0c0.7-0.7,0.7-1.9,0-2.6L9.9,7.3 c0-0.1,0-0.2,0-0.3C9.9,7,10,7,10.1,7l2.2-0.2c0.1,0,0.1,0,0.2,0.1l2.1,2.1c0.4,0.4,0.7,0.8,0.9,1.3c0.2,0.5,0.3,1,0.3,1.5 c0,0.5-0.1,1-0.3,1.5c-0.8,2-3.2,3-5.2,2.2c-0.5-0.2-0.9-0.5-1.3-0.9l-2.1-2.1c-0.1,0-0.1-0.1-0.1-0.2L7,10.1C7,10,7,9.9,7.1,9.9 C7.2,9.8,7.3,9.9,7.4,9.9z M1.2,1.1C1.6,0.7,2,0.4,2.5,0.3c1-0.4,2.1-0.4,3.1,0C6,0.4,6.5,0.7,6.8,1.1L9,3.2C9,3.3,9.1,3.3,9,3.4 L8.8,5.6c0,0.1-0.1,0.2-0.2,0.2c-0.1,0.1-0.2,0.1-0.3,0L5.3,2.7C5,2.3,4.5,2.1,4,2.1c-0.5,0-0.9,0.2-1.3,0.5c0,0,0,0,0,0 C2,3.4,2,4.5,2.7,5.2l3.1,3.2c0.1,0.1,0.1,0.2,0,0.3c0,0.1-0.1,0.1-0.2,0.1L3.5,9C3.4,9,3.4,9,3.3,8.9L1.2,6.8c0,0,0,0,0,0 C-0.4,5.2-0.4,2.7,1.2,1.1L1.2,1.1z M14.3,6h-2.6c0,0,0,0,0,0c-0.1,0-0.2-0.1-0.2-0.2c0-0.1,0-0.2,0.1-0.3l2.5-0.7 c0.1,0,0.1,0,0.2,0c0.1,0,0.1,0.1,0.1,0.2l0.1,0.8c0,0.1,0,0.1-0.1,0.2C14.5,6,14.4,6,14.3,6L14.3,6z M10.2,4.1 c0,0.1-0.1,0.2-0.2,0.2l0,0c0,0,0,0,0,0C9.8,4.2,9.7,4.1,9.8,4L9.7,1.4c0-0.1,0-0.1,0.1-0.2c0.1,0,0.1,0,0.2,0h0.8 c0.1,0,0.1,0,0.2,0.1c0,0.1,0,0.1,0,0.2L10.2,4.1L10.2,4.1z M1.5,9.7h1.3h1.3c0.1,0,0.2,0.1,0.2,0.2c0,0.1,0,0.2-0.1,0.3l-2.5,0.6 H1.6c0,0-0.1,0-0.1,0c-0.1,0-0.1-0.1-0.1-0.2L1.2,9.9c0-0.1,0-0.1,0.1-0.2c0-0.1,0.1-0.1,0.2-0.1L1.5,9.7z M5.6,11.6 C5.6,11.6,5.6,11.6,5.6,11.6c0-0.1,0.1-0.2,0.3-0.1c0,0,0,0,0,0c0.1,0,0.2,0.1,0.2,0.2v2.6c0,0.1,0,0.1-0.1,0.2 c0,0-0.1,0.1-0.2,0.1L5,14.5c-0.1,0-0.1,0-0.2-0.1c0-0.1,0-0.1,0-0.2L5.6,11.6L5.6,11.6z"/></g></svg>',unlink:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.7 15.7" xml:space="preserve"><g><path d="M14.6,14.6c1.6-1.6,1.6-4.1,0-5.7l0,0l-3.1-3.1l-1.2,1.6l2.9,2.9c0.4,0.4,0.6,0.9,0.6,1.5c0,1.1-0.9,2.1-2.1,2.1l0,0 c-0.6,0-1.1-0.2-1.5-0.6l-0.4-0.4l-1.7,1l0.8,0.8C10.4,16.2,13,16.2,14.6,14.6L14.6,14.6L14.6,14.6z M3.6,6C3,5.9,2.6,5.5,2.3,5 S1.9,4,2.1,3.4C2.3,2.9,2.6,2.5,3,2.2C3.5,2,4.1,1.9,4.6,2l3.3,1.4l0.5-2L5.1,0.1C4-0.1,2.9,0,2,0.5C1.1,1.1,0.4,1.9,0.2,3 C-0.1,4,0,5.1,0.6,6C1.1,6.9,1.9,7.6,3,7.8l5.4,2l0.5-2L6.2,6.9L3.6,6z"/></g></svg>'},redo:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.59 14.18"><g><path d="M11.58,18.48a6.84,6.84,0,1,1,6.85-6.85s0,.26,0,.67a8,8,0,0,1-.22,1.44l.91-.55a.51.51,0,0,1,.36,0,.45.45,0,0,1,.29.22.47.47,0,0,1,.06.36.45.45,0,0,1-.22.29L17.42,15.3l-.12,0h-.25l-.12-.06-.09-.09-.06-.07,0-.06-.87-2.12a.43.43,0,0,1,0-.37.49.49,0,0,1,.27-.26.41.41,0,0,1,.36,0,.53.53,0,0,1,.27.26l.44,1.09a6.51,6.51,0,0,0,.24-1.36,4.58,4.58,0,0,0,0-.64,5.83,5.83,0,0,0-1.73-4.17,5.88,5.88,0,0,0-8.34,0,5.9,5.9,0,0,0,4.17,10.06.51.51,0,0,1,.33.15.48.48,0,0,1,0,.68.53.53,0,0,1-.33.12Z" transform="translate(-4.48 -4.54)"/></g></svg>',undo:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.59 14.18"><g><path d="M5,14a.43.43,0,0,1-.22-.29.46.46,0,0,1,.06-.36.43.43,0,0,1,.29-.22.56.56,0,0,1,.36,0l.91.55a8.27,8.27,0,0,1-.22-1.45,5.07,5.07,0,0,1,0-.67A6.85,6.85,0,1,1,13,18.47a.44.44,0,0,1-.33-.13.48.48,0,0,1,0-.68.51.51,0,0,1,.33-.15A5.89,5.89,0,0,0,17.15,7.45a5.88,5.88,0,0,0-8.33,0,5.84,5.84,0,0,0-1.73,4.17s0,.25,0,.65a6.49,6.49,0,0,0,.24,1.37l.44-1.09a.57.57,0,0,1,.27-.26.41.41,0,0,1,.36,0,.53.53,0,0,1,.27.26.43.43,0,0,1,0,.37L7.82,15l0,.09-.09.09-.1.07-.06,0H7.28l-.13,0-1.09-.63c-.65-.36-1-.57-1.1-.63Z" transform="translate(-4.49 -4.53)"/></g></svg>',bold:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 11.76 15.75"><g><path d="M6.4,3.76V19.5h6.76a5.55,5.55,0,0,0,2-.32,4.93,4.93,0,0,0,1.52-1,4.27,4.27,0,0,0,1.48-3.34,3.87,3.87,0,0,0-.69-2.37,5.74,5.74,0,0,0-.71-.83,3.44,3.44,0,0,0-1.1-.65,3.6,3.6,0,0,0,1.58-1.36,3.66,3.66,0,0,0,.53-1.93,3.7,3.7,0,0,0-1.21-2.87,4.65,4.65,0,0,0-3.25-1.1H6.4Zm2.46,6.65V5.57h3.52a4.91,4.91,0,0,1,1.36.15,2.3,2.3,0,0,1,.85.45,2.06,2.06,0,0,1,.74,1.71,2.3,2.3,0,0,1-.78,1.92,2.54,2.54,0,0,1-.86.46,4.7,4.7,0,0,1-1.32.15H8.86Zm0,7.27V12.15H12.7a4.56,4.56,0,0,1,1.38.17,3.43,3.43,0,0,1,.95.49,2.29,2.29,0,0,1,.92,2,2.73,2.73,0,0,1-.83,2.1,2.66,2.66,0,0,1-.83.58,3.25,3.25,0,0,1-1.26.2H8.86Z" transform="translate(-6.4 -3.75)"/></g></svg>',underline:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 9.78 15.74"><g><path d="M14.64,3.76h2.52v7.72a4.51,4.51,0,0,1-.59,2.31,3.76,3.76,0,0,1-1.71,1.53,6.12,6.12,0,0,1-2.64.53,5,5,0,0,1-3.57-1.18,4.17,4.17,0,0,1-1.27-3.24V3.76H9.9v7.3a3,3,0,0,0,.55,2,2.3,2.3,0,0,0,1.83.65,2.26,2.26,0,0,0,1.8-.65,3.09,3.09,0,0,0,.55-2V3.76Zm2.52,13.31V19.5H7.39V17.08h9.77Z" transform="translate(-7.38 -3.76)"/></g></svg>',italic:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10.49 15.76"><g><path d="M17.16,3.79l.37,0-.06.38-.14.52A10,10,0,0,1,16.21,5a9.37,9.37,0,0,0-1,.32,6.68,6.68,0,0,0-.25.89c-.06.31-.11.59-.14.85-.3,1.36-.52,2.41-.68,3.14l-.61,3.18L13.1,15l-.43,2.4-.12.46a.62.62,0,0,0,0,.28c.44.1.85.17,1.23.22l.68.11a4.51,4.51,0,0,1-.08.6l-.09.42a.92.92,0,0,0-.23,0l-.43,0a1.37,1.37,0,0,1-.29,0c-.13,0-.63-.08-1.49-.16l-2,0c-.28,0-.87,0-1.78.12L7,19.5l.17-.88.8-.2A6.61,6.61,0,0,0,9.19,18,2.62,2.62,0,0,0,9.61,17l.28-1.41.58-2.75.12-.66c.05-.3.11-.58.17-.86s.12-.51.17-.69l.12-.48.12-.43.31-1.6.15-.65.31-1.91V5.14a3.86,3.86,0,0,0-1.48-.29l-.38,0,.2-1.06,3.24.14.75,0c.45,0,1.18,0,2.18-.09.23,0,.46,0,.71,0Z" transform="translate(-7.04 -3.76)"/></g></svg>',strike:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 14.9"><g><path d="M12.94,13a4.27,4.27,0,0,1,1.32.58,1.46,1.46,0,0,1,.55,1.2,1.87,1.87,0,0,1-.88,1.64,4.17,4.17,0,0,1-2.35.59,4.44,4.44,0,0,1-2.74-.71,2.72,2.72,0,0,1-1-2.17H5.57a4.56,4.56,0,0,0,1.55,3.7,7,7,0,0,0,4.47,1.23,6,6,0,0,0,4.07-1.3,4.24,4.24,0,0,0,1.52-3.37,4,4,0,0,0-.26-1.4h-4ZM6.37,10.24A3.27,3.27,0,0,1,6,8.68a4,4,0,0,1,1.48-3.3,5.92,5.92,0,0,1,3.88-1.21,5.58,5.58,0,0,1,3.91,1.24,4.36,4.36,0,0,1,1.45,3.17H14.44a2.12,2.12,0,0,0-.91-1.81,4.45,4.45,0,0,0-2.44-.55,3.69,3.69,0,0,0-2,.51A1.64,1.64,0,0,0,8.3,8.22a1.3,1.3,0,0,0,.48,1.11,7,7,0,0,0,2.1.78l.28.06.28.08H6.37Zm13.09.68a.73.73,0,0,1,.***********,0,0,1,.**********,0,0,1-.**********,0,0,1-.49.19H5.1a.67.67,0,0,1-.49-.19.66.66,0,0,1-.2-.48.64.64,0,0,1,.2-.48.73.73,0,0,1,.49-.21H19.46Z" transform="translate(-4.41 -4.17)"/></g></svg>',subscript:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.75 14.61"><g><path d="M15.38,4.33H12.74L11.19,7c-.28.46-.51.87-.69,1.21L10.07,9h0l-.44-.8c-.22-.4-.45-.81-.71-1.23L7.34,4.33H4.68L8.26,10,4.4,16.08H7.1l1.69-2.83c.38-.63.72-1.22,1-1.78l.25-.46h0l.49.92c.***********.74,1.32L13,16.08h2.61L11.84,10l1.77-2.84,1.77-2.85Zm4.77,13.75H17v-.15c0-.4.05-.64.16-.72a4.42,4.42,0,0,1,1.16-.31,3.3,3.3,0,0,0,1.54-.56A1.84,1.84,0,0,0,20.15,15a1.78,1.78,0,0,0-.44-1.41A2.8,2.8,0,0,0,18,13.25a2.71,2.71,0,0,0-1.69.37,1.83,1.83,0,0,0-.44,1.43v.23H17v-.23q0-.63.18-.78a1.62,1.62,0,0,1,.88-.15,1.59,1.59,0,0,1,.88.15q.18.15.18.75t-.18.75a3.58,3.58,0,0,1-1.18.33,3.33,3.33,0,0,0-1.52.51,1.57,1.57,0,0,0-.32,1.18v1.15h4.27v-.86Z" transform="translate(-4.4 -4.33)"/></g></svg>',superscript:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.75 15.42"><g><path d="M12,13.14l3.61-5.81H12.94L11.33,10c-.28.46-.51.88-.69,1.25l-.45.83h0l-.45-.85c-.22-.41-.45-.82-.71-1.24L7.4,7.33H4.68l3.66,5.81L4.4,19.33H7.14l1.74-2.87q.58-1,1-1.83l.25-.48h0l.51.94.75,1.37,1.72,2.87h2.67l-1.92-3.09c-1.12-1.8-1.76-2.83-1.92-3.1Zm4.84-4.41h0l0,.15h3.27v.86H15.77V8.58a1.66,1.66,0,0,1,.33-1.22,3.51,3.51,0,0,1,1.56-.51,3.68,3.68,0,0,0,1.21-.34c.13-.1.19-.36.19-.77S19,5.07,18.87,5A1.63,1.63,0,0,0,18,4.8a1.58,1.58,0,0,0-.91.17c-.13.11-.19.38-.19.8V6H15.78V5.76a1.87,1.87,0,0,1,.45-1.47A2.84,2.84,0,0,1,18,3.91a2.8,2.8,0,0,1,1.72.38,1.84,1.84,0,0,1,.45,1.44,1.91,1.91,0,0,1-.34,1.35,3.24,3.24,0,0,1-1.58.57A3.69,3.69,0,0,0,17,8c-.12.1-.17.35-.17.76Z" transform="translate(-4.4 -3.91)"/></g></svg>',erase:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 13.76"><g><path d="M13.69,17.2h6.46v1.31H8.56L4.41,14.37,14,4.75l6.06,6.06L16.89,14l-3.2,3.19Zm-4.61,0h2.77L14.09,15,9.88,10.75,6.25,14.38l1.41,1.41c.84.82,1.31,1.29,1.42,1.41Z" transform="translate(-4.41 -4.75)"/></g></svg>',indent:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 12.36"><g><path d="M4.68,14.45a.27.27,0,0,1-.19-.08.3.3,0,0,1-.08-.21V9.1a.27.27,0,0,1,.08-.19.28.28,0,0,1,.2-.08.25.25,0,0,1,.19.07l2.54,2.54a.29.29,0,0,1,0,.4L4.88,14.36a.24.24,0,0,1-.2.09Zm15.19,1.12a.27.27,0,0,1,.19.08.25.25,0,0,1,.08.19v1.69a.27.27,0,0,1-.08.19.25.25,0,0,1-.19.08H4.68a.27.27,0,0,1-.19-.08.25.25,0,0,1-.08-.19V15.84a.27.27,0,0,1,.27-.27H19.87Zm0-3.38a.27.27,0,0,1,.19.08.28.28,0,0,1,.08.21v1.68a.32.32,0,0,1-.08.21.25.25,0,0,1-.19.08H10.31a.27.27,0,0,1-.19-.08.3.3,0,0,1-.08-.21V12.48a.32.32,0,0,1,.08-.21.24.24,0,0,1,.19-.08h9.56Zm0-3.37a.27.27,0,0,1,.19.08.25.25,0,0,1,.08.19v1.69a.27.27,0,0,1-.08.19.25.25,0,0,1-.19.08H10.31a.27.27,0,0,1-.27-.27V9.1a.27.27,0,0,1,.27-.27h9.56Zm.2-3.29a.28.28,0,0,1,.08.2V7.41a.32.32,0,0,1-.08.21.25.25,0,0,1-.19.08H4.68a.27.27,0,0,1-.19-.08.3.3,0,0,1-.08-.21V5.73a.32.32,0,0,1,.08-.21.25.25,0,0,1,.19-.08H19.87a.28.28,0,0,1,.2.09Z" transform="translate(-4.41 -5.44)"/></g></svg>',outdent:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 12.36"><g><path d="M19.87,15.57a.27.27,0,0,1,.19.08.25.25,0,0,1,.08.19v1.69a.27.27,0,0,1-.08.19.25.25,0,0,1-.19.08H4.68a.27.27,0,0,1-.19-.08.25.25,0,0,1-.08-.19V15.84a.27.27,0,0,1,.27-.27H19.87ZM7.5,14.45a.25.25,0,0,1-.2-.09L4.76,11.84a.29.29,0,0,1,0-.4L7.3,8.9a.29.29,0,0,1,.4,0,.31.31,0,0,1,.07.2v5.06a.32.32,0,0,1-.08.21.26.26,0,0,1-.19.08ZM19.87,8.82a.27.27,0,0,1,.19.08.25.25,0,0,1,.08.19v1.69a.27.27,0,0,1-.08.19.25.25,0,0,1-.19.08H10.31a.27.27,0,0,1-.27-.27V9.1a.27.27,0,0,1,.27-.27h9.56Zm0,3.37a.27.27,0,0,1,.19.08.28.28,0,0,1,.08.21v1.68a.32.32,0,0,1-.08.21.25.25,0,0,1-.19.08H10.31a.27.27,0,0,1-.19-.08.3.3,0,0,1-.08-.21V12.48a.32.32,0,0,1,.08-.21.24.24,0,0,1,.19-.08h9.56Zm.2-6.66a.28.28,0,0,1,.08.2V7.41a.32.32,0,0,1-.08.21.25.25,0,0,1-.19.08H4.68a.27.27,0,0,1-.19-.08.3.3,0,0,1-.08-.21V5.73a.32.32,0,0,1,.08-.21.25.25,0,0,1,.19-.08H19.87a.28.28,0,0,1,.2.09Z" transform="translate(-4.41 -5.44)"/></g></svg>',expansion:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 15.74"><g><path d="M11.8,13.06l-5.1,5.1H9.51V19.5H4.41V14.4H5.75v2.81L8.3,14.66q2.25-2.23,2.55-2.55Zm8.35-9.3v5.1H18.81V6.05l-5.1,5.1-1-1,5.1-5.1H15.05V3.76Z" transform="translate(-4.41 -3.76)"/></g></svg>',reduction:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 15.74"><g><path d="M14.91,10h2.87v1.38H12.55V6.12h1.38V9l5.24-5.24.48.49.49.48ZM6.77,11.92H12v5.23H10.62V14.26L5.37,19.5l-1-1L9.63,13.3H6.77Z" transform="translate(-4.4 -3.76)"/></g></svg>',code_view:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.73 11.8"><g><path d="M8.09,7.94a.76.76,0,0,1,.53.22.72.72,0,0,1,.21.52.76.76,0,0,1-.22.54L6.18,11.63l2.43,2.44a.69.69,0,0,1,.2.51.66.66,0,0,1-.21.51.75.75,0,0,1-.51.22.63.63,0,0,1-.51-.21h0L4.63,12.15a.7.7,0,0,1-.22-.53.67.67,0,0,1,.25-.55L7.57,8.16a.82.82,0,0,1,.52-.22Zm12.05,3.69a.7.7,0,0,1-.23.52L17,15.1h0a.66.66,0,0,1-.51.21.73.73,0,0,1-.51-.22.75.75,0,0,1-.22-.51.63.63,0,0,1,.21-.51l2.43-2.44L15.92,9.22a.73.73,0,0,1-.22-.53A.74.74,0,0,1,17,8.18h0l2.91,2.91a.67.67,0,0,1,.27.54Zm-5.9-5.9a.73.73,0,0,1,.61.32.71.71,0,0,1,.07.68L11,17a1,1,0,0,1-.22.32.6.6,0,0,1-.35.16.75.75,0,0,1-.69-.26.69.69,0,0,1-.12-.72L13.56,6.23a.75.75,0,0,1,.26-.35.74.74,0,0,1,.42-.15Z" transform="translate(-4.41 -5.73)"/></g></svg>',preview:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.65 15.66"><g><path d="M16.19,14.43l2.49,2.49a.73.73,0,0,1,.21.52.67.67,0,0,1-.22.51.7.7,0,0,1-.52.22.69.69,0,0,1-.51-.21l-2.49-2.48a5.17,5.17,0,0,1-1.34.69,4.64,4.64,0,0,1-1.48.24,4.78,4.78,0,1,1,0-9.56,4.79,4.79,0,0,1,1.84.36,4.9,4.9,0,0,1,1.56,1,4.77,4.77,0,0,1,.46,6.18ZM10,14a3.3,3.3,0,0,0,2.34.93A3.37,3.37,0,0,0,14.7,14a3.3,3.3,0,0,0-1.08-5.41,3.47,3.47,0,0,0-2.56,0A3,3,0,0,0,10,9.28,3.31,3.31,0,0,0,10,14ZM16,4a3.86,3.86,0,0,1,2.77,1.14A3.9,3.9,0,0,1,20,7.85v4a.77.77,0,0,1-.22.53.7.7,0,0,1-.52.21.72.72,0,0,1-.74-.74v-4a2.46,2.46,0,0,0-.72-1.73A2.37,2.37,0,0,0,16,5.45H8.53A2.42,2.42,0,0,0,6.08,7.89v7.52a2.41,2.41,0,0,0,.71,1.73,2.46,2.46,0,0,0,1.74.72h4.08a.73.73,0,0,1,0,1.46H8.53a3.85,3.85,0,0,1-2.78-1.14A3.93,3.93,0,0,1,4.6,15.4V7.87A3.94,3.94,0,0,1,5.76,5.09,3.88,3.88,0,0,1,8.54,4H16Z" transform="translate(-4.45 -3.8)"/></g></svg>',print:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16.05 16.04"><g><path d="M19.76,15.84a1.29,1.29,0,0,0,.39-.92V8.35A2.05,2.05,0,0,0,19.57,7a1.93,1.93,0,0,0-1.38-.57H6.37a1.95,1.95,0,0,0-2,2v6.56a1.23,1.23,0,0,0,.38.92,1.35,1.35,0,0,0,.93.38h2V14.9l-2,0V8.35a.67.67,0,0,1,.18-.47.62.62,0,0,1,.48-.19H18.18a.6.6,0,0,1,.46.19.66.66,0,0,1,.18.47V14.9h-2v1.32h2A1.35,1.35,0,0,0,19.76,15.84ZM17.52,7.69V5.06a1.31,1.31,0,0,0-.38-.92,1.34,1.34,0,0,0-.94-.38H8.34A1.3,1.3,0,0,0,7,5.06V7.69H8.34V5.06h7.87V7.69h1.31ZM8.34,12.93h7.87l0,5.26H8.34V12.93Zm7.87,5.26v0Zm.65,1.31a.6.6,0,0,0,.46-.19.72.72,0,0,0,.2-.47V12.29a.74.74,0,0,0-.2-.47.6.6,0,0,0-.46-.19H7.68a.6.6,0,0,0-.46.19.72.72,0,0,0-.2.47v6.55a.74.74,0,0,0,.2.47.6.6,0,0,0,.46.19h9.18ZM16.67,9.28a.7.7,0,0,0-.94,0,.63.63,0,0,0-.18.46.67.67,0,0,0,.18.47.68.68,0,0,0,.94,0,.66.66,0,0,0,.18-.47A.58.58,0,0,0,16.67,9.28Z" transform="translate(-4.25 -3.61)"/></g></svg>',template:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14.27 15.64"><g><path d="M18.18,19.16a1,1,0,0,0,1-1V5.73a1,1,0,0,0-1-1h-2v1h2V18.19H6.37V5.73h2v-1h-2A.94.94,0,0,0,5.68,5a1,1,0,0,0-.29.7V18.18a.94.94,0,0,0,.29.69,1,1,0,0,0,.69.29H18.18ZM9.82,10.31h4.92a.49.49,0,0,0,.35-.15.47.47,0,0,0,.15-.35.49.49,0,0,0-.15-.35.47.47,0,0,0-.35-.15H9.82a.49.49,0,0,0-.35.15.47.47,0,0,0-.15.35.49.49,0,0,0,.15.35.47.47,0,0,0,.35.15Zm5.9,4.92H8.83a.49.49,0,0,0-.35.15.47.47,0,0,0-.15.35.49.49,0,0,0,.15.35.47.47,0,0,0,.35.15h6.89a.49.49,0,0,0,.35-.15.47.47,0,0,0,.15-.35.51.51,0,0,0-.5-.5ZM7.36,12.77a.49.49,0,0,0,.15.35.47.47,0,0,0,.35.15h8.85a.49.49,0,0,0,.35-.15.47.47,0,0,0,.15-.35.49.49,0,0,0-.15-.35.47.47,0,0,0-.35-.15H7.85a.49.49,0,0,0-.35.15.52.52,0,0,0-.14.35Z" transform="translate(-5.14 -3.77)"/><path d="M14.24,6.71a1,1,0,0,0,1-1,1,1,0,0,0-1-1,1,1,0,0,0-1-1h-2a.94.94,0,0,0-.69.28,1,1,0,0,0-.29.7A.94.94,0,0,0,9.62,5a.91.91,0,0,0-.29.69,1,1,0,0,0,.29.7,1,1,0,0,0,.69.29h3.93Z" transform="translate(-5.14 -3.77)"/></g></svg>',line_height:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.76 13.56"><g><path d="M4.4,4.88V8.26a2,2,0,0,0,.5.39s.1,0,.18-.12a.62.62,0,0,0,.17-.28c.06-.19.13-.44.21-.74s.14-.52.19-.66a.58.58,0,0,1,.21-.3,2.41,2.41,0,0,1,.63-.21,3.83,3.83,0,0,1,.88-.12,9.15,9.15,0,0,1,1.31.06.16.16,0,0,1,.11,0,.26.26,0,0,1,.06.14,4,4,0,0,1,0,.49v2l.05,3.77c0,1.41,0,2.68-.05,3.81a1.79,1.79,0,0,1-.11.49,10.68,10.68,0,0,1-1.4.45,1.12,1.12,0,0,0-.69.43v.31l0,.22.61,0c.85-.08,1.54-.12,2.06-.12a19.76,19.76,0,0,1,2.09.08,15.08,15.08,0,0,0,1.64.08,1.4,1.4,0,0,0,.29,0,1.58,1.58,0,0,0,0-.26l-.05-.43a2.26,2.26,0,0,0-.43-.17l-.77-.22-.15,0a2.55,2.55,0,0,1-.78-.28,2.56,2.56,0,0,1-.11-.75l0-1.29,0-3.15V7.53a10.51,10.51,0,0,1,.06-1.2,3.83,3.83,0,0,1,.6,0l1.88,0a2.18,2.18,0,0,1,.38,0,.45.45,0,0,1,.23.17.9.9,0,0,1,.05.25c0,.16.06.35.1.58a3.33,3.33,0,0,0,.14.55A6.39,6.39,0,0,0,15,9a2.91,2.91,0,0,0,.6-.15,2.77,2.77,0,0,0,0-.46l0-.51,0-2.95-.25,0-.38,0L15,4.94a.71.71,0,0,1-.18.15.45.45,0,0,1-.25.07l-.29,0H8.75l-.15,0H7.45a17,17,0,0,1-1.86,0L5.36,5l-.25-.13ZM19.75,16.14h-.69v-9h.69A.4.4,0,0,0,20.13,7c.06-.11,0-.24-.1-.39L18.92,5.15a.52.52,0,0,0-.86,0L17,6.58c-.12.15-.16.28-.1.39s.18.16.38.16h.69v9h-.69a.4.4,0,0,0-.38.16c-.06.11,0,.24.1.39l1.11,1.43a.52.52,0,0,0,.86,0L20,16.69c.12-.15.16-.28.1-.39a.4.4,0,0,0-.38-.16Z" transform="translate(-4.4 -4.86)"/></g></svg>',paragraph_style:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 11.81 15.74"><g><path d="M18.18,3.76v2h-2V19.5h-2V5.73h-2V19.5h-2V11.63a3.94,3.94,0,0,1,0-7.87h7.87Z" transform="translate(-6.37 -3.76)"/></g></svg>',text_style:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13.76 15.74"><g><path d="M17.68,6.71a2.22,2.22,0,0,0,1.06-.22.74.74,0,0,0,.42-.7.73.73,0,0,0-.08-.33.67.67,0,0,0-.17-.22,1,1,0,0,0-.31-.15L18.26,5l-.45-.09A15.27,15.27,0,0,0,13.26,5V4.74c0-.66-.63-1-1.92-1-.24,0-.43.15-.59.46a4,4,0,0,0-.36,1.14h0v0a26.45,26.45,0,0,1-3.5.35A2,2,0,0,0,5.77,6a.84.84,0,0,0-.37.79,2.14,2.14,0,0,0,.41,1.29,1.23,1.23,0,0,0,1.05.63,16.62,16.62,0,0,0,3.29-.45l-.34,3.35c-.16,1.61-.29,2.9-.37,3.86s-.12,1.66-.12,2.09l0,.65a5.15,5.15,0,0,0,.05.6,1.28,1.28,0,0,0,.16.54.34.34,0,0,0,.28.18,1.16,1.16,0,0,0,.79-.46,3.66,3.66,0,0,0,.68-1,22.08,22.08,0,0,0,1-4.33q.49-3.1.78-6.15a24.69,24.69,0,0,1,4.62-.84Z" transform="translate(-5.4 -3.76)"/></g></svg>',save:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 15.74"><g><path d="M18.53,19.5l.2-.05A1.78,1.78,0,0,0,20.13,18l0-.09V7.14a2,2,0,0,0-.28-.64A3.18,3.18,0,0,0,19.43,6c-.5-.52-1-1-1.55-1.54A2.59,2.59,0,0,0,17.37,4a1.83,1.83,0,0,0-.61-.25H6l-.21,0a1.78,1.78,0,0,0-1.4,1.49l0,.1V17.87a2.49,2.49,0,0,0,.09.37,1.79,1.79,0,0,0,1.44,1.23l.09,0Zm-6.25-.6H6.92a.61.61,0,0,1-.68-.48.78.78,0,0,1,0-.22V12.3a.62.62,0,0,1,.69-.68H17.64a.62.62,0,0,1,.69.69V18.2a.64.64,0,0,1-.71.69H12.28ZM12,9.81H8.15a.63.63,0,0,1-.72-.71v-4a.64.64,0,0,1,.72-.72h7.66a.64.64,0,0,1,.72.72v4a.65.65,0,0,1-.74.72ZM13.5,5V9.18h1.78V5Z" transform="translate(-4.41 -3.76)"/></g></svg>',blockquote:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 475.082 475.081"><g><path d="M164.45,219.27h-63.954c-7.614,0-14.087-2.664-19.417-7.994c-5.327-5.33-7.994-11.801-7.994-19.417v-9.132c0-20.177,7.139-37.401,21.416-51.678c14.276-14.272,31.503-21.411,51.678-21.411h18.271c4.948,0,9.229-1.809,12.847-5.424c3.616-3.617,5.424-7.898,5.424-12.847V54.819c0-4.948-1.809-9.233-5.424-12.85c-3.617-3.612-7.898-5.424-12.847-5.424h-18.271c-19.797,0-38.684,3.858-56.673,11.563c-17.987,7.71-33.545,18.132-46.68,31.267c-13.134,13.129-23.553,28.688-31.262,46.677C3.855,144.039,0,162.931,0,182.726v200.991c0,15.235,5.327,28.171,15.986,38.834c10.66,10.657,23.606,15.985,38.832,15.985h109.639c15.225,0,28.167-5.328,38.828-15.985c10.657-10.663,15.987-23.599,15.987-38.834V274.088c0-15.232-5.33-28.168-15.994-38.832C192.622,224.6,179.675,219.27,164.45,219.27z"/><path d="M459.103,235.256c-10.656-10.656-23.599-15.986-38.828-15.986h-63.953c-7.61,0-14.089-2.664-19.41-7.994c-5.332-5.33-7.994-11.801-7.994-19.417v-9.132c0-20.177,7.139-37.401,21.409-51.678c14.271-14.272,31.497-21.411,51.682-21.411h18.267c4.949,0,9.233-1.809,12.848-5.424c3.613-3.617,5.428-7.898,5.428-12.847V54.819c0-4.948-1.814-9.233-5.428-12.85c-3.614-3.612-7.898-5.424-12.848-5.424h-18.267c-19.808,0-38.691,3.858-56.685,11.563c-17.984,7.71-33.537,18.132-46.672,31.267c-13.135,13.129-23.559,28.688-31.265,46.677c-7.707,17.987-11.567,36.879-11.567,56.674v200.991c0,15.235,5.332,28.171,15.988,38.834c10.657,10.657,23.6,15.985,38.828,15.985h109.633c15.229,0,28.171-5.328,38.827-15.985c10.664-10.663,15.985-23.599,15.985-38.834V274.088C475.082,258.855,469.76,245.92,459.103,235.256z"/></g></svg>',arrow_down:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.73 8.67"><g><path d="M18.79,7.52a.8.8,0,0,1,.56-.23.82.82,0,0,1,.79.79.8.8,0,0,1-.23.56l-7.07,7.07a.79.79,0,0,1-.57.25.77.77,0,0,1-.57-.25h0L4.64,8.65a.8.8,0,0,1-.23-.57.82.82,0,0,1,.79-.79.8.8,0,0,1,.56.23L12.28,14l3.26-3.26,3.25-3.26Z" transform="translate(-4.41 -7.29)"/></g></svg>',align_justify:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 13.77"><g><path d="M4.41,4.74v2H20.15v-2H4.41Zm0,5.9H20.15v-2H4.41v2Zm0,3.94H20.15v-2H4.41v2Zm0,3.93h7.87v-2H4.41v2Z" transform="translate(-4.41 -4.74)"/></g></svg>',align_left:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 13.77"><g><path d="M4.41,4.74v2H20.15v-2H4.41Zm11.8,3.94H4.41v2H16.22v-2Zm-11.8,5.9H18.18v-2H4.41v2Zm0,3.93h9.84v-2H4.41v2Z" transform="translate(-4.41 -4.74)"/></g></svg>',align_right:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 13.77"><g><path d="M4.41,4.74v2H20.15v-2H4.41Zm3.93,5.9H20.15v-2H8.34v2Zm-2,3.94H20.14v-2H6.37v2Zm3.94,3.93h9.84v-2H10.31v2Z" transform="translate(-4.41 -4.74)"/></g></svg>',align_center:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 13.77"><g><path d="M4.41,4.74v2H20.15v-2H4.41Zm2,3.94v2H18.18v-2H6.37Zm-1,5.9H19.16v-2H5.39v2Zm2,3.93H17.2v-2H7.36v2Z" transform="translate(-4.41 -4.74)"/></g></svg>',font_color:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 14.61"><g><path d="M18.5,15.57,14.28,4.32h-3.4L6.65,15.57h3l.8-2.26h4.23l.8,2.26h3ZM14,11.07H11.14L12.54,7,13.25,9c.41,1.18.64,1.86.7,2ZM4.41,16.69v2.24H20.15V16.69H4.41Z" transform="translate(-4.41 -4.32)"/></g></svg>',highlight_color:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.66 15.74"><g><path d="M12.32,9.31,13.38,13H11.21l.52-1.83q.46-1.61.54-1.83ZM4.44,3.76H20.1V19.5H4.44V3.76ZM14.71,17.32h2.63L13.7,6H10.89L7.26,17.32H9.89l.63-2.24h3.55l.32,1.12c.18.65.29,1,.32,1.12Z" transform="translate(-4.44 -3.76)"/></g></svg>',list_bullets:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 12.37"><g><path d="M7.77,16.12a1.59,1.59,0,0,0-.49-1.18,1.62,1.62,0,0,0-1.19-.49,1.68,1.68,0,1,0,0,3.36,1.67,1.67,0,0,0,1.68-1.69Zm0-4.48A1.67,1.67,0,0,0,6.09,10,1.68,1.68,0,0,0,4.9,12.82a1.62,1.62,0,0,0,1.19.49,1.67,1.67,0,0,0,1.68-1.67Zm12.38,3.64a.27.27,0,0,0-.08-.19.28.28,0,0,0-.2-.09H9.19a.28.28,0,0,0-.2.08.29.29,0,0,0-.08.19V17a.27.27,0,0,0,.28.28H19.87a.27.27,0,0,0,.19-.08.24.24,0,0,0,.08-.2V15.28ZM7.77,7.13a1.63,1.63,0,0,0-.49-1.2,1.61,1.61,0,0,0-1.19-.49,1.61,1.61,0,0,0-1.19.49,1.71,1.71,0,0,0,0,2.4,1.62,1.62,0,0,0,1.19.49,1.61,1.61,0,0,0,1.19-.49,1.63,1.63,0,0,0,.49-1.2Zm12.38,3.66a.28.28,0,0,0-.08-.2.29.29,0,0,0-.19-.08H9.19a.27.27,0,0,0-.28.28v1.69a.27.27,0,0,0,.08.19.24.24,0,0,0,.2.08H19.87a.27.27,0,0,0,.19-.08.25.25,0,0,0,.08-.19V10.79Zm0-4.5a.27.27,0,0,0-.08-.19A.25.25,0,0,0,19.88,6H9.19A.28.28,0,0,0,9,6.1a.26.26,0,0,0-.08.19V8A.27.27,0,0,0,9,8.17a.24.24,0,0,0,.2.08H19.87a.27.27,0,0,0,.19-.08A.25.25,0,0,0,20.14,8V6.29Z" transform="translate(-4.41 -5.44)"/></g></svg>',list_number:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.69 15.74"><g><path d="M7.66,18a1.24,1.24,0,0,0-.26-.78,1.17,1.17,0,0,0-.72-.42l.85-1V15H4.58v1.34h.94v-.46l.85,0h0c-.11.11-.22.23-.32.35s-.23.27-.37.47L5.39,17l.23.51c.61-.05.92.11.92.49a.42.42,0,0,1-.18.37.79.79,0,0,1-.45.12A1.41,1.41,0,0,1,5,18.15l-.51.77A2.06,2.06,0,0,0,6,19.5a1.8,1.8,0,0,0,1.2-.41A1.38,1.38,0,0,0,7.66,18Zm0-5.54H6.75V13H5.63A.72.72,0,0,1,6,12.51a5.45,5.45,0,0,1,.66-.45,2.71,2.71,0,0,0,.67-.57,1.19,1.19,0,0,0,.31-.81,1.29,1.29,0,0,0-.45-1,1.86,1.86,0,0,0-2-.11,1.51,1.51,0,0,0-.62.7l.74.52A.87.87,0,0,1,6,10.28a.51.51,0,0,1,.35.12.42.42,0,0,1,.13.33.55.55,0,0,1-.21.4,3,3,0,0,1-.5.38c-.19.13-.39.27-.58.42a2,2,0,0,0-.5.6,1.63,1.63,0,0,0-.21.81,3.89,3.89,0,0,0,.05.48h3.2V12.44Zm12.45,2.82a.27.27,0,0,0-.08-.19.28.28,0,0,0-.21-.08H9.1a.32.32,0,0,0-.21.08.24.24,0,0,0-.08.2V17a.27.27,0,0,0,.08.19.3.3,0,0,0,.21.08H19.83a.32.32,0,0,0,.21-.08.25.25,0,0,0,.08-.19V15.26ZM7.69,7.32h-1V3.76H5.8L4.6,4.88l.63.68a1.85,1.85,0,0,0,.43-.48h0l0,2.24H4.74V8.2h3V7.32Zm12.43,3.42a.27.27,0,0,0-.08-.19.28.28,0,0,0-.21-.08H9.1a.32.32,0,0,0-.21.08.24.24,0,0,0-.08.2v1.71a.27.27,0,0,0,.08.19.3.3,0,0,0,.21.08H19.83a.32.32,0,0,0,.21-.08.25.25,0,0,0,.08-.19V10.74Zm0-4.52A.27.27,0,0,0,20,6,.28.28,0,0,0,19.83,6H9.1A.32.32,0,0,0,8.89,6a.24.24,0,0,0-.08.19V7.93a.27.27,0,0,0,.08.19.32.32,0,0,0,.21.08H19.83A.32.32,0,0,0,20,8.12a.26.26,0,0,0,.08-.2V6.22Z" transform="translate(-4.43 -3.76)"/></g></svg>',table:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 15.74"><g><path d="M4.41,8.05V3.76H8.7V8.05H4.41Zm5.71,0V3.76h4.3V8.05h-4.3Zm5.74-4.29h4.29V8.05H15.86V3.76Zm-11.45,10V9.48H8.7v4.3H4.41Zm5.71,0V9.48h4.3v4.3h-4.3Zm5.74,0V9.48h4.29v4.3H15.86ZM4.41,19.5V15.21H8.7V19.5H4.41Zm5.71,0V15.21h4.3V19.5h-4.3Zm5.74,0V15.21h4.29V19.5H15.86Z" transform="translate(-4.41 -3.76)"/></g></svg>',horizontal_rule:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 2.24"><g><path d="M20.15,12.75V10.51H4.41v2.24H20.15Z" transform="translate(-4.41 -10.51)"/></g></svg>',show_blocks:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.66 15.67"><g><path d="M19.72,5.58a1.64,1.64,0,0,0-1.64-1.64H6.23a1.62,1.62,0,0,0-1.16.48,1.63,1.63,0,0,0-.48,1.16V9.63a1.6,1.6,0,0,0,.48,1.16,1.62,1.62,0,0,0,1.16.47H18.09a1.67,1.67,0,0,0,1.16-.47,1.62,1.62,0,0,0,.48-1.16V5.58Zm-.94,4.05a.68.68,0,0,1-.7.7H6.23a.66.66,0,0,1-.48-.2.74.74,0,0,1-.21-.5V5.58a.66.66,0,0,1,.2-.48.71.71,0,0,1,.48-.21H18.08a.74.74,0,0,1,.5.21.66.66,0,0,1,.2.48ZM6.48,7.72a.21.21,0,0,0,.17-.07.22.22,0,0,0,.07-.17V7.06a1.27,1.27,0,0,1,.11-.52.37.37,0,0,1,.36-.23H8.77A.25.25,0,0,0,9,6.17a.19.19,0,0,0,0-.23.27.27,0,0,0-.2-.12H7.19a.88.88,0,0,0-.72.39,1.51,1.51,0,0,0-.23.85v.42a.24.24,0,0,0,.24.24Zm-.19.81a.21.21,0,0,0,.17-.07.26.26,0,0,0,.07-.17.24.24,0,0,0-.24-.24.2.2,0,0,0-.16.09.2.2,0,0,0-.07.16.22.22,0,0,0,.07.17.23.23,0,0,0,.16.06Zm8.46,5.1a1.63,1.63,0,0,0-.47-1.16A1.61,1.61,0,0,0,13.12,12H6.23a1.6,1.6,0,0,0-1.16.46,1.62,1.62,0,0,0-.48,1.16v4.05a1.64,1.64,0,0,0,1.64,1.64h6.89a1.6,1.6,0,0,0,1.16-.48,1.62,1.62,0,0,0,.47-1.16Zm-.94,4a.7.7,0,0,1-.2.49.65.65,0,0,1-.5.2H6.23a.66.66,0,0,1-.48-.2.75.75,0,0,1-.21-.49v-4a.74.74,0,0,1,.21-.5.66.66,0,0,1,.48-.2h6.89a.68.68,0,0,1,.7.7v4Zm6.15,0v-4a1.6,1.6,0,0,0-.48-1.16A1.67,1.67,0,0,0,18.32,12H17.1a1.63,1.63,0,0,0-1.16.47,1.61,1.61,0,0,0-.47,1.16v4a1.67,1.67,0,0,0,.47,1.16,1.62,1.62,0,0,0,1.16.48h1.22A1.64,1.64,0,0,0,20,17.68Zm-.94-4v4a.75.75,0,0,1-.21.49.62.62,0,0,1-.48.2H17.11a.69.69,0,0,1-.5-.2.7.7,0,0,1-.2-.49v-4a.68.68,0,0,1,.7-.7h1.22a.66.66,0,0,1,.48.2.72.72,0,0,1,.21.5Z" transform="translate(-4.44 -3.79)"/></g></svg>',cancel:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 15.74"><g><path d="M14.15,11.63l5.61,5.61a1.29,1.29,0,0,1,.38.93,1.27,1.27,0,0,1-.4.93,1.25,1.25,0,0,1-.92.4,1.31,1.31,0,0,1-.94-.4l-5.61-5.61L6.67,19.1a1.31,1.31,0,0,1-.94.4,1.24,1.24,0,0,1-.92-.4,1.27,1.27,0,0,1-.4-.93,1.33,1.33,0,0,1,.38-.93l5.61-5.63L4.79,6a1.26,1.26,0,0,1-.38-.93,1.22,1.22,0,0,1,.4-.92,1.28,1.28,0,0,1,.92-.39,1.38,1.38,0,0,1,.94.38l5.61,5.61,5.61-5.61a1.33,1.33,0,0,1,.94-.38,1.26,1.26,0,0,1,.92.39,1.24,1.24,0,0,1,.4.92,1.29,1.29,0,0,1-.39.93L17,8.81l-2.8,2.82Z" transform="translate(-4.41 -3.76)"/></g></svg>',image:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.75 15.77"><g><path d="M8.77,8.72a.88.88,0,0,1-.61-.27.82.82,0,0,1-.25-.61.89.89,0,0,1,.25-.62A.82.82,0,0,1,8.77,7a.81.81,0,0,1,.61.25.83.83,0,0,1,.27.62.81.81,0,0,1-.25.61.91.91,0,0,1-.63.27Zm9.62-5a1.74,1.74,0,0,1,1.76,1.76V17.76a1.74,1.74,0,0,1-1.76,1.76H6.16A1.74,1.74,0,0,1,4.4,17.76V5.51A1.74,1.74,0,0,1,6.16,3.75H18.39Zm0,1.75H6.16v8L8.53,11.8a.94.94,0,0,1,.54-.17.86.86,0,0,1,.54.2L11.09,13l3.64-4.55a.78.78,0,0,1,.34-.25.85.85,0,0,1,.42-.07.89.89,0,0,1,.39.12.78.78,0,0,1,.28.29l2.24,3.67V5.51Zm0,12.24V15.6L15.3,10.53,11.89,14.8a.89.89,0,0,1-.59.32.82.82,0,0,1-.64-.18L9,13.62,6.16,15.74v2Z" transform="translate(-4.4 -3.75)"/></g></svg>',video:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 14.55"><g><path d="M20.15,10.26V18.9l-3.94-1.57v1.2H4.41V10.66H16.22v1.23l2-.81,2-.82ZM14.64,17h0V12.54h0v-.31H6V17h8.67Zm3.94-.37v-4l-2.37,1v2l1.18.48,1.19.48ZM7.94,9.86A2.77,2.77,0,0,1,5.19,7.11a2.76,2.76,0,0,1,5.51,0A2.78,2.78,0,0,1,7.94,9.86Zm0-3.93a1.21,1.21,0,0,0-.83.35,1.15,1.15,0,0,0-.34.84A1.09,1.09,0,0,0,7.11,8,1.15,1.15,0,0,0,8,8.28,1.13,1.13,0,0,0,9.11,7.12,1.16,1.16,0,0,0,7.94,5.93Zm5.9,3.93a2.34,2.34,0,0,1-1.67-.68,2.3,2.3,0,0,1-.68-1.67,2.35,2.35,0,0,1,4-1.67,2.37,2.37,0,0,1,0,3.34,2.33,2.33,0,0,1-1.68.68Zm0-3.14a.75.75,0,1,0,.55.22.73.73,0,0,0-.55-.22Z" transform="translate(-4.41 -4.35)"/></g></svg>',link:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 15.72"><g><path d="M13.05,13.63a.24.24,0,0,1,.15.22L13.42,16a.19.19,0,0,1-.08.18l-2.12,2.14a4.08,4.08,0,0,1-1.29.85A4,4,0,0,1,4.71,17a3.92,3.92,0,0,1-.3-1.52A4,4,0,0,1,4.71,14a3.91,3.91,0,0,1,.87-1.3L7.7,10.56a.25.25,0,0,1,.2-.06l2.17.22a.21.21,0,0,1,.19.15.24.24,0,0,1,0,.25L7.12,14.23a1.81,1.81,0,0,0,0,2.58,1.78,1.78,0,0,0,1.29.52,1.74,1.74,0,0,0,1.28-.52L12.8,13.7a.24.24,0,0,1,.25-.07ZM19,4.92a4,4,0,0,1,0,5.66L16.86,12.7a.25.25,0,0,1-.17.08l-2.2-.23a.21.21,0,0,1-.19-.15.22.22,0,0,1,0-.25L17.44,9a1.81,1.81,0,0,0,0-2.58,1.78,1.78,0,0,0-1.29-.52,1.74,1.74,0,0,0-1.28.52L11.76,9.57a.21.21,0,0,1-.25,0,.24.24,0,0,1-.16-.21l-.22-2.17a.19.19,0,0,1,.08-.18l2.12-2.14a4.08,4.08,0,0,1,1.29-.85,4.05,4.05,0,0,1,3.06,0,3.85,3.85,0,0,1,1.3.85ZM5.84,9.82a.25.25,0,0,1-.18-.08.19.19,0,0,1-.07-.19l.11-.77a.2.2,0,0,1,.11-.17.24.24,0,0,1,.2,0l2.5.72a.25.25,0,0,1,.15.27.22.22,0,0,1-.23.21l-2.59,0Zm4.12-2-.73-2.5a.27.27,0,0,1,0-.2A.21.21,0,0,1,9.41,5L10.19,5a.25.25,0,0,1,.19,0,.23.23,0,0,1,.08.18l-.05,2.61a.2.2,0,0,1-.19.23h0A.22.22,0,0,1,10,7.85Zm8.76,5.58a.25.25,0,0,1,.18.08.23.23,0,0,1,.06.2l-.11.77a.25.25,0,0,1-.11.17.21.21,0,0,1-.12,0l-.08,0L16,14a.25.25,0,0,1-.15-.27.22.22,0,0,1,.22-.21l1.29,0,1.33,0Zm-4.12,2,.74,2.51a.28.28,0,0,1,0,.2.23.23,0,0,1-.18.11l-.8.11a.23.23,0,0,1-.17-.07.25.25,0,0,1-.08-.18l0-2.61a.22.22,0,0,1,.22-.22.21.21,0,0,1,.26.15Z" transform="translate(-4.41 -3.77)"/></g></svg>',math:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 11.81 15.73"><g><path d="M17.19,5.73a1,1,0,0,0,.71-.29,1,1,0,0,0,.28-.7,1,1,0,0,0-1-1H7.35a1,1,0,0,0-1,1,.77.77,0,0,0,.13.47h0l4.58,6.43L6.68,17.81a1.25,1.25,0,0,0-.29.71.94.94,0,0,0,.28.7.92.92,0,0,0,.69.28H17.2a1,1,0,0,0,.71-.28,1,1,0,0,0,0-1.39.92.92,0,0,0-.71-.29H9.26l3.87-5.43a.86.86,0,0,0,0-.95L9.26,5.73h7.93Z" transform="translate(-6.38 -3.77)"/></g></svg>',unlink:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 15.72"><g><path d="M19,18.32a4,4,0,0,0,0-5.68L15.85,9.5l-1.17,1.55L17.57,14a2,2,0,0,1,.61,1.47,2.08,2.08,0,0,1-2.09,2.09,2,2,0,0,1-1.47-.61l-.38-.37-1.74,1,.8.78a4,4,0,0,0,5.68,0ZM8,9.77a2,2,0,0,1-1.27-1,1.89,1.89,0,0,1-.21-1.57A2.1,2.1,0,0,1,7.45,6,2,2,0,0,1,9,5.76L12.27,7.2l.49-2L9.48,3.9a4,4,0,0,0-3.06.41A3.82,3.82,0,0,0,4.56,6.73a3.8,3.8,0,0,0,.4,3A3.78,3.78,0,0,0,7.39,11.6l5.38,2,.49-2-2.64-.94L8,9.77Z" transform="translate(-4.41 -3.76)"/></g></svg>',table_header:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.75 15.74"><g><path d="M17,19.5v-.78H15.5v.78H17Zm-3,0v-.78H12.5v.78H14Zm-3,0v-.78H9.53v.78H11Zm-3,0v-.78H6.53v.78H8Zm10.55,0a1.73,1.73,0,0,0,.85-.35,1.67,1.67,0,0,0,.56-.76l-.71-.31a1.21,1.21,0,0,1-.35.4,1.34,1.34,0,0,1-.53.23l.08.38c.06.24.09.38.1.41Zm-13.7-.63.55-.55A.77.77,0,0,1,5.25,18a1.31,1.31,0,0,1-.06-.38v-.38H4.41v.38a2,2,0,0,0,.12.68,1.6,1.6,0,0,0,.35.57Zm15.27-2.12V15.26h-.78v1.49h.78Zm-15-1V14.23H4.41v1.49h.78Zm15-2V12.26h-.78v1.49h.78Zm-15-1V11.22H4.41v1.51h.78Zm15-2V9.26h-.78v1.51h.78Zm-15-1V8.17H4.41V9.74h.78Zm15-2V6.28h-.78V7.77h.78Zm-15-1.11V5.33L4.48,5.1a.77.77,0,0,0-.07.27,2.72,2.72,0,0,0,0,.28v1h.79ZM19.21,5l.63-.4A1.62,1.62,0,0,0,19.16,4a1.94,1.94,0,0,0-.91-.22v.78a1.31,1.31,0,0,1,.56.12.88.88,0,0,1,.4.36ZM6,4.54H7.78V3.76H6a.82.82,0,0,0-.28.06l.12.35c.07.21.1.33.11.36Zm10.8,0V3.76H15.28v.78h1.49Zm-3,0V3.76H12.28v.78h1.49Zm-3,0V3.76H9.28v.78h1.51ZM6,10.84h12.6V6.91H6Z" transform="translate(-4.4 -3.76)"/></g></svg>',merge_cell:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.76 15.74"><g><path d="M18.92,13.5h1.23v4.15A1.84,1.84,0,0,1,18.3,19.5H14V18.27H18.3a.6.6,0,0,0,.44-.18.59.59,0,0,0,.18-.44V13.5ZM18.3,3.76a1.84,1.84,0,0,1,1.85,1.85V9.82H18.92V5.6a.6.6,0,0,0-.18-.44A.59.59,0,0,0,18.3,5H14V3.76H18.3Zm1.85,8.51H15.6L17.26,14l-.86.86-3.14-3.17L16.4,8.51l.86.86L15.62,11h4.54v1.24Zm-13.9,6h4.27V19.5H6.25A1.84,1.84,0,0,1,4.4,17.65V13.5H5.63v4.15a.61.61,0,0,0,.62.62Zm0-14.51h4.27V5H6.25a.6.6,0,0,0-.44.18.57.57,0,0,0-.17.43V9.81H4.41V5.6A1.83,1.83,0,0,1,6.25,3.76Zm5,7.9L8.15,14.83,7.3,14,9,12.27H4.41V11H8.94L7.3,9.38,7.73,9l.43-.43Z" transform="translate(-4.4 -3.76)"/></g></svg>',split_cell:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.75 15.74"><g><path d="M10.37,12.25H6.74L8.4,13.94l-.87.86L4.41,11.63,7.53,8.5l.87.86L6.74,11h3.62v1.23Zm9.78-.61L17,14.81,16.13,14l1.66-1.69H14.16V11h3.63L16.13,9.37l.43-.43A5.24,5.24,0,0,1,17,8.51ZM18.9,8.22V5.61a.57.57,0,0,0-.18-.43A.65.65,0,0,0,18.29,5H12.88V18.28h5.41a.7.7,0,0,0,.44-.18.57.57,0,0,0,.18-.43V15h1.23v2.64a1.84,1.84,0,0,1-1.85,1.83h-12A1.84,1.84,0,0,1,4.94,19a1.81,1.81,0,0,1-.54-1.29V15H5.63v2.64a.57.57,0,0,0,.18.43.67.67,0,0,0,.44.18h5.41V5H6.25a.7.7,0,0,0-.44.18.56.56,0,0,0-.17.43V8.22H4.41V5.61A1.8,1.8,0,0,1,5,4.31a1.91,1.91,0,0,1,1.31-.55h12a1.89,1.89,0,0,1,1.31.55,1.8,1.8,0,0,1,.54,1.3V8.23H18.9Z" transform="translate(-4.4 -3.76)"/></g></svg>',caption:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 13.79"><g><path d="M4.41,18.52H20.15v-2H4.41ZM20,4.73H18.07V6h.65v.65H20V4.73ZM17,6V4.73H14.55V6H17ZM13.49,6V4.73H11V6h2.47ZM10,6V4.73H7.5V6H10ZM5.79,6h.65V4.73H4.5V6.67H5.8V6ZM4.5,11.34H5.79V8.48H4.5ZM6.44,13.8H5.79v-.65H4.5v1.94H6.44ZM17,15.09V13.8H14.55v1.29H17Zm-3.52,0V13.8H11v1.29h2.47Zm-3.53,0V13.8H7.5v1.29H10ZM20,13.16H18.72v.65h-.65V15.1H20Zm-1.29-1.82H20V8.48h-1.3v2.86Z" transform="translate(-4.41 -4.73)"/></g></svg>',edit:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 15.73"><g><path d="M7.51,5.68h6l1.52-1.57H6.94a2.4,2.4,0,0,0-1.79.82A2.8,2.8,0,0,0,4.41,6.8V17a2.55,2.55,0,0,0,.75,1.8A2.48,2.48,0,0,0,7,19.5H17.22a2.57,2.57,0,0,0,1.83-.74,2.52,2.52,0,0,0,.77-1.8V8.83l-1.58,1.54v6a1.54,1.54,0,0,1-1.53,1.53H7.51A1.54,1.54,0,0,1,6,16.41V7.21A1.52,1.52,0,0,1,7.51,5.68Zm5.63,7.47h0L10.7,10.74l-1,3.38,1.71-.48,1.7-.49Zm.34-.34h0l5.36-5.32L16.4,5.08,11,10.4l1.23,1.21,1.21,1.2ZM19.93,6.4a.82.82,0,0,0,.22-.48A.54.54,0,0,0,20,5.47L18.45,4A.67.67,0,0,0,18,3.77a.7.7,0,0,0-.48.21l-.74.72,2.44,2.43.37-.37.35-.36Z" transform="translate(-4.41 -3.77)"/></g></svg>',delete:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.73 15.74"><g><path d="M19.16,6.71a.94.94,0,0,0,.69-.28.91.91,0,0,0,.29-.68A1,1,0,0,0,19.85,5a.93.93,0,0,0-.69-.3H14.24A.94.94,0,0,0,14,4.06a.92.92,0,0,0-.7-.3h-2a1,1,0,0,0-.7.3.93.93,0,0,0-.28.68H5.39A.92.92,0,0,0,4.7,5a1,1,0,0,0-.29.71.91.91,0,0,0,.29.68,1,1,0,0,0,.69.28H19.16Zm-12.79,1a1,1,0,0,0-.7.3.94.94,0,0,0-.28.69v8.85A1.88,1.88,0,0,0,6,18.93a1.9,1.9,0,0,0,1.39.57H17.2a1.87,1.87,0,0,0,1.39-.58,1.91,1.91,0,0,0,.58-1.39V8.68A1,1,0,0,0,18.88,8a.89.89,0,0,0-.7-.29,1,1,0,0,0-.69.29.92.92,0,0,0-.29.68v7.87a1,1,0,0,1-1,1H8.34a.94.94,0,0,1-.69-.28,1,1,0,0,1-.29-.71V8.68a1,1,0,0,0-1-1Z" transform="translate(-4.41 -3.76)"/></g></svg>',modify:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.7 15.74"><g><path d="M19.79,15.23a.66.66,0,0,1,.3.38.59.59,0,0,1-.07.48l-.8,1.38a.66.66,0,0,1-.38.3.59.59,0,0,1-.48-.07l-.68-.38a4.55,4.55,0,0,1-1.34.77v.78a.64.64,0,0,1-.18.45.61.61,0,0,1-.45.18h-1.6a.6.6,0,0,1-.44-.18.66.66,0,0,1-.19-.45v-.78a4.36,4.36,0,0,1-1.32-.77l-.69.38a.58.58,0,0,1-.48.07.66.66,0,0,1-.38-.3l-.38-.66h.83a1.77,1.77,0,0,0,1.23-.52,1.72,1.72,0,0,0,.51-1.23v-.18a3,3,0,0,0,.49-.28l.15.09a1.83,1.83,0,0,0,.88.23A1.75,1.75,0,0,0,15.84,14l.88-1.52a1.7,1.7,0,0,0,.17-1.32,1.66,1.66,0,0,0-.3-.61,1.84,1.84,0,0,0-.51-.45l-.15-.09,0-.29,0-.28.15-.09a1,1,0,0,0,.26-.18l0,.06v.78a4.34,4.34,0,0,1,1.34.77l.68-.38a.68.68,0,0,1,.48-.06.64.64,0,0,1,.38.29l.8,1.38a.58.58,0,0,1,.07.48.63.63,0,0,1-.3.38l-.68.4a3.84,3.84,0,0,1,.08.76,4.13,4.13,0,0,1-.08.78l.34.18.32.2ZM10.17,7.86a1.9,1.9,0,0,1,1.35,3.23,1.85,1.85,0,0,1-1.35.55A1.9,1.9,0,0,1,8.83,8.41a1.92,1.92,0,0,1,1.34-.55Zm1.58,7.2a.73.73,0,0,1-.21.49.66.66,0,0,1-.48.2H9.29a.68.68,0,0,1-.69-.69V14.2a4.75,4.75,0,0,1-1.48-.86l-.75.45a.73.73,0,0,1-.7,0,.63.63,0,0,1-.25-.26L4.54,12a.67.67,0,0,1-.08-.53.71.71,0,0,1,.32-.42l.75-.43a4.8,4.8,0,0,1-.08-.85,4.71,4.71,0,0,1,.08-.85l-.74-.44a.71.71,0,0,1-.32-.42.65.65,0,0,1,.07-.54L5.42,6a.66.66,0,0,1,.42-.32l.18,0a.73.73,0,0,1,.35.09l.75.43A4.68,4.68,0,0,1,8.6,5.33V4.45a.68.68,0,0,1,.69-.69h1.77a.64.64,0,0,1,.48.2.73.73,0,0,1,.21.49v.88a4.75,4.75,0,0,1,1.48.85L14,5.75a.67.67,0,0,1,.34-.09l.18,0a.71.71,0,0,1,.42.32l.89,1.54a.67.67,0,0,1,.06.52.73.73,0,0,1-.32.43l-.75.42a4.8,4.8,0,0,1,.08.85,4.71,4.71,0,0,1-.08.85l.75.43a.66.66,0,0,1,.32.42.73.73,0,0,1-.06.54l-.89,1.52a.69.69,0,0,1-.25.26.7.7,0,0,1-.35.09.64.64,0,0,1-.34-.09l-.75-.45a4.87,4.87,0,0,1-1.48.86v.87ZM7.23,9.75a3,3,0,0,0,.86,2.08,2.94,2.94,0,1,0,4.16-4.16,3,3,0,0,0-2.08-.85A2.94,2.94,0,0,0,7.23,9.75Z" transform="translate(-4.44 -3.76)"/></g></svg>',revert:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.76 14.69"><g><path d="M18.26,15V12.3l1.89-2V15a2.58,2.58,0,0,1-.24,1c-.2.58-.75.92-1.65,1H7.56v2L4.41,15.63,7.56,13v2h10.7ZM6.3,8.28V11L4.41,13V8.28a2.58,2.58,0,0,1,.24-1c.2-.58.75-.92,1.65-1H17v-2l3.15,3.34L17,10.3v-2H6.3Z" transform="translate(-4.4 -4.28)"/></g></svg>',auto_size:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 15.74"><g><path d="M6.71,17.19,6.89,16l1.21-.15A6,6,0,0,1,6.81,13.9a5.78,5.78,0,0,1-.45-2.27A6,6,0,0,1,8.1,7.45a5.83,5.83,0,0,1,4.17-1.73l1-1-1-1A7.89,7.89,0,0,0,5,14.64a7.73,7.73,0,0,0,1.71,2.55Zm5.57,2.31h0A7.86,7.86,0,0,0,17.85,6.07L17.67,7.3l-1.21.15a5.9,5.9,0,0,1,1.29,1.92,5.81,5.81,0,0,1,.45,2.26,5.91,5.91,0,0,1-5.9,5.9l-1,1,.49.49.47.5Z" transform="translate(-4.41 -3.76)"/></g></svg>',insert_row_below:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.8 15.8"><g><path d="M15.7,1.3c-0.1-0.1-0.1-0.2-0.2-0.2L15.3,1H0.4L0.3,1.1c0,0-0.1,0.1-0.1,0.1c0,0-0.1,0.1-0.1,0.1L0,1.4v7.7l0.1,0.1c0,0.1,0.1,0.1,0.2,0.2l0.1,0.1h2.3V9.3l0.1-0.5L3,8.5l0.1-0.2c-0.1,0-0.2,0-0.3,0H1.2v-6h13.3v6h-1.6c-0.1,0-0.2,0-0.3,0l0.1,0.2l0.2,0.4C12.9,9,13,9.2,13,9.3v0.1h2.3l0.2-0.1c0.1,0,0.1-0.1,0.2-0.2l0.1-0.1V1.4L15.7,1.3z"/><path d="M10.5,7.5C9.9,7.1,9.3,6.8,8.6,6.7c-0.2,0-0.5-0.1-0.7,0c-0.2,0-0.5,0-0.7,0C6.6,6.7,6.1,6.9,5.6,7.3C5.2,7.6,4.7,8,4.4,8.4C4.3,8.6,4.2,8.8,4.2,8.9C4.1,9.1,4,9.3,3.9,9.4C3.9,9.6,3.8,9.7,3.8,9.9c0,0.2-0.1,0.3-0.1,0.5v-0.1c-0.1,0.8,0.1,1.6,0.5,2.4c0.4,0.7,1,1.3,1.7,1.7c0.3,0.2,0.6,0.3,0.9,0.3c0.3,0.1,0.7,0.1,1,0.1c0.3,0,0.7,0,1-0.1c0.3-0.1,0.6-0.2,0.9-0.3c0.5-0.3,0.9-0.6,1.3-1c0.3-0.4,0.6-0.8,0.8-1.3c0.1-0.4,0.2-0.9,0.2-1.4c0-0.5-0.1-1-0.3-1.4C11.5,8.6,11.1,8,10.5,7.5z M10.1,11.3H8.5v1.6H8H7.9H7.3v0v-0.1v-1.4H5.7v-0.4v-0.2v-0.6h0h1.5V8.5h1.2v1.6h1.6V11.3z"/></g></svg>',insert_row_above:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.8 15.8"><g><path d="M0.1,14.5c0.1,0.1,0.1,0.2,0.2,0.2l0.1,0.1h14.9l0.1-0.1c0,0,0.1-0.1,0.1-0.1c0,0,0.1-0.1,0.1-0.1l0.1-0.1V6.7l-0.1-0.1c0-0.1-0.1-0.1-0.2-0.2l-0.1-0.1h-2.3v0.1l-0.1,0.5l-0.2,0.4l-0.1,0.2c0.1,0,0.2,0,0.3,0h1.6v6H1.3v-6h1.6c0.1,0,0.2,0,0.3,0L3.1,7.3L2.9,6.9C2.8,6.8,2.8,6.6,2.7,6.5V6.3H0.4L0.3,6.4c-0.1,0-0.1,0.1-0.2,0.2L0,6.7v7.7L0.1,14.5z"/><path d="M5.3,8.3c0.6,0.5,1.2,0.8,1.9,0.9c0.2,0,0.5,0.1,0.7,0c0.2,0,0.5,0,0.7,0c0.6-0.1,1.1-0.3,1.6-0.6c0.5-0.3,0.9-0.7,1.2-1.2c0.1-0.2,0.2-0.3,0.3-0.5c0.1-0.2,0.2-0.4,0.2-0.5c0.1-0.1,0.1-0.3,0.1-0.4C12,5.8,12,5.6,12,5.4v0.1c0.1-0.8-0.1-1.6-0.5-2.4c-0.4-0.7-1-1.3-1.7-1.7C9.5,1.3,9.2,1.2,8.9,1.1C8.5,1,8.2,1,7.9,1c-0.3,0-0.7,0-1,0.1C6.6,1.2,6.3,1.3,6,1.4C5.5,1.7,5.1,2,4.7,2.4C4.4,2.8,4.1,3.3,3.9,3.8C3.8,4.2,3.7,4.7,3.7,5.2c0,0.5,0.1,1,0.3,1.4C4.3,7.2,4.7,7.8,5.3,8.3z M5.7,4.5h1.6V2.9h0.5h0.1h0.6v0v0.1v1.4H10v0.4v0.2v0.6h0H8.5v1.6H7.3V5.7H5.7V4.5z"/></g></svg>',insert_column_left:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.8 15.8"><g><path d="M14.5,15.7c0.1-0.1,0.2-0.1,0.2-0.2l0.1-0.1V0.4l-0.1-0.1c0,0-0.1-0.1-0.1-0.1c0,0-0.1-0.1-0.1-0.1L14.4,0H6.7L6.6,0.1c-0.1,0-0.1,0.1-0.2,0.2L6.3,0.4v2.3h0.1l0.5,0.1L7.3,3l0.2,0.1c0-0.1,0-0.2,0-0.3V1.2h6v13.3h-6v-1.6c0-0.1,0-0.2,0-0.3l-0.2,0.1l-0.4,0.2C6.7,12.9,6.6,13,6.4,13H6.3v2.3l0.1,0.2c0,0.1,0.1,0.1,0.2,0.2l0.1,0.1h7.7L14.5,15.7z"/><path d="M8.3,10.5C8.7,10,9,9.3,9.1,8.6c0-0.2,0.1-0.5,0-0.7c0-0.2,0-0.5,0-0.7C9,6.7,8.8,6.1,8.5,5.7C8.2,5.2,7.8,4.8,7.3,4.5C7.2,4.4,7,4.3,6.9,4.2C6.7,4.1,6.5,4,6.4,4C6.2,3.9,6.1,3.9,5.9,3.8c-0.2,0-0.3-0.1-0.5-0.1h0.1C4.7,3.7,3.8,3.9,3.1,4.3C2.4,4.7,1.8,5.3,1.4,6C1.3,6.3,1.2,6.6,1.1,6.9C1,7.2,1,7.6,1,7.9c0,0.3,0,0.7,0.1,1c0.1,0.3,0.2,0.6,0.3,0.9c0.3,0.5,0.6,0.9,1,1.3c0.4,0.3,0.8,0.6,1.3,0.8C4.2,12,4.7,12.1,5.1,12c0.5,0,1-0.1,1.4-0.3C7.2,11.5,7.8,11.1,8.3,10.5zM4.5,10.1V8.5H2.9V8V7.9V7.3h0H3h1.4V5.7h0.4h0.2h0.6v0v1.5h1.6v1.2H5.7v1.6H4.5z"/></g></svg>',insert_column_right:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.8 15.8"><g><path d="M1.3,0.1C1.2,0.2,1.1,0.2,1.1,0.3L1,0.4v14.9l0.1,0.1c0,0,0.1,0.1,0.1,0.1c0,0,0.1,0.1,0.1,0.1l0.1,0.1h7.7l0.1-0.1c0.1,0,0.1-0.1,0.2-0.2l0.1-0.1v-2.3H9.3l-0.5-0.1l-0.4-0.2l-0.2-0.1c0,0.1,0,0.2,0,0.3v1.6h-6V1.3h6v1.6c0,0.1,0,0.2,0,0.3l0.2-0.1l0.4-0.2C9,2.9,9.2,2.8,9.3,2.8h0.1V0.5L9.4,0.3c0-0.1-0.1-0.1-0.2-0.2L9.1,0H1.4L1.3,0.1z"/><path d="M7.5,5.3C7,5.8,6.7,6.5,6.6,7.2c0,0.2-0.1,0.5,0,0.7c0,0.2,0,0.5,0,0.7c0.1,0.6,0.3,1.1,0.6,1.6c0.3,0.5,0.7,0.9,1.2,1.2c0.2,0.1,0.3,0.2,0.5,0.3c0.2,0.1,0.4,0.2,0.5,0.2c0.1,0.1,0.3,0.1,0.4,0.1c0.2,0,0.3,0.1,0.5,0.1h-0.1c0.8,0.1,1.6-0.1,2.4-0.5c0.7-0.4,1.3-1,1.7-1.7c0.2-0.3,0.3-0.6,0.3-0.9c0.1-0.3,0.1-0.7,0.1-1c0-0.3,0-0.7-0.1-1c-0.1-0.3-0.2-0.6-0.3-0.9c-0.3-0.5-0.6-0.9-1-1.3C13,4.4,12.5,4.2,12,4c-0.4-0.1-0.9-0.2-1.4-0.2c-0.5,0-1,0.1-1.4,0.2C8.5,4.3,7.9,4.7,7.5,5.3z M11.3,5.7v1.6h1.6v0.5v0.1v0.6h0h-0.1h-1.4v1.6h-0.4h-0.2h-0.6v0V8.5H8.5V7.3h1.6V5.7H11.3z"/></g></svg>',delete_row:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.75 13.83"><g><path d="M4.7,18.46l.12.08H19.73l.12-.08a.58.58,0,0,0,.22-.22l.08-.12,0-7.69-.08-.11a.77.77,0,0,0-.18-.18l-.11-.08-2.31,0-.08.28-.1.29a1.58,1.58,0,0,1-.12.29l-.14.34s0,0,.18,0H18.9v6H5.64v-6H7.35c.14,0,.2,0,.18,0l-.14-.34a2.85,2.85,0,0,1-.12-.29l-.1-.29-.07-.27-2.31,0-.11.08a.77.77,0,0,0-.18.18l-.08.11,0,7.69.08.12a.47.47,0,0,0,.09.12l.13.09ZM12.11,13a4,4,0,0,0,1.46-.21,4.51,4.51,0,0,0,1.31-.71A4,4,0,0,0,16.26,10a4.32,4.32,0,0,0-.08-2.54,4.34,4.34,0,0,0-1-1.52,4.15,4.15,0,0,0-1.54-1,4.34,4.34,0,0,0-1.35-.22A4.07,4.07,0,0,0,11,4.93,3.94,3.94,0,0,0,9.24,6.07,3.92,3.92,0,0,0,8.15,8.88a3.91,3.91,0,0,0,.12.95A4.16,4.16,0,0,0,12.11,13Zm2.35-4.14v.58H10.09V8.27h4.37v.58Z" transform="translate(-4.4 -4.71)"/></g></svg>',delete_column:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13.81 15.74"><g><path d="M5.66,19.42l.12.08,7.69,0,.11-.08a.77.77,0,0,0,.18-.18l.08-.11,0-2.32-.15,0-.45-.15-.42-.18-.17-.07a1,1,0,0,0,0,.27v1.63h-6V5h6V6.62a.9.9,0,0,0,0,.26l.17-.07.42-.17a3.91,3.91,0,0,1,.45-.15l.15,0,0-2.32L13.75,4a.77.77,0,0,0-.18-.18l-.11-.08H5.79l-.13.07a.63.63,0,0,0-.21.22l-.08.12V19.08l.08.12a.47.47,0,0,0,.09.12.35.35,0,0,0,.12.1Zm9-3.67a4.16,4.16,0,0,0,2.36-.51,4.08,4.08,0,0,0,1.67-1.72,4,4,0,0,0,.35-.91,3.79,3.79,0,0,0,.1-1,4.71,4.71,0,0,0-.11-1,5,5,0,0,0-.3-.87,4.25,4.25,0,0,0-1-1.25,4.49,4.49,0,0,0-1.34-.81A4.26,4.26,0,0,0,15,7.48a3.88,3.88,0,0,0-1.41.25A4.32,4.32,0,0,0,11.86,9,4,4,0,0,0,11,10.94a4.4,4.4,0,0,0-.05.68,4.5,4.5,0,0,0,.05.68,3.93,3.93,0,0,0,.61,1.57,4.22,4.22,0,0,0,1.18,1.2,4.59,4.59,0,0,0,.48.27c.2.1.37.17.5.22a2.44,2.44,0,0,0,.45.12,4.61,4.61,0,0,0,.5.07Zm2.54-4.12v.58H12.87V11h4.37v.59Z" transform="translate(-5.37 -3.76)"/></g></svg>',fixed_column_width:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M6,5H18A1,1 0 0,1 19,6A1,1 0 0,1 18,7H6A1,1 0 0,1 5,6A1,1 0 0,1 6,5M21,2V4H3V2H21M15,8H17V22H15V8M7,8H9V22H7V8M11,8H13V22H11V8Z" /></svg>',rotate_left:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.8 15.8"><g><path d="M0.5,10.2c0,0.1,0,0.2,0,0.3v0.2l0,0c0.1,0.3,0.3,0.6,0.4,0.9l0,0C1,11.8,1.3,12,1.5,11.9h0.1h0.2h0.1c0.1-0.1,0.3-0.3,0.4-0.5v-0.2c0-0.1,0-0.2-0.1-0.3l0,0c-0.2-0.2-0.3-0.4-0.3-0.7l0,0C1.8,10,1.7,9.9,1.5,9.8c-0.1,0-0.2,0-0.3,0H0.9C0.7,9.9,0.6,10,0.5,10.2L0.5,10.2z"/><path d="M2.2,11.5L2.2,11.5L2.2,11.5z"/><path d="M5.9,3.6L5.9,3.6L5.9,3.6z"/><path d="M0.1,7.9c0,0.3,0,0.6,0,0.9l0,0l0,0l0,0l0,0c0,0.2,0.1,0.3,0.2,0.4l0,0c0.2,0.1,0.3,0.2,0.5,0.2l0,0l0,0c0.2,0,0.4-0.1,0.5-0.3l0,0c0-0.1,0.1-0.3,0.1-0.4V8.6l0,0c0-0.2,0-0.5,0-0.7l0,0c0-0.2-0.1-0.4-0.2-0.5C1.1,7.3,0.9,7.2,0.7,7.2S0.3,7.3,0.2,7.4C0.1,7.5,0,7.7,0.1,7.9z"/><path d="M1.9,12.7L1.9,12.7c0,0.2,0,0.4,0.2,0.5l0,0l0.2,0.3l0,0c0.2,0.1,0.3,0.2,0.5,0.4l0,0l0,0l0,0l0,0C2.9,14,3,14.1,3.2,14.1s0.4-0.1,0.5-0.2c0.1-0.2,0.2-0.3,0.2-0.5v-0.1c0-0.2-0.1-0.4-0.2-0.5l0,0l-0.4-0.4l-0.2-0.2l0,0C3,12.1,2.8,12,2.6,12l0,0c-0.2,0-0.4,0.1-0.5,0.2l0,0C2,12.3,1.9,12.5,1.9,12.7z"/><path d="M6.6,15c0,0.2,0.1,0.4,0.2,0.5c0.1,0.1,0.2,0.2,0.4,0.3l0,0c0.3,0,0.5,0,0.7,0h0.3l0,0c0.2,0,0.4-0.1,0.5-0.2c0.1-0.2,0.2-0.3,0.2-0.5l0,0l0,0c0-0.2-0.1-0.4-0.2-0.5l0,0c-0.1-0.1-0.3-0.2-0.5-0.2l0,0H7.9c-0.1,0-0.3,0-0.5,0l0,0H7.3c-0.2-0.1-0.3,0-0.5,0.1l0,0C6.7,14.6,6.6,14.8,6.6,15L6.6,15L6.6,15L6.6,15z"/><path d="M4.2,7.4C4,7.5,4,7.7,4,7.9c0,0.2,0,0.4,0.2,0.5l0,0l3.2,3.2l0,0c0.1,0.1,0.3,0.2,0.5,0.2s0.3-0.1,0.5-0.2l0,0l3.2-3.2l0,0c0.1-0.1,0.2-0.3,0.2-0.5c0-0.2-0.1-0.4-0.2-0.5l0,0C11.5,7.3,11,6.7,10,5.8l0,0L8.4,4.2l0,0C8.3,4.1,8.1,4,7.9,4S7.5,4.1,7.4,4.2L4.2,7.4L4.2,7.4z M6.8,9L5.7,7.9l2.2-2.2l2.3,2.2l-2.3,2.2C7.7,9.9,7.3,9.5,6.8,9L6.8,9z"/><path d="M4.1,14.1C4,14.2,4,14.3,4,14.4v0.2l0,0c0.1,0.1,0.2,0.3,0.4,0.4l0,0c0.3,0.1,0.6,0.2,0.9,0.4h0.1h0.1l0,0c0.2,0,0.3-0.1,0.5-0.1l0,0c0.2-0.1,0.3-0.3,0.3-0.4l0,0l0,0l0,0l0,0v-0.2c0-0.1-0.1-0.2-0.1-0.3l0,0C6.1,14.2,6,14.1,5.8,14l0,0c-0.3-0.1-0.5-0.2-0.8-0.2l0,0c-0.1-0.1-0.2-0.1-0.3-0.1H4.5C4.3,13.7,4.2,13.9,4.1,14.1z"/><path d="M9.3,14.4c0,0.1-0.1,0.3,0,0.4V15l0,0c0,0.1,0.1,0.3,0.5,0.4c0.1,0.1,0.3,0.1,0.4,0.1l0,0h0.1l0,0c0.3-0.1,0.6-0.2,0.9-0.3l0,0c0.1-0.1,0.2-0.2,0.3-0.4l0.1-0.3c0-0.1-0.1-0.2-0.1-0.3l0,0c-0.1-0.2-0.2-0.3-0.4-0.4l0,0h-0.3c-0.1,0-0.2,0-0.3,0l0,0c-0.2,0.1-0.5,0.2-0.8,0.3l0,0C9.5,14.1,9.4,14.2,9.3,14.4L9.3,14.4z"/><path d="M11.4,14.7L11.4,14.7L11.4,14.7z"/><path d="M9.5,15.3L9.5,15.3L9.5,15.3z"/><path d="M15.9,7.9c0-1-0.2-2-0.6-3l0,0c-0.4-1-1-1.9-1.7-2.6C12.8,1.6,12,1,11,0.6l0,0C10.1,0.2,9,0,8,0C7.3,0,6.5,0.1,5.8,0.3l0,0C5.2,0.5,4.6,0.8,4,1.1L3.1,0.2l0,0C2.9,0.1,2.8,0,2.6,0H2.4l0,0C2.2,0,2,0.2,1.9,0.4l0,0L0.1,4.9l0,0C0,5,0,5.1,0,5.2c0,0.2,0.1,0.4,0.2,0.5l0,0c0.2,0.1,0.3,0.2,0.5,0.2h0.1H1l0,0l4.7-1.8l0,0C5.9,4,6.1,3.8,6.1,3.6V3.4C6.1,3.2,6,3,5.9,2.9l0,0L5.1,2.1c0.4-0.2,0.8-0.4,1.3-0.5c0.5-0.1,1.1-0.2,1.7-0.2c0.9,0,1.7,0.2,2.5,0.5l0,0c0.8,0.3,1.5,0.8,2.1,1.4c0.6,0.6,1.1,1.3,1.4,2.1l0,0c0.3,0.8,0.5,1.6,0.5,2.5s-0.2,1.7-0.5,2.5l0,0c-0.3,0.8-0.8,1.5-1.4,2.1c-0.2,0.2-0.4,0.3-0.6,0.5l0,0c-0.2,0.1-0.3,0.3-0.3,0.5v0.1c0,0.1,0,0.3,0.1,0.4l0,0c0.1,0.2,0.3,0.3,0.5,0.3l0,0c0.1,0,0.3-0.1,0.4-0.2l0,0l0,0l0,0l0,0c0.2-0.2,0.5-0.4,0.7-0.6l0,0l0,0l0,0l0,0c0.7-0.8,1.3-1.6,1.7-2.6C15.6,10,15.8,9,15.9,7.9z M1.9,4C2,3.8,2.1,3.5,2.3,3.1l0,0L2.7,2l1.2,1.2L1.9,4z"/><path d="M6.8,15.5L6.8,15.5L6.8,15.5z"/></g></svg>',rotate_right:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.8 15.8"><g><path d="M9.9,15.3L9.9,15.3L9.9,15.3z"/><path d="M6.9,15.1L6.9,15.1c0,0.1,0.1,0.3,0.2,0.4l0,0c0.1,0.2,0.3,0.3,0.5,0.3l0,0h0.3c0.2,0,0.4,0,0.7,0l0,0c0.2-0.1,0.3-0.2,0.4-0.3c0.1-0.1,0.2-0.2,0.2-0.4V15c0-0.2-0.1-0.4-0.2-0.4c-0.2-0.1-0.3-0.2-0.5-0.2H8.4l0,0c-0.1,0-0.3,0-0.5,0H7.6l0,0c-0.2,0-0.4,0.1-0.5,0.2C7,14.7,6.9,14.9,6.9,15.1z"/><path d="M6.5,14.4L6.5,14.4L6.5,14.4z"/><path d="M5.8,5.8L5.8,5.8c-1,0.9-1.5,1.5-1.7,1.6l0,0C4,7.5,4,7.7,4,7.9c0,0.2,0,0.4,0.2,0.5l0,0l3.2,3.2l0,0c0.2,0.1,0.3,0.2,0.5,0.2s0.4-0.1,0.5-0.2l0,0l3.2-3.2l0,0c0.1-0.1,0.2-0.3,0.2-0.5c0-0.2-0.1-0.4-0.2-0.5l0,0L8.4,4.2C8.3,4.1,8.1,4,7.9,4C7.7,4,7.5,4.1,7.4,4.2l0,0L5.8,5.8z M5.6,7.9l2.3-2.2l2.2,2.2L9,9l0,0l0,0l0,0l0,0c-0.5,0.6-0.9,0.9-1.1,1.1L5.6,7.9z"/><path d="M9,15.5L9,15.5L9,15.5z"/><path d="M9.6,14.7v0.2l0,0l0,0l0,0l0,0c0.1,0.2,0.1,0.3,0.3,0.3c0.1,0.1,0.3,0.1,0.4,0.1l0,0h0.1h0.1c0.3-0.1,0.6-0.3,0.9-0.4l0,0c0.1-0.1,0.2-0.2,0.3-0.4l0,0v-0.2c0-0.1,0-0.2-0.1-0.3c-0.1-0.2-0.2-0.3-0.4-0.4H11c-0.1,0-0.2,0.1-0.3,0.1l0,0c-0.2,0.1-0.4,0.2-0.7,0.3l0,0l0,0c-0.1,0.1-0.3,0.2-0.4,0.4C9.6,14.5,9.6,14.6,9.6,14.7z"/><path d="M9,14.5L9,14.5L9,14.5z"/><path d="M9.6,14.4L9.6,14.4L9.6,14.4z"/><path d="M11.7,14L11.7,14L11.7,14z"/><path d="M15.6,7.4L15.6,7.4L15.6,7.4z"/><path d="M15,9.4c0.2,0,0.4,0,0.6-0.2l0,0c0.1-0.1,0.2-0.2,0.2-0.4l0,0l0,0l0,0l0,0c0-0.3,0-0.6,0-0.9c0-0.2-0.1-0.4-0.2-0.5c-0.1-0.1-0.3-0.2-0.5-0.2s-0.4,0.1-0.5,0.2c-0.1,0.1-0.2,0.3-0.2,0.5l0,0c0,0.2,0,0.4,0,0.7l0,0v0.1c0,0.1,0,0.3,0.1,0.4l0,0C14.6,9.3,14.8,9.4,15,9.4L15,9.4L15,9.4z"/><path d="M14,12h0.1h0.2h0.1c0.2,0,0.5-0.2,0.6-0.4l0,0c0.2-0.3,0.3-0.6,0.4-0.9l0,0v-0.2c0-0.1-0.1-0.2-0.1-0.3c-0.1-0.2-0.2-0.3-0.4-0.4h-0.3c-0.1,0-0.2,0-0.3,0C14.2,9.9,14,10,14,10.3l0,0c-0.1,0.2-0.2,0.5-0.3,0.7l0,0c-0.1,0.1-0.1,0.2-0.1,0.3v0.2l0,0l0,0C13.6,11.6,13.8,11.8,14,12z"/><path d="M14.6,7.4L14.6,7.4L14.6,7.4z"/><path d="M4.4,14.2c-0.1,0.1-0.1,0.2-0.1,0.3l0.1,0.2c0,0.2,0.2,0.3,0.3,0.4l0,0c0.3,0.1,0.6,0.3,1.1,0.4l0,0h0.1l0,0c0.1,0,0.2-0.1,0.4-0.2c0.1,0,0.2-0.2,0.3-0.3l0,0v-0.2c0-0.1-0.1-0.3-0.2-0.4c-0.1-0.1-0.2-0.2-0.4-0.3l0,0c-0.2-0.1-0.5-0.2-0.7-0.3l0,0c-0.1,0-0.2,0-0.3,0H4.7l0,0C4.6,13.9,4.4,14,4.4,14.2L4.4,14.2z"/><path d="M11.9,13.3c0,0.2,0.1,0.4,0.2,0.6c0.1,0.1,0.3,0.2,0.5,0.2s0.4-0.1,0.5-0.2l0,0l0,0l0,0l0,0c0.1-0.1,0.3-0.3,0.4-0.4l0,0l0.2-0.3l0,0c0.1-0.2,0.2-0.3,0.2-0.5l0,0c0-0.2-0.1-0.4-0.2-0.5l0,0c-0.1-0.1-0.3-0.2-0.5-0.2l0,0c-0.2,0-0.4,0.1-0.5,0.2l0,0l-0.2,0.2l-0.4,0.4l0,0C12,13,11.9,13.1,11.9,13.3L11.9,13.3z"/><path d="M12.1,13.8L12.1,13.8L12.1,13.8z"/><path d="M11.9,13.3L11.9,13.3L11.9,13.3z"/><path d="M15.9,5.2c0-0.1-0.1-0.2-0.1-0.3l0,0L14,0.4l0,0C13.9,0.2,13.7,0,13.5,0l0,0l0,0h-0.2c-0.2,0-0.4,0.1-0.5,0.2l0,0l-0.9,0.9c-0.5-0.3-1.1-0.6-1.8-0.8l0,0C9.4,0.1,8.7,0,7.9,0c-1,0-2,0.2-3,0.6S3,1.6,2.3,2.3C1.6,3.1,1,3.9,0.6,4.9l0,0C0.2,5.8,0,6.8,0,7.9c0,1,0.2,2,0.6,3s0.9,1.8,1.7,2.6l0,0l0,0l0,0l0,0c0.2,0.2,0.5,0.4,0.7,0.6l0,0l0,0l0,0l0,0c0.2,0.1,0.3,0.2,0.5,0.2l0,0c0.2,0,0.4-0.1,0.6-0.3l0,0c0.1-0.1,0.1-0.3,0.1-0.4v-0.1l0,0C4.1,13.3,4,13.1,3.9,13l0,0c-0.2-0.1-0.4-0.3-0.6-0.5c-0.6-0.6-1.1-1.3-1.4-2.1l0,0C1.6,9.6,1.4,8.8,1.4,7.9s0.2-1.7,0.5-2.5l0,0c0.3-0.8,0.8-1.5,1.4-2.1c0.6-0.6,1.3-1.1,2.1-1.4l0,0C6.2,1.6,7,1.4,7.9,1.4c0.6,0,1.1,0.1,1.7,0.2c0.5,0.1,0.9,0.3,1.3,0.5l-0.8,0.8l0,0C10,3.1,9.9,3.2,9.9,3.4v0.2l0,0l0,0c0,0.2,0.2,0.4,0.4,0.5l0,0l4.5,1.8l0,0H15h0.1c0.2,0,0.4-0.1,0.5-0.2l0,0C15.7,5.6,15.8,5.4,15.9,5.2z M11.8,3.2L13,2l0.4,1.1l0,0c0.2,0.4,0.3,0.7,0.4,0.9L11.8,3.2z"/></g></svg>',mirror_horizontal:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14.75 15.74"><g><path d="M13.75,3.76l5.9,15.74h-5.9V3.76ZM4.9,19.5,10.8,3.76V19.5H4.9Z" transform="translate(-4.9 -3.76)"/></g></svg>',mirror_vertical:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.74 14.75"><g><path d="M20.15,13.1,4.41,19V13.1H20.15ZM4.41,4.25l15.74,5.9H4.41V4.25Z" transform="translate(-4.41 -4.25)"/></g></svg>',checked:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.75 12.1"><g><path d="M4.59,12.23l.12.18L9.43,17.5a.58.58,0,0,0,.84,0L20,7.45h0a.58.58,0,0,0,0-.84l-.85-.85a.58.58,0,0,0-.84,0H18.2l-8.12,8.41a.29.29,0,0,1-.42,0l-3.4-3.63a.58.58,0,0,0-.84,0l-.85.85a.6.6,0,0,0-.14.21.51.51,0,0,0,0,.44c.05.06.1.13.16.19Z" transform="translate(-4.38 -5.58)"/></g></svg>',line_break:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19,6a1,1,0,0,0-1,1v4a1,1,0,0,1-1,1H7.41l1.3-1.29A1,1,0,0,0,7.29,9.29l-3,3a1,1,0,0,0-.21.33,1,1,0,0,0,0,.76,1,1,0,0,0,.21.33l3,3a1,1,0,0,0,1.42,0,1,1,0,0,0,0-1.42L7.41,14H17a3,3,0,0,0,3-3V7A1,1,0,0,0,19,6Z"/></svg>',audio:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M14,3.23V5.29C16.89,6.15 19,8.83 19,12C19,15.17 16.89,17.84 14,18.7V20.77C18,19.86 21,16.28 21,12C21,7.72 18,4.14 14,3.23M16.5,12C16.5,10.23 15.5,8.71 14,7.97V16C15.5,15.29 16.5,13.76 16.5,12M3,9V15H7L12,20V4L7,9H3Z" /></svg>',image_gallery:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="30 30 150 150"><g><path d="M152.775,120.548V51.651c0-12.271-9.984-22.254-22.254-22.254H43.727c-12.271,0-22.254,9.983-22.254,22.254v68.896c0,12.27,9.983,22.254,22.254,22.254h86.795C142.791,142.802,152.775,132.817,152.775,120.548z M36.394,51.651c0-4.042,3.291-7.333,7.333-7.333h86.795c4.042,0,7.332,3.291,7.332,7.333v23.917l-14.938-17.767c-1.41-1.678-3.487-2.649-5.68-2.658h-0.029c-2.184,0-4.255,0.954-5.674,2.613L76.709,98.519l-9.096-9.398c-1.427-1.474-3.392-2.291-5.448-2.273c-2.052,0.025-4.004,0.893-5.396,2.4L36.394,111.32V51.651z M41.684,127.585l20.697-22.416l9.312,9.622c1.461,1.511,3.489,2.334,5.592,2.27c2.101-0.066,4.075-1.013,5.44-2.612l34.436-40.308l20.693,24.613v21.794c0,4.042-3.29,7.332-7.332,7.332H43.727C43.018,127.88,42.334,127.775,41.684,127.585z M182.616,152.5V75.657c0-4.12-3.34-7.46-7.461-7.46c-4.119,0-7.46,3.34-7.46,7.46V152.5c0,4.112-3.347,7.46-7.461,7.46h-94c-4.119,0-7.46,3.339-7.46,7.459c0,4.123,3.341,7.462,7.46,7.462h94C172.576,174.881,182.616,164.841,182.616,152.5z"/></g></svg>',bookmark:'<svg viewBox="0 0 24 24"><path d="M17,3H7A2,2 0 0,0 5,5V21L12,18L19,21V5C19,3.89 18.1,3 17,3Z" /></svg>',download:'<svg viewBox="0 0 24 24"><path d="M2 12H4V17H20V12H22V17C22 18.11 21.11 19 20 19H4C2.9 19 2 18.11 2 17V12M12 15L17.55 9.54L16.13 8.13L13 11.25V2H11V11.25L7.88 8.13L6.46 9.55L12 15Z" /></svg>',dir_ltr:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M0 0h24v24H0V0z" fill="none"/><path d="M9 4v4c-1.1 0-2-.9-2-2s.9-2 2-2m8-2H9C6.79 2 5 3.79 5 6s1.79 4 4 4v5h2V4h2v11h2V4h2V2zm0 12v3H5v2h12v3l4-4-4-4z"/></svg>',dir_rtl:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M0 0h24v24H0V0z" fill="none"/><path d="M10 4v4c-1.1 0-2-.9-2-2s.9-2 2-2m8-2h-8C7.79 2 6 3.79 6 6s1.79 4 4 4v5h2V4h2v11h2V4h2V2zM8 14l-4 4 4 4v-3h12v-2H8v-3z"/></svg>',alert_outline:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M11,15H13V17H11V15M11,7H13V13H11V7M12,2C6.47,2 2,6.5 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20Z" /></svg>',more_text:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="10 10 180 180"><g><path d="M49.711,142.188h49.027c2.328,0.002,4.394,1.492,5.129,3.699l9.742,29.252c0.363,1.092,1.385,1.828,2.537,1.83l15.883,0.01c0.859,0,1.667-0.412,2.17-1.109s0.641-1.594,0.37-2.41l-16.625-50.045L86.503,28.953c-0.36-1.097-1.383-1.839-2.537-1.842H64.532c-1.153-0.001-2.178,0.736-2.542,1.831L13.847,173.457c-0.271,0.816-0.135,1.713,0.369,2.412c0.503,0.697,1.311,1.109,2.171,1.109h15.872c1.151,0,2.173-0.736,2.537-1.828l9.793-29.287C45.325,143.66,47.39,142.18,49.711,142.188L49.711,142.188z M53.493,119.098l15.607-46.9c0.744-2.196,2.806-3.674,5.125-3.674s4.381,1.478,5.125,3.674l15.607,46.904c0.537,1.621,0.263,3.402-0.736,4.789c-1.018,1.408-2.649,2.24-4.386,2.24H58.615c-1.736,0-3.368-0.832-4.386-2.24C53.23,122.504,52.956,120.721,53.493,119.098L53.493,119.098z M190.465,63.32c0-2.919-1.015-5.396-3.059-7.428c-2.029-2.031-4.496-3.047-7.383-3.047c-2.889,0-5.355,1.016-7.388,3.047c-2.029,2.032-3.056,4.498-3.056,7.386c0,2.889,1.026,5.354,3.056,7.385c2.032,2.032,4.499,3.059,7.388,3.059c2.887,0,5.354-1.026,7.383-3.059C189.45,68.633,190.465,66.178,190.465,63.32L190.465,63.32z M190.465,101.994c0-2.858-1.015-5.313-3.059-7.333c-2.029-2.042-4.496-3.047-7.383-3.047c-2.889,0-5.355,1.005-7.388,3.047c-2.029,2.021-3.056,4.486-3.056,7.376c0,2.887,1.026,5.352,3.056,7.395c2.032,2.021,4.499,3.047,7.388,3.047c2.887,0,5.354-1.025,7.383-3.047C189.45,107.389,190.465,104.914,190.465,101.994L190.465,101.994z M190.465,140.76c0-2.918-1.015-5.395-3.059-7.438c-2.029-2.041-4.496-3.047-7.383-3.047c-2.889,0-5.355,1.006-7.388,3.047c-2.029,2.043-3.056,4.52-3.056,7.438c0,2.922,1.026,5.398,3.056,7.439c2.032,2.021,4.499,3.047,7.388,3.047c2.887,0,5.354-1.025,7.383-3.047C189.45,146.158,190.465,143.682,190.465,140.76L190.465,140.76z"/></g></svg>',more_paragraph:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="10 10 180 180"><g><path d="M128.39,28.499H63.493c-25.558,0-46.354,20.796-46.354,46.354c0,25.559,20.796,46.353,46.354,46.353h9.271v55.625h18.542V47.04h9.271V176.83h18.543V47.04h9.271V28.499z M72.764,102.664h-9.271c-15.337,0-27.813-12.475-27.813-27.812c0-15.336,12.476-27.813,27.813-27.813h9.271V102.664z M190.465,63.32c0-2.919-1.015-5.396-3.059-7.428c-2.029-2.031-4.496-3.047-7.383-3.047c-2.889,0-5.355,1.016-7.388,3.047c-2.029,2.032-3.056,4.498-3.056,7.386c0,2.889,1.026,5.354,3.056,7.385c2.032,2.032,4.499,3.059,7.388,3.059c2.887,0,5.354-1.026,7.383-3.059C189.45,68.633,190.465,66.178,190.465,63.32L190.465,63.32z M190.465,101.994c0-2.858-1.015-5.313-3.059-7.333c-2.029-2.042-4.496-3.047-7.383-3.047c-2.889,0-5.355,1.005-7.388,3.047c-2.029,2.021-3.056,4.486-3.056,7.376c0,2.887,1.026,5.352,3.056,7.395c2.032,2.021,4.499,3.047,7.388,3.047c2.887,0,5.354-1.025,7.383-3.047C189.45,107.389,190.465,104.914,190.465,101.994L190.465,101.994z M190.465,140.76c0-2.918-1.015-5.395-3.059-7.438c-2.029-2.041-4.496-3.047-7.383-3.047c-2.889,0-5.355,1.006-7.388,3.047c-2.029,2.043-3.056,4.52-3.056,7.438c0,2.922,1.026,5.398,3.056,7.439c2.032,2.021,4.499,3.047,7.388,3.047c2.887,0,5.354-1.025,7.383-3.047C189.45,146.158,190.465,143.682,190.465,140.76L190.465,140.76z"/></g></svg>',more_plus:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="35 30 140 140"><g><path d="M137.215,102.045c0,3.498-2.835,6.332-6.333,6.332H24.549c-3.498,0-6.334-2.834-6.334-6.332l0,0c0-3.498,2.836-6.333,6.334-6.333h106.333C134.38,95.711,137.215,98.547,137.215,102.045L137.215,102.045z M77.715,161.545c-3.498,0-6.333-2.836-6.333-6.334V48.878c0-3.498,2.836-6.333,6.333-6.333l0,0c3.498,0,6.334,2.835,6.334,6.333v106.333C84.049,158.709,81.213,161.545,77.715,161.545L77.715,161.545z M190.465,63.32c0-2.919-1.015-5.396-3.059-7.428c-2.029-2.031-4.496-3.047-7.383-3.047c-2.889,0-5.355,1.016-7.388,3.047c-2.029,2.032-3.056,4.498-3.056,7.386c0,2.889,1.026,5.354,3.056,7.385c2.032,2.032,4.499,3.059,7.388,3.059c2.887,0,5.354-1.026,7.383-3.059C189.45,68.632,190.465,66.177,190.465,63.32L190.465,63.32z M190.465,101.993c0-2.858-1.015-5.313-3.059-7.333c-2.029-2.042-4.496-3.047-7.383-3.047c-2.889,0-5.355,1.005-7.388,3.047c-2.029,2.021-3.056,4.486-3.056,7.376c0,2.888,1.026,5.353,3.056,7.396c2.032,2.021,4.499,3.047,7.388,3.047c2.887,0,5.354-1.025,7.383-3.047C189.45,107.389,190.465,104.914,190.465,101.993L190.465,101.993z M190.465,140.76c0-2.918-1.015-5.395-3.059-7.438c-2.029-2.041-4.496-3.047-7.383-3.047c-2.889,0-5.355,1.006-7.388,3.047c-2.029,2.043-3.056,4.52-3.056,7.438c0,2.922,1.026,5.398,3.056,7.439c2.032,2.021,4.499,3.047,7.388,3.047c2.887,0,5.354-1.025,7.383-3.047C189.45,146.158,190.465,143.682,190.465,140.76L190.465,140.76z"/></g></svg>',more_horizontal:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.76 3.58"><g><path d="M4.64,10.73a1.84,1.84,0,0,1,.65-.65,1.76,1.76,0,0,1,1.79,0A1.79,1.79,0,0,1,8,11.63a1.84,1.84,0,0,1-.25.9,1.69,1.69,0,0,1-.65.65,1.8,1.8,0,0,1-2.69-1.55A2.08,2.08,0,0,1,4.64,10.73Zm6.09,0a1.84,1.84,0,0,1,.65-.65,1.78,1.78,0,0,1,2.67,1.55,1.73,1.73,0,0,1-.24.9,1.84,1.84,0,0,1-.65.65,1.76,1.76,0,0,1-1.79,0,1.79,1.79,0,0,1-.64-2.44Zm6.08,0a1.69,1.69,0,0,1,.65-.65,1.76,1.76,0,0,1,1.79,0,1.79,1.79,0,0,1,.9,1.54,1.73,1.73,0,0,1-.24.9,1.84,1.84,0,0,1-.65.65,1.8,1.8,0,0,1-2.69-1.55A2,2,0,0,1,16.81,10.73Z" transform="translate(-4.39 -9.84)"/></g></svg>',more_vertical:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 3.94 15.75"><g><path d="M12.28,7.69a1.92,1.92,0,0,1-1.39-.58,2,2,0,0,1-.58-1.39,1.92,1.92,0,0,1,.58-1.39,2,2,0,0,1,1.39-.58,1.92,1.92,0,0,1,1.39.58,2,2,0,0,1,.58,1.39,1.92,1.92,0,0,1-.58,1.39,2,2,0,0,1-1.39.58Zm0,2a1.92,1.92,0,0,1,1.39.58,2,2,0,0,1,.58,1.39A1.92,1.92,0,0,1,13.67,13a2,2,0,0,1-1.39.58A1.92,1.92,0,0,1,10.89,13a2,2,0,0,1-.58-1.39,2,2,0,0,1,2-2Zm0,5.9a1.92,1.92,0,0,1,1.39.58,2,2,0,0,1,.58,1.39,1.92,1.92,0,0,1-.58,1.39,2,2,0,0,1-1.39.58,1.92,1.92,0,0,1-1.39-.58,2,2,0,0,1-.58-1.39,1.92,1.92,0,0,1,.58-1.39,1.94,1.94,0,0,1,1.39-.58Z" transform="translate(-10.31 -3.75)"/></g></svg>',attachment:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 8.38 15.68"><g><path d="M15.23,6h1v9.78a3.88,3.88,0,0,1-1.31,2.45,4,4,0,0,1-6.57-2.45V7A3,3,0,0,1,9.2,4.89a3,3,0,0,1,5,2.09v8.31a1.92,1.92,0,0,1-.58,1.39,2,2,0,0,1-1.39.58,1.92,1.92,0,0,1-1.39-.58,2,2,0,0,1-.58-1.39V8h1v7.32a1,1,0,0,0,.29.69,1,1,0,0,0,.69.28A.9.9,0,0,0,13,16a1,1,0,0,0,.29-.69V7a1.92,1.92,0,0,0-.58-1.39A2,2,0,0,0,11.27,5a1.92,1.92,0,0,0-1.39.58A2,2,0,0,0,9.33,7v8.31a3,3,0,1,0,5.9,0V6Z" transform="translate(-8.08 -3.78)"/></g></svg>',map:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 11.7 15.62"><g><path d="M12.05,12.42a2.93,2.93,0,1,1,2.07-5A2.88,2.88,0,0,1,15,9.49a3,3,0,0,1-.86,2.07,2.89,2.89,0,0,1-2.07.86Zm0-5.36a2.43,2.43,0,0,0-1.72,4.16,2.48,2.48,0,0,0,1.72.72,2.44,2.44,0,0,0,0-4.88Zm0-3.3A5.84,5.84,0,0,1,17.9,9.62a9.94,9.94,0,0,1-1.73,5A33.59,33.59,0,0,1,12.84,19a1.52,1.52,0,0,1-.23.2,1,1,0,0,1-.55.2h0a1,1,0,0,1-.55-.2,1.52,1.52,0,0,1-.23-.2,33.59,33.59,0,0,1-3.33-4.32,9.93,9.93,0,0,1-1.72-5,5.84,5.84,0,0,1,5.85-5.86ZM12,18.34l.08.05.06-.06a35.58,35.58,0,0,0,3.06-3.93,9.35,9.35,0,0,0,1.74-4.77,4.88,4.88,0,0,0-4.88-4.88A4.79,4.79,0,0,0,8.6,6.17,4.84,4.84,0,0,0,7.17,9.62,9.29,9.29,0,0,0,8.91,14.4,36,36,0,0,0,12,18.34Z" transform="translate(-6.2 -3.76)"/></g></svg>',magic_stick:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15.73 15.75"><g><path d="M19.86,19.21a1,1,0,0,0,.28-.68,1,1,0,0,0-.28-.7L13,10.93a1,1,0,0,0-.7-.28,1,1,0,0,0-.68,1.65l6.9,6.9a1,1,0,0,0,.69.29.93.93,0,0,0,.69-.28ZM9.19,8.55a3,3,0,0,0,1.68,0,14.12,14.12,0,0,0,1.41-.32A11.26,11.26,0,0,0,10.8,7.06c-.56-.36-.86-.56-.91-.58S10,5.91,10,5.11s0-1.26-.15-1.37a4.35,4.35,0,0,0-1.19.71c-.53.4-.81.62-.87.68a9,9,0,0,0-2-.6,6.84,6.84,0,0,0-.76-.09s0,.27.08.77a8.6,8.6,0,0,0,.61,2q-.09.09-.69.87a3.59,3.59,0,0,0-.68,1.17c.12.17.57.23,1.36.15S7,9.26,7.15,9.23s.21.36.57.91a10.49,10.49,0,0,0,1.14,1.48c0-.1.14-.57.31-1.4a3,3,0,0,0,0-1.67Z" transform="translate(-4.41 -3.74)"/></g></svg>',empty_file:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12.78 15.75"><g><path d="M14.73,3.76,18.67,7.7v9.84a2,2,0,0,1-2,2H7.84a1.89,1.89,0,0,1-1.38-.58,2,2,0,0,1-.57-1.39V5.73a1.93,1.93,0,0,1,.57-1.38,2,2,0,0,1,1.38-.58h6.62l.26,0v0Zm2.95,4.92h-2a1.93,1.93,0,0,1-1.38-.57,2,2,0,0,1-.58-1.4V6.17c0-.36,0-.84,0-1.43H7.85a1,1,0,0,0-.7.29,1,1,0,0,0-.29.7V17.54a1,1,0,0,0,.29.69,1,1,0,0,0,.69.29h8.85a1,1,0,0,0,.71-.29.92.92,0,0,0,.28-.69Zm0-1L14.73,4.74v2A1,1,0,0,0,15,7.4a1,1,0,0,0,.69.29Z" transform="translate(-5.89 -3.76)"/></g></svg>'};var Ee={exports:{}};(function(n){(function(l,g){n.exports=l.document?g(l,!0):function(C){if(!C.document)throw new Error("SUNEDITOR_LANG a window with a document");return g(C)}})(typeof window<"u"?window:Se,function(l,g){const C={code:"en",toolbar:{default:"Default",save:"Save",font:"Font",formats:"Formats",fontSize:"Size",bold:"Bold",underline:"Underline",italic:"Italic",strike:"Strike",subscript:"Subscript",superscript:"Superscript",removeFormat:"Remove Format",fontColor:"Font Color",hiliteColor:"Highlight Color",indent:"Indent",outdent:"Outdent",align:"Align",alignLeft:"Align left",alignRight:"Align right",alignCenter:"Align center",alignJustify:"Align justify",list:"List",orderList:"Ordered list",unorderList:"Unordered list",horizontalRule:"Horizontal line",hr_solid:"Solid",hr_dotted:"Dotted",hr_dashed:"Dashed",table:"Table",link:"Link",math:"Math",image:"Image",video:"Video",audio:"Audio",fullScreen:"Full screen",showBlocks:"Show blocks",codeView:"Code view",undo:"Undo",redo:"Redo",preview:"Preview",print:"print",tag_p:"Paragraph",tag_div:"Normal (DIV)",tag_h:"Header",tag_blockquote:"Quote",tag_pre:"Code",template:"Template",lineHeight:"Line height",paragraphStyle:"Paragraph style",textStyle:"Text style",imageGallery:"Image gallery",dir_ltr:"Left to right",dir_rtl:"Right to left",mention:"Mention"},dialogBox:{linkBox:{title:"Insert Link",url:"URL to link",text:"Text to display",newWindowCheck:"Open in new window",downloadLinkCheck:"Download link",bookmark:"Bookmark"},mathBox:{title:"Math",inputLabel:"Mathematical Notation",fontSizeLabel:"Font Size",previewLabel:"Preview"},imageBox:{title:"Insert image",file:"Select from files",url:"Image URL",altText:"Alternative text"},videoBox:{title:"Insert Video",file:"Select from files",url:"Media embed URL, YouTube/Vimeo"},audioBox:{title:"Insert Audio",file:"Select from files",url:"Audio URL"},browser:{tags:"Tags",search:"Search"},caption:"Insert description",close:"Close",submitButton:"Submit",revertButton:"Revert",proportion:"Constrain proportions",basic:"Basic",left:"Left",right:"Right",center:"Center",width:"Width",height:"Height",size:"Size",ratio:"Ratio"},controller:{edit:"Edit",unlink:"Unlink",remove:"Remove",insertRowAbove:"Insert row above",insertRowBelow:"Insert row below",deleteRow:"Delete row",insertColumnBefore:"Insert column before",insertColumnAfter:"Insert column after",deleteColumn:"Delete column",fixedColumnWidth:"Fixed column width",resize100:"Resize 100%",resize75:"Resize 75%",resize50:"Resize 50%",resize25:"Resize 25%",autoSize:"Auto size",mirrorHorizontal:"Mirror, Horizontal",mirrorVertical:"Mirror, Vertical",rotateLeft:"Rotate left",rotateRight:"Rotate right",maxSize:"Max size",minSize:"Min size",tableHeader:"Table header",mergeCells:"Merge cells",splitCells:"Split Cells",HorizontalSplit:"Horizontal split",VerticalSplit:"Vertical split"},menu:{spaced:"Spaced",bordered:"Bordered",neon:"Neon",translucent:"Translucent",shadow:"Shadow",code:"Code"}};return typeof g>"u"&&(l.SUNEDITOR_LANG||Object.defineProperty(l,"SUNEDITOR_LANG",{enumerable:!0,writable:!1,configurable:!1,value:{}}),Object.defineProperty(l.SUNEDITOR_LANG,"en",{enumerable:!0,writable:!0,configurable:!0,value:C})),C})})(Ee);var xe=Ee.exports;const Ne=Le(xe),me={_d:null,_w:null,isIE:null,isIE_Edge:null,isOSX_IOS:null,isChromium:null,isMobile:null,isResizeObserverSupported:null,_propertiesInit:function(){this._d||(this._d=document,this._w=window,this.isIE=navigator.userAgent.indexOf("Trident")>-1,this.isIE_Edge=navigator.userAgent.indexOf("Trident")>-1||navigator.appVersion.indexOf("Edge")>-1,this.isOSX_IOS=/(Mac|iPhone|iPod|iPad)/.test(navigator.platform),this.isChromium=!!window.chrome,this.isResizeObserverSupported=typeof ResizeObserver=="function",this.isMobile=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)||"ontouchstart"in window||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0)},_allowedEmptyNodeList:".se-component, pre, blockquote, hr, li, table, img, iframe, video, audio, canvas",_HTMLConvertor:function(n){const l={"&":"&amp;"," ":"&nbsp;","'":"&apos;",'"':"&quot;","<":"&lt;",">":"&gt;"};return n.replace(/&|\u00A0|'|"|<|>/g,function(g){return typeof l[g]=="string"?l[g]:g})},zeroWidthSpace:String.fromCharCode(8203),zeroWidthRegExp:new RegExp(String.fromCharCode(8203),"g"),onlyZeroWidthRegExp:new RegExp("^"+String.fromCharCode(8203)+"+$"),fontValueMap:{"xx-small":1,"x-small":2,small:3,medium:4,large:5,"x-large":6,"xx-large":7},onlyZeroWidthSpace:function(n){return n==null?!1:(typeof n!="string"&&(n=n.textContent),n===""||this.onlyZeroWidthRegExp.test(n))},getXMLHttpRequest:function(){if(this._w.ActiveXObject)try{return new ActiveXObject("Msxml2.XMLHTTP")}catch{try{return new ActiveXObject("Microsoft.XMLHTTP")}catch{return null}}else return this._w.XMLHttpRequest?new XMLHttpRequest:null},getValues:function(n){return n?this._w.Object.keys(n).map(function(l){return n[l]}):[]},camelToKebabCase:function(n){return typeof n=="string"?n.replace(/[A-Z]/g,function(l){return"-"+l.toLowerCase()}):n.map(function(l){return me.camelToKebabCase(l)})},kebabToCamelCase:function(n){return typeof n=="string"?n.replace(/-[a-zA-Z]/g,function(l){return l.replace("-","").toUpperCase()}):n.map(function(l){return me.camelToKebabCase(l)})},createElement:function(n){return this._d.createElement(n)},createTextNode:function(n){return this._d.createTextNode(n||"")},HTMLEncoder:function(n){const l={"<":"$lt;",">":"$gt;"};return n.replace(/<|>/g,function(g){return typeof l[g]=="string"?l[g]:g})},HTMLDecoder:function(n){const l={"$lt;":"<","$gt;":">"};return n.replace(/\$lt;|\$gt;/g,function(g){return typeof l[g]=="string"?l[g]:g})},hasOwn:function(n,l){return this._hasOwn.call(n,l)},_hasOwn:Object.prototype.hasOwnProperty,getIncludePath:function(n,l){let g="";const C=[],o=l==="js"?"script":"link",w=l==="js"?"src":"href";let L="(?:";for(let R=0,a=n.length;R<a;R++)L+=n[R]+(R<a-1?"|":")");const E=new this._w.RegExp("(^|.*[\\/])"+L+"(\\.[^\\/]+)?."+l+"(?:\\?.*|;.*)?$","i"),t=new this._w.RegExp(".+\\."+l+"(?:\\?.*|;.*)?$","i");for(let R=this._d.getElementsByTagName(o),a=0;a<R.length;a++)t.test(R[a][w])&&C.push(R[a]);for(let R=0;R<C.length;R++){let a=C[R][w].match(E);if(a){g=a[0];break}}if(g===""&&(g=C.length>0?C[0][w]:""),g.indexOf(":/")===-1&&g.slice(0,2)!=="//"&&(g=g.indexOf("/")===0?location.href.match(/^.*?:\/\/[^\/]*/)[0]+g:location.href.match(/^[^\?]*\/(?:)/)[0]+g),!g)throw"[SUNEDITOR.util.getIncludePath.fail] The SUNEDITOR installation path could not be automatically detected. (name: +"+name+", extension: "+l+")";return g},getPageStyle:function(n){let l="";const g=(n||this._d).styleSheets;for(let C=0,o=g.length,w;C<o;C++){try{w=g[C].cssRules}catch{continue}if(w)for(let L=0,E=w.length;L<E;L++)l+=w[L].cssText}return l},getIframeDocument:function(n){let l=n.contentWindow||n.contentDocument;return l.document&&(l=l.document),l},getAttributesToString:function(n,l){if(!n.attributes)return"";const g=n.attributes;let C="";for(let o=0,w=g.length;o<w;o++)l&&l.indexOf(g[o].name)>-1||(C+=g[o].name+'="'+g[o].value+'" ');return C},getByteLength:function(n){if(!n||!n.toString)return 0;n=n.toString();const l=this._w.encodeURIComponent;let g,C;return this.isIE_Edge?(C=this._w.unescape(l(n)).length,g=0,l(n).match(/(%0A|%0D)/gi)!==null&&(g=l(n).match(/(%0A|%0D)/gi).length),C+g):(C=new this._w.TextEncoder("utf-8").encode(n).length,g=0,l(n).match(/(%0A|%0D)/gi)!==null&&(g=l(n).match(/(%0A|%0D)/gi).length),C+g)},isWysiwygDiv:function(n){return n&&n.nodeType===1&&(this.hasClass(n,"se-wrapper-wysiwyg")||/^BODY$/i.test(n.nodeName))},isNonEditable:function(n){return n&&n.nodeType===1&&n.getAttribute("contenteditable")==="false"},isTextStyleElement:function(n){return n&&n.nodeType!==3&&/^(strong|span|font|b|var|i|em|u|ins|s|strike|del|sub|sup|mark|a|label|code|summary)$/i.test(n.nodeName)},isInputElement:function(n){return n&&n.nodeType===1&&/^(INPUT|TEXTAREA)$/i.test(n.nodeName)},isFormatElement:function(n){return n&&n.nodeType===1&&(/^(P|DIV|H[1-6]|PRE|LI|TH|TD|DETAILS)$/i.test(n.nodeName)||this.hasClass(n,"(\\s|^)__se__format__replace_.+(\\s|$)|(\\s|^)__se__format__free_.+(\\s|$)"))&&!this.isComponent(n)&&!this.isWysiwygDiv(n)},isRangeFormatElement:function(n){return n&&n.nodeType===1&&(/^(BLOCKQUOTE|OL|UL|FIGCAPTION|TABLE|THEAD|TBODY|TR|TH|TD|DETAILS)$/i.test(n.nodeName)||this.hasClass(n,"(\\s|^)__se__format__range_.+(\\s|$)"))},isClosureRangeFormatElement:function(n){return n&&n.nodeType===1&&(/^(TH|TD)$/i.test(n.nodeName)||this.hasClass(n,"(\\s|^)__se__format__range__closure_.+(\\s|$)"))},isFreeFormatElement:function(n){return n&&n.nodeType===1&&(/^PRE$/i.test(n.nodeName)||this.hasClass(n,"(\\s|^)__se__format__free_.+(\\s|$)"))&&!this.isComponent(n)&&!this.isWysiwygDiv(n)},isClosureFreeFormatElement:function(n){return n&&n.nodeType===1&&this.hasClass(n,"(\\s|^)__se__format__free__closure_.+(\\s|$)")},isComponent:function(n){return n&&(/se-component/.test(n.className)||/^(TABLE|HR)$/.test(n.nodeName))},isUneditableComponent:function(n){return n&&this.hasClass(n,"__se__uneditable")},isMediaComponent:function(n){return n&&/se-component/.test(n.className)},isNotCheckingNode:function(n){return n&&/katex|__se__tag/.test(n.className)},getFormatElement:function(n,l){if(!n)return null;for(l||(l=function(){return!0});n;){if(this.isWysiwygDiv(n))return null;if(this.isRangeFormatElement(n)&&n.firstElementChild,this.isFormatElement(n)&&l(n))return n;n=n.parentNode}return null},getRangeFormatElement:function(n,l){if(!n)return null;for(l||(l=function(){return!0});n;){if(this.isWysiwygDiv(n))return null;if(this.isRangeFormatElement(n)&&!/^(THEAD|TBODY|TR)$/i.test(n.nodeName)&&l(n))return n;n=n.parentNode}return null},getFreeFormatElement:function(n,l){if(!n)return null;for(l||(l=function(){return!0});n;){if(this.isWysiwygDiv(n))return null;if(this.isFreeFormatElement(n)&&l(n))return n;n=n.parentNode}return null},getClosureFreeFormatElement:function(n,l){if(!n)return null;for(l||(l=function(){return!0});n;){if(this.isWysiwygDiv(n))return null;if(this.isClosureFreeFormatElement(n)&&l(n))return n;n=n.parentNode}return null},copyTagAttributes:function(n,l,g){if(l.style.cssText){const o=l.style;for(let w=0,L=o.length;w<L;w++)n.style[o[w]]=o[o[w]]}const C=l.attributes;for(let o=0,w=C.length,L;o<w;o++)L=C[o].name.toLowerCase(),g&&g.indexOf(L)>-1||!C[o].value?n.removeAttribute(L):L!=="style"&&n.setAttribute(C[o].name,C[o].value)},copyFormatAttributes:function(n,l){l=l.cloneNode(!1),l.className=l.className.replace(/(\s|^)__se__format__[^\s]+/g,""),this.copyTagAttributes(n,l)},getArrayItem:function(n,l,g){if(!n||n.length===0)return null;l=l||function(){return!0};const C=[];for(let o=0,w=n.length,L;o<w;o++)if(L=n[o],l(L))if(g)C.push(L);else return L;return g?C:null},arrayIncludes:function(n,l){for(let g=0;g<n.length;g++)if(n[g]===l)return!0;return!1},getArrayIndex:function(n,l){let g=-1;for(let C=0,o=n.length;C<o;C++)if(n[C]===l){g=C;break}return g},nextIdx:function(n,l){let g=this.getArrayIndex(n,l);return g===-1?-1:g+1},prevIdx:function(n,l){let g=this.getArrayIndex(n,l);return g===-1?-1:g-1},getPositionIndex:function(n){let l=0;for(;n=n.previousSibling;)l+=1;return l},getNodePath:function(n,l,g){const C=[];let o=!0;return this.getParentElement(n,(function(w){if(w===l&&(o=!1),o&&!this.isWysiwygDiv(w)){if(g&&w.nodeType===3){let L=null,E=null;g.s=g.e=0;let t=w.previousSibling;for(;t&&t.nodeType===3;)E=t.textContent.replace(this.zeroWidthRegExp,""),g.s+=E.length,w.textContent=E+w.textContent,L=t,t=t.previousSibling,this.removeItem(L);let R=w.nextSibling;for(;R&&R.nodeType===3;)E=R.textContent.replace(this.zeroWidthRegExp,""),g.e+=E.length,w.textContent+=E,L=R,R=R.nextSibling,this.removeItem(L)}C.push(w)}return!1}).bind(this)),C.map(this.getPositionIndex).reverse()},getNodeFromPath:function(n,l){let g=l,C;for(let o=0,w=n.length;o<w&&(C=g.childNodes,C.length!==0);o++)C.length<=n[o]?g=C[C.length-1]:g=C[n[o]];return g},isSameAttributes:function(n,l){if(n.nodeType===3&&l.nodeType===3)return!0;if(n.nodeType===3||l.nodeType===3)return!1;const g=n.style,C=l.style;let o=0;for(let R=0,a=g.length;R<a;R++)g[g[R]]===C[g[R]]&&o++;const w=n.classList,L=l.classList,E=this._w.RegExp;let t=0;for(let R=0,a=w.length;R<a;R++)E("(s|^)"+w[R]+"(s|$)").test(L.value)&&t++;return o===C.length&&o===g.length&&t===L.length&&t===w.length},isEmptyLine:function(n){return!n||!n.parentNode||!n.querySelector("IMG, IFRAME, AUDIO, VIDEO, CANVAS, TABLE")&&n.children.length===0&&this.onlyZeroWidthSpace(n.textContent)},isSpanWithoutAttr:function(n){return!!n&&n.nodeType===1&&/^SPAN$/i.test(n.nodeName)&&!n.className&&!n.style.cssText},isList:function(n){return n&&/^(OL|UL)$/i.test(typeof n=="string"?n:n.nodeName)},isListCell:function(n){return n&&/^LI$/i.test(typeof n=="string"?n:n.nodeName)},isTable:function(n){return n&&/^(TABLE|THEAD|TBODY|TR|TH|TD)$/i.test(typeof n=="string"?n:n.nodeName)},isCell:function(n){return n&&/^(TD|TH)$/i.test(typeof n=="string"?n:n.nodeName)},isBreak:function(n){return n&&/^BR$/i.test(typeof n=="string"?n:n.nodeName)},isAnchor:function(n){return n&&/^A$/i.test(typeof n=="string"?n:n.nodeName)},isMedia:function(n){return n&&/^(IMG|IFRAME|AUDIO|VIDEO|CANVAS)$/i.test(typeof n=="string"?n:n.nodeName)},isFigures:function(n){return n&&(this.isMedia(n)||/^(FIGURE)$/i.test(typeof n=="string"?n:n.nodeName))},isNumber:function(n){return!!n&&/^-?\d+(\.\d+)?$/.test(n+"")},getNumber:function(n,l){if(!n)return 0;let g=(n+"").match(/-?\d+(\.\d+)?/);return!g||!g[0]?0:(g=g[0],l<0?g*1:l===0?this._w.Math.round(g*1):(g*1).toFixed(l)*1)},getListChildren:function(n,l){const g=[];return!n||!n.children||n.children.length===0||(l=l||function(){return!0},function C(o){if(n!==o&&l(o)&&g.push(o),o.children)for(let w=0,L=o.children.length;w<L;w++)C(o.children[w])}(n)),g},getListChildNodes:function(n,l){const g=[];return!n||n.childNodes.length===0||(l=l||function(){return!0},function C(o){n!==o&&l(o)&&g.push(o);for(let w=0,L=o.childNodes.length;w<L;w++)C(o.childNodes[w])}(n)),g},getElementDepth:function(n){if(!n||this.isWysiwygDiv(n))return-1;let l=0;for(n=n.parentNode;n&&!this.isWysiwygDiv(n);)l+=1,n=n.parentNode;return l},compareElements:function(n,l){let g=n,C=l;for(;g&&C&&g.parentNode!==C.parentNode;)g=g.parentNode,C=C.parentNode;if(!g||!C)return{ancestor:null,a:n,b:l,result:0};const o=g.parentNode.childNodes,w=this.getArrayIndex(o,g),L=this.getArrayIndex(o,C);return{ancestor:g.parentNode,a:g,b:C,result:w>L?1:w<L?-1:0}},getParentElement:function(n,l){let g;if(typeof l=="function")g=l;else{let C;/^\./.test(l)?(C="className",l=l.split(".")[1]):/^#/.test(l)?(C="id",l="^"+l.split("#")[1]+"$"):/^:/.test(l)?(C="name",l="^"+l.split(":")[1]+"$"):(C="nodeName",l="^"+l+"$");const o=new this._w.RegExp(l,"i");g=function(w){return o.test(w[C])}}for(;n&&!g(n);){if(this.isWysiwygDiv(n))return null;n=n.parentNode}return n},getPreviousDeepestNode:function(n,l){let g=n.previousSibling;if(!g){for(let C=n.parentNode;C;C=C.parentNode){if(C===l)return null;if(C.previousSibling){g=C.previousSibling;break}}if(!g)return null}for(;g.lastChild;)g=g.lastChild;return g},getNextDeepestNode:function(n,l){let g=n.nextSibling;if(!g){for(let C=n.parentNode;C;C=C.parentNode){if(C===l)return null;if(C.nextSibling){g=C.nextSibling;break}}if(!g)return null}for(;g.firstChild;)g=g.firstChild;return g},getChildElement:function(n,l,g){let C;if(typeof l=="function")C=l;else{let w;/^\./.test(l)?(w="className",l=l.split(".")[1]):/^#/.test(l)?(w="id",l="^"+l.split("#")[1]+"$"):/^:/.test(l)?(w="name",l="^"+l.split(":")[1]+"$"):(w="nodeName",l="^"+(l==="text"?"#"+l:l)+"$");const L=new this._w.RegExp(l,"i");C=function(E){return L.test(E[w])}}const o=this.getListChildNodes(n,function(w){return C(w)});return o[g?o.length-1:0]},getEdgeChildNodes:function(n,l){if(n){for(l||(l=n);n&&n.nodeType===1&&n.childNodes.length>0&&!this.isBreak(n);)n=n.firstChild;for(;l&&l.nodeType===1&&l.childNodes.length>0&&!this.isBreak(l);)l=l.lastChild;return{sc:n,ec:l||n}}},getOffset:function(n,l){let g=0,C=0,o=n.nodeType===3?n.parentElement:n;const w=this.getParentElement(n,this.isWysiwygDiv.bind(this));for(;o&&!this.hasClass(o,"se-container")&&o!==w;)g+=o.offsetLeft,C+=o.offsetTop,o=o.offsetParent;const L=l&&/iframe/i.test(l.nodeName);return{left:g+(L?l.parentElement.offsetLeft:0),top:C-(w?w.scrollTop:0)+(L?l.parentElement.offsetTop:0)}},getOverlapRangeAtIndex:function(n,l,g,C){if(n<=C?l<g:l>g)return 0;const o=(n>g?n:g)-(l<C?l:C);return(o<0?o*-1:o)+1},changeTxt:function(n,l){!n||!l||(n.textContent=l)},changeElement:function(n,l){if(typeof l=="string")if(n.outerHTML)n.outerHTML=l;else{const g=this.createElement("DIV");g.innerHTML=l,l=g.firstChild,n.parentNode.replaceChild(l,n)}else l.nodeType===1&&n.parentNode.replaceChild(l,n)},setStyle:function(n,l,g){n.style[l]=g,!g&&!n.style.cssText&&n.removeAttribute("style")},hasClass:function(n,l){if(n)return new this._w.RegExp(l).test(n.className)},addClass:function(n,l){!n||new this._w.RegExp("(\\s|^)"+l+"(\\s|$)").test(n.className)||(n.className+=(n.className.length>0?" ":"")+l)},removeClass:function(n,l){if(!n)return;const g=new this._w.RegExp("(\\s|^)"+l+"(\\s|$)");n.className=n.className.replace(g," ").trim(),n.className.trim()||n.removeAttribute("class")},toggleClass:function(n,l){if(!n)return;let g=!1;const C=new this._w.RegExp("(\\s|^)"+l+"(\\s|$)");return C.test(n.className)?n.className=n.className.replace(C," ").trim():(n.className+=" "+l,g=!0),n.className.trim()||n.removeAttribute("class"),g},isImportantDisabled:function(n){return n.hasAttribute("data-important-disabled")},setDisabledButtons:function(n,l,g){for(let C=0,o=l.length;C<o;C++){let w=l[C];(g||!this.isImportantDisabled(w))&&(w.disabled=n),g&&(n?w.setAttribute("data-important-disabled",""):w.removeAttribute("data-important-disabled"))}},removeItem:function(n){n&&(typeof n.remove=="function"?n.remove():n.parentNode&&n.parentNode.removeChild(n))},removeItemAllParents:function(n,l,g){if(!n)return null;let C=null;return l||(l=(function(o){if(o===g||this.isComponent(o))return!1;const w=o.textContent.trim();return w.length===0||/^(\n|\u200B)+$/.test(w)}).bind(this)),function o(w){if(!me.isWysiwygDiv(w)){const L=w.parentNode;L&&l(w)&&(C={sc:w.previousElementSibling,ec:w.nextElementSibling},me.removeItem(w),o(L))}}(n),C},detachNestedList:function(n,l){const g=this._deleteNestedList(n);let C,o;if(g){C=g.cloneNode(!1),o=g.childNodes;const L=this.getPositionIndex(n);for(;o[L];)C.appendChild(o[L])}else C=n;let w;if(l)w=this.getListChildren(C,(function(L){return this.isListCell(L)&&!L.previousElementSibling}).bind(this));else{const L=this.getElementDepth(n)+2;w=this.getListChildren(n,(function(E){return this.isListCell(E)&&!E.previousElementSibling&&this.getElementDepth(E)===L}).bind(this))}for(let L=0,E=w.length;L<E;L++)this._deleteNestedList(w[L]);return g&&(g.parentNode.insertBefore(C,g.nextSibling),o&&o.length===0&&this.removeItem(g)),C===n?C.parentNode:C},_deleteNestedList:function(n){const l=n.parentNode;let g=l,C=g.parentNode,o,w,L,E,t;for(;this.isListCell(C);){for(E=this.getPositionIndex(n),o=C.nextElementSibling,w=C.parentNode,L=g;L;){if(g=g.nextSibling,this.isList(L)){for(t=L.childNodes;t[E];)w.insertBefore(t[E],o);t.length===0&&this.removeItem(L)}else w.appendChild(L);L=g}g=w,C=w.parentNode}return l.children.length===0&&this.removeItem(l),w},splitElement:function(n,l,g){if(this.isWysiwygDiv(n))return n;if(l&&!this.isNumber(l)){const F=n.childNodes;let I=this.getPositionIndex(l);const K=n.cloneNode(!1),G=n.cloneNode(!1);for(let e=0,i=F.length;e<i;e++){if(e<I)K.appendChild(F[e]);else if(e>I)G.appendChild(F[e]);else continue;e--,i--,I--}return K.childNodes.length>0&&n.parentNode.insertBefore(K,n),G.childNodes.length>0&&n.parentNode.insertBefore(G,n.nextElementSibling),n}const C=n.parentNode;let o=0,w=1,L=!0,E,t,R;if((!g||g<0)&&(g=0),n.nodeType===3){if(o=this.getPositionIndex(n),l>=0&&n.length!==l){n.splitText(l);const F=this.getNodeFromPath([o+1],C);this.onlyZeroWidthSpace(F)&&(F.data=this.zeroWidthSpace)}}else if(n.nodeType===1){if(l===0){for(;n.firstChild;)n=n.firstChild;if(n.nodeType===3){const F=this.createTextNode(this.zeroWidthSpace);n.parentNode.insertBefore(F,n),n=F}}n.previousSibling?n=n.previousSibling:this.getElementDepth(n)===g&&(L=!1)}n.nodeType===1&&(w=0);let a=n;for(;this.getElementDepth(a)>g;)for(o=this.getPositionIndex(a)+w,a=a.parentNode,R=E,E=a.cloneNode(!1),t=a.childNodes,R&&(this.isListCell(E)&&this.isList(R)&&R.firstElementChild?(E.innerHTML=R.firstElementChild.innerHTML,me.removeItem(R.firstElementChild),R.children.length>0&&E.appendChild(R)):E.appendChild(R));t[o];)E.appendChild(t[o]);a.childNodes.length<=1&&(!a.firstChild||a.firstChild.textContent.length===0)&&(a.innerHTML="<br>");const p=a.parentNode;return L&&(a=a.nextSibling),E?(this.mergeSameTags(E,null,!1),this.mergeNestedTags(E,(function(F){return this.isList(F)}).bind(this)),E.childNodes.length>0?p.insertBefore(E,a):E=a,this.isListCell(E)&&E.children&&this.isList(E.children[0])&&E.insertBefore(this.createElement("BR"),E.children[0]),C.childNodes.length===0&&this.removeItem(C),E):a},mergeSameTags:function(n,l,g){const C=this,o=l?l.length:0;let w=null;return o&&(w=this._w.Array.apply(null,new this._w.Array(o)).map(this._w.Number.prototype.valueOf,0)),function L(E,t,R){const a=E.childNodes;for(let p=0,F=a.length,I,K;p<F&&(I=a[p],K=a[p+1],!!I);p++){if(g&&C._isIgnoreNodeChange(I)||!g&&(C.isTable(I)||C.isListCell(I)||C.isFormatElement(I)&&!C.isFreeFormatElement(I))){(C.isTable(I)||C.isListCell(I))&&L(I,t+1,p);continue}if(F===1&&E.nodeName===I.nodeName&&E.parentNode){if(o){let G,e,i,s,r;for(let c=0;c<o;c++)if(G=l[c],G&&G[t]===p){for(e=I,i=E,s=t,r=!0;s>=0;){if(C.getArrayIndex(i.childNodes,e)!==G[s]){r=!1;break}e=I.parentNode,i=e.parentNode,s--}r&&(G.splice(t,1),G[t]=p)}}C.copyTagAttributes(I,E),E.parentNode.insertBefore(I,E),C.removeItem(E)}if(!K){I.nodeType===1&&L(I,t+1,p);break}if(I.nodeName===K.nodeName&&C.isSameAttributes(I,K)&&I.href===K.href){const G=I.childNodes;let e=0;for(let c=0,d=G.length;c<d;c++)G[c].textContent.length>0&&e++;const i=I.lastChild,s=K.firstChild;let r=0;if(i&&s){const c=i.nodeType===3&&s.nodeType===3;r=i.textContent.length;let d=i.previousSibling;for(;d&&d.nodeType===3;)r+=d.textContent.length,d=d.previousSibling;if(e>0&&i.nodeType===3&&s.nodeType===3&&(i.textContent.length>0||s.textContent.length>0)&&e--,o){let f=null;for(let u=0;u<o;u++)if(f=l[u],f&&f[t]>p){if(t>0&&f[t-1]!==R)continue;f[t]-=1,f[t+1]>=0&&f[t]===p&&(f[t+1]+=e,c&&i&&i.nodeType===3&&s&&s.nodeType===3&&(w[u]+=r))}}}if(I.nodeType===3){if(r=I.textContent.length,I.textContent+=K.textContent,o){let c=null;for(let d=0;d<o;d++)if(c=l[d],c&&c[t]>p){if(t>0&&c[t-1]!==R)continue;c[t]-=1,c[t+1]>=0&&c[t]===p&&(c[t+1]+=e,w[d]+=r)}}}else I.innerHTML+=K.innerHTML;C.removeItem(K),p--}else I.nodeType===1&&L(I,t+1,p)}}(n,0,0),w},mergeNestedTags:function(n,l){typeof l=="string"?l=(function(g){return this.test(g.tagName)}).bind(new this._w.RegExp("^("+(l||".+")+")$","i")):typeof l!="function"&&(l=function(){return!0}),function g(C){let o=C.children;if(o.length===1&&o[0].nodeName===C.nodeName&&l(C)){const w=o[0];for(o=w.children;o[0];)C.appendChild(o[0]);C.removeChild(w)}for(let w=0,L=C.children.length;w<L;w++)g(C.children[w])}(n)},removeEmptyNode:function(n,l,g){const C=this;l&&(l=C.getParentElement(l,function(o){return n===o.parentElement})),function o(w){if(C._notTextNode(w)||w===l||C.isNonEditable(w))return 0;if(w!==n&&C.onlyZeroWidthSpace(w.textContent)&&(!w.firstChild||!C.isBreak(w.firstChild))&&!w.querySelector(C._allowedEmptyNodeList)){if(w.parentNode)return w.parentNode.removeChild(w),-1}else{const L=w.children;for(let E=0,t=L.length,R=0;E<t;E++)!L[E+R]||C.isComponent(L[E+R])||(R+=o(L[E+R]))}return 0}(n),n.childNodes.length===0&&(g?this.removeItem(n):n.innerHTML="<br>")},htmlRemoveWhiteSpace:function(n){return n?n.trim().replace(/<\/?(?!strong|span|font|b|var|i|em|u|ins|s|strike|del|sub|sup|mark|a|label|code|summary)[^>^<]+>\s+(?=<)/ig,function(l){return l.replace(/\n/g,"").replace(/\s+/," ")}):""},htmlCompress:function(n){return n.replace(/\n/g,"").replace(/(>)(?:\s+)(<)/g,"$1$2")},sortByDepth:function(n,l){const g=l?1:-1,C=g*-1;n.sort((function(o,w){return!this.isListCell(o)||!this.isListCell(w)?0:(o=this.getElementDepth(o),w=this.getElementDepth(w),o>w?g:o<w?C:0)}).bind(this))},escapeStringRegexp:function(n){if(typeof n!="string")throw new TypeError("Expected a string");return n.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")},_isExcludeSelectionElement:function(n){return!/FIGCAPTION/i.test(n.nodeName)&&(this.isComponent(n)||/FIGURE/i.test(n.nodeName))},_isIgnoreNodeChange:function(n){return n&&n.nodeType!==3&&(this.isNonEditable(n)||!this.isTextStyleElement(n))},_isMaintainedNode:function(n){return n&&n.nodeType!==3&&/^(a|label|code|summary)$/i.test(typeof n=="string"?n:n.nodeName)},_isSizeNode:function(n){return n&&n.nodeType!==3&&this.isTextStyleElement(n)&&!!n.style.fontSize},_notTextNode:function(n){return n&&n.nodeType!==3&&(this.isComponent(n)||/^(br|input|select|canvas|img|iframe|audio|video)$/i.test(typeof n=="string"?n:n.nodeName))},_disallowedTags:function(n){return/^(meta|script|link|style|[a-z]+\:[a-z]+)$/i.test(n.nodeName)},createTagsWhitelist:function(n){return new RegExp("<\\/?\\b(?!\\b"+(n||"").replace(/\|/g,"\\b|\\b")+"\\b)[^>]*>","gi")},createTagsBlacklist:function(n){return new RegExp("<\\/?\\b(?:\\b"+(n||"^").replace(/\|/g,"\\b|\\b")+"\\b)[^>]*>","gi")},_consistencyCheckOfHTML:function(n,l,g,C){const o=[],w=[],L=[],E=[],t=this.getListChildNodes(n,(function(a){if(a.nodeType!==1)return this.isList(a.parentElement)&&o.push(a),!1;if(g.test(a.nodeName)||!l.test(a.nodeName)&&a.childNodes.length===0&&this.isNotCheckingNode(a))return o.push(a),!1;const p=!this.getParentElement(a,this.isNotCheckingNode);if(!this.isTable(a)&&!this.isListCell(a)&&!this.isAnchor(a)&&(this.isFormatElement(a)||this.isRangeFormatElement(a)||this.isTextStyleElement(a))&&a.childNodes.length===0&&p)return w.push(a),!1;if(this.isList(a.parentNode)&&!this.isList(a)&&!this.isListCell(a))return L.push(a),!1;if(this.isCell(a)){const I=a.firstElementChild;if(!this.isFormatElement(I)&&!this.isRangeFormatElement(I)&&!this.isComponent(I))return E.push(a),!1}if(p&&a.className){const I=new this._w.Array(a.classList).map(C).join(" ").trim();I?a.className=I:a.removeAttribute("class")}return a.parentNode!==n&&p&&(this.isListCell(a)&&!this.isList(a.parentNode)||(this.isFormatElement(a)||this.isComponent(a))&&!this.isRangeFormatElement(a.parentNode)&&!this.getParentElement(a,this.isComponent))}).bind(this));for(let a=0,p=o.length;a<p;a++)this.removeItem(o[a]);const R=[];for(let a=0,p=t.length,F,I;a<p;a++)if(F=t[a],I=F.parentNode,!(!I||!I.parentNode))if(this.getParentElement(F,this.isListCell)){const K=F.childNodes;for(let G=K.length-1;p>=0;G--)I.insertBefore(F,K[G]);R.push(F)}else I.parentNode.insertBefore(F,I),R.push(I);for(let a=0,p=R.length,F;a<p;a++)F=R[a],this.onlyZeroWidthSpace(F.textContent.trim())&&this.removeItem(F);for(let a=0,p=w.length;a<p;a++)this.removeItem(w[a]);for(let a=0,p=L.length,F,I,K,G;a<p;a++)if(F=L[a],G=F.parentNode,!!G)if(I=this.createElement("LI"),this.isFormatElement(F)){for(K=F.childNodes;K[0];)I.appendChild(K[0]);G.insertBefore(I,F),this.removeItem(F)}else F=F.nextSibling,I.appendChild(L[a]),G.insertBefore(I,F);for(let a=0,p=E.length,F,I;a<p;a++)F=E[a],I=this.createElement("DIV"),I.innerHTML=F.textContent.trim().length===0&&F.children.length===0?"<br>":F.innerHTML,F.innerHTML=I.outerHTML},_setDefaultOptionStyle:function(n,l){let g="";n.height&&(g+="height:"+n.height+";"),n.minHeight&&(g+="min-height:"+n.minHeight+";"),n.maxHeight&&(g+="max-height:"+n.maxHeight+";"),n.position&&(g+="position:"+n.position+";"),n.width&&(g+="width:"+n.width+";"),n.minWidth&&(g+="min-width:"+n.minWidth+";"),n.maxWidth&&(g+="max-width:"+n.maxWidth+";");let C="",o="",w="";l=g+l;const L=l.split(";");for(let E=0,t=L.length,R;E<t;E++)if(R=L[E].trim(),!!R){if(/^(min-|max-)?width\s*:/.test(R)||/^(z-index|position)\s*:/.test(R)){C+=R+";";continue}if(/^(min-|max-)?height\s*:/.test(R)){/^height/.test(R)&&R.split(":")[1].trim()==="auto"&&(n.height="auto"),o+=R+";";continue}w+=R+";"}return{top:C,frame:o,editor:w}},_setIframeDocument:function(n,l){n.setAttribute("scrolling","auto"),n.contentDocument.head.innerHTML='<meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">'+this._setIframeCssTags(l),n.contentDocument.body.className=l._editableClass,n.contentDocument.body.setAttribute("contenteditable",!0),n.contentDocument.body.setAttribute("autocorrect","off")},_setIframeCssTags:function(n){const l=n.iframeCSSFileName,g=this._w.RegExp;let C="";for(let o=0,w=l.length,L;o<w;o++){if(L=[],/(^https?:\/\/)|(^data:text\/css,)/.test(l[o]))L.push(l[o]);else{const E=new g("(^|.*[\\/])"+l[o]+"(\\..+)?\\.css(?:\\?.*|;.*)?$","i");for(let t=document.getElementsByTagName("link"),R=0,a=t.length,p;R<a;R++)p=t[R].href.match(E),p&&L.push(p[0])}if(!L||L.length===0)throw'[SUNEDITOR.constructor.iframe.fail] The suneditor CSS files installation path could not be automatically detected. Please set the option property "iframeCSSFileName" before creating editor instances.';for(let E=0,t=L.length;E<t;E++)C+='<link href="'+L[E]+'" rel="stylesheet">'}return C+(n.height==="auto"?`<style>
/** Iframe height auto */
body{height: min-content; overflow: hidden;}
</style>`:"")}},j=me,be={init:function(n,l){typeof l!="object"&&(l={});const g=document;this._initOptions(n,l);const C=g.createElement("DIV");C.className="sun-editor"+(l.rtl?" se-rtl":""),n.id&&(C.id="suneditor_"+n.id);const o=g.createElement("DIV");o.className="se-container";const w=this._createToolBar(g,l.buttonList,l.plugins,l),L=w.element.cloneNode(!1);L.className+=" se-toolbar-shadow",w.element.style.visibility="hidden",w.pluginCallButtons.math&&this._checkKatexMath(l.katex);const E=g.createElement("DIV");E.className="se-arrow";const t=g.createElement("DIV");t.className="se-toolbar-sticky-dummy";const R=g.createElement("DIV");R.className="se-wrapper";const a=this._initElements(l,C,w.element,E),p=a.bottomBar,F=a.wysiwygFrame,I=a.placeholder;let K=a.codeView;const G=p.resizingBar,e=p.navigation,i=p.charWrapper,s=p.charCounter,r=g.createElement("DIV");r.className="se-loading-box sun-editor-common",r.innerHTML='<div class="se-loading-effect"></div>';const c=g.createElement("DIV");c.className="se-line-breaker",c.innerHTML='<button class="se-btn">'+l.icons.line_break+"</button>";const d=g.createElement("DIV");d.className+="se-line-breaker-component";const f=d.cloneNode(!0);d.innerHTML=f.innerHTML=l.icons.line_break;const u=g.createElement("DIV");u.className="se-resizing-back";const m=g.createElement("INPUT");m.tabIndex=-1,m.style.cssText="position: fixed !important; top: -10000px !important; display: block !important; width: 0 !important; height: 0 !important; margin: 0 !important; padding: 0 !important;";const h=l.toolbarContainer;h&&(h.appendChild(w.element),h.appendChild(L));const y=l.resizingBarContainer;return G&&y&&y.appendChild(G),R.appendChild(K),I&&R.appendChild(I),h||(o.appendChild(w.element),o.appendChild(L)),o.appendChild(t),o.appendChild(R),o.appendChild(u),o.appendChild(r),o.appendChild(c),o.appendChild(d),o.appendChild(f),o.appendChild(m),G&&!y&&o.appendChild(G),C.appendChild(o),K=this._checkCodeMirror(l,K),{constructed:{_top:C,_relative:o,_toolBar:w.element,_toolbarShadow:L,_menuTray:w._menuTray,_editorArea:R,_wysiwygArea:F,_codeArea:K,_placeholder:I,_resizingBar:G,_navigation:e,_charWrapper:i,_charCounter:s,_loading:r,_lineBreaker:c,_lineBreaker_t:d,_lineBreaker_b:f,_resizeBack:u,_stickyDummy:t,_arrow:E,_focusTemp:m},options:l,plugins:w.plugins,pluginCallButtons:w.pluginCallButtons,_responsiveButtons:w.responsiveButtons}},_checkCodeMirror:function(n,l){if(n.codeMirror){const g=[{mode:"htmlmixed",htmlMode:!0,lineNumbers:!0,lineWrapping:!0},n.codeMirror.options||{}].reduce(function(o,w){for(let L in w)j.hasOwn(w,L)&&(o[L]=w[L]);return o},{});n.height==="auto"&&(g.viewportMargin=1/0,g.height="auto");const C=n.codeMirror.src.fromTextArea(l,g);C.display.wrapper.style.cssText=l.style.cssText,n.codeMirrorEditor=C,l=C.display.wrapper,l.className+=" se-wrapper-code-mirror"}return l},_checkKatexMath:function(n){if(!n)throw Error('[SUNEDITOR.create.fail] To use the math button you need to add a "katex" object to the options.');const l=[{throwOnError:!1},n.options||{}].reduce(function(g,C){for(let o in C)j.hasOwn(C,o)&&(g[o]=C[o]);return g},{});n.options=l},_setOptions:function(n,l,g){this._initOptions(l.element.originElement,n);const C=l.element,o=C.relative,w=C.editorArea,L=n.toolbarContainer&&n.toolbarContainer!==g.toolbarContainer,E=n.lang!==g.lang||n.buttonList!==g.buttonList||n.mode!==g.mode||L,t=this._createToolBar(document,E?n.buttonList:g.buttonList,n.plugins,n);t.pluginCallButtons.math&&this._checkKatexMath(n.katex);const R=document.createElement("DIV");R.className="se-arrow",E&&(t.element.style.visibility="hidden",L?(n.toolbarContainer.appendChild(t.element),C.toolbar.parentElement.removeChild(C.toolbar)):C.toolbar.parentElement.replaceChild(t.element,C.toolbar),C.toolbar=t.element,C._menuTray=t._menuTray,C._arrow=R);const a=this._initElements(n,C.topArea,E?t.element:C.toolbar,R),p=a.bottomBar,F=a.wysiwygFrame,I=a.placeholder;let K=a.codeView;return C.resizingBar&&j.removeItem(C.resizingBar),p.resizingBar&&(n.resizingBarContainer&&n.resizingBarContainer!==g.resizingBarContainer?n.resizingBarContainer.appendChild(p.resizingBar):o.appendChild(p.resizingBar)),w.innerHTML="",w.appendChild(K),I&&w.appendChild(I),K=this._checkCodeMirror(n,K),C.resizingBar=p.resizingBar,C.navigation=p.navigation,C.charWrapper=p.charWrapper,C.charCounter=p.charCounter,C.wysiwygFrame=F,C.code=K,C.placeholder=I,n.rtl?j.addClass(C.topArea,"se-rtl"):j.removeClass(C.topArea,"se-rtl"),{callButtons:t.pluginCallButtons,plugins:t.plugins,toolbar:t}},_initElements:function(n,l,g,C){l.style.cssText=n._editorStyles.top,/inline/i.test(n.mode)?(g.className+=" se-toolbar-inline",g.style.width=n.toolbarWidth):/balloon/i.test(n.mode)&&(g.className+=" se-toolbar-balloon",g.style.width=n.toolbarWidth,g.appendChild(C));const o=document.createElement(n.iframe?"IFRAME":"DIV");if(o.className="se-wrapper-inner se-wrapper-wysiwyg",n.iframe)o.allowFullscreen=!0,o.frameBorder=0,o.style.cssText=n._editorStyles.frame,o.className+=n.className;else{o.setAttribute("contenteditable",!0),o.setAttribute("autocorrect","off"),o.setAttribute("scrolling","auto");for(let p in n.iframeAttributes)o.setAttribute(p,n.iframeAttributes[p]);o.className+=" "+n._editableClass,o.style.cssText=n._editorStyles.frame+n._editorStyles.editor,o.className+=n.className}const w=document.createElement("TEXTAREA");w.className="se-wrapper-inner se-wrapper-code"+n.className,w.style.cssText=n._editorStyles.frame,w.style.display="none",n.height==="auto"&&(w.style.overflow="hidden");let L=null,E=null,t=null,R=null;if(n.resizingBar&&(L=document.createElement("DIV"),L.className="se-resizing-bar sun-editor-common",E=document.createElement("DIV"),E.className="se-navigation sun-editor-common",L.appendChild(E),n.charCounter)){if(t=document.createElement("DIV"),t.className="se-char-counter-wrapper",n.charCounterLabel){const p=document.createElement("SPAN");p.className="se-char-label",p.textContent=n.charCounterLabel,t.appendChild(p)}if(R=document.createElement("SPAN"),R.className="se-char-counter",R.textContent="0",t.appendChild(R),n.maxCharCount>0){const p=document.createElement("SPAN");p.textContent=" / "+n.maxCharCount,t.appendChild(p)}L.appendChild(t)}let a=null;return n.placeholder&&(a=document.createElement("SPAN"),a.className="se-placeholder",a.innerText=n.placeholder),{bottomBar:{resizingBar:L,navigation:E,charWrapper:t,charCounter:R},wysiwygFrame:o,codeView:w,placeholder:a}},_initOptions:function(n,l){const g={};if(l.plugins){const w=l.plugins,L=w.length?w:Object.keys(w).map(function(E){return w[E]});for(let E=0,t=L.length,R;E<t;E++)R=L[E].default||L[E],g[R.name]=R}l.plugins=g,l.strictMode=l.strictMode!==!1,l.lang=l.lang||Ne,l.value=typeof l.value=="string"?l.value:null,l.allowedClassNames=new j._w.RegExp((l.allowedClassNames&&typeof l.allowedClassNames=="string"?l.allowedClassNames+"|":"")+"^__se__|se-|katex"),l.historyStackDelayTime=typeof l.historyStackDelayTime=="number"?l.historyStackDelayTime:400,l.frameAttrbutes=l.frameAttrbutes||{},l.defaultTag=typeof l.defaultTag=="string"&&l.defaultTag.length>0?l.defaultTag:"p";const C=l.textTags=[{bold:"STRONG",underline:"U",italic:"EM",strike:"DEL",sub:"SUB",sup:"SUP"},l.textTags||{}].reduce(function(w,L){for(let E in L)w[E]=L[E];return w},{});l._textTagsMap={strong:C.bold.toLowerCase(),b:C.bold.toLowerCase(),u:C.underline.toLowerCase(),ins:C.underline.toLowerCase(),em:C.italic.toLowerCase(),i:C.italic.toLowerCase(),del:C.strike.toLowerCase(),strike:C.strike.toLowerCase(),s:C.strike.toLowerCase(),sub:C.sub.toLowerCase(),sup:C.sup.toLowerCase()},l._defaultCommand={bold:l.textTags.bold,underline:l.textTags.underline,italic:l.textTags.italic,strike:l.textTags.strike,subscript:l.textTags.sub,superscript:l.textTags.sup},l.__allowedScriptTag=l.__allowedScriptTag===!0;const o="br|p|div|pre|blockquote|h1|h2|h3|h4|h5|h6|ol|ul|li|hr|figure|figcaption|img|iframe|audio|video|source|table|thead|tbody|tr|th|td|a|b|strong|var|i|em|u|ins|s|span|strike|del|sub|sup|code|svg|path|details|summary";l.tagsBlacklist=l.tagsBlacklist||"",l._defaultTagsWhitelist=(typeof l._defaultTagsWhitelist=="string"?l._defaultTagsWhitelist:o)+(l.__allowedScriptTag?"|script":""),l._editorTagsWhitelist=l.addTagsWhitelist==="*"?"*":this._setWhitelist(l._defaultTagsWhitelist+(typeof l.addTagsWhitelist=="string"&&l.addTagsWhitelist.length>0?"|"+l.addTagsWhitelist:""),l.tagsBlacklist),l.pasteTagsBlacklist=l.tagsBlacklist+(l.tagsBlacklist&&l.pasteTagsBlacklist?"|"+l.pasteTagsBlacklist:l.pasteTagsBlacklist||""),l.pasteTagsWhitelist=l.pasteTagsWhitelist==="*"?"*":this._setWhitelist(typeof l.pasteTagsWhitelist=="string"?l.pasteTagsWhitelist:l._editorTagsWhitelist,l.pasteTagsBlacklist),l.attributesWhitelist=!l.attributesWhitelist||typeof l.attributesWhitelist!="object"?null:l.attributesWhitelist,l.attributesBlacklist=!l.attributesBlacklist||typeof l.attributesBlacklist!="object"?null:l.attributesBlacklist,l.mode=l.mode||"classic",l.rtl=!!l.rtl,l.lineAttrReset=["id"].concat(l.lineAttrReset&&typeof l.lineAttrReset=="string"?l.lineAttrReset.toLowerCase().split("|"):[]),l._editableClass="sun-editor-editable"+(l.rtl?" se-rtl":""),l._printClass=typeof l._printClass=="string"?l._printClass:null,l.toolbarWidth=l.toolbarWidth?j.isNumber(l.toolbarWidth)?l.toolbarWidth+"px":l.toolbarWidth:"auto",l.toolbarContainer=typeof l.toolbarContainer=="string"?document.querySelector(l.toolbarContainer):l.toolbarContainer,l.stickyToolbar=/balloon/i.test(l.mode)||l.toolbarContainer?-1:l.stickyToolbar===void 0?0:/^\d+/.test(l.stickyToolbar)?j.getNumber(l.stickyToolbar,0):-1,l.hideToolbar=!!l.hideToolbar,l.fullScreenOffset=l.fullScreenOffset===void 0?0:/^\d+/.test(l.fullScreenOffset)?j.getNumber(l.fullScreenOffset,0):0,l.fullPage=!!l.fullPage,l.iframe=l.fullPage||!!l.iframe,l.iframeAttributes=l.iframeAttributes||{},l.iframeCSSFileName=l.iframe?typeof l.iframeCSSFileName=="string"?[l.iframeCSSFileName]:l.iframeCSSFileName||["suneditor"]:null,l.previewTemplate=typeof l.previewTemplate=="string"?l.previewTemplate:null,l.printTemplate=typeof l.printTemplate=="string"?l.printTemplate:null,l.codeMirror=l.codeMirror?l.codeMirror.src?l.codeMirror:{src:l.codeMirror}:null,l.katex=l.katex?l.katex.src?l.katex:{src:l.katex}:null,l.mathFontSize=l.mathFontSize?l.mathFontSize:[{text:"1",value:"1em"},{text:"1.5",value:"1.5em"},{text:"2",value:"2em"},{text:"2.5",value:"2.5em"}],l.position=typeof l.position=="string"?l.position:null,l.display=l.display||(n.style.display==="none"||!n.style.display?"block":n.style.display),l.popupDisplay=l.popupDisplay||"full",l.resizingBar=l.resizingBar===void 0?!/inline|balloon/i.test(l.mode):l.resizingBar,l.showPathLabel=l.resizingBar?typeof l.showPathLabel=="boolean"?l.showPathLabel:!0:!1,l.resizeEnable=l.resizeEnable===void 0?!0:!!l.resizeEnable,l.resizingBarContainer=typeof l.resizingBarContainer=="string"?document.querySelector(l.resizingBarContainer):l.resizingBarContainer,l.charCounter=l.maxCharCount>0?!0:typeof l.charCounter=="boolean"?l.charCounter:!1,l.charCounterType=typeof l.charCounterType=="string"?l.charCounterType:"char",l.charCounterLabel=typeof l.charCounterLabel=="string"?l.charCounterLabel.trim():null,l.maxCharCount=j.isNumber(l.maxCharCount)&&l.maxCharCount>-1?l.maxCharCount*1:null,l.width=l.width?j.isNumber(l.width)?l.width+"px":l.width:n.clientWidth?n.clientWidth+"px":"100%",l.minWidth=(j.isNumber(l.minWidth)?l.minWidth+"px":l.minWidth)||"",l.maxWidth=(j.isNumber(l.maxWidth)?l.maxWidth+"px":l.maxWidth)||"",l.height=l.height?j.isNumber(l.height)?l.height+"px":l.height:n.clientHeight?n.clientHeight+"px":"auto",l.minHeight=(j.isNumber(l.minHeight)?l.minHeight+"px":l.minHeight)||"",l.maxHeight=(j.isNumber(l.maxHeight)?l.maxHeight+"px":l.maxHeight)||"",l.className=typeof l.className=="string"&&l.className.length>0?" "+l.className:"",l.defaultStyle=typeof l.defaultStyle=="string"?l.defaultStyle:"",l.font=l.font?l.font:["Arial","Comic Sans MS","Courier New","Impact","Georgia","tahoma","Trebuchet MS","Verdana"],l.fontSize=l.fontSize?l.fontSize:null,l.formats=l.formats?l.formats:null,l.colorList=l.colorList?l.colorList:null,l.lineHeights=l.lineHeights?l.lineHeights:null,l.paragraphStyles=l.paragraphStyles?l.paragraphStyles:null,l.textStyles=l.textStyles?l.textStyles:null,l.fontSizeUnit=typeof l.fontSizeUnit=="string"&&l.fontSizeUnit.trim().toLowerCase()||"px",l.alignItems=typeof l.alignItems=="object"?l.alignItems:l.rtl?["right","center","left","justify"]:["left","center","right","justify"],l.imageResizing=l.imageResizing===void 0?!0:l.imageResizing,l.imageHeightShow=l.imageHeightShow===void 0?!0:!!l.imageHeightShow,l.imageAlignShow=l.imageAlignShow===void 0?!0:!!l.imageAlignShow,l.imageWidth=l.imageWidth?j.isNumber(l.imageWidth)?l.imageWidth+"px":l.imageWidth:"auto",l.imageHeight=l.imageHeight?j.isNumber(l.imageHeight)?l.imageHeight+"px":l.imageHeight:"auto",l.imageSizeOnlyPercentage=!!l.imageSizeOnlyPercentage,l._imageSizeUnit=l.imageSizeOnlyPercentage?"%":"px",l.imageRotation=l.imageRotation!==void 0?l.imageRotation:!(l.imageSizeOnlyPercentage||!l.imageHeightShow),l.imageFileInput=l.imageFileInput===void 0?!0:l.imageFileInput,l.imageUrlInput=l.imageUrlInput===void 0||!l.imageFileInput?!0:l.imageUrlInput,l.imageUploadHeader=l.imageUploadHeader||null,l.imageUploadUrl=typeof l.imageUploadUrl=="string"?l.imageUploadUrl:null,l.imageUploadSizeLimit=/\d+/.test(l.imageUploadSizeLimit)?j.getNumber(l.imageUploadSizeLimit,0):null,l.imageMultipleFile=!!l.imageMultipleFile,l.imageAccept=typeof l.imageAccept!="string"||l.imageAccept.trim()==="*"?"image/*":l.imageAccept.trim()||"image/*",l.imageGalleryUrl=typeof l.imageGalleryUrl=="string"?l.imageGalleryUrl:null,l.imageGalleryHeader=l.imageGalleryHeader||null,l.videoResizing=l.videoResizing===void 0?!0:l.videoResizing,l.videoHeightShow=l.videoHeightShow===void 0?!0:!!l.videoHeightShow,l.videoAlignShow=l.videoAlignShow===void 0?!0:!!l.videoAlignShow,l.videoRatioShow=l.videoRatioShow===void 0?!0:!!l.videoRatioShow,l.videoWidth=!l.videoWidth||!j.getNumber(l.videoWidth,0)?"":j.isNumber(l.videoWidth)?l.videoWidth+"px":l.videoWidth,l.videoHeight=!l.videoHeight||!j.getNumber(l.videoHeight,0)?"":j.isNumber(l.videoHeight)?l.videoHeight+"px":l.videoHeight,l.videoSizeOnlyPercentage=!!l.videoSizeOnlyPercentage,l._videoSizeUnit=l.videoSizeOnlyPercentage?"%":"px",l.videoRotation=l.videoRotation!==void 0?l.videoRotation:!(l.videoSizeOnlyPercentage||!l.videoHeightShow),l.videoRatio=j.getNumber(l.videoRatio,4)||.5625,l.videoRatioList=l.videoRatioList?l.videoRatioList:null,l.youtubeQuery=(l.youtubeQuery||"").replace("?",""),l.vimeoQuery=(l.vimeoQuery||"").replace("?",""),l.videoFileInput=!!l.videoFileInput,l.videoUrlInput=l.videoUrlInput===void 0||!l.videoFileInput?!0:l.videoUrlInput,l.videoUploadHeader=l.videoUploadHeader||null,l.videoUploadUrl=typeof l.videoUploadUrl=="string"?l.videoUploadUrl:null,l.videoUploadSizeLimit=/\d+/.test(l.videoUploadSizeLimit)?j.getNumber(l.videoUploadSizeLimit,0):null,l.videoMultipleFile=!!l.videoMultipleFile,l.videoTagAttrs=l.videoTagAttrs||null,l.videoIframeAttrs=l.videoIframeAttrs||null,l.videoAccept=typeof l.videoAccept!="string"||l.videoAccept.trim()==="*"?"video/*":l.videoAccept.trim()||"video/*",l.audioWidth=l.audioWidth?j.isNumber(l.audioWidth)?l.audioWidth+"px":l.audioWidth:"",l.audioHeight=l.audioHeight?j.isNumber(l.audioHeight)?l.audioHeight+"px":l.audioHeight:"",l.audioFileInput=!!l.audioFileInput,l.audioUrlInput=l.audioUrlInput===void 0||!l.audioFileInput?!0:l.audioUrlInput,l.audioUploadHeader=l.audioUploadHeader||null,l.audioUploadUrl=typeof l.audioUploadUrl=="string"?l.audioUploadUrl:null,l.audioUploadSizeLimit=/\d+/.test(l.audioUploadSizeLimit)?j.getNumber(l.audioUploadSizeLimit,0):null,l.audioMultipleFile=!!l.audioMultipleFile,l.audioTagAttrs=l.audioTagAttrs||null,l.audioAccept=typeof l.audioAccept!="string"||l.audioAccept.trim()==="*"?"audio/*":l.audioAccept.trim()||"audio/*",l.tableCellControllerPosition=typeof l.tableCellControllerPosition=="string"?l.tableCellControllerPosition.toLowerCase():"cell",l.linkTargetNewWindow=!!l.linkTargetNewWindow,l.linkProtocol=typeof l.linkProtocol=="string"?l.linkProtocol:null,l.linkRel=Array.isArray(l.linkRel)?l.linkRel:[],l.linkRelDefault=l.linkRelDefault||{},l.tabDisable=!!l.tabDisable,l.shortcutsDisable=Array.isArray(l.shortcutsDisable)?l.shortcutsDisable:[],l.shortcutsHint=l.shortcutsHint===void 0?!0:!!l.shortcutsHint,l.callBackSave=l.callBackSave?l.callBackSave:null,l.templates=l.templates?l.templates:null,l.placeholder=typeof l.placeholder=="string"?l.placeholder:null,l.mediaAutoSelect=l.mediaAutoSelect===void 0?!0:!!l.mediaAutoSelect,l.buttonList=l.buttonList?l.buttonList:[["undo","redo"],["bold","underline","italic","strike","subscript","superscript"],["removeFormat"],["outdent","indent"],["fullScreen","showBlocks","codeView"],["preview","print"]],l.rtl&&(l.buttonList=l.buttonList.reverse()),l.icons=!l.icons||typeof l.icons!="object"?we:[we,l.icons].reduce(function(w,L){for(let E in L)j.hasOwn(L,E)&&(w[E]=L[E]);return w},{}),l.icons=l.rtl?[l.icons,l.icons.rtl].reduce(function(w,L){for(let E in L)j.hasOwn(L,E)&&(w[E]=L[E]);return w},{}):l.icons,l.__listCommonStyle=l.__listCommonStyle||["fontSize","color","fontFamily","fontWeight","fontStyle"],l._editorStyles=j._setDefaultOptionStyle(l,l.defaultStyle)},_setWhitelist:function(n,l){if(typeof l!="string")return n;l=l.split("|"),n=n.split("|");for(let g=0,C=l.length,o;g<C;g++)o=n.indexOf(l[g]),o>-1&&n.splice(o,1);return n.join("|")},_defaultButtons:function(n){const l=n.icons,g=n.lang,C=j.isOSX_IOS?"⌘":"CTRL",o=j.isOSX_IOS?"⇧":"+SHIFT",w=n.shortcutsHint?n.shortcutsDisable:["bold","strike","underline","italic","undo","indent","save"],L=n.rtl?["[","]"]:["]","["],E=n.rtl?[l.outdent,l.indent]:[l.indent,l.outdent];return{bold:["",g.toolbar.bold+'<span class="se-shortcut">'+(w.indexOf("bold")>-1?"":C+'+<span class="se-shortcut-key">B</span>')+"</span>","bold","",l.bold],underline:["",g.toolbar.underline+'<span class="se-shortcut">'+(w.indexOf("underline")>-1?"":C+'+<span class="se-shortcut-key">U</span>')+"</span>","underline","",l.underline],italic:["",g.toolbar.italic+'<span class="se-shortcut">'+(w.indexOf("italic")>-1?"":C+'+<span class="se-shortcut-key">I</span>')+"</span>","italic","",l.italic],strike:["",g.toolbar.strike+'<span class="se-shortcut">'+(w.indexOf("strike")>-1?"":C+o+'+<span class="se-shortcut-key">S</span>')+"</span>","strike","",l.strike],subscript:["",g.toolbar.subscript,"SUB","",l.subscript],superscript:["",g.toolbar.superscript,"SUP","",l.superscript],removeFormat:["",g.toolbar.removeFormat,"removeFormat","",l.erase],indent:["",g.toolbar.indent+'<span class="se-shortcut">'+(w.indexOf("indent")>-1?"":C+'+<span class="se-shortcut-key">'+L[0]+"</span>")+"</span>","indent","",E[0]],outdent:["",g.toolbar.outdent+'<span class="se-shortcut">'+(w.indexOf("indent")>-1?"":C+'+<span class="se-shortcut-key">'+L[1]+"</span>")+"</span>","outdent","",E[1]],fullScreen:["se-code-view-enabled se-resizing-enabled",g.toolbar.fullScreen,"fullScreen","",l.expansion],showBlocks:["",g.toolbar.showBlocks,"showBlocks","",l.show_blocks],codeView:["se-code-view-enabled se-resizing-enabled",g.toolbar.codeView,"codeView","",l.code_view],undo:["",g.toolbar.undo+'<span class="se-shortcut">'+(w.indexOf("undo")>-1?"":C+'+<span class="se-shortcut-key">Z</span>')+"</span>","undo","",l.undo],redo:["",g.toolbar.redo+'<span class="se-shortcut">'+(w.indexOf("undo")>-1?"":C+'+<span class="se-shortcut-key">Y</span> / '+C+o+'+<span class="se-shortcut-key">Z</span>')+"</span>","redo","",l.redo],preview:["se-resizing-enabled",g.toolbar.preview,"preview","",l.preview],print:["se-resizing-enabled",g.toolbar.print,"print","",l.print],dir:["",g.toolbar[n.rtl?"dir_ltr":"dir_rtl"],"dir","",l[n.rtl?"dir_ltr":"dir_rtl"]],dir_ltr:["",g.toolbar.dir_ltr,"dir_ltr","",l.dir_ltr],dir_rtl:["",g.toolbar.dir_rtl,"dir_rtl","",l.dir_rtl],save:["se-resizing-enabled",g.toolbar.save+'<span class="se-shortcut">'+(w.indexOf("save")>-1?"":C+'+<span class="se-shortcut-key">S</span>')+"</span>","save","",l.save],blockquote:["",g.toolbar.tag_blockquote,"blockquote","command",l.blockquote],font:["se-btn-select se-btn-tool-font",g.toolbar.font,"font","submenu",'<span class="txt">'+g.toolbar.font+"</span>"+l.arrow_down],formatBlock:["se-btn-select se-btn-tool-format",g.toolbar.formats,"formatBlock","submenu",'<span class="txt">'+g.toolbar.formats+"</span>"+l.arrow_down],fontSize:["se-btn-select se-btn-tool-size",g.toolbar.fontSize,"fontSize","submenu",'<span class="txt">'+g.toolbar.fontSize+"</span>"+l.arrow_down],fontColor:["",g.toolbar.fontColor,"fontColor","submenu",l.font_color],hiliteColor:["",g.toolbar.hiliteColor,"hiliteColor","submenu",l.highlight_color],align:["se-btn-align",g.toolbar.align,"align","submenu",n.rtl?l.align_right:l.align_left],list:["",g.toolbar.list,"list","submenu",l.list_number],horizontalRule:["btn_line",g.toolbar.horizontalRule,"horizontalRule","submenu",l.horizontal_rule],table:["",g.toolbar.table,"table","submenu",l.table],lineHeight:["",g.toolbar.lineHeight,"lineHeight","submenu",l.line_height],template:["",g.toolbar.template,"template","submenu",l.template],paragraphStyle:["",g.toolbar.paragraphStyle,"paragraphStyle","submenu",l.paragraph_style],textStyle:["",g.toolbar.textStyle,"textStyle","submenu",l.text_style],link:["",g.toolbar.link,"link","dialog",l.link],image:["",g.toolbar.image,"image","dialog",l.image],video:["",g.toolbar.video,"video","dialog",l.video],audio:["",g.toolbar.audio,"audio","dialog",l.audio],math:["",g.toolbar.math,"math","dialog",l.math],imageGallery:["",g.toolbar.imageGallery,"imageGallery","fileBrowser",l.image_gallery]}},_createModuleGroup:function(){const n=j.createElement("DIV");n.className="se-btn-module se-btn-module-border";const l=j.createElement("UL");return l.className="se-menu-list",n.appendChild(l),{div:n,ul:l}},_createButton:function(n,l,g,C,o,w,L){const E=j.createElement("LI"),t=j.createElement("BUTTON"),R=l||g;return t.setAttribute("type","button"),t.setAttribute("class","se-btn"+(n?" "+n:"")+" se-tooltip"),t.setAttribute("data-command",g),t.setAttribute("data-display",C),t.setAttribute("aria-label",R.replace(/<span .+<\/span>/,"")),t.setAttribute("tabindex","-1"),o||(o='<span class="se-icon-text">!</span>'),/^default\./i.test(o)&&(o=L[o.replace(/^default\./i,"")]),/^text\./i.test(o)&&(o=o.replace(/^text\./i,""),t.className+=" se-btn-more-text"),o+='<span class="se-tooltip-inner"><span class="se-tooltip-text">'+R+"</span></span>",w&&t.setAttribute("disabled",!0),t.innerHTML=o,E.appendChild(t),{li:E,button:t}},_createToolBar:function(n,l,g,C){const o=n.createElement("DIV");o.className="se-toolbar-separator-vertical";const w=n.createElement("DIV");w.className="se-toolbar sun-editor-common";const L=n.createElement("DIV");L.className="se-btn-tray",w.appendChild(L),l=JSON.parse(JSON.stringify(l));const E=C.icons,t=this._defaultButtons(C),R={},a=[];let p=null,F=null,I=null,K=null,G="",e=!1;const i=j.createElement("DIV");i.className="se-toolbar-more-layer";e:for(let c=0,d,f,u,m,h;c<l.length;c++)if(d=!1,h="",m=l[c],I=this._createModuleGroup(),typeof m=="object"){for(let y=0,B;y<m.length;y++){if(F=m[y],B=!1,/^\%\d+/.test(F)&&y===0){m[0]=F.replace(/[^\d]/g,""),a.push(m),l.splice(c--,1);continue e}if(typeof F=="object")typeof F.add=="function"?(G=F.name,p=t[G],g[G]=F):(G=F.name,p=[F.buttonClass,F.title,F.name,F.dataDisplay,F.innerHTML,F._disabled]);else{if(/^\-/.test(F)){h=F.substr(1),I.div.className+=" module-float-"+h;continue}if(/^\#/.test(F)){F.substr(1)==="fix"&&(I.ul.className+=" se-menu-dir-fix");continue}if(/^\:/.test(F)){B=!0;const b=F.match(/^\:([^\-]+)\-([^\-]+)\-([^\-]+)/);u="__se__"+b[1].trim();const S=b[2].trim(),x=b[3].trim();p=["se-btn-more",S,u,"MORE",x]}else p=t[F];if(G=F,!p){const b=g[G];if(!b)throw Error("[SUNEDITOR.create.toolbar.fail] The button name of a plugin that does not exist. ["+G+"]");p=[b.buttonClass,b.title,b.name,b.display,b.innerHTML,b._disabled]}}K=this._createButton(p[0],p[1],p[2],p[3],p[4],p[5],E),(d?f:I.ul).appendChild(K.li),g[G]&&(R[G]=K.button),B&&(d=!0,f=j.createElement("DIV"),f.className="se-more-layer "+u,f.innerHTML='<div class="se-more-form"><ul class="se-menu-list"'+(h?' style="float: '+h+';"':"")+"></ul></div>",i.appendChild(f),f=f.firstElementChild.firstElementChild)}if(e){const y=o.cloneNode(!1);L.appendChild(y)}L.appendChild(I.div),e=!0}else if(/^\/$/.test(m)){const y=n.createElement("DIV");y.className="se-btn-module-enter",L.appendChild(y),e=!1}switch(L.children.length){case 0:L.style.display="none";break;case 1:j.removeClass(L.firstElementChild,"se-btn-module-border");break;default:if(C.rtl){const c=o.cloneNode(!1);c.style.float=L.lastElementChild.style.float,L.appendChild(c)}}a.length>0&&a.unshift(l),i.children.length>0&&L.appendChild(i);const s=n.createElement("DIV");s.className="se-menu-tray",w.appendChild(s);const r=n.createElement("DIV");return r.className="se-toolbar-cover",w.appendChild(r),C.hideToolbar&&(w.style.display="none"),{element:w,plugins:g,pluginCallButtons:R,responsiveButtons:a,_menuTray:s,_buttonTray:L}}},Be=function(n,l,g){return{element:{originElement:n,topArea:l._top,relative:l._relative,toolbar:l._toolBar,_toolbarShadow:l._toolbarShadow,_buttonTray:l._toolBar.querySelector(".se-btn-tray"),_menuTray:l._menuTray,resizingBar:l._resizingBar,navigation:l._navigation,charWrapper:l._charWrapper,charCounter:l._charCounter,editorArea:l._editorArea,wysiwygFrame:l._wysiwygArea,wysiwyg:l._wysiwygArea,code:l._codeArea,placeholder:l._placeholder,loading:l._loading,lineBreaker:l._lineBreaker,lineBreaker_t:l._lineBreaker_t,lineBreaker_b:l._lineBreaker_b,resizeBackground:l._resizeBack,_stickyDummy:l._stickyDummy,_arrow:l._arrow,_focusTemp:l._focusTemp},tool:{cover:l._toolBar.querySelector(".se-toolbar-cover"),bold:l._toolBar.querySelector('[data-command="bold"]'),underline:l._toolBar.querySelector('[data-command="underline"]'),italic:l._toolBar.querySelector('[data-command="italic"]'),strike:l._toolBar.querySelector('[data-command="strike"]'),sub:l._toolBar.querySelector('[data-command="SUB"]'),sup:l._toolBar.querySelector('[data-command="SUP"]'),undo:l._toolBar.querySelector('[data-command="undo"]'),redo:l._toolBar.querySelector('[data-command="redo"]'),save:l._toolBar.querySelector('[data-command="save"]'),outdent:l._toolBar.querySelector('[data-command="outdent"]'),indent:l._toolBar.querySelector('[data-command="indent"]'),fullScreen:l._toolBar.querySelector('[data-command="fullScreen"]'),showBlocks:l._toolBar.querySelector('[data-command="showBlocks"]'),codeView:l._toolBar.querySelector('[data-command="codeView"]'),dir:l._toolBar.querySelector('[data-command="dir"]'),dir_ltr:l._toolBar.querySelector('[data-command="dir_ltr"]'),dir_rtl:l._toolBar.querySelector('[data-command="dir_rtl"]')},options:g,option:g}},ve=Be;function ke(n,l){const g=n._w,C=n.util,o=n.options.historyStackDelayTime;let w=n.context.element,L=n.context.tool.undo,E=n.context.tool.redo,t=null,R=0,a=[];function p(){const I=a[R];w.wysiwyg.innerHTML=I.contents,n.setRange(C.getNodeFromPath(I.s.path,w.wysiwyg),I.s.offset,C.getNodeFromPath(I.e.path,w.wysiwyg),I.e.offset),n.focus(),a.length<=1?(L&&L.setAttribute("disabled",!0),E&&E.setAttribute("disabled",!0)):R===0?(L&&L.setAttribute("disabled",!0),E&&E.removeAttribute("disabled")):R===a.length-1?(L&&L.removeAttribute("disabled"),E&&E.setAttribute("disabled",!0)):(L&&L.removeAttribute("disabled"),E&&E.removeAttribute("disabled")),n.controllersOff(),n._checkComponents(),n._setCharCount(),n._resourcesStateChange(),l()}function F(){n._checkComponents();const I=w.wysiwyg.innerHTML;if(!I||a[R]&&I===a[R].contents)return;R++;const K=n._variable._range;a.length>R&&(a=a.slice(0,R),E&&E.setAttribute("disabled",!0)),K?a[R]={contents:I,s:{path:C.getNodePath(K.startContainer,null,null),offset:K.startOffset},e:{path:C.getNodePath(K.endContainer,null,null),offset:K.endOffset}}:a[R]={contents:I,s:{path:[0,0],offset:[0,0]},e:{path:0,offset:0}},R===1&&L&&L.removeAttribute("disabled"),n._setCharCount(),l()}return{stack:a,push:function(I){g.setTimeout(n._resourcesStateChange.bind(n));const K=typeof I=="number"?I>0?I:0:I?o:0;if((!K||t)&&(g.clearTimeout(t),!K)){F();return}t=g.setTimeout(function(){g.clearTimeout(t),t=null,F()},K)},undo:function(){R>0&&(R--,p())},redo:function(){a.length-1>R&&(R++,p())},go:function(I){R=I<0?a.length-1:I,p()},getCurrentIndex:function(){return R},reset:function(I){L&&L.setAttribute("disabled",!0),E&&E.setAttribute("disabled",!0),n._variable.isChanged=!1,n.context.tool.save&&n.context.tool.save.setAttribute("disabled",!0),a.splice(0),R=0,a[R]={contents:n.getContents(!0),s:{path:[0,0],offset:0},e:{path:[0,0],offset:0}},I||l()},_resetCachingButton:function(){w=n.context.element,L=n.context.tool.undo,E=n.context.tool.redo,R===0?(L&&L.setAttribute("disabled",!0),E&&R===a.length-1&&E.setAttribute("disabled",!0),n._variable.isChanged=!1,n.context.tool.save&&n.context.tool.save.setAttribute("disabled",!0)):R===a.length-1&&E&&E.setAttribute("disabled",!0)},_destroy:function(){t&&g.clearTimeout(t),a=null}}}const Te={name:"notice",add:function(n){const l=n.context;l.notice={};let g=n.util.createElement("DIV"),C=n.util.createElement("SPAN"),o=n.util.createElement("BUTTON");g.className="se-notice",o.className="close",o.setAttribute("aria-label","Close"),o.setAttribute("title",n.lang.dialogBox.close),o.innerHTML=n.icons.cancel,g.appendChild(C),g.appendChild(o),l.notice.modal=g,l.notice.message=C,o.addEventListener("click",this.onClick_cancel.bind(n)),l.element.editorArea.appendChild(g),g=null},onClick_cancel:function(n){n.preventDefault(),n.stopPropagation(),this.plugins.notice.close.call(this)},open:function(n){this.context.notice.message.textContent=n,this.context.notice.modal.style.display="block"},close:function(){this.context.notice.modal.style.display="none"}};function He(n,l,g,C,o,w){const L=n.element.originElement.ownerDocument||document,E=L.defaultView||window,t=j,R=o.icons,a={_d:L,_w:E,_parser:new E.DOMParser,_prevRtl:o.rtl,_editorHeight:0,_editorHeightPadding:0,_listCamel:o.__listCommonStyle,_listKebab:t.camelToKebabCase(o.__listCommonStyle),__focusTemp:n.element._focusTemp,_wd:null,_ww:null,_shadowRoot:null,_shadowRootControllerEventTarget:null,util:t,functions:null,options:null,wwComputedStyle:null,notice:Te,icons:R,history:null,context:n,pluginCallButtons:l,plugins:g||{},initPlugins:{},_targetPlugins:{},_menuTray:{},lang:C,effectNode:null,submenu:null,container:null,_submenuName:"",_bindedSubmenuOff:null,_bindedContainerOff:null,submenuActiveButton:null,containerActiveButton:null,controllerArray:[],currentControllerName:"",currentControllerTarget:null,currentFileComponentInfo:null,codeViewDisabledButtons:[],resizingDisabledButtons:[],_moreLayerActiveButton:null,_htmlCheckWhitelistRegExp:null,_htmlCheckBlacklistRegExp:null,_disallowedTextTagsRegExp:null,editorTagsWhitelistRegExp:null,editorTagsBlacklistRegExp:null,pasteTagsWhitelistRegExp:null,pasteTagsBlacklistRegExp:null,hasFocus:!1,isDisabled:!1,isReadOnly:!1,_attributesWhitelistRegExp:null,_attributesWhitelistRegExp_all_data:null,_attributesBlacklistRegExp:null,_attributesTagsWhitelist:null,_attributesTagsBlacklist:null,_bindControllersOff:null,_isInline:null,_isBalloon:null,_isBalloonAlways:null,_inlineToolbarAttr:{top:"",width:"",isShow:!1},_notHideToolbar:!1,_sticky:!1,_antiBlur:!1,_lineBreaker:null,_lineBreakerButton:null,_componentsInfoInit:!0,_componentsInfoReset:!1,activePlugins:null,managedTagsInfo:null,_charTypeHTML:!1,_fileInfoPluginsCheck:null,_fileInfoPluginsReset:null,_fileManager:{tags:null,regExp:null,queryString:null,pluginRegExp:null,pluginMap:null},commandMap:{},_commandMapStyles:{STRONG:["font-weight"],U:["text-decoration"],EM:["font-style"],DEL:["text-decoration"]},_styleCommandMap:null,_cleanStyleRegExp:{div:new E.RegExp("\\s*[^-a-zA-Z](.+)\\s*:[^;]+(?!;)*","ig"),span:new E.RegExp("\\s*[^-a-zA-Z](font-family|font-size|color|background-color)\\s*:[^;]+(?!;)*","ig"),format:new E.RegExp("\\s*[^-a-zA-Z](text-align|margin-left|margin-right|width|height|line-height)\\s*:[^;]+(?!;)*","ig"),fontSizeUnit:new E.RegExp("\\d+"+o.fontSizeUnit+"$","i")},_variable:{isChanged:!1,isCodeView:!1,isFullScreen:!1,innerHeight_fullScreen:0,resizeClientY:0,tabSize:4,codeIndent:2,minResizingSize:t.getNumber(n.element.wysiwygFrame.style.minHeight||"65",0),currentNodes:[],currentNodesMap:[],_range:null,_selectionNode:null,_originCssText:n.element.topArea.style.cssText,_bodyOverflow:"",_editorAreaOriginCssText:"",_wysiwygOriginCssText:"",_codeOriginCssText:"",_fullScreenAttrs:{sticky:!1,balloon:!1,inline:!1},_lineBreakComp:null,_lineBreakDir:""},_formatAttrsTemp:null,_saveButtonStates:function(){this.allCommandButtons||(this.allCommandButtons={});const e=this.context.element._buttonTray.querySelectorAll(".se-menu-list button[data-display]");for(let i=0,s,r;i<e.length;i++)s=e[i],r=s.getAttribute("data-command"),this.allCommandButtons[r]=s},_recoverButtonStates:function(){if(this.allCommandButtons){const e=this.context.element._buttonTray.querySelectorAll(".se-menu-list button[data-display]");for(let i=0,s,r,c;i<e.length;i++)s=e[i],r=s.getAttribute("data-command"),c=this.allCommandButtons[r],c&&(s.parentElement.replaceChild(c,s),this.context.tool[r]&&(this.context.tool[r]=c))}},callPlugin:function(e,i,s){if(s=s||l[e],this.plugins[e])this.initPlugins[e]?typeof this._targetPlugins[e]=="object"&&s&&this.initMenuTarget(e,s,this._targetPlugins[e]):(this.plugins[e].add(this,s),this.initPlugins[e]=!0);else throw Error('[SUNEDITOR.core.callPlugin.fail] The called plugin does not exist or is in an invalid format. (pluginName:"'+e+'")');this.plugins[e].active&&!this.commandMap[e]&&s&&(this.commandMap[e]=s,this.activePlugins.push(e)),typeof i=="function"&&i()},addModule:function(e){for(let i=0,s=e.length,r;i<s;i++)r=e[i].name,this.plugins[r]||(this.plugins[r]=e[i]),this.initPlugins[r]||(this.initPlugins[r]=!0,typeof this.plugins[r].add=="function"&&this.plugins[r].add(this))},getGlobalScrollOffset:function(){let e=0,i=0,s=n.element.topArea;for(;s;)e+=s.scrollTop,i+=s.scrollLeft,s=s.parentElement;for(s=this._shadowRoot?this._shadowRoot.host:null;s;)e+=s.scrollTop,i+=s.scrollLeft,s=s.parentElement;return{top:e,left:i}},initMenuTarget:function(e,i,s){i?(n.element._menuTray.appendChild(s),this._targetPlugins[e]=!0,this._menuTray[i.getAttribute("data-command")]=s):this._targetPlugins[e]=s},submenuOn:function(e){this._bindedSubmenuOff&&this._bindedSubmenuOff(),this._bindControllersOff&&this.controllersOff();const i=this._submenuName=e.getAttribute("data-command"),s=this.submenu=this._menuTray[i];this.submenuActiveButton=e,this._setMenuPosition(e,s),this._bindedSubmenuOff=this.submenuOff.bind(this),this.addDocEvent("mousedown",this._bindedSubmenuOff,!1),this.plugins[i].on&&this.plugins[i].on.call(this),this._antiBlur=!0},submenuOff:function(){this.removeDocEvent("mousedown",this._bindedSubmenuOff),this._bindedSubmenuOff=null,this.submenu&&(this._submenuName="",this.submenu.style.display="none",this.submenu=null,t.removeClass(this.submenuActiveButton,"on"),this.submenuActiveButton=null,this._notHideToolbar=!1),this._antiBlur=!1},moreLayerOff:function(){if(this._moreLayerActiveButton){const e=n.element.toolbar.querySelector("."+this._moreLayerActiveButton.getAttribute("data-command"));e.style.display="none",t.removeClass(this._moreLayerActiveButton,"on"),this._moreLayerActiveButton=null}},containerOn:function(e){this._bindedContainerOff&&this._bindedContainerOff();const i=this._containerName=e.getAttribute("data-command"),s=this.container=this._menuTray[i];this.containerActiveButton=e,this._setMenuPosition(e,s),this._bindedContainerOff=this.containerOff.bind(this),this.addDocEvent("mousedown",this._bindedContainerOff,!1),this.plugins[i].on&&this.plugins[i].on.call(this),this._antiBlur=!0},containerOff:function(){this.removeDocEvent("mousedown",this._bindedContainerOff),this._bindedContainerOff=null,this.container&&(this._containerName="",this.container.style.display="none",this.container=null,t.removeClass(this.containerActiveButton,"on"),this.containerActiveButton=null,this._notHideToolbar=!1),this._antiBlur=!1},_setMenuPosition:function(e,i){i.style.visibility="hidden",i.style.display="block",i.style.height="",t.addClass(e,"on");const s=this.context.element.toolbar,r=s.offsetWidth,c=p._getEditorOffsets(n.element.toolbar),d=i.offsetWidth,f=e.parentElement.offsetLeft+3;if(o.rtl){const x=e.offsetWidth,D=d>x?d-x:0,z=D>0?0:x-d;i.style.left=f-D+z+"px",c.left>p._getEditorOffsets(i).left&&(i.style.left="0px")}else{const x=r<=d?0:r-(f+d);x<0?i.style.left=f+x+"px":i.style.left=f+"px"}let u=0,m=e;for(;m&&m!==s;)u+=m.offsetTop,m=m.offsetParent;const h=u;this._isBalloon?u+=s.offsetTop+e.offsetHeight:u-=e.offsetHeight;const y=c.top,B=i.offsetHeight,b=this.getGlobalScrollOffset().top,S=E.innerHeight-(y-b+h+e.parentElement.offsetHeight);if(S<B){let x=-1*(B-h+3);const D=y-b+x,z=B+(D<0?D:0);z>S?(i.style.height=z+"px",x=-1*(z-h+3)):(i.style.height=S+"px",x=h+e.parentElement.offsetHeight),i.style.top=x+"px"}else i.style.top=h+e.parentElement.offsetHeight+"px";i.style.visibility=""},controllersOn:function(){this._bindControllersOff&&this._bindControllersOff(),this.controllerArray=[];for(let e=0,i;e<arguments.length;e++)if(i=arguments[e],!!i){if(typeof i=="string"){this.currentControllerName=i;continue}if(typeof i=="function"){this.controllerArray.push(i);continue}if(!t.hasClass(i,"se-controller")){this.currentControllerTarget=i,this.currentFileComponentInfo=this.getFileComponent(i);continue}i.style&&(i.style.display="block",this._shadowRoot&&this._shadowRootControllerEventTarget.indexOf(i)===-1&&(i.addEventListener("mousedown",function(s){s.preventDefault(),s.stopPropagation()}),this._shadowRootControllerEventTarget.push(i))),this.controllerArray.push(i)}this._bindControllersOff=this.controllersOff.bind(this),this.addDocEvent("mousedown",this._bindControllersOff,!1),this.addDocEvent("keydown",this._bindControllersOff,!1),this._antiBlur=!0,typeof F.showController=="function"&&F.showController(this.currentControllerName,this.controllerArray,this)},controllersOff:function(e){this._lineBreaker.style.display="none";const i=this.controllerArray.length;if(e&&e.target&&i>0){for(let s=0;s<i;s++)if(typeof this.controllerArray[s].contains=="function"&&this.controllerArray[s].contains(e.target))return}if(!(this._fileManager.pluginRegExp.test(this.currentControllerName)&&e&&e.type==="keydown"&&e.keyCode!==27)&&(n.element.lineBreaker_t.style.display=n.element.lineBreaker_b.style.display="none",this._variable._lineBreakComp=null,this.currentControllerName="",this.currentControllerTarget=null,this.currentFileComponentInfo=null,this.effectNode=null,!!this._bindControllersOff)){if(this.removeDocEvent("mousedown",this._bindControllersOff),this.removeDocEvent("keydown",this._bindControllersOff),this._bindControllersOff=null,i>0){for(let s=0;s<i;s++)typeof this.controllerArray[s]=="function"?this.controllerArray[s]():this.controllerArray[s].style.display="none";this.controllerArray=[]}this._antiBlur=!1}},setControllerPosition:function(e,i,s,r){o.rtl&&(r.left*=-1);const c=t.getOffset(i,n.element.wysiwygFrame);e.style.visibility="hidden",e.style.display="block";const d=s==="top"?-(e.offsetHeight+2):i.offsetHeight+12;e.style.top=c.top+d+r.top+"px";const f=c.left-n.element.wysiwygFrame.scrollLeft+r.left,u=e.offsetWidth,m=i.offsetWidth,h=t.hasClass(e.firstElementChild,"se-arrow")?e.firstElementChild:null;if(o.rtl){const y=u>m?u-m:0,B=y>0?0:m-u;e.style.left=f-y+B+"px",y>0&&h&&(h.style.left=(u-14<10+y?u-14:10+y)+"px");const b=n.element.wysiwygFrame.offsetLeft-e.offsetLeft;b>0&&(e.style.left="0px",h&&(h.style.left=b+"px"))}else{e.style.left=f+"px";const y=n.element.wysiwygFrame.offsetWidth-(e.offsetLeft+u);y<0?(e.style.left=e.offsetLeft+y+"px",h&&(h.style.left=20-y+"px")):h&&(h.style.left="20px")}e.style.visibility=""},execCommand:function(e,i,s){this._wd.execCommand(e,i,e==="formatBlock"?"<"+s+">":s),this.history.push(!0)},nativeFocus:function(){this.__focus(),this._editorRange()},__focus:function(){const e=t.getParentElement(this.getSelectionNode(),"figcaption");e?e.focus():n.element.wysiwyg.focus()},focus:function(){if(n.element.wysiwygFrame.style.display!=="none"){if(o.iframe)this.nativeFocus();else try{const e=this.getRange();if(e.startContainer===e.endContainer&&t.isWysiwygDiv(e.startContainer)){const i=e.commonAncestorContainer.children[e.startOffset];if(!t.isFormatElement(i)&&!t.isComponent(i)){const s=t.createElement(o.defaultTag),r=t.createElement("BR");s.appendChild(r),n.element.wysiwyg.insertBefore(s,i),this.setRange(r,0,r,0);return}}this.setRange(e.startContainer,e.startOffset,e.endContainer,e.endOffset)}catch{this.nativeFocus()}p._applyTagEffects(),this._isBalloon&&p._toggleToolbarBalloon()}},focusEdge:function(e){e||(e=n.element.wysiwyg.lastElementChild);const i=this.getFileComponent(e);i?this.selectComponent(i.target,i.pluginName):e?(e=t.getChildElement(e,function(s){return s.childNodes.length===0||s.nodeType===3},!0),e?this.setRange(e,e.textContent.length,e,e.textContent.length):this.nativeFocus()):this.focus()},blur:function(){o.iframe?n.element.wysiwygFrame.blur():n.element.wysiwyg.blur()},setRange:function(e,i,s,r){if(!e||!s)return;i>e.textContent.length&&(i=e.textContent.length),r>s.textContent.length&&(r=s.textContent.length),t.isFormatElement(e)&&(e=e.childNodes[i]||e.childNodes[i-1]||e,i=i>0?e.nodeType===1?1:e.textContent?e.textContent.length:0:0),t.isFormatElement(s)&&(s=s.childNodes[r]||s.childNodes[r-1]||s,r=r>0?s.nodeType===1?1:s.textContent?s.textContent.length:0:0);const c=this._wd.createRange();try{c.setStart(e,i),c.setEnd(s,r)}catch(f){console.warn("[SUNEDITOR.core.focus.error] "+f),this.nativeFocus();return}const d=this.getSelection();return d.removeAllRanges&&d.removeAllRanges(),d.addRange(c),this._rangeInfo(c,this.getSelection()),o.iframe&&this.__focus(),c},removeRange:function(){this._variable._range=null,this._variable._selectionNode=null,this.hasFocus&&this.getSelection().removeAllRanges(),this._setKeyEffect([])},getRange:function(){const e=this._variable._range||this._createDefaultRange(),i=this.getSelection();if(e.collapsed===i.isCollapsed||!n.element.wysiwyg.contains(i.focusNode))return e;if(i.rangeCount>0)return this._variable._range=i.getRangeAt(0),this._variable._range;{const s=i.anchorNode,r=i.focusNode,c=i.anchorOffset,d=i.focusOffset,f=t.compareElements(s,r),u=f.ancestor&&(f.result===0?c<=d:f.result>1);return this.setRange(u?s:r,u?c:d,u?r:s,u?d:c)}},getRange_addLine:function(e,i){if(this._selectionVoid(e)){const s=n.element.wysiwyg,r=t.createElement(o.defaultTag);r.innerHTML="<br>",s.insertBefore(r,i&&i!==s?i.nextElementSibling:s.firstElementChild),this.setRange(r.firstElementChild,0,r.firstElementChild,1),e=this._variable._range}return e},getSelection:function(){const e=this._shadowRoot&&this._shadowRoot.getSelection?this._shadowRoot.getSelection():this._ww.getSelection();return!this._variable._range&&!n.element.wysiwyg.contains(e.focusNode)&&(e.removeAllRanges(),e.addRange(this._createDefaultRange())),e},getSelectionNode:function(){if(n.element.wysiwyg.contains(this._variable._selectionNode)||this._editorRange(),!this._variable._selectionNode){const e=t.getChildElement(n.element.wysiwyg.firstChild,function(i){return i.childNodes.length===0||i.nodeType===3},!1);if(!e)this._editorRange();else return this._variable._selectionNode=e,e}return this._variable._selectionNode},_editorRange:function(){const e=this._wd.activeElement;if(t.isInputElement(e))return this._variable._selectionNode=e,e;const i=this.getSelection();if(!i)return null;let s=null;i.rangeCount>0?s=i.getRangeAt(0):s=this._createDefaultRange(),this._rangeInfo(s,i)},_rangeInfo:function(e,i){let s=null;this._variable._range=e,e.collapsed?t.isWysiwygDiv(e.commonAncestorContainer)?s=e.commonAncestorContainer.children[e.startOffset]||e.commonAncestorContainer:s=e.commonAncestorContainer:s=i.extentNode||i.anchorNode,this._variable._selectionNode=s},_createDefaultRange:function(){const e=n.element.wysiwyg,i=this._wd.createRange();let s=e.firstElementChild,r=null;return s?(r=s.firstChild,r||(r=t.createElement("BR"),s.appendChild(r))):(s=t.createElement(o.defaultTag),r=t.createElement("BR"),s.appendChild(r),e.appendChild(s)),i.setStart(r,0),i.setEnd(r,0),i},_selectionVoid:function(e){const i=e.commonAncestorContainer;return t.isWysiwygDiv(e.startContainer)&&t.isWysiwygDiv(e.endContainer)||/FIGURE/i.test(i.nodeName)||this._fileManager.regExp.test(i.nodeName)||t.isMediaComponent(i)},_resetRangeToTextNode:function(){const e=this.getRange();if(this._selectionVoid(e))return!1;let i=e.startContainer,s=e.startOffset,r=e.endContainer,c=e.endOffset,d,f,u;if(t.isFormatElement(i))for(i.childNodes[s]?(i=i.childNodes[s]||i,s=0):(i=i.lastChild||i,s=i.textContent.length);i&&i.nodeType===1&&i.firstChild;)i=i.firstChild||i,s=0;if(t.isFormatElement(r)){for(r=r.childNodes[c]||r.lastChild||r;r&&r.nodeType===1&&r.lastChild;)r=r.lastChild;c=r.textContent.length}if(d=t.isWysiwygDiv(i)?n.element.wysiwyg.firstChild:i,f=s,t.isBreak(d)||d.nodeType===1&&d.childNodes.length>0){const m=t.isBreak(d);if(!m){for(;d&&!t.isBreak(d)&&d.nodeType===1;)d=d.childNodes[f]||d.nextElementSibling||d.nextSibling,f=0;let h=t.getFormatElement(d,null);h===t.getRangeFormatElement(h,null)&&(h=t.createElement(t.getParentElement(d,t.isCell)?"DIV":o.defaultTag),d.parentNode.insertBefore(h,d),h.appendChild(d))}if(t.isBreak(d)){const h=t.createTextNode(t.zeroWidthSpace);d.parentNode.insertBefore(h,d),d=h,m&&i===r&&(r=d,c=1)}}if(i=d,s=f,d=t.isWysiwygDiv(r)?n.element.wysiwyg.lastChild:r,f=c,t.isBreak(d)||d.nodeType===1&&d.childNodes.length>0){const m=t.isBreak(d);if(!m){for(;d&&!t.isBreak(d)&&d.nodeType===1&&(u=d.childNodes,u.length!==0);)d=u[f>0?f-1:f]||!/FIGURE/i.test(u[0].nodeName)?u[0]:d.previousElementSibling||d.previousSibling||i,f=f>0?d.textContent.length:f;let h=t.getFormatElement(d,null);h===t.getRangeFormatElement(h,null)&&(h=t.createElement(t.isCell(h)?"DIV":o.defaultTag),d.parentNode.insertBefore(h,d),h.appendChild(d))}if(t.isBreak(d)){const h=t.createTextNode(t.zeroWidthSpace);d.parentNode.insertBefore(h,d),d=h,f=1,m&&!d.previousSibling&&t.removeItem(r)}}return r=d,c=f,this.setRange(i,s,r,c),!0},getSelectedElements:function(e){if(!this._resetRangeToTextNode())return[];let i=this.getRange();if(t.isWysiwygDiv(i.startContainer)){const x=n.element.wysiwyg.children;if(x.length===0)return[];this.setRange(x[0],0,x[x.length-1],x[x.length-1].textContent.trim().length),i=this.getRange()}const s=i.startContainer,r=i.endContainer,c=i.commonAncestorContainer,d=t.getListChildren(c,function(x){return e?e(x):t.isFormatElement(x)});if(!t.isWysiwygDiv(c)&&!t.isRangeFormatElement(c)&&d.unshift(t.getFormatElement(c,null)),s===r||d.length===1)return d;let f=t.getFormatElement(s,null),u=t.getFormatElement(r,null),m=null,h=null;const y=function(x){return t.isTable(x)?/^TABLE$/i.test(x.nodeName):!0};let B=t.getRangeFormatElement(f,y),b=t.getRangeFormatElement(u,y);t.isTable(B)&&t.isListCell(B.parentNode)&&(B=B.parentNode),t.isTable(b)&&t.isListCell(b.parentNode)&&(b=b.parentNode);const S=B===b;for(let x=0,D=d.length,z;x<D;x++){if(z=d[x],f===z||!S&&z===B){m=x;continue}if(u===z||!S&&z===b){h=x;break}}return m===null&&(m=0),h===null&&(h=d.length-1),d.slice(m,h+1)},getSelectedElementsAndComponents:function(e){const i=this.getRange().commonAncestorContainer,s=t.getParentElement(i,t.isComponent),r=t.isTable(i)?this.getSelectedElements(null):this.getSelectedElements((function(c){const d=this.getParentElement(c,this.isComponent);return this.isFormatElement(c)&&(!d||d===s)||this.isComponent(c)&&!this.getFormatElement(c)}).bind(t));if(e){for(let c=0,d=r.length;c<d;c++)for(let f=c-1;f>=0;f--)if(r[f].contains(r[c])){r.splice(c,1),c--,d--;break}}return r},isEdgePoint:function(e,i,s){return s!=="end"&&i===0||(!s||s!=="front")&&!e.nodeValue&&i===1||(!s||s==="end")&&!!e.nodeValue&&i===e.nodeValue.length},_isEdgeFormat:function(e,i,s){if(!this.isEdgePoint(e,i,s))return!1;const r=[];for(s=s==="front"?"previousSibling":"nextSibling";e&&!t.isFormatElement(e)&&!t.isWysiwygDiv(e);)if(!e[s]||t.isBreak(e[s])&&!e[s][s])e.nodeType===1&&r.push(e.cloneNode(!1)),e=e.parentNode;else return null;return r},showLoading:function(){n.element.loading.style.display="block"},closeLoading:function(){n.element.loading.style.display="none"},appendFormatTag:function(e,i){if(!e||!e.parentNode)return null;const s=t.getFormatElement(this.getSelectionNode(),null);let r=null;if(!t.isFormatElement(e)&&t.isFreeFormatElement(s||e.parentNode))r=t.createElement("BR");else{const c=i?typeof i=="string"?i:i.nodeName:t.isFormatElement(s)&&!t.isRangeFormatElement(s)&&!t.isFreeFormatElement(s)?s.nodeName:o.defaultTag;r=t.createElement(c),r.innerHTML="<br>",(i&&typeof i!="string"||!i&&t.isFormatElement(s))&&t.copyTagAttributes(r,i||s,["id"])}return t.isCell(e)?e.insertBefore(r,e.nextElementSibling):e.parentNode.insertBefore(r,e.nextElementSibling),r},insertComponent:function(e,i,s,r){if(this.isReadOnly||s&&!this.checkCharCount(e,null))return null;const c=this.removeNode();this.getRange_addLine(this.getRange(),c.container);let d=null,f=this.getSelectionNode(),u=t.getFormatElement(f,null);if(t.isListCell(u))this.insertNode(e,f===u?null:c.container.nextSibling,!1),e.nextSibling||e.parentNode.appendChild(t.createElement("BR"));else{if(this.getRange().collapsed&&(c.container.nodeType===3||t.isBreak(c.container))){const m=t.getParentElement(c.container,(function(h){return this.isRangeFormatElement(h)}).bind(t));d=t.splitElement(c.container,c.offset,m?t.getElementDepth(m)+1:0),d&&(u=d.previousSibling)}this.insertNode(e,t.isRangeFormatElement(u)?null:u,!1),u&&t.onlyZeroWidthSpace(u)&&t.removeItem(u)}if(!r){this.setRange(e,0,e,0);const m=this.getFileComponent(e);m?this.selectComponent(m.target,m.pluginName):d&&(d=t.getEdgeChildNodes(d,null).sc||d,this.setRange(d,0,d,0))}return i||this.history.push(1),d||e},getFileComponent:function(e){if(!this._fileManager.queryString||!e)return null;let i,s;return(/^FIGURE$/i.test(e.nodeName)||/se-component/.test(e.className))&&(i=e.querySelector(this._fileManager.queryString)),!i&&e.nodeName&&this._fileManager.regExp.test(e.nodeName)&&(i=e),i&&(s=this._fileManager.pluginMap[i.nodeName.toLowerCase()],s)?{target:i,component:t.getParentElement(i,t.isComponent),pluginName:s}:null},selectComponent:function(e,i){if(t.isUneditableComponent(t.getParentElement(e,t.isComponent))||t.isUneditableComponent(e))return!1;this.hasFocus||this.focus();const s=this.plugins[i];s&&E.setTimeout((function(){typeof s.select=="function"&&this.callPlugin(i,s.select.bind(this,e),null),this._setComponentLineBreaker(e)}).bind(this))},_setComponentLineBreaker:function(e){this._lineBreaker.style.display="none";const i=t.getParentElement(e,t.isComponent),s=n.element.lineBreaker_t.style,r=n.element.lineBreaker_b.style,c=this.context.resizing.resizeContainer.style.display==="block"?this.context.resizing.resizeContainer:e,d=t.isListCell(i.parentNode);let f,u,m;(d?!i.previousSibling:!t.isFormatElement(i.previousElementSibling))?(this._variable._lineBreakComp=i,u=n.element.wysiwyg.scrollTop,f=t.getOffset(e,n.element.wysiwygFrame).top+u,m=c.offsetWidth/2/2,s.top=f-u-12+"px",s.left=t.getOffset(c).left+m+"px",s.display="block"):s.display="none",(d?!i.nextSibling:!t.isFormatElement(i.nextElementSibling))?(f||(this._variable._lineBreakComp=i,u=n.element.wysiwyg.scrollTop,f=t.getOffset(e,n.element.wysiwygFrame).top+u,m=c.offsetWidth/2/2),r.top=f+c.offsetHeight-u-12+"px",r.left=t.getOffset(c).left+c.offsetWidth-m-24+"px",r.display="block"):r.display="none"},_checkDuplicateNode:function(e,i){(function s(r){a._dupleCheck(r,i);const c=r.childNodes;for(let d=0,f=c.length;d<f;d++)s(c[d])})(e)},_dupleCheck:function(e,i){if(!t.isTextStyleElement(e))return;const s=(e.style.cssText.match(/[^;]+;/g)||[]).map(function(d){return d.trim()}),r=e.nodeName;if(/^span$/i.test(r)&&s.length===0)return e;let c=!1;return function d(f){if(!(t.isWysiwygDiv(f)||!t.isTextStyleElement(f))){if(f.nodeName===r){c=!0;const u=f.style.cssText.match(/[^;]+;/g)||[];for(let m=0,h=u.length,y;m<h;m++)(y=s.indexOf(u[m].trim()))>-1&&s.splice(y,1);for(let m=0,h=f.classList.length;m<h;m++)e.classList.remove(f.classList[m])}d(f.parentElement)}}(i),c&&((e.style.cssText=s.join(" "))||(e.setAttribute("style",""),e.removeAttribute("style")),e.attributes.length||e.setAttribute("data-se-duple","true")),e},insertNode:function(e,i,s){if(this.isReadOnly||s&&!this.checkCharCount(e,null))return null;let r=null,c=this.getRange(),d=t.isListCell(c.commonAncestorContainer)?c.commonAncestorContainer:t.getFormatElement(this.getSelectionNode(),null),f=t.isListCell(d)&&(t.isListCell(e)||t.isList(e)),u,m,h,y=null;const B=t.isFreeFormatElement(d),b=!B&&(t.isFormatElement(e)||t.isRangeFormatElement(e))||t.isComponent(e);if(f&&(h=i||t.isList(e)?d.lastChild:d.nextElementSibling,y=t.isList(e)?d:(h||d).parentNode),!i&&(b||t.isComponent(e)||t.isMedia(e))){const M=this.isEdgePoint(c.endContainer,c.endOffset,"end"),T=this.removeNode(),k=T.container,_=k===T.prevContainer&&c.collapsed?null:T.prevContainer;if(f&&_)if(y=_.nodeType===3?_.parentNode:_,y.contains(k)){let A=!0;for(h=k;h.parentNode&&h.parentNode!==y;)h=h.parentNode,A=!1;A&&k===_&&(h=h.nextSibling)}else h=null;else if(f&&t.isListCell(k)&&!d.parentElement)d=t.createElement("LI"),y.appendChild(d),k.appendChild(y),h=null;else if(k.nodeType===3||t.isBreak(k)||f){const A=t.getParentElement(k,(function(v){return this.isRangeFormatElement(v)||this.isListCell(v)}).bind(t));if(i=t.splitElement(k,T.offset,A?t.getElementDepth(A)+1:0),!i)h=i=d;else if(f){if(d.contains(k)){const v=t.isList(d.lastElementChild);let N=null;M||(N=d.cloneNode(!1),N.appendChild(i.textContent.trim()?i:t.createTextNode(t.zeroWidthSpace))),v&&(N||(N=d.cloneNode(!1),N.appendChild(t.createTextNode(t.zeroWidthSpace))),N.appendChild(d.lastElementChild)),N&&(d.parentNode.insertBefore(N,d.nextElementSibling),h=i=N)}}else i=i.previousSibling}}c=!i&&!b?this.getRange_addLine(this.getRange(),null):this.getRange();const S=c.commonAncestorContainer,x=c.startOffset,D=c.endOffset,z=c.startContainer===S&&t.isFormatElement(S),O=z&&(S.childNodes[x]||S.childNodes[0])||c.startContainer,H=z&&(S.childNodes[D]||S.childNodes[S.childNodes.length-1])||c.endContainer;if(!f)if(i)u=i.parentNode,i=i.nextSibling,m=!0;else if(u=O,O.nodeType===3&&(u=O.parentNode),c.collapsed)if(S.nodeType===3)S.textContent.length>D?i=S.splitText(D):i=S.nextSibling;else if(t.isBreak(u))i=u,u=u.parentNode;else{let M=u.childNodes[x];const T=M&&M.nodeType===3&&t.onlyZeroWidthSpace(M)&&t.isBreak(M.nextSibling)?M.nextSibling:M;T?!T.nextSibling&&t.isBreak(T)?(u.removeChild(T),i=null):i=t.isBreak(T)&&!t.isBreak(e)?T:T.nextSibling:i=null}else if(O===H){this.isEdgePoint(H,D)?i=H.nextSibling:i=H.splitText(D);let T=O;this.isEdgePoint(O,x)||(T=O.splitText(x)),u.removeChild(T),u.childNodes.length===0&&b&&(u.innerHTML="<br>")}else{const T=this.removeNode(),k=T.container,_=T.prevContainer;if(k&&k.childNodes.length===0&&b&&(t.isFormatElement(k)?k.innerHTML="<br>":t.isRangeFormatElement(k)&&(k.innerHTML="<"+o.defaultTag+"><br></"+o.defaultTag+">")),t.isListCell(k)&&e.nodeType===3)u=k,i=null;else if(!b&&_)if(u=_.nodeType===3?_.parentNode:_,u.contains(k)){let A=!0;for(i=k;i.parentNode&&i.parentNode!==u;)i=i.parentNode,A=!1;A&&k===_&&(i=i.nextSibling)}else i=null;else t.isWysiwygDiv(k)&&!t.isFormatElement(e)?(u=k.appendChild(t.createElement(o.defaultTag)),i=null):(i=b?H:k===_?k.nextSibling:k,u=!i||!i.parentNode?S:i.parentNode);for(;i&&!t.isFormatElement(i)&&i.parentNode!==S;)i=i.parentNode}try{if(!f){if((t.isWysiwygDiv(i)||u===n.element.wysiwyg.parentNode)&&(u=n.element.wysiwyg,i=null),t.isFormatElement(e)||t.isRangeFormatElement(e)||!t.isListCell(u)&&t.isComponent(e)){const M=u;if(t.isList(i))u=i,i=null;else if(t.isListCell(i))u=i.previousElementSibling||i;else if(!m&&!i){const T=this.removeNode(),k=T.container.nodeType===3?t.isListCell(t.getFormatElement(T.container,null))?T.container:t.getFormatElement(T.container,null)||T.container.parentNode:T.container,_=t.isWysiwygDiv(k)||t.isRangeFormatElement(k);u=_?k:k.parentNode,i=_?null:k.nextSibling}M.childNodes.length===0&&u!==M&&t.removeItem(M)}if(b&&!B&&!t.isRangeFormatElement(u)&&!t.isListCell(u)&&!t.isWysiwygDiv(u)&&(i=u.nextElementSibling,u=u.parentNode),t.isWysiwygDiv(u)&&(e.nodeType===3||t.isBreak(e))){const M=t.createElement(o.defaultTag);M.appendChild(e),r=e,e=M}}if(f?y.parentNode?(u=y,i=h):(u=n.element.wysiwyg,i=null):i=u===i?u.lastChild:i,t.isListCell(e)&&!t.isList(u)){if(t.isListCell(u))i=u.nextElementSibling,u=u.parentNode;else{const M=t.createElement("ol");u.insertBefore(M,i),u=M,i=null}f=!0}if(this._checkDuplicateNode(e,u),u.insertBefore(e,i),f)if(t.onlyZeroWidthSpace(d.textContent.trim()))t.removeItem(d),e=e.lastChild;else{const M=t.getArrayItem(d.children,t.isList);M&&(e!==M?(e.appendChild(M),e=M.previousSibling):(u.appendChild(e),e=u),t.onlyZeroWidthSpace(d.textContent.trim())&&t.removeItem(d))}}catch(M){u.appendChild(e),console.warn("[SUNEDITOR.insertNode.warn] "+M)}finally{r&&(e=r);const M=u.querySelectorAll("[data-se-duple]");if(M.length>0)for(let T=0,k=M.length,_,A,v,N;T<k;T++){for(_=M[T],v=_.childNodes,N=_.parentNode;v[0];)A=v[0],N.insertBefore(A,_);_===e&&(e=A),t.removeItem(_)}if((t.isFormatElement(e)||t.isComponent(e))&&O===H){const T=t.getFormatElement(S,null);T&&T.nodeType===1&&t.isEmptyLine(T)&&t.removeItem(T)}if(B&&(t.isFormatElement(e)||t.isRangeFormatElement(e))&&(e=this._setIntoFreeFormat(e)),!t.isComponent(e)){let T=1;if(e.nodeType===3)T=e.textContent.length,this.setRange(e,T,e,T);else if(!t.isBreak(e)&&!t.isListCell(e)&&t.isFormatElement(u)){let k=null;(!e.previousSibling||t.isBreak(e.previousSibling))&&(k=t.createTextNode(t.zeroWidthSpace),e.parentNode.insertBefore(k,e)),(!e.nextSibling||t.isBreak(e.nextSibling))&&(k=t.createTextNode(t.zeroWidthSpace),e.parentNode.insertBefore(k,e.nextSibling)),t._isIgnoreNodeChange(e)&&(e=e.nextSibling,T=0)}this.setRange(e,T,e,T)}return e}},_setIntoFreeFormat:function(e){const i=e.parentNode;let s,r;for(;t.isFormatElement(e)||t.isRangeFormatElement(e);){for(s=e.childNodes,r=null;s[0];){if(r=s[0],t.isFormatElement(r)||t.isRangeFormatElement(r)){if(this._setIntoFreeFormat(r),!e.parentNode)break;s=e.childNodes;continue}i.insertBefore(r,e)}e.childNodes.length===0&&t.removeItem(e),e=t.createElement("BR"),i.insertBefore(e,r.nextSibling)}return e},removeNode:function(){this._resetRangeToTextNode();const e=this.getRange();if(e.startContainer===e.endContainer){const T=t.getParentElement(e.startContainer,t.isMediaComponent);if(T){const k=t.createElement("BR"),_=t.createElement(o.defaultTag);return _.appendChild(k),t.changeElement(T,_),a.setRange(_,0,_,0),this.history.push(!0),{container:_,offset:0,prevContainer:null}}}const i=e.startOffset===0,s=a.isEdgePoint(e.endContainer,e.endOffset,"end");let r=null,c=null,d=null;i&&(c=t.getFormatElement(e.startContainer),c&&(r=c.previousElementSibling,c=r)),s&&(d=t.getFormatElement(e.endContainer),d=d&&d.nextElementSibling);let f,u=0,m=e.startContainer,h=e.endContainer,y=e.startOffset,B=e.endOffset;const b=e.commonAncestorContainer.nodeType===3&&e.commonAncestorContainer.parentNode===m.parentNode?m.parentNode:e.commonAncestorContainer;if(b===m&&b===h&&(m=b.children[y],h=b.children[B],y=B=0),!m||!h)return{container:b,offset:0};if(m===h&&e.collapsed&&m.textContent&&t.onlyZeroWidthSpace(m.textContent.substr(y)))return{container:m,offset:y,prevContainer:m&&m.parentNode?m:null};let S=null,x=null;const D=t.getListChildNodes(b,null);let z=t.getArrayIndex(D,m),O=t.getArrayIndex(D,h);if(D.length>0&&z>-1&&O>-1){for(let T=z+1,k=m;T>=0;T--)D[T]===k.parentNode&&D[T].firstChild===k&&y===0&&(z=T,k=k.parentNode);for(let T=O-1,k=h;T>z;T--)D[T]===k.parentNode&&D[T].nodeType===1&&(D.splice(T,1),k=k.parentNode,--O)}else{if(D.length===0){if(t.isFormatElement(b)||t.isRangeFormatElement(b)||t.isWysiwygDiv(b)||t.isBreak(b)||t.isMedia(b))return{container:b,offset:0};if(b.nodeType===3)return{container:b,offset:B};D.push(b),m=h=b}else if(m=h=D[0],t.isBreak(m)||t.onlyZeroWidthSpace(m))return{container:t.isMedia(b)?b:m,offset:0};z=O=0}for(let T=z;T<=O;T++){const k=D[T];if(k.length===0||k.nodeType===3&&k.data===void 0){this._nodeRemoveListItem(k);continue}if(k===m){if(m.nodeType===1){if(t.isComponent(m))continue;S=t.createTextNode(m.textContent)}else k===h?(S=t.createTextNode(m.substringData(0,y)+h.substringData(B,h.length-B)),u=y):S=t.createTextNode(m.substringData(0,y));if(S.length>0?m.data=S.data:this._nodeRemoveListItem(m),k===h)break;continue}if(k===h){if(h.nodeType===1){if(t.isComponent(h))continue;x=t.createTextNode(h.textContent)}else x=t.createTextNode(h.substringData(B,h.length-B));x.length>0?h.data=x.data:this._nodeRemoveListItem(h);continue}this._nodeRemoveListItem(k)}const H=t.getParentElement(h,"ul"),M=t.getParentElement(m,"li");if(H&&M&&M.contains(H)?(f=H.previousSibling,u=f.textContent.length):(f=h&&h.parentNode?h:m&&m.parentNode?m:e.endContainer||e.startContainer,u=!i&&!s?u:s?f.textContent.length:0),!t.isWysiwygDiv(f)&&f.childNodes.length===0){const T=t.removeItemAllParents(f,null,null);T&&(f=T.sc||T.ec||n.element.wysiwyg)}return!t.getFormatElement(f)&&!(m&&m.parentNode)&&(d?(f=d,u=0):c&&(f=c,u=1)),this.setRange(f,u,f,u),this.history.push(!0),{container:f,offset:u,prevContainer:r}},_nodeRemoveListItem:function(e){const i=t.getFormatElement(e,null);t.removeItem(e),t.isListCell(i)&&(t.removeItemAllParents(i,null,null),i&&t.isList(i.firstChild)&&i.insertBefore(t.createTextNode(t.zeroWidthSpace),i.firstChild))},applyRangeFormatElement:function(e){this.getRange_addLine(this.getRange(),null);const i=this.getSelectedElementsAndComponents(!1);if(!i||i.length===0)return;e:for(let B=0,b=i.length,S,x,D,z,O,H;B<b;B++)if(S=i[B],!!t.isListCell(S)&&(x=S.lastElementChild,x&&t.isListCell(S.nextElementSibling)&&i.indexOf(S.nextElementSibling)>-1&&(z=x.lastElementChild,i.indexOf(z)>-1))){let M=null;for(;M=z.lastElementChild;)if(t.isList(M))if(i.indexOf(M.lastElementChild)>-1)z=M.lastElementChild;else continue e;D=x.firstElementChild,O=i.indexOf(D),H=i.indexOf(z),i.splice(O,H-O+1),b=i.length;continue}let s=i[i.length-1],r,c,d;t.isRangeFormatElement(s)||t.isFormatElement(s)?r=s:r=t.getRangeFormatElement(s,null)||t.getFormatElement(s,null),t.isCell(r)?(c=null,d=r):(c=r.nextSibling,d=r.parentNode);let f=t.getElementDepth(r),u=null;const m=[],h=function(B,b,S){let x=null;if(B!==b&&!t.isTable(b)){if(b&&t.getElementDepth(B)===t.getElementDepth(b))return S;x=t.removeItemAllParents(b,null,B)}return x?x.ec:S};for(let B=0,b=i.length,S,x,D,z,O,H,M;B<b;B++)if(S=i[B],x=S.parentNode,!(!x||e.contains(x)))if(D=t.getElementDepth(S),t.isList(x)){if(u===null&&(H?(u=H,M=!0,H=null):u=x.cloneNode(!1)),m.push(S),O=i[B+1],B===b-1||O&&O.parentNode!==x){O&&S.contains(O.parentNode)&&(H=O.parentNode.cloneNode(!1));let T=x.parentNode,k;for(;t.isList(T);)k=t.createElement(T.nodeName),k.appendChild(u),u=k,T=T.parentNode;const _=this.detachRangeFormatElement(x,m,null,!0,!0);f>=D?(f=D,d=_.cc,c=h(d,x,_.ec),c&&(d=c.parentNode)):d===_.cc&&(c=_.ec),d!==_.cc&&(z=h(d,_.cc,z),z!==void 0?c=z:c=_.cc);for(let A=0,v=_.removeArray.length;A<v;A++)u.appendChild(_.removeArray[A]);M||e.appendChild(u),H&&_.removeArray[_.removeArray.length-1].appendChild(H),u=null,M=!1}}else f>=D&&(f=D,d=x,c=S.nextSibling),e.appendChild(S),d!==x&&(z=h(d,x),z!==void 0&&(c=z));if(this.effectNode=null,t.mergeSameTags(e,null,!1),t.mergeNestedTags(e,(function(B){return this.isList(B)}).bind(t)),c&&t.getElementDepth(c)>0&&(t.isList(c.parentNode)||t.isList(c.parentNode.parentNode))){const B=t.getParentElement(c,(function(S){return this.isRangeFormatElement(S)&&!this.isList(S)}).bind(t)),b=t.splitElement(c,null,B?t.getElementDepth(B)+1:0);b.parentNode.insertBefore(e,b)}else d.insertBefore(e,c),h(e,c);const y=t.getEdgeChildNodes(e.firstElementChild,e.lastElementChild);i.length>1?this.setRange(y.sc,0,y.ec,y.ec.textContent.length):this.setRange(y.ec,y.ec.textContent.length,y.ec,y.ec.textContent.length),this.history.push(!1)},detachRangeFormatElement:function(e,i,s,r,c){const d=this.getRange();let f=d.startOffset,u=d.endOffset,m=t.getListChildNodes(e,function(_){return _.parentNode===e}),h=e.parentNode,y=null,B=null,b=e.cloneNode(!1);const S=[],x=t.isList(s);let D=!1,z=!1,O=!1;function H(_,A,v,N){if(t.onlyZeroWidthSpace(A)&&(A.innerHTML=t.zeroWidthSpace,f=u=1),A.nodeType===3)return _.insertBefore(A,v),A;const P=(O?A:N).childNodes;let V=A.cloneNode(!1),U=null,Z=null;for(;P[0];)Z=P[0],t._notTextNode(Z)&&!t.isBreak(Z)&&!t.isListCell(V)?(V.childNodes.length>0&&(U||(U=V),_.insertBefore(V,v),V=A.cloneNode(!1)),_.insertBefore(Z,v),U||(U=Z)):V.appendChild(Z);if(V.childNodes.length>0){if(t.isListCell(_)&&t.isListCell(V)&&t.isList(v))if(x){for(U=v;v;)V.appendChild(v),v=v.nextSibling;_.parentNode.insertBefore(V,_.nextElementSibling)}else{const W=N.nextElementSibling,$=t.detachNestedList(N,!1);if(e!==$||W!==N.nextElementSibling){const q=V.childNodes;for(;q[0];)N.appendChild(q[0]);e=$,z=!0}}else _.insertBefore(V,v);U||(U=V)}return U}for(let _=0,A=m.length,v,N,P;_<A;_++)if(v=m[_],!(v.nodeType===3&&t.isList(b)))if(O=!1,r&&_===0&&(!i||i.length===A||i[0]===v?y=e.previousSibling:y=b),i&&(N=i.indexOf(v)),i&&N===-1)b||(b=e.cloneNode(!1)),b.appendChild(v);else{if(i&&(P=i[N+1]),b&&b.children.length>0&&(h.insertBefore(b,e),b=null),!x&&t.isListCell(v))if(P&&t.getElementDepth(v)!==t.getElementDepth(P)&&(t.isListCell(h)||t.getArrayItem(v.children,t.isList,!1))){const V=v.nextElementSibling,U=t.detachNestedList(v,!1);(e!==U||V!==v.nextElementSibling)&&(e=U,z=!0)}else{const V=v;v=t.createElement(r?V.nodeName:t.isList(e.parentNode)||t.isListCell(e.parentNode)?"LI":t.isCell(e.parentNode)?"DIV":o.defaultTag);const U=t.isListCell(v),Z=V.childNodes;for(;Z[0]&&!(t.isList(Z[0])&&!U);)v.appendChild(Z[0]);t.copyFormatAttributes(v,V),O=!0}else v=v.cloneNode(!1);if(!z&&(r?(S.push(v),t.removeItem(m[_])):(s?(D||(h.insertBefore(s,e),D=!0),v=H(s,v,null,m[_])):v=H(h,v,e,m[_]),z||(i?(B=v,y||(y=v)):y||(y=B=v))),z)){z=O=!1,m=t.getListChildNodes(e,function(V){return V.parentNode===e}),b=e.cloneNode(!1),h=e.parentNode,_=-1,A=m.length;continue}}const M=e.parentNode;let T=e.nextSibling;b&&b.children.length>0&&M.insertBefore(b,T),s?y=s.previousSibling:y||(y=e.previousSibling),T=e.nextSibling!==b?e.nextSibling:b?b.nextSibling:null,e.children.length===0||e.textContent.length===0?t.removeItem(e):t.removeEmptyNode(e,null,!1);let k=null;if(r)k={cc:M,sc:y,so:f,ec:T,eo:u,removeArray:S};else{y||(y=B),B||(B=y);const _=t.getEdgeChildNodes(y,B.parentNode?y:B);k={cc:(_.sc||_.ec).parentNode,sc:_.sc,so:f,ec:_.ec,eo:u,removeArray:null}}if(this.effectNode=null,c)return k;!r&&k&&(i?this.setRange(k.sc,f,k.ec,u):this.setRange(k.sc,0,k.sc,0)),this.history.push(!1)},detachList:function(e,i){let s={},r=!1,c=!1,d=null,f=null;const u=(function(m){return!this.isComponent(m)}).bind(t);for(let m=0,h=e.length,y,B,b,S;m<h;m++){if(b=m===h-1,B=t.getRangeFormatElement(e[m],u),S=t.isList(B),!y&&S)y=B,s={r:y,f:[t.getParentElement(e[m],"LI")]},m===0&&(r=!0);else if(y&&S)if(y!==B){const x=this.detachRangeFormatElement(s.f[0].parentNode,s.f,null,i,!0);B=e[m].parentNode,r&&(d=x.sc,r=!1),b&&(f=x.ec),S?(y=B,s={r:y,f:[t.getParentElement(e[m],"LI")]},b&&(c=!0)):y=null}else s.f.push(t.getParentElement(e[m],"LI")),b&&(c=!0);if(b&&t.isList(y)){const x=this.detachRangeFormatElement(s.f[0].parentNode,s.f,null,i,!0);(c||h===1)&&(f=x.ec),r&&(d=x.sc||f)}}return{sc:d,ec:f}},nodeChange:function(e,i,s,r){this._resetRangeToTextNode();let c=this.getRange_addLine(this.getRange(),null);i=i&&i.length>0?i:!1,s=s&&s.length>0?s:!1;const d=!e,f=d&&!s&&!i;let u=c.startContainer,m=c.startOffset,h=c.endContainer,y=c.endOffset;if(f&&c.collapsed&&t.isFormatElement(u.parentNode)||u===h&&u.nodeType===1&&t.isNonEditable(u)){const Z=u.parentNode;if(!t.isListCell(Z)||!t.getValues(Z.style).some((function(W){return this._listKebab.indexOf(W)>-1}).bind(this)))return}if(c.collapsed&&!f&&u.nodeType===1&&!t.isBreak(u)&&!t.isComponent(u)){let Z=null;const W=u.childNodes[m];W&&(W.nextSibling?Z=t.isBreak(W)?W:W.nextSibling:Z=null);const $=t.createTextNode(t.zeroWidthSpace);u.insertBefore($,Z),this.setRange($,1,$,1),c=this.getRange(),u=c.startContainer,m=c.startOffset,h=c.endContainer,y=c.endOffset}t.isFormatElement(u)&&(u=u.childNodes[m]||u.firstChild,m=0),t.isFormatElement(h)&&(h=h.childNodes[y]||h.lastChild,y=h.textContent.length),d&&(e=t.createElement("DIV"));const B=E.RegExp,b=e.nodeName;if(!f&&u===h&&!s&&e){let Z=u,W=0;const $=[],q=e.style;for(let X=0,Q=q.length;X<Q;X++)$.push(q[X]);const Y=e.classList;for(let X=0,Q=Y.length;X<Q;X++)$.push("."+Y[X]);if($.length>0){for(;!t.isFormatElement(Z)&&!t.isWysiwygDiv(Z);){for(let X=0;X<$.length;X++)if(Z.nodeType===1){const Q=$[X],ne=/^\./.test(Q)?new B("\\s*"+Q.replace(/^\./,"")+"(\\s+|$)","ig"):!1,fe=d?!!Z.style[Q]:!!Z.style[Q]&&!!e.style[Q]&&Z.style[Q]===e.style[Q],J=ne===!1?!1:d?!!Z.className.match(ne):!!Z.className.match(ne)&&!!e.className.match(ne);(fe||J)&&W++}Z=Z.parentNode}if(W>=$.length)return}}let S={},x={},D,z="",O="",H="";if(i){for(let Z=0,W=i.length,$;Z<W;Z++)$=i[Z],/^\./.test($)?O+=(O?"|":"\\s*(?:")+$.replace(/^\./,""):z+=(z?"|":"(?:;|^|\\s)(?:")+$;z&&(z+=")\\s*:[^;]*\\s*(?:;|$)",z=new B(z,"ig")),O&&(O+=")(?=\\s+|$)",O=new B(O,"ig"))}if(s){H="^(?:"+s[0];for(let Z=1;Z<s.length;Z++)H+="|"+s[Z];H+=")$",H=new B(H,"i")}const M=E.Boolean,T={v:!1},k=function(Z){const W=Z.cloneNode(!1);if(W.nodeType===3||t.isBreak(W))return W;if(f)return null;const $=!H&&d||H&&H.test(W.nodeName);if($&&!r)return T.v=!0,null;const q=W.style.cssText;let Y="";z&&q.length>0&&(Y=q.replace(z,"").trim(),Y!==q&&(T.v=!0));const X=W.className;let Q="";return O&&X.length>0&&(Q=X.replace(O,"").trim(),Q!==X&&(T.v=!0)),d&&(O||!X)&&(z||!q)&&!Y&&!Q&&$?(T.v=!0,null):Y||Q||W.nodeName!==b||M(z)!==M(q)||M(O)!==M(X)?(z&&q.length>0&&(W.style.cssText=Y),W.style.cssText||W.removeAttribute("style"),O&&X.length>0&&(W.className=Q.trim()),W.className.trim()||W.removeAttribute("class"),!W.style.cssText&&!W.className&&(W.nodeName===b||$)?(T.v=!0,null):W):(T.v=!0,null)},_=this.getSelectedElements(null);c=this.getRange(),u=c.startContainer,m=c.startOffset,h=c.endContainer,y=c.endOffset,t.getFormatElement(u,null)||(u=t.getChildElement(_[0],function(Z){return Z.nodeType===3},!1),m=0),t.getFormatElement(h,null)||(h=t.getChildElement(_[_.length-1],function(Z){return Z.nodeType===3},!1),y=h.textContent.length);const A=t.getFormatElement(u,null)===t.getFormatElement(h,null),v=_.length-(A?0:1);D=e.cloneNode(!1);const N=f||d&&function(Z){for(let W=0,$=Z.length;W<$;W++)if(t._isMaintainedNode(Z[W])||t._isSizeNode(Z[W]))return!0;return!1}(s),P=d||t._isSizeNode(D),V=this._util_getMaintainedNode.bind(t,N,P),U=this._util_isMaintainedNode.bind(t,N,P);if(A){this._resetCommonListCell(_[0],i)&&(c=this.setRange(u,m,h,y));const Z=this._nodeChange_oneLine(_[0],D,k,u,m,h,y,f,d,c.collapsed,T,V,U);S.container=Z.startContainer,S.offset=Z.startOffset,x.container=Z.endContainer,x.offset=Z.endOffset,S.container===x.container&&t.onlyZeroWidthSpace(S.container)&&(S.offset=x.offset=1),this._setCommonListStyle(Z.ancestor,null)}else{let Z=!1;v>0&&this._resetCommonListCell(_[v],i)&&(Z=!0),this._resetCommonListCell(_[0],i)&&(Z=!0),Z&&this.setRange(u,m,h,y),v>0&&(D=e.cloneNode(!1),x=this._nodeChange_endLine(_[v],D,k,h,y,f,d,T,V,U));for(let W=v-1,$;W>0;W--)this._resetCommonListCell(_[W],i),D=e.cloneNode(!1),$=this._nodeChange_middleLine(_[W],D,k,f,d,T,x.container),$.endContainer&&$.ancestor.contains($.endContainer)&&(x.ancestor=null,x.container=$.endContainer),this._setCommonListStyle($.ancestor,null);D=e.cloneNode(!1),S=this._nodeChange_startLine(_[0],D,k,u,m,f,d,T,V,U,x.container),S.endContainer&&(x.ancestor=null,x.container=S.endContainer),v<=0?x=S:x.container||(x.ancestor=null,x.container=S.container,x.offset=S.container.textContent.length),this._setCommonListStyle(S.ancestor,null),this._setCommonListStyle(x.ancestor||t.getFormatElement(x.container),null)}this.controllersOff(),this.setRange(S.container,S.offset,x.container,x.offset),this.history.push(!1)},_resetCommonListCell:function(e,i){if(!t.isListCell(e))return;i||(i=this._listKebab);const s=t.getArrayItem(e.childNodes,function(B){return!t.isBreak(B)},!0),r=e.style,c=[],d=[],f=t.getValues(r);for(let B=0,b=this._listKebab.length;B<b;B++)f.indexOf(this._listKebab[B])>-1&&i.indexOf(this._listKebab[B])>-1&&(c.push(this._listCamel[B]),d.push(this._listKebab[B]));if(!c.length)return;const u=t.createElement("SPAN");for(let B=0,b=c.length;B<b;B++)u.style[c[B]]=r[d[B]],r.removeProperty(d[B]);let m=u.cloneNode(!1),h=null,y=!1;for(let B=0,b=s.length,S,x;B<b;B++)S=s[B],!o._textTagsMap[S.nodeName.toLowerCase()]&&(x=t.getValues(S.style),x.length===0||c.some(function(D){return x.indexOf(D)===-1})&&x.some(function(D){})?(h=S.nextSibling,m.appendChild(S)):m.childNodes.length>0&&(e.insertBefore(m,h),m=u.cloneNode(!1),h=null,y=!0));return m.childNodes.length>0&&(e.insertBefore(m,h),y=!0),r.length||e.removeAttribute("style"),y},_setCommonListStyle:function(e,i){if(!t.isListCell(e))return;const s=t.getArrayItem((i||e).childNodes,function(m){return!t.isBreak(m)},!0);if(i=s[0],!i||s.length>1||i.nodeType!==1)return;const r=i.style,c=e.style,d=i.nodeName.toLowerCase();let f=!1;o._textTagsMap[d]===o._defaultCommand.bold.toLowerCase()&&(c.fontWeight="bold"),o._textTagsMap[d]===o._defaultCommand.italic.toLowerCase()&&(c.fontStyle="italic");const u=t.getValues(r);if(u.length>0)for(let m=0,h=this._listCamel.length;m<h;m++)u.indexOf(this._listKebab[m])>-1&&(c[this._listCamel[m]]=r[this._listCamel[m]],r.removeProperty(this._listKebab[m]),f=!0);if(this._setCommonListStyle(e,i),!!f&&!r.length){const m=i.childNodes,h=i.parentNode,y=i.nextSibling;for(;m.length>0;)h.insertBefore(m[0],y);t.removeItem(i)}},_stripRemoveNode:function(e){const i=e.parentNode;if(!e||e.nodeType===3||!i)return;const s=e.childNodes;for(;s[0];)i.insertBefore(s[0],e);i.removeChild(e)},_util_getMaintainedNode:function(e,i,s){return!s||e?null:this.getParentElement(s,this._isMaintainedNode.bind(this))||(i?null:this.getParentElement(s,this._isSizeNode.bind(this)))},_util_isMaintainedNode:function(e,i,s){if(!s||e||s.nodeType!==1)return!1;const r=this._isMaintainedNode(s);return this.getParentElement(s,this._isMaintainedNode.bind(this))?r:r||(i?!1:this._isSizeNode(s))},_nodeChange_oneLine:function(e,i,s,r,c,d,f,u,m,h,y,B,b){let S=r.parentNode;for(;!S.nextSibling&&!S.previousSibling&&!t.isFormatElement(S.parentNode)&&!t.isWysiwygDiv(S.parentNode)&&S.nodeName!==i.nodeName;)S=S.parentNode;if(!m&&S===d.parentNode&&S.nodeName===i.nodeName&&t.onlyZeroWidthSpace(r.textContent.slice(0,c))&&t.onlyZeroWidthSpace(d.textContent.slice(f))){const J=S.childNodes;let le=!0;for(let te=0,ue=J.length,oe,de,ae,ee;te<ue;te++){if(oe=J[te],ee=!t.onlyZeroWidthSpace(oe),oe===r){de=!0;continue}if(oe===d){ae=!0;continue}if(!de&&ee||de&&ae&&ee){le=!1;break}}if(le)return t.copyTagAttributes(S,i),{ancestor:e,startContainer:r,startOffset:c,endContainer:d,endOffset:f}}y.v=!1;const x=e,D=[i],z=e.cloneNode(!1),O=r===d;let H=r,M=c,T=d,k=f,_=!1,A=!1,v,N,P,V,U;const Z=E.RegExp;function W(J){const le=new Z("(?:;|^|\\s)(?:"+V+"null)\\s*:[^;]*\\s*(?:;|$)","ig");let te="";return le&&J.style.cssText.length>0&&(te=le.test(J.style.cssText)),!te}if(function J(le,te){const ue=le.childNodes;for(let oe=0,de=ue.length,ae;oe<de;oe++){let ee=ue[oe];if(!ee)continue;let _e=te,ge;if(!_&&ee===H){let ce=z;U=B(ee);const ie=t.createTextNode(H.nodeType===1?"":H.substringData(0,M)),re=t.createTextNode(H.nodeType===1?"":H.substringData(M,O&&k>=M?k-M:H.data.length-M));if(U){const ye=B(te);if(ye&&ye.parentNode!==ce){let he=ye,Ce=null;for(;he.parentNode!==ce;){for(te=Ce=he.parentNode.cloneNode(!1);he.childNodes[0];)Ce.appendChild(he.childNodes[0]);he.appendChild(Ce),he=he.parentNode}he.parentNode.appendChild(ye)}U=U.cloneNode(!1)}t.onlyZeroWidthSpace(ie)||te.appendChild(ie);const se=B(te);for(se&&(U=se),U&&(ce=U),N=ee,v=[],V="";N!==ce&&N!==x&&N!==null;)ae=b(N)?null:s(N),ae&&N.nodeType===1&&W(N)&&(v.push(ae),V+=N.style.cssText.substr(0,N.style.cssText.indexOf(":"))+"|"),N=N.parentNode;const pe=v.pop()||re;for(P=N=pe;v.length>0;)N=v.pop(),P.appendChild(N),P=N;if(i.appendChild(pe),ce.appendChild(i),U&&!B(T)&&(i=i.cloneNode(!1),z.appendChild(i),D.push(i)),H=re,M=0,_=!0,N!==re&&N.appendChild(H),!O)continue}if(!A&&ee===T){U=B(ee);const ce=t.createTextNode(T.nodeType===1?"":T.substringData(k,T.length-k)),ie=t.createTextNode(O||T.nodeType===1?"":T.substringData(0,k));if(U?U=U.cloneNode(!1):b(i.parentNode)&&!U&&(i=i.cloneNode(!1),z.appendChild(i),D.push(i)),!t.onlyZeroWidthSpace(ce)){N=ee,V="",v=[];const se=[];for(;N!==z&&N!==x&&N!==null;)N.nodeType===1&&W(N)&&(b(N)?se.push(N.cloneNode(!1)):v.push(N.cloneNode(!1)),V+=N.style.cssText.substr(0,N.style.cssText.indexOf(":"))+"|"),N=N.parentNode;for(v=v.concat(se),ge=P=N=v.pop()||ce;v.length>0;)N=v.pop(),P.appendChild(N),P=N;z.appendChild(ge),N.textContent=ce.data}if(U&&ge){const se=B(ge);se&&(U=se)}for(N=ee,v=[],V="";N!==z&&N!==x&&N!==null;)ae=b(N)?null:s(N),ae&&N.nodeType===1&&W(N)&&(v.push(ae),V+=N.style.cssText.substr(0,N.style.cssText.indexOf(":"))+"|"),N=N.parentNode;const re=v.pop()||ie;for(P=N=re;v.length>0;)N=v.pop(),P.appendChild(N),P=N;U?(i=i.cloneNode(!1),i.appendChild(re),U.insertBefore(i,U.firstChild),z.appendChild(U),D.push(i),U=null):i.appendChild(re),T=ie,k=ie.data.length,A=!0,!u&&h&&(i=ie,ie.textContent=t.zeroWidthSpace),N!==ie&&N.appendChild(T);continue}if(_){if(ee.nodeType===1&&!t.isBreak(ee)){t._isIgnoreNodeChange(ee)?(z.appendChild(ee.cloneNode(!0)),h||(i=i.cloneNode(!1),z.appendChild(i),D.push(i))):J(ee,ee);continue}N=ee,v=[],V="";const ce=[];for(;N.parentNode!==null&&N!==x&&N!==i;)ae=A?N.cloneNode(!1):s(N),N.nodeType===1&&!t.isBreak(ee)&&ae&&W(N)&&(b(N)?U||ce.push(ae):v.push(ae),V+=N.style.cssText.substr(0,N.style.cssText.indexOf(":"))+"|"),N=N.parentNode;v=v.concat(ce);const ie=v.pop()||ee;for(P=N=ie;v.length>0;)N=v.pop(),P.appendChild(N),P=N;if(b(i.parentNode)&&!b(ie)&&!t.onlyZeroWidthSpace(i)&&(i=i.cloneNode(!1),z.appendChild(i),D.push(i)),!A&&!U&&b(ie)){i=i.cloneNode(!1);const re=ie.childNodes;for(let se=0,pe=re.length;se<pe;se++)i.appendChild(re[se]);ie.appendChild(i),z.appendChild(ie),D.push(i),i.children.length>0?te=N:te=i}else ie===ee?A?te=z:te=i:A?(z.appendChild(ie),te=N):(i.appendChild(ie),te=N);if(U&&ee.nodeType===3)if(B(ee)){const re=t.getParentElement(te,(function(se){return this._isMaintainedNode(se.parentNode)||se.parentNode===z}).bind(t));U.appendChild(re),i=re.cloneNode(!1),D.push(i),z.appendChild(i)}else U=null}ge=ee.cloneNode(!1),te.appendChild(ge),ee.nodeType===1&&!t.isBreak(ee)&&(_e=ge),J(ee,_e)}}(e,z),m&&!u&&!y.v)return{ancestor:e,startContainer:r,startOffset:c,endContainer:d,endOffset:f};if(u=u&&m,u)for(let J=0;J<D.length;J++){let le=D[J],te,ue,oe;if(h)te=t.createTextNode(t.zeroWidthSpace),z.replaceChild(te,le);else{const de=le.childNodes;for(ue=de[0];de[0];)oe=de[0],z.insertBefore(oe,le);t.removeItem(le)}J===0&&(h?H=T=te:(H=ue,T=oe))}else{if(m)for(let J=0;J<D.length;J++)this._stripRemoveNode(D[J]);h&&(H=T=i)}t.removeEmptyNode(z,i,!1),h&&(M=H.textContent.length,k=T.textContent.length);const $=u||T.textContent.length===0;!t.isBreak(T)&&T.textContent.length===0&&(t.removeItem(T),T=H),k=$?T.textContent.length:k;const q={s:0,e:0},Y=t.getNodePath(H,z,q),X=!T.parentNode;X&&(T=H);const Q={s:0,e:0},ne=t.getNodePath(T,z,!X&&!$?Q:null);M+=q.s,k=h?M:X?H.textContent.length:$?k+q.s:k+Q.s;const fe=t.mergeSameTags(z,[Y,ne],!0);return e.parentNode.replaceChild(z,e),H=t.getNodeFromPath(Y,z),T=t.getNodeFromPath(ne,z),{ancestor:z,startContainer:H,startOffset:M+fe[0],endContainer:T,endOffset:k+fe[1]}},_nodeChange_startLine:function(e,i,s,r,c,d,f,u,m,h,y){let B=r.parentNode;for(;!B.nextSibling&&!B.previousSibling&&!t.isFormatElement(B.parentNode)&&!t.isWysiwygDiv(B.parentNode)&&B.nodeName!==i.nodeName;)B=B.parentNode;if(!f&&B.nodeName===i.nodeName&&!t.isFormatElement(B)&&!B.nextSibling&&t.onlyZeroWidthSpace(r.textContent.slice(0,c))){let _=!0,A=r.previousSibling;for(;A;){if(!t.onlyZeroWidthSpace(A)){_=!1;break}A=A.previousSibling}if(_)return t.copyTagAttributes(B,i),{ancestor:e,container:r,offset:c}}u.v=!1;const b=e,S=[i],x=e.cloneNode(!1);let D=r,z=c,O=!1,H,M,T,k;if(function _(A,v){const N=A.childNodes;for(let P=0,V=N.length,U,Z;P<V;P++){const W=N[P];if(!W)continue;let $=v;if(O&&!t.isBreak(W)){if(W.nodeType===1){if(t._isIgnoreNodeChange(W)){if(i=i.cloneNode(!1),Z=W.cloneNode(!0),x.appendChild(Z),x.appendChild(i),S.push(i),y&&W.contains(y)){const Q=t.getNodePath(y,W);y=t.getNodeFromPath(Q,Z)}}else _(W,W);continue}M=W,H=[];const q=[];for(;M.parentNode!==null&&M!==b&&M!==i;)U=s(M),M.nodeType===1&&U&&(h(M)?k||q.push(U):H.push(U)),M=M.parentNode;H=H.concat(q);const Y=H.length>0,X=H.pop()||W;for(T=M=X;H.length>0;)M=H.pop(),T.appendChild(M),T=M;if(h(i.parentNode)&&!h(X)&&(i=i.cloneNode(!1),x.appendChild(i),S.push(i)),!k&&h(X)){i=i.cloneNode(!1);const Q=X.childNodes;for(let ne=0,fe=Q.length;ne<fe;ne++)i.appendChild(Q[ne]);X.appendChild(i),x.appendChild(X),v=h(M)?i:M,S.push(i)}else Y?(i.appendChild(X),v=M):v=i;if(k&&W.nodeType===3)if(m(W)){const Q=t.getParentElement(v,(function(ne){return this._isMaintainedNode(ne.parentNode)||ne.parentNode===x}).bind(t));k.appendChild(Q),i=Q.cloneNode(!1),S.push(i),x.appendChild(i)}else k=null}if(!O&&W===D){let q=x;k=m(W);const Y=t.createTextNode(D.nodeType===1?"":D.substringData(0,z)),X=t.createTextNode(D.nodeType===1?"":D.substringData(z,D.length-z));if(k){const fe=m(v);if(fe&&fe.parentNode!==q){let J=fe,le=null;for(;J.parentNode!==q;){for(v=le=J.parentNode.cloneNode(!1);J.childNodes[0];)le.appendChild(J.childNodes[0]);J.appendChild(le),J=J.parentNode}J.parentNode.appendChild(fe)}k=k.cloneNode(!1)}t.onlyZeroWidthSpace(Y)||v.appendChild(Y);const Q=m(v);for(Q&&(k=Q),k&&(q=k),M=v,H=[];M!==q&&M!==null;)U=s(M),M.nodeType===1&&U&&H.push(U),M=M.parentNode;const ne=H.pop()||v;for(T=M=ne;H.length>0;)M=H.pop(),T.appendChild(M),T=M;ne!==v?(i.appendChild(ne),v=M):v=i,t.isBreak(W)&&i.appendChild(W.cloneNode(!1)),q.appendChild(i),D=X,z=0,O=!0,v.appendChild(D);continue}U=O?s(W):W.cloneNode(!1),U&&(v.appendChild(U),W.nodeType===1&&!t.isBreak(W)&&($=U)),_(W,$)}}(e,x),f&&!d&&!u.v)return{ancestor:e,container:r,offset:c,endContainer:y};if(d=d&&f,d)for(let _=0;_<S.length;_++){let A=S[_];const v=A.childNodes,N=v[0];for(;v[0];)x.insertBefore(v[0],A);t.removeItem(A),_===0&&(D=N)}else if(f){i=i.firstChild;for(let _=0;_<S.length;_++)this._stripRemoveNode(S[_])}if(!d&&x.childNodes.length===0)e.childNodes?D=e.childNodes[0]:(D=t.createTextNode(t.zeroWidthSpace),e.appendChild(D));else{t.removeEmptyNode(x,i,!1),t.onlyZeroWidthSpace(x.textContent)&&(D=x.firstChild,z=0);const _={s:0,e:0},A=t.getNodePath(D,x,_);z+=_.s;const v=t.mergeSameTags(x,[A],!0);e.parentNode.replaceChild(x,e),D=t.getNodeFromPath(A,x),z+=v[0]}return{ancestor:x,container:D,offset:z,endContainer:y}},_nodeChange_middleLine:function(e,i,s,r,c,d,f){if(!c){let y=null;f&&e.contains(f)&&(y=t.getNodePath(f,e));const B=e.cloneNode(!0),b=i.nodeName,S=i.style.cssText,x=i.className;let D=B.childNodes,z=0,O=D.length;for(let H;z<O&&(H=D[z],H.nodeType!==3);z++)if(H.nodeName===b)H.style.cssText+=S,t.addClass(H,x);else{if(!t.isBreak(H)&&t._isIgnoreNodeChange(H))continue;if(O===1){D=H.childNodes,O=D.length,z=-1;continue}else break}if(O>0&&z===O)return e.innerHTML=B.innerHTML,{ancestor:e,endContainer:y?t.getNodeFromPath(y,e):null}}d.v=!1;const u=e.cloneNode(!1),m=[i];let h=!0;if(function y(B,b){const S=B.childNodes;for(let x=0,D=S.length,z,O;x<D;x++){let H=S[x];if(!H)continue;let M=b;if(!t.isBreak(H)&&t._isIgnoreNodeChange(H)){if(i.childNodes.length>0&&(u.appendChild(i),i=i.cloneNode(!1)),O=H.cloneNode(!0),u.appendChild(O),u.appendChild(i),m.push(i),b=i,f&&H.contains(f)){const T=t.getNodePath(f,H);f=t.getNodeFromPath(T,O)}continue}else z=s(H),z&&(h=!1,b.appendChild(z),H.nodeType===1&&(M=z));t.isBreak(H)||y(H,M)}}(e,i),h||c&&!r&&!d.v)return{ancestor:e,endContainer:f};if(u.appendChild(i),r&&c)for(let y=0;y<m.length;y++){let B=m[y];const b=B.childNodes;for(;b[0];)u.insertBefore(b[0],B);t.removeItem(B)}else if(c){i=i.firstChild;for(let y=0;y<m.length;y++)this._stripRemoveNode(m[y])}return t.removeEmptyNode(u,i,!1),t.mergeSameTags(u,null,!0),e.parentNode.replaceChild(u,e),{ancestor:u,endContainer:f}},_nodeChange_endLine:function(e,i,s,r,c,d,f,u,m,h){let y=r.parentNode;for(;!y.nextSibling&&!y.previousSibling&&!t.isFormatElement(y.parentNode)&&!t.isWysiwygDiv(y.parentNode)&&y.nodeName!==i.nodeName;)y=y.parentNode;if(!f&&y.nodeName===i.nodeName&&!t.isFormatElement(y)&&!y.previousSibling&&t.onlyZeroWidthSpace(r.textContent.slice(c))){let k=!0,_=r.nextSibling;for(;_;){if(!t.onlyZeroWidthSpace(_)){k=!1;break}_=_.nextSibling}if(k)return t.copyTagAttributes(y,i),{ancestor:e,container:r,offset:c}}u.v=!1;const B=e,b=[i],S=e.cloneNode(!1);let x=r,D=c,z=!1,O,H,M,T;if(function k(_,A){const v=_.childNodes;for(let N=v.length-1,P;0<=N;N--){const V=v[N];if(!V)continue;let U=A;if(z&&!t.isBreak(V)){if(V.nodeType===1){if(t._isIgnoreNodeChange(V)){i=i.cloneNode(!1);const q=V.cloneNode(!0);S.insertBefore(q,A),S.insertBefore(i,q),b.push(i)}else k(V,V);continue}H=V,O=[];const Z=[];for(;H.parentNode!==null&&H!==B&&H!==i;)P=s(H),P&&H.nodeType===1&&(h(H)?T||Z.push(P):O.push(P)),H=H.parentNode;O=O.concat(Z);const W=O.length>0,$=O.pop()||V;for(M=H=$;O.length>0;)H=O.pop(),M.appendChild(H),M=H;if(h(i.parentNode)&&!h($)&&(i=i.cloneNode(!1),S.insertBefore(i,S.firstChild),b.push(i)),!T&&h($)){i=i.cloneNode(!1);const q=$.childNodes;for(let Y=0,X=q.length;Y<X;Y++)i.appendChild(q[Y]);$.appendChild(i),S.insertBefore($,S.firstChild),b.push(i),i.children.length>0?A=H:A=i}else W?(i.insertBefore($,i.firstChild),A=H):A=i;if(T&&V.nodeType===3)if(m(V)){const q=t.getParentElement(A,(function(Y){return this._isMaintainedNode(Y.parentNode)||Y.parentNode===S}).bind(t));T.appendChild(q),i=q.cloneNode(!1),b.push(i),S.insertBefore(i,S.firstChild)}else T=null}if(!z&&V===x){T=m(V);const Z=t.createTextNode(x.nodeType===1?"":x.substringData(D,x.length-D)),W=t.createTextNode(x.nodeType===1?"":x.substringData(0,D));if(T){T=T.cloneNode(!1);const q=m(A);if(q&&q.parentNode!==S){let Y=q,X=null;for(;Y.parentNode!==S;){for(A=X=Y.parentNode.cloneNode(!1);Y.childNodes[0];)X.appendChild(Y.childNodes[0]);Y.appendChild(X),Y=Y.parentNode}Y.parentNode.insertBefore(q,Y.parentNode.firstChild)}T=T.cloneNode(!1)}else h(i.parentNode)&&!T&&(i=i.cloneNode(!1),S.appendChild(i),b.push(i));for(t.onlyZeroWidthSpace(Z)||A.insertBefore(Z,A.firstChild),H=A,O=[];H!==S&&H!==null;)P=h(H)?null:s(H),P&&H.nodeType===1&&O.push(P),H=H.parentNode;const $=O.pop()||A;for(M=H=$;O.length>0;)H=O.pop(),M.appendChild(H),M=H;$!==A?(i.insertBefore($,i.firstChild),A=H):A=i,t.isBreak(V)&&i.appendChild(V.cloneNode(!1)),T?(T.insertBefore(i,T.firstChild),S.insertBefore(T,S.firstChild),T=null):S.insertBefore(i,S.firstChild),x=W,D=W.data.length,z=!0,A.insertBefore(x,A.firstChild);continue}P=z?s(V):V.cloneNode(!1),P&&(A.insertBefore(P,A.firstChild),V.nodeType===1&&!t.isBreak(V)&&(U=P)),k(V,U)}}(e,S),f&&!d&&!u.v)return{ancestor:e,container:r,offset:c};if(d=d&&f,d)for(let k=0;k<b.length;k++){let _=b[k];const A=_.childNodes;let v=null;for(;A[0];)v=A[0],S.insertBefore(v,_);t.removeItem(_),k===b.length-1&&(x=v,D=v.textContent.length)}else if(f){i=i.firstChild;for(let k=0;k<b.length;k++)this._stripRemoveNode(b[k])}if(!d&&S.childNodes.length===0)e.childNodes?x=e.childNodes[0]:(x=t.createTextNode(t.zeroWidthSpace),e.appendChild(x));else{if(!f&&i.textContent.length===0)return t.removeEmptyNode(S,null,!1),{ancestor:null,container:null,offset:0};t.removeEmptyNode(S,i,!1),t.onlyZeroWidthSpace(S.textContent)?(x=S.firstChild,D=x.textContent.length):t.onlyZeroWidthSpace(x)&&(x=i,D=1);const k={s:0,e:0},_=t.getNodePath(x,S,k);D+=k.s;const A=t.mergeSameTags(S,[_],!0);e.parentNode.replaceChild(S,e),x=t.getNodeFromPath(_,S),D+=A[0]}return{ancestor:S,container:x,offset:x.nodeType===1&&D===1?x.childNodes.length:D}},actionCall:function(e,i,s){if(i){if(/more/i.test(i)){if(s!==this._moreLayerActiveButton){const r=n.element.toolbar.querySelector("."+e);r&&(this._moreLayerActiveButton&&this.moreLayerOff(),this._moreLayerActiveButton=s,r.style.display="block",p._showToolbarBalloon(),p._showToolbarInline()),t.addClass(s,"on")}else n.element.toolbar.querySelector("."+this._moreLayerActiveButton.getAttribute("data-command"))&&(this.moreLayerOff(),p._showToolbarBalloon(),p._showToolbarInline());return}if(/container/.test(i)&&(this._menuTray[e]===null||s!==this.containerActiveButton)){this.callPlugin(e,this.containerOn.bind(this,s),s);return}if(this.isReadOnly&&t.arrayIncludes(this.resizingDisabledButtons,s))return;if(/submenu/.test(i)&&(this._menuTray[e]===null||s!==this.submenuActiveButton)){this.callPlugin(e,this.submenuOn.bind(this,s),s);return}else if(/dialog/.test(i)){this.callPlugin(e,this.plugins[e].open.bind(this),s);return}else/command/.test(i)?this.callPlugin(e,this.plugins[e].action.bind(this),s):/fileBrowser/.test(i)&&this.callPlugin(e,this.plugins[e].open.bind(this,null),s)}else e&&this.commandHandler(s,e);/submenu/.test(i)?this.submenuOff():/command/.test(i)||(this.submenuOff(),this.containerOff())},commandHandler:function(e,i){if(!(a.isReadOnly&&!/copy|cut|selectAll|codeView|fullScreen|print|preview|showBlocks/.test(i)))switch(i){case"copy":case"cut":this.execCommand(i);break;case"paste":break;case"selectAll":this.containerOff();const s=n.element.wysiwyg;let r=t.getChildElement(s.firstChild,function(m){return m.childNodes.length===0||m.nodeType===3},!1)||s.firstChild,c=t.getChildElement(s.lastChild,function(m){return m.childNodes.length===0||m.nodeType===3},!0)||s.lastChild;if(!r||!c)return;if(t.isMedia(r)){const m=this.getFileComponent(r),h=t.createElement("BR"),y=t.createElement(o.defaultTag);y.appendChild(h),r=m?m.component:r,r.parentNode.insertBefore(y,r),r=h}if(t.isMedia(c)){const m=t.createElement("BR"),h=t.createElement(o.defaultTag);h.appendChild(m),s.appendChild(h),c=m}p._showToolbarBalloon(this.setRange(r,0,c,c.textContent.length));break;case"codeView":this.toggleCodeView();break;case"fullScreen":this.toggleFullScreen(e);break;case"indent":case"outdent":this.indent(i);break;case"undo":this.history.undo();break;case"redo":this.history.redo();break;case"removeFormat":this.removeFormat(),this.focus();break;case"print":this.print();break;case"preview":this.preview();break;case"showBlocks":this.toggleDisplayBlocks();break;case"dir":this.setDir(o.rtl?"ltr":"rtl");break;case"dir_ltr":this.setDir("ltr");break;case"dir_rtl":this.setDir("rtl");break;case"save":if(typeof o.callBackSave=="function")o.callBackSave(this.getContents(!1),this._variable.isChanged);else if(this._variable.isChanged&&typeof F.save=="function")F.save();else throw Error("[SUNEDITOR.core.commandHandler.fail] Please register call back function in creation option. (callBackSave : Function)");this._variable.isChanged=!1,n.tool.save&&n.tool.save.setAttribute("disabled",!0);break;default:i=o._defaultCommand[i.toLowerCase()]||i,this.commandMap[i]||(this.commandMap[i]=e);const d=this._variable.currentNodesMap,f=d.indexOf(i)>-1?null:t.createElement(i);let u=i;/^SUB$/i.test(i)&&d.indexOf("SUP")>-1?u="SUP":/^SUP$/i.test(i)&&d.indexOf("SUB")>-1&&(u="SUB"),this.nodeChange(f,this._commandMapStyles[i]||null,[u],!1),this.focus()}},removeFormat:function(){this.nodeChange(null,null,null,null)},indent:function(e){const i=this.getRange(),s=this.getSelectedElements(null),r=[],c=e!=="indent",d=o.rtl?"marginRight":"marginLeft";let f=i.startContainer,u=i.endContainer,m=i.startOffset,h=i.endOffset;for(let y=0,B=s.length,b,S;y<B;y++)b=s[y],!t.isListCell(b)||!this.plugins.list?(S=/\d+/.test(b.style[d])?t.getNumber(b.style[d],0):0,c?S-=25:S+=25,t.setStyle(b,d,S<=0?"":S+"px")):(c||b.previousElementSibling)&&r.push(b);r.length>0&&this.plugins.list.editInsideList.call(this,c,r),this.effectNode=null,this.setRange(f,m,u,h),this.history.push(!1)},toggleDisplayBlocks:function(){const e=n.element.wysiwyg;t.toggleClass(e,"se-show-block"),t.hasClass(e,"se-show-block")?t.addClass(this._styleCommandMap.showBlocks,"active"):t.removeClass(this._styleCommandMap.showBlocks,"active"),this._resourcesStateChange()},toggleCodeView:function(){const e=this._variable.isCodeView;this.controllersOff(),t.setDisabledButtons(!e,this.codeViewDisabledButtons),e?(t.isNonEditable(n.element.wysiwygFrame)||this._setCodeDataToEditor(),n.element.wysiwygFrame.scrollTop=0,n.element.code.style.display="none",n.element.wysiwygFrame.style.display="block",this._variable._codeOriginCssText=this._variable._codeOriginCssText.replace(/(\s?display(\s+)?:(\s+)?)[a-zA-Z]+(?=;)/,"display: none"),this._variable._wysiwygOriginCssText=this._variable._wysiwygOriginCssText.replace(/(\s?display(\s+)?:(\s+)?)[a-zA-Z]+(?=;)/,"display: block"),o.height==="auto"&&!o.codeMirrorEditor&&(n.element.code.style.height="0px"),this._variable.isCodeView=!1,this._variable.isFullScreen||(this._notHideToolbar=!1,/balloon|balloon-always/i.test(o.mode)&&(n.element._arrow.style.display="",this._isInline=!1,this._isBalloon=!0,p._hideToolbar())),this.nativeFocus(),t.removeClass(this._styleCommandMap.codeView,"active"),t.isNonEditable(n.element.wysiwygFrame)||(this.history.push(!1),this.history._resetCachingButton())):(this._setEditorDataToCodeView(),this._variable._codeOriginCssText=this._variable._codeOriginCssText.replace(/(\s?display(\s+)?:(\s+)?)[a-zA-Z]+(?=;)/,"display: block"),this._variable._wysiwygOriginCssText=this._variable._wysiwygOriginCssText.replace(/(\s?display(\s+)?:(\s+)?)[a-zA-Z]+(?=;)/,"display: none"),this._variable.isFullScreen?n.element.code.style.height="100%":o.height==="auto"&&!o.codeMirrorEditor&&(n.element.code.style.height=n.element.code.scrollHeight>0?n.element.code.scrollHeight+"px":"auto"),o.codeMirrorEditor&&o.codeMirrorEditor.refresh(),this._variable.isCodeView=!0,this._variable.isFullScreen||(this._notHideToolbar=!0,this._isBalloon&&(n.element._arrow.style.display="none",n.element.toolbar.style.left="",this._isInline=!0,this._isBalloon=!1,p._showToolbarInline())),this._variable._range=null,n.element.code.focus(),t.addClass(this._styleCommandMap.codeView,"active")),this._checkPlaceholder(),this.isReadOnly&&t.setDisabledButtons(!0,this.resizingDisabledButtons),typeof F.toggleCodeView=="function"&&F.toggleCodeView(this._variable.isCodeView,this)},_setCodeDataToEditor:function(){const e=this._getCodeView();if(o.fullPage){const i=this._parser.parseFromString(e,"text/html");if(!this.options.__allowedScriptTag){const c=i.head.children;for(let d=0,f=c.length;d<f;d++)/^script$/i.test(c[d].tagName)&&(i.head.removeChild(c[d]),d--,f--)}let s=i.head.innerHTML;(!i.head.querySelector('link[rel="stylesheet"]')||this.options.height==="auto"&&!i.head.querySelector("style"))&&(s+=t._setIframeCssTags(this.options)),this._wd.head.innerHTML=s,this._wd.body.innerHTML=this.convertContentsForEditor(i.body.innerHTML);const r=i.body.attributes;for(let c=0,d=r.length;c<d;c++)r[c].name!=="contenteditable"&&this._wd.body.setAttribute(r[c].name,r[c].value);if(!t.hasClass(this._wd.body,"sun-editor-editable")){const c=o._editableClass.split(" ");for(let d=0;d<c.length;d++)t.addClass(this._wd.body,o._editableClass[d])}}else n.element.wysiwyg.innerHTML=e.length>0?this.convertContentsForEditor(e):"<"+o.defaultTag+"><br></"+o.defaultTag+">"},_setEditorDataToCodeView:function(){const e=this.convertHTMLForCodeView(n.element.wysiwyg,!1);let i="";if(o.fullPage){const s=t.getAttributesToString(this._wd.body,null);i=`<!DOCTYPE html>
<html>
`+this._wd.head.outerHTML.replace(/>(?!\n)/g,`>
`)+"<body "+s+`>
`+e+`</body>
</html>`}else i=e;n.element.code.style.display="block",n.element.wysiwygFrame.style.display="none",this._setCodeView(i)},toggleFullScreen:function(e){const i=n.element.topArea,s=n.element.toolbar,r=n.element.editorArea,c=n.element.wysiwygFrame,d=n.element.code,f=this._variable;this.controllersOff();const u=s.style.display==="none"||this._isInline&&!this._inlineToolbarAttr.isShow;f.isFullScreen?(f.isFullScreen=!1,c.style.cssText=f._wysiwygOriginCssText,d.style.cssText=f._codeOriginCssText,s.style.cssText="",r.style.cssText=f._editorAreaOriginCssText,i.style.cssText=f._originCssText,L.body.style.overflow=f._bodyOverflow,o.height==="auto"&&!o.codeMirrorEditor&&p._codeViewAutoHeight(),o.toolbarContainer&&o.toolbarContainer.appendChild(s),o.stickyToolbar>-1&&t.removeClass(s,"se-toolbar-sticky"),f._fullScreenAttrs.sticky&&!o.toolbarContainer&&(f._fullScreenAttrs.sticky=!1,n.element._stickyDummy.style.display="block",t.addClass(s,"se-toolbar-sticky")),this._isInline=f._fullScreenAttrs.inline,this._isBalloon=f._fullScreenAttrs.balloon,this._isInline&&p._showToolbarInline(),o.toolbarContainer&&t.removeClass(s,"se-toolbar-balloon"),p.onScroll_window(),e&&t.changeElement(e.firstElementChild,R.expansion),n.element.topArea.style.marginTop="",t.removeClass(this._styleCommandMap.fullScreen,"active")):(f.isFullScreen=!0,f._fullScreenAttrs.inline=this._isInline,f._fullScreenAttrs.balloon=this._isBalloon,(this._isInline||this._isBalloon)&&(this._isInline=!1,this._isBalloon=!1),o.toolbarContainer&&n.element.relative.insertBefore(s,r),i.style.position="fixed",i.style.top="0",i.style.left="0",i.style.width="100%",i.style.maxWidth="100%",i.style.height="100%",i.style.zIndex="2147483647",n.element._stickyDummy.style.display!==""&&(f._fullScreenAttrs.sticky=!0,n.element._stickyDummy.style.display="none",t.removeClass(s,"se-toolbar-sticky")),f._bodyOverflow=L.body.style.overflow,L.body.style.overflow="hidden",f._editorAreaOriginCssText=r.style.cssText,f._wysiwygOriginCssText=c.style.cssText,f._codeOriginCssText=d.style.cssText,r.style.cssText=s.style.cssText="",c.style.cssText=(c.style.cssText.match(/\s?display(\s+)?:(\s+)?[a-zA-Z]+;/)||[""])[0]+o._editorStyles.editor,d.style.cssText=(d.style.cssText.match(/\s?display(\s+)?:(\s+)?[a-zA-Z]+;/)||[""])[0],s.style.width=c.style.height=d.style.height="100%",s.style.position="relative",s.style.display="block",f.innerHeight_fullScreen=E.innerHeight-s.offsetHeight,r.style.height=f.innerHeight_fullScreen-o.fullScreenOffset+"px",e&&t.changeElement(e.firstElementChild,R.reduction),o.iframe&&o.height==="auto"&&(r.style.overflow="auto",this._iframeAutoHeight()),n.element.topArea.style.marginTop=o.fullScreenOffset+"px",t.addClass(this._styleCommandMap.fullScreen,"active")),u&&F.toolbar.hide(),typeof F.toggleFullScreen=="function"&&F.toggleFullScreen(this._variable.isFullScreen,this)},print:function(){const e=t.createElement("IFRAME");e.style.display="none",L.body.appendChild(e);const i=o.printTemplate?o.printTemplate.replace(/\{\{\s*contents\s*\}\}/i,this.getContents(!0)):this.getContents(!0),s=t.getIframeDocument(e),r=this._wd;if(o.iframe){const c=o._printClass!==null?'class="'+o._printClass+'"':o.fullPage?t.getAttributesToString(r.body,["contenteditable"]):'class="'+o._editableClass+'"';s.write("<!DOCTYPE html><html><head>"+r.head.innerHTML+"</head><body "+c+">"+i+"</body></html>")}else{const c=L.head.getElementsByTagName("link"),d=L.head.getElementsByTagName("style");let f="";for(let u=0,m=c.length;u<m;u++)f+=c[u].outerHTML;for(let u=0,m=d.length;u<m;u++)f+=d[u].outerHTML;s.write("<!DOCTYPE html><html><head>"+f+'</head><body class="'+(o._printClass!==null?o._printClass:o._editableClass)+'">'+i+"</body></html>")}this.showLoading(),E.setTimeout(function(){try{if(e.focus(),t.isIE_Edge||t.isChromium||L.documentMode||E.StyleMedia)try{e.contentWindow.document.execCommand("print",!1,null)}catch{e.contentWindow.print()}else e.contentWindow.print()}catch(c){throw Error("[SUNEDITOR.core.print.fail] error: "+c)}finally{a.closeLoading(),t.removeItem(e)}},1e3)},preview:function(){a.submenuOff(),a.containerOff(),a.controllersOff();const e=o.previewTemplate?o.previewTemplate.replace(/\{\{\s*contents\s*\}\}/i,this.getContents(!0)):this.getContents(!0),i=E.open("","_blank");i.mimeType="text/html";const s=this._wd;if(o.iframe){const r=o._printClass!==null?'class="'+o._printClass+'"':o.fullPage?t.getAttributesToString(s.body,["contenteditable"]):'class="'+o._editableClass+'"';i.document.write("<!DOCTYPE html><html><head>"+s.head.innerHTML+"<style>body {overflow:auto !important; margin: 10px auto !important; height:auto !important; outline:1px dashed #ccc;}</style></head><body "+r+">"+e+"</body></html>")}else{const r=L.head.getElementsByTagName("link"),c=L.head.getElementsByTagName("style");let d="";for(let f=0,u=r.length;f<u;f++)d+=r[f].outerHTML;for(let f=0,u=c.length;f<u;f++)d+=c[f].outerHTML;i.document.write('<!DOCTYPE html><html><head><meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1"><title>'+C.toolbar.preview+"</title>"+d+'</head><body class="'+(o._printClass!==null?o._printClass:o._editableClass)+'" style="margin:10px auto !important; height:auto !important; outline:1px dashed #ccc;">'+e+"</body></html>")}},setDir:function(e){const i=e==="rtl",s=this._prevRtl!==i;this._prevRtl=o.rtl=i,s&&(this.plugins.align&&this.plugins.align.exchangeDir.call(this),n.tool.indent&&t.changeElement(n.tool.indent.firstElementChild,R.indent),n.tool.outdent&&t.changeElement(n.tool.outdent.firstElementChild,R.outdent));const r=n.element;i?(t.addClass(r.topArea,"se-rtl"),t.addClass(r.wysiwygFrame,"se-rtl")):(t.removeClass(r.topArea,"se-rtl"),t.removeClass(r.wysiwygFrame,"se-rtl"));const c=t.getListChildren(r.wysiwyg,function(f){return t.isFormatElement(f)&&(f.style.marginRight||f.style.marginLeft||f.style.textAlign)});for(let f=0,u=c.length,m,h,y;f<u;f++)m=c[f],y=m.style.marginRight,h=m.style.marginLeft,(y||h)&&(m.style.marginRight=h,m.style.marginLeft=y),y=m.style.textAlign,y==="left"?m.style.textAlign="right":y==="right"&&(m.style.textAlign="left");const d=n.tool;d.dir&&(t.changeTxt(d.dir.querySelector(".se-tooltip-text"),C.toolbar[o.rtl?"dir_ltr":"dir_rtl"]),t.changeElement(d.dir.firstElementChild,R[o.rtl?"dir_ltr":"dir_rtl"])),d.dir_ltr&&(i?t.removeClass(d.dir_ltr,"active"):t.addClass(d.dir_ltr,"active")),d.dir_rtl&&(i?t.addClass(d.dir_rtl,"active"):t.removeClass(d.dir_rtl,"active"))},setContents:function(e){this.removeRange();const i=e==null?"":this.convertContentsForEditor(e,null,null);if(!this._variable.isCodeView)n.element.wysiwyg.innerHTML=i,this._resetComponents(),this.history.push(!1);else{const s=this.convertHTMLForCodeView(i,!1);this._setCodeView(s)}},setIframeContents:function(e){if(!o.iframe)return!1;e.head&&(this._wd.head.innerHTML=this.options.__allowedScriptTag?e.head:e.head.replace(this.__scriptTagRegExp,"")),e.body&&(this._wd.body.innerHTML=this.convertContentsForEditor(e.body)),this._resetComponents()},getContents:function(e){const i=this.cleanHTML(n.element.wysiwyg.innerHTML,null,null),s=t.createElement("DIV");s.innerHTML=i;const r=t.getListChildren(s,function(c){return c.hasAttribute("contenteditable")});for(let c=0,d=r.length;c<d;c++)r[c].removeAttribute("contenteditable");if(o.fullPage&&!e){const c=t.getAttributesToString(this._wd.body,["contenteditable"]);return"<!DOCTYPE html><html>"+this._wd.head.outerHTML+"<body "+c+">"+s.innerHTML+"</body></html>"}else return s.innerHTML},getFullContents:function(e){return'<div class="sun-editor-editable'+(o.rtl?" se-rtl":"")+'">'+this.getContents(e)+"</div>"},_makeLine:function(e,i){const s=o.defaultTag;if(e.nodeType===1){if(this.__disallowedTagNameRegExp.test(e.nodeName))return"";if(/__se__tag/.test(e.className))return e.outerHTML;const r=t.getListChildNodes(e,function(c){return t.isSpanWithoutAttr(c)&&!t.getParentElement(c,t.isNotCheckingNode)})||[];for(let c=r.length-1;c>=0;c--)r[c].outerHTML=r[c].innerHTML;return!i||t.isFormatElement(e)||t.isRangeFormatElement(e)||t.isComponent(e)||t.isFigures(e)||t.isAnchor(e)&&t.isMedia(e.firstElementChild)?t.isSpanWithoutAttr(e)?e.innerHTML:e.outerHTML:"<"+s+">"+(t.isSpanWithoutAttr(e)?e.innerHTML:e.outerHTML)+"</"+s+">"}if(e.nodeType===3){if(!i)return t._HTMLConvertor(e.textContent);const r=e.textContent.split(/\n/g);let c="";for(let d=0,f=r.length,u;d<f;d++)u=r[d].trim(),u.length>0&&(c+="<"+s+">"+t._HTMLConvertor(u)+"</"+s+">");return c}return e.nodeType===8&&this._allowHTMLComments?"<!--"+e.textContent.trim()+"-->":""},_tagConvertor:function(e){if(!this._disallowedTextTagsRegExp)return e;const i=o._textTagsMap;return e.replace(this._disallowedTextTagsRegExp,function(s,r,c,d){return r+(typeof i[c]=="string"?i[c]:c)+(d?" "+d:"")})},_deleteDisallowedTags:function(e){return e=e.replace(this.__disallowedTagsRegExp,"").replace(/<[a-z0-9]+\:[a-z0-9]+[^>^\/]*>[^>]*<\/[a-z0-9]+\:[a-z0-9]+>/gi,""),/\bfont\b/i.test(this.options._editorTagsWhitelist)||(e=e.replace(/(<\/?)font(\s?)/gi,"$1span$2")),e.replace(this.editorTagsWhitelistRegExp,"").replace(this.editorTagsBlacklistRegExp,"")},_convertFontSize:function(e,i){const s=this._w.Math,r=i.match(/(\d+(?:\.\d+)?)(.+)/),c=r?r[1]*1:t.fontValueMap[i],d=r?r[2]:"rem";let f=c;switch(/em/.test(d)?f=s.round(c/.0625):d==="pt"?f=s.round(c*1.333):d==="%"&&(f=c/100),e){case"em":case"rem":case"%":return(f*.0625).toFixed(2)+e;case"pt":return s.floor(f/1.333)+e;default:return f+e}},_cleanStyle:function(e,i,s){let r=(e.match(/style\s*=\s*(?:"|')[^"']*(?:"|')/)||[])[0];if(/span/i.test(s)&&!r&&(e.match(/<[^\s]+\s(.+)/)||[])[1]){const c=(e.match(/\ssize="([^"]+)"/i)||[])[1],d=(e.match(/\sface="([^"]+)"/i)||[])[1],f=(e.match(/\scolor="([^"]+)"/i)||[])[1];(c||d||f)&&(r='style="'+(c?"font-size:"+this.util.getNumber(c/3.333,1)+"rem;":"")+(d?"font-family:"+d+";":"")+(f?"color:"+f+";":"")+'"')}if(r){i||(i=[]);const c=r.replace(/&quot;/g,"").match(this._cleanStyleRegExp[s]);if(c){const d=[];for(let f=0,u=c.length,m;f<u;f++)if(m=c[f].match(/([a-zA-Z0-9-]+)(:)([^"']+)/),m&&!/inherit|initial|revert|unset/i.test(m[3])){const h=t.kebabToCamelCase(m[1].trim()),y=this.wwComputedStyle[h]?this.wwComputedStyle[h].replace(/"/g,""):"",B=m[3].trim();switch(h){case"fontFamily":if(!o.plugins.font||o.font.indexOf(B)===-1)continue;break;case"fontSize":if(!o.plugins.fontSize)continue;this._cleanStyleRegExp.fontSizeUnit.test(m[0])||(m[0]=m[0].replace((m[0].match(/:\s*([^;]+)/)||[])[1],this._convertFontSize.bind(this,o.fontSizeUnit)));break;case"color":if(!o.plugins.fontColor||/rgba\(([0-9]+\s*,\s*){3}0\)|windowtext/i.test(B))continue;break;case"backgroundColor":if(!o.plugins.hiliteColor||/rgba\(([0-9]+\s*,\s*){3}0\)|windowtext/i.test(B))continue;break}y!==B&&d.push(m[0])}d.length>0&&i.push('style="'+d.join(";")+'"')}}return i},_cleanTags:function(e,i,s){if(/^<[a-z0-9]+\:[a-z0-9]+/i.test(i))return i;let r=null;const c=s.match(/(?!<)[a-zA-Z0-9\-]+/)[0].toLowerCase(),d=this._attributesTagsBlacklist[c];i=i.replace(/\s(?:on[a-z]+)\s*=\s*(")[^"]*\1/ig,""),d?i=i.replace(d,""):i=i.replace(this._attributesBlacklistRegExp,"");const f=this._attributesTagsWhitelist[c];if(f?r=i.match(f):r=i.match(e?this._attributesWhitelistRegExp:this._attributesWhitelistRegExp_all_data),e||c==="span"||c==="li"||this._cleanStyleRegExp[c])if(c==="a"){const u=i.match(/(?:(?:id|name)\s*=\s*(?:"|')[^"']*(?:"|'))/g);u&&(r||(r=[]),r.push(u[0]))}else(!r||!/style=/i.test(r.toString()))&&((c==="span"||c==="li")&&(r=this._cleanStyle(i,r,"span")),this._cleanStyleRegExp[c]?r=this._cleanStyle(i,r,c):/^(P|DIV|H[1-6]|PRE)$/i.test(c)&&(r=this._cleanStyle(i,r,"format")));else{const u=i.match(/style\s*=\s*(?:"|')[^"']*(?:"|')/);u&&!r?r=[u[0]]:u&&!r.some(function(m){return/^style/.test(m.trim())})&&r.push(u[0])}if(t.isFigures(c)){const u=i.match(/style\s*=\s*(?:"|')[^"']*(?:"|')/);r||(r=[]),u&&r.push(u[0])}if(r)for(let u=0,m=r.length,h;u<m;u++)h=/^(?:href|src)\s*=\s*('|"|\s)*javascript\s*\:/i.test(r[u].trim())?"":r[u],s+=(/^\s/.test(h)?"":" ")+h;return s},_editFormat:function(e){let i="",s;const r=e.childNodes;for(let c=0,d=r.length,f;c<d;c++)f=r[c],f.nodeType===8?i+="<!-- "+f.textContent+" -->":!t.isFormatElement(f)&&!t.isRangeFormatElement(f)&&!t.isComponent(f)&&!/meta/i.test(f.nodeName)?(s||(s=t.createElement(o.defaultTag)),s.appendChild(f),c--,d--):(s&&(i+=s.outerHTML,s=null),i+=f.outerHTML);return s&&(i+=s.outerHTML),L.createRange().createContextualFragment(i)},_convertListCell:function(e){let i="";for(let s=0,r=e.length,c;s<r;s++)c=e[s],c.nodeType===1?t.isList(c)?i+=c.innerHTML:t.isListCell(c)?i+=c.outerHTML:t.isFormatElement(c)?i+="<li>"+(c.innerHTML.trim()||"<br>")+"</li>":t.isRangeFormatElement(c)&&!t.isTable(c)?i+=this._convertListCell(c):i+="<li>"+c.outerHTML+"</li>":i+="<li>"+(c.textContent||"<br>")+"</li>";return i},_isFormatData:function(e){let i=!1;for(let s=0,r=e.length,c;s<r;s++)if(c=e[s],c.nodeType===1&&!t.isTextStyleElement(c)&&!t.isBreak(c)&&!this.__disallowedTagNameRegExp.test(c.nodeName)){i=!0;break}return i},cleanHTML:function(e,i,s){if(!o.strictMode)return e;e=this._deleteDisallowedTags(this._parser.parseFromString(t.htmlCompress(e),"text/html").body.innerHTML).replace(/(<[a-zA-Z0-9\-]+)[^>]*(?=>)/g,this._cleanTags.bind(this,!0)).replace(/<br\/?>$/i,"");const r=L.createRange().createContextualFragment(e);try{t._consistencyCheckOfHTML(r,this._htmlCheckWhitelistRegExp,this._htmlCheckBlacklistRegExp,this._classNameFilter)}catch(u){console.warn("[SUNEDITOR.cleanHTML.consistencyCheck.fail] "+u)}if(this.managedTagsInfo&&this.managedTagsInfo.query){const u=r.querySelectorAll(this.managedTagsInfo.query);for(let m=0,h=u.length,y,B;m<h;m++){B=[].slice.call(u[m].classList);for(let b=0,S=B.length;b<S;b++)if(y=this.managedTagsInfo.map[B[b]],y){y(u[m]);break}}}let c=r.childNodes,d="";const f=this._isFormatData(c);f&&(c=this._editFormat(r).childNodes);for(let u=0,m=c.length,h;u<m;u++){if(h=c[u],this.__allowedScriptRegExp.test(h.nodeName)){d+=h.outerHTML;continue}d+=this._makeLine(h,f)}return d=t.htmlRemoveWhiteSpace(d),d?(i&&(d=d.replace(typeof i=="string"?t.createTagsWhitelist(i):i,"")),s&&(d=d.replace(typeof s=="string"?t.createTagsBlacklist(s):s,""))):d=e,this._tagConvertor(d)},convertContentsForEditor:function(e){if(!o.strictMode)return e;e=this._deleteDisallowedTags(this._parser.parseFromString(t.htmlCompress(e),"text/html").body.innerHTML).replace(/(<[a-zA-Z0-9\-]+)[^>]*(?=>)/g,this._cleanTags.bind(this,!0));const i=L.createRange().createContextualFragment(e);try{t._consistencyCheckOfHTML(i,this._htmlCheckWhitelistRegExp,this._htmlCheckBlacklistRegExp,this._classNameFilter)}catch(d){console.warn("[SUNEDITOR.convertContentsForEditor.consistencyCheck.fail] "+d)}if(this.managedTagsInfo&&this.managedTagsInfo.query){const d=i.querySelectorAll(this.managedTagsInfo.query);for(let f=0,u=d.length,m,h;f<u;f++){h=[].slice.call(d[f].classList);for(let y=0,B=h.length;y<B;y++)if(m=this.managedTagsInfo.map[h[y]],m){m(d[f]);break}}}const s=i.childNodes;let r="",c=null;for(let d=0,f;d<s.length;d++){if(f=s[d],this.__allowedScriptRegExp.test(f.nodeName)){r+=f.outerHTML;continue}if(!t.isFormatElement(f)&&!t.isRangeFormatElement(f)&&!t.isComponent(f)&&!t.isFigures(f)&&f.nodeType!==8&&!/__se__tag/.test(f.className)){if(c||(c=t.createElement(o.defaultTag)),c.appendChild(f),d--,s[d+1]&&!t.isFormatElement(s[d+1]))continue;f=c,c=null}c&&(r+=this._makeLine(c,!0),c=null),r+=this._makeLine(f,!0)}return c&&(r+=this._makeLine(c,!0)),r.length===0?"<"+o.defaultTag+"><br></"+o.defaultTag+">":(r=t.htmlRemoveWhiteSpace(r),this._tagConvertor(r))},convertHTMLForCodeView:function(e,i){let s="";const r=E.RegExp,c=new r("^(BLOCKQUOTE|PRE|TABLE|THEAD|TBODY|TR|TH|TD|OL|UL|IMG|IFRAME|VIDEO|AUDIO|FIGURE|FIGCAPTION|HR|BR|CANVAS|SELECT)$","i"),d=typeof e=="string"?L.createRange().createContextualFragment(e):e,f=(function(h){return this.isFormatElement(h)||this.isComponent(h)}).bind(t),u=i?"":`
`;let m=i?0:this._variable.codeIndent*1;return m=m>0?new E.Array(m+1).join(" "):"",function h(y,B){const b=y.childNodes,S=c.test(y.nodeName),x=S?B:"";for(let D=0,z=b.length,O,H,M,T,k,_;D<z;D++){if(O=b[D],T=c.test(O.nodeName),H=T?u:"",M=f(O)&&!S&&!/^(TH|TD)$/i.test(y.nodeName)?u:"",O.nodeType===8){s+=`
<!-- `+O.textContent.trim()+" -->"+H;continue}if(O.nodeType===3){t.isList(O.parentElement)||(s+=t._HTMLConvertor(/^\n+$/.test(O.data)?"":O.data));continue}if(O.childNodes.length===0){s+=(/^HR$/i.test(O.nodeName)?u:"")+(/^PRE$/i.test(O.parentElement.nodeName)&&/^BR$/i.test(O.nodeName)?"":x)+O.outerHTML+H;continue}O.outerHTML?(k=O.nodeName.toLowerCase(),_=x||T?B:"",s+=(M||(S?"":H))+_+O.outerHTML.match(r("<"+k+"[^>]*>","i"))[0]+H,h(O,B+m),s+=(/\n$/.test(s)?_:"")+"</"+k+">"+(M||H||S||/^(TH|TD)$/i.test(O.nodeName)?u:"")):s+=new E.XMLSerializer().serializeToString(O)}}(d,""),s.trim()+u},addDocEvent:function(e,i,s){L.addEventListener(e,i,s),o.iframe&&this._wd.addEventListener(e,i)},removeDocEvent:function(e,i){L.removeEventListener(e,i),o.iframe&&this._wd.removeEventListener(e,i)},_charCount:function(e){const i=o.maxCharCount,s=o.charCounterType;let r=0;if(e&&(r=this.getCharLength(e,s)),this._setCharCount(),i>0){let c=!1;const d=F.getCharCount(s);if(d>i){if(c=!0,r>0){this._editorRange();const f=this.getRange(),u=f.endOffset-1,m=this.getSelectionNode().textContent,h=f.endOffset-(d-i);this.getSelectionNode().textContent=m.slice(0,h<0?0:h)+m.slice(f.endOffset,m.length),this.setRange(f.endContainer,u,f.endContainer,u)}}else d+r>i&&(c=!0);if(c&&(this._callCounterBlink(),r>0))return!1}return!0},checkCharCount:function(e,i){if(o.maxCharCount){const s=i||o.charCounterType,r=this.getCharLength(typeof e=="string"?e:this._charTypeHTML&&e.nodeType===1?e.outerHTML:e.textContent,s);if(r>0&&r+F.getCharCount(s)>o.maxCharCount)return this._callCounterBlink(),!1}return!0},getCharLength:function(e,i){return/byte/.test(i)?t.getByteLength(e):e.length},resetResponsiveToolbar:function(){a.controllersOff();const e=p._responsiveButtonSize;if(e){let i=0;(a._isBalloon||a._isInline)&&o.toolbarWidth==="auto"?i=n.element.topArea.offsetWidth:i=n.element.toolbar.offsetWidth;let s="default";for(let r=1,c=e.length;r<c;r++)if(i<e[r]){s=e[r]+"";break}p._responsiveCurrentSize!==s&&(p._responsiveCurrentSize=s,F.setToolbarButtons(p._responsiveButtons[s]))}},_setCharCount:function(){n.element.charCounter&&E.setTimeout((function(e,i){this.textContent&&e&&(this.textContent=e.getCharCount(i.charCounterType))}).bind(n.element.charCounter,F,o),0)},_callCounterBlink:function(){const e=n.element.charWrapper;e&&!t.hasClass(e,"se-blink")&&(t.addClass(e,"se-blink"),E.setTimeout(function(){t.removeClass(e,"se-blink")},600))},_checkComponents:function(){for(let e=0,i=this._fileInfoPluginsCheck.length;e<i;e++)this._fileInfoPluginsCheck[e]()},_resetComponents:function(){for(let e=0,i=this._fileInfoPluginsReset.length;e<i;e++)this._fileInfoPluginsReset[e]()},_setCodeView:function(e){o.codeMirrorEditor?o.codeMirrorEditor.getDoc().setValue(e):n.element.code.value=e},_getCodeView:function(){return o.codeMirrorEditor?o.codeMirrorEditor.getDoc().getValue():n.element.code.value},_setKeyEffect:function(e){const i=this.commandMap,s=this.activePlugins;for(let r in i)e.indexOf(r)>-1||!t.hasOwn(i,r)||(s.indexOf(r)>-1?g[r].active.call(this,null):i.OUTDENT&&/^OUTDENT$/i.test(r)?t.isImportantDisabled(i.OUTDENT)||i.OUTDENT.setAttribute("disabled",!0):i.INDENT&&/^INDENT$/i.test(r)?t.isImportantDisabled(i.INDENT)||i.INDENT.removeAttribute("disabled"):t.removeClass(i[r],"active"))},_init:function(e,i){const s=E.RegExp;this._ww=o.iframe?n.element.wysiwygFrame.contentWindow:E,this._wd=L,this._charTypeHTML=o.charCounterType==="byte-html",this.wwComputedStyle=E.getComputedStyle(n.element.wysiwyg),this._editorHeight=n.element.wysiwygFrame.offsetHeight,this._editorHeightPadding=t.getNumber(this.wwComputedStyle.getPropertyValue("padding-top"))+t.getNumber(this.wwComputedStyle.getPropertyValue("padding-bottom")),this._classNameFilter=(function(T){return this.test(T)?T:""}).bind(o.allowedClassNames);const r=o.__allowedScriptTag?"":"script|";if(this.__scriptTagRegExp=new s("<(script)[^>]*>([\\s\\S]*?)<\\/\\1>|<script[^>]*\\/?>","gi"),this.__disallowedTagsRegExp=new s("<("+r+"style)[^>]*>([\\s\\S]*?)<\\/\\1>|<("+r+"style)[^>]*\\/?>","gi"),this.__disallowedTagNameRegExp=new s("^("+r+"meta|link|style|[a-z]+:[a-z]+)$","i"),this.__allowedScriptRegExp=new s("^"+(o.__allowedScriptTag?"script":"")+"$","i"),!o.iframe&&typeof E.ShadowRoot=="function"){let T=n.element.wysiwygFrame;for(;T;){if(T.shadowRoot){this._shadowRoot=T.shadowRoot;break}else if(T instanceof E.ShadowRoot){this._shadowRoot=T;break}T=T.parentNode}this._shadowRoot&&(this._shadowRootControllerEventTarget=[])}const c=E.Object.keys(o._textTagsMap),d=o.addTagsWhitelist?o.addTagsWhitelist.split("|").filter(function(T){return/b|i|ins|s|strike/i.test(T)}):[];for(let T=0;T<d.length;T++)c.splice(c.indexOf(d[T].toLowerCase()),1);this._disallowedTextTagsRegExp=c.length===0?null:new s("(<\\/?)("+c.join("|")+")\\b\\s*([^>^<]+)?\\s*(?=>)","gi");const f=function(T,k){return T?T==="*"?"[a-z-]+":k?T+"|"+k:T:"^"},u="|controls|autoplay|loop|muted|poster|preload|playsinline",m="|allowfullscreen|sandbox|loading|allow|referrerpolicy|frameborder|scrolling",h="contenteditable|colspan|rowspan|target|href|download|rel|src|alt|class|type|origin-size"+u+m,y="data-format|data-size|data-file-size|data-file-name|data-origin|data-align|data-image-link|data-rotate|data-proportion|data-percentage|data-exp|data-font-size";this._allowHTMLComments=o._editorTagsWhitelist.indexOf("//")>-1||o._editorTagsWhitelist==="*",this._htmlCheckWhitelistRegExp=new s("^("+f(o._editorTagsWhitelist.replace("|//",""),"")+")$","i"),this._htmlCheckBlacklistRegExp=new s("^("+(o.tagsBlacklist||"^")+")$","i"),this.editorTagsWhitelistRegExp=t.createTagsWhitelist(f(o._editorTagsWhitelist.replace("|//","|<!--|-->"),"")),this.editorTagsBlacklistRegExp=t.createTagsBlacklist(o.tagsBlacklist.replace("|//","|<!--|-->")),this.pasteTagsWhitelistRegExp=t.createTagsWhitelist(f(o.pasteTagsWhitelist,"")),this.pasteTagsBlacklistRegExp=t.createTagsBlacklist(o.pasteTagsBlacklist);const B='\\s*=\\s*(")[^"]*\\1',b=o.attributesWhitelist;let S={},x="";if(b)for(let T in b)!t.hasOwn(b,T)||/^on[a-z]+$/i.test(b[T])||(T==="all"?x=f(b[T],h):S[T]=new s("\\s(?:"+f(b[T],"")+")"+B,"ig"));this._attributesWhitelistRegExp=new s("\\s(?:"+(x||h+"|"+y)+")"+B,"ig"),this._attributesWhitelistRegExp_all_data=new s("\\s(?:"+((x||h)+"|data-[a-z0-9\\-]+")+")"+B,"ig"),this._attributesTagsWhitelist=S;const D=o.attributesBlacklist;if(S={},x="",D)for(let T in D)t.hasOwn(D,T)&&(T==="all"?x=f(D[T],""):S[T]=new s("\\s(?:"+f(D[T],"")+")"+B,"ig"));this._attributesBlacklistRegExp=new s("\\s(?:"+(x||"^")+")"+B,"ig"),this._attributesTagsBlacklist=S,this._isInline=/inline/i.test(o.mode),this._isBalloon=/balloon|balloon-always/i.test(o.mode),this._isBalloonAlways=/balloon-always/i.test(o.mode),this._cachingButtons(),this._fileInfoPluginsCheck=[],this._fileInfoPluginsReset=[],this.managedTagsInfo={query:"",map:{}};const z=[];this.activePlugins=[],this._fileManager.tags=[],this._fileManager.pluginMap={};let O=[],H,M;for(let T in g)if(t.hasOwn(g,T)){if(H=g[T],M=l[T],(H.active||H.action)&&M&&this.callPlugin(T,null,M),typeof H.checkFileInfo=="function"&&typeof H.resetFileInfo=="function"&&(this.callPlugin(T,null,M),this._fileInfoPluginsCheck.push(H.checkFileInfo.bind(this)),this._fileInfoPluginsReset.push(H.resetFileInfo.bind(this))),E.Array.isArray(H.fileTags)){const k=H.fileTags;this.callPlugin(T,null,M),this._fileManager.tags=this._fileManager.tags.concat(k),O.push(T);for(let _=0,A=k.length;_<A;_++)this._fileManager.pluginMap[k[_].toLowerCase()]=T}if(H.managedTags){const k=H.managedTags();z.push("."+k.className),this.managedTagsInfo.map[k.className]=k.method.bind(this)}}this.managedTagsInfo.query=z.toString(),this._fileManager.queryString=this._fileManager.tags.join(","),this._fileManager.regExp=new s("^("+(this._fileManager.tags.join("|")||"^")+")$","i"),this._fileManager.pluginRegExp=new s("^("+(O.length===0?"^":O.join("|"))+")$","i"),this._variable._originCssText=n.element.topArea.style.cssText,this._placeholder=n.element.placeholder,this._lineBreaker=n.element.lineBreaker,this._lineBreakerButton=this._lineBreaker.querySelector("button"),this.history=ke(this,this._onChange_historyStack.bind(this)),this.addModule([Te]),o.iframe&&(this._wd=n.element.wysiwygFrame.contentDocument,n.element.wysiwyg=this._wd.body,o._editorStyles.editor&&(n.element.wysiwyg.style.cssText=o._editorStyles.editor),o.height==="auto"&&(this._iframeAuto=this._wd.body)),this._initWysiwygArea(e,i)},_cachingButtons:function(){this.codeViewDisabledButtons=n.element._buttonTray.querySelectorAll('.se-menu-list button[data-display]:not([class~="se-code-view-enabled"]):not([data-display="MORE"])'),this.resizingDisabledButtons=n.element._buttonTray.querySelectorAll('.se-menu-list button[data-display]:not([class~="se-resizing-enabled"]):not([data-display="MORE"])');const e=n.tool,i=this.commandMap;i.INDENT=e.indent,i.OUTDENT=e.outdent,i[o.textTags.bold.toUpperCase()]=e.bold,i[o.textTags.underline.toUpperCase()]=e.underline,i[o.textTags.italic.toUpperCase()]=e.italic,i[o.textTags.strike.toUpperCase()]=e.strike,i[o.textTags.sub.toUpperCase()]=e.subscript,i[o.textTags.sup.toUpperCase()]=e.superscript,this._styleCommandMap={fullScreen:e.fullScreen,showBlocks:e.showBlocks,codeView:e.codeView},this._saveButtonStates()},_initWysiwygArea:function(e,i){n.element.wysiwyg.innerHTML=e?i:this.convertContentsForEditor((typeof i=="string"?i:/^TEXTAREA$/i.test(n.element.originElement.nodeName)?n.element.originElement.value:n.element.originElement.innerHTML)||"")},_resourcesStateChange:function(){this._iframeAutoHeight(),this._checkPlaceholder()},_onChange_historyStack:function(){this.hasFocus&&p._applyTagEffects(),this._variable.isChanged=!0,n.tool.save&&n.tool.save.removeAttribute("disabled"),F.onChange&&F.onChange(this.getContents(!0),this),n.element.toolbar.style.display==="block"&&p._showToolbarBalloon()},_iframeAutoHeight:function(){this._iframeAuto?E.setTimeout(function(){const e=a._iframeAuto.offsetHeight;n.element.wysiwygFrame.style.height=e+"px",t.isResizeObserverSupported||a.__callResizeFunction(e,null)}):t.isResizeObserverSupported||a.__callResizeFunction(n.element.wysiwygFrame.offsetHeight,null)},__callResizeFunction:function(e,i){e=e===-1?i.borderBoxSize&&i.borderBoxSize[0]?i.borderBoxSize[0].blockSize:i.contentRect.height+this._editorHeightPadding:e,this._editorHeight!==e&&(typeof F.onResizeEditor=="function"&&F.onResizeEditor(e,this._editorHeight,a,i),this._editorHeight=e)},_checkPlaceholder:function(){if(this._placeholder){if(this._variable.isCodeView){this._placeholder.style.display="none";return}const e=n.element.wysiwyg;!t.onlyZeroWidthSpace(e.textContent)||e.querySelector(t._allowedEmptyNodeList)||(e.innerText.match(/\n/g)||"").length>1?this._placeholder.style.display="none":this._placeholder.style.display="block"}},_setDefaultFormat:function(e){if(this._fileManager.pluginRegExp.test(this.currentControllerName))return;const i=this.getRange(),s=i.commonAncestorContainer,r=i.startContainer,c=t.getRangeFormatElement(s,null);let d,f,u;const m=t.getParentElement(s,t.isComponent);if(!(m&&!t.isTable(m))){if(s.nodeType===1&&s.getAttribute("data-se-embed")==="true"){let h=s.nextElementSibling;t.isFormatElement(h)||(h=this.appendFormatTag(s,o.defaultTag)),this.setRange(h.firstChild,0,h.firstChild,0);return}if(!((t.isRangeFormatElement(r)||t.isWysiwygDiv(r))&&(t.isComponent(r.children[i.startOffset])||t.isComponent(r.children[i.startOffset-1])))){if(t.getParentElement(s,t.isNotCheckingNode))return null;if(c){u=t.createElement(e||o.defaultTag),u.innerHTML=c.innerHTML,u.childNodes.length===0&&(u.innerHTML=t.zeroWidthSpace),c.innerHTML=u.outerHTML,u=c.firstChild,d=t.getEdgeChildNodes(u,null).sc,d||(d=t.createTextNode(t.zeroWidthSpace),u.insertBefore(d,u.firstChild)),f=d.textContent.length,this.setRange(d,f,d,f);return}if(t.isRangeFormatElement(s)&&s.childNodes.length<=1){let h=null;s.childNodes.length===1&&t.isBreak(s.firstChild)?h=s.firstChild:(h=t.createTextNode(t.zeroWidthSpace),s.appendChild(h)),this.setRange(h,1,h,1);return}try{if(s.nodeType===3&&(u=t.createElement(e||o.defaultTag),s.parentNode.insertBefore(u,s),u.appendChild(s)),t.isBreak(u.nextSibling)&&t.removeItem(u.nextSibling),t.isBreak(u.previousSibling)&&t.removeItem(u.previousSibling),t.isBreak(d)){const h=t.createTextNode(t.zeroWidthSpace);d.parentNode.insertBefore(h,d),d=h}}catch{this.execCommand("formatBlock",!1,e||o.defaultTag),this.removeRange(),this._editorRange()}if(u&&(t.isBreak(u.nextSibling)&&t.removeItem(u.nextSibling),t.isBreak(u.previousSibling)&&t.removeItem(u.previousSibling),t.isBreak(d))){const h=t.createTextNode(t.zeroWidthSpace);d.parentNode.insertBefore(h,d),d=h}this.effectNode=null,this.nativeFocus()}}},_setOptionsInit:function(e,i){this.context=n=ve(e.originElement,this._getConstructed(e),o),this._componentsInfoReset=!0,this._editorInit(!0,i)},_editorInit:function(e,i){this._init(e,i),p._addEvent(),this._setCharCount(),p._offStickyToolbar(),p.onResize_window(),n.element.toolbar.style.visibility="";const s=o.frameAttrbutes;for(let r in s)n.element.wysiwyg.setAttribute(r,s[r]);this._checkComponents(),this._componentsInfoInit=!1,this._componentsInfoReset=!1,this.history.reset(!0),E.setTimeout(function(){typeof a._resourcesStateChange=="function"&&(p._resizeObserver&&p._resizeObserver.observe(n.element.wysiwygFrame),p._toolbarObserver&&p._toolbarObserver.observe(n.element._toolbarShadow),a._resourcesStateChange(),typeof F.onload=="function"&&F.onload(a,e))})},_getConstructed:function(e){return{_top:e.topArea,_relative:e.relative,_toolBar:e.toolbar,_toolbarShadow:e._toolbarShadow,_menuTray:e._menuTray,_editorArea:e.editorArea,_wysiwygArea:e.wysiwygFrame,_codeArea:e.code,_placeholder:e.placeholder,_resizingBar:e.resizingBar,_navigation:e.navigation,_charCounter:e.charCounter,_charWrapper:e.charWrapper,_loading:e.loading,_lineBreaker:e.lineBreaker,_lineBreaker_t:e.lineBreaker_t,_lineBreaker_b:e.lineBreaker_b,_resizeBack:e.resizeBackground,_stickyDummy:e._stickyDummy,_arrow:e._arrow}}},p={_IEisComposing:!1,_lineBreakerBind:null,_responsiveCurrentSize:"default",_responsiveButtonSize:null,_responsiveButtons:null,_cursorMoveKeyCode:new E.RegExp("^(8|3[2-9]|40|46)$"),_directionKeyCode:new E.RegExp("^(8|13|3[2-9]|40|46)$"),_nonTextKeyCode:new E.RegExp("^(8|13|1[6-9]|20|27|3[3-9]|40|45|46|11[2-9]|12[0-3]|144|145)$"),_historyIgnoreKeyCode:new E.RegExp("^(1[6-9]|20|27|3[3-9]|40|45|11[2-9]|12[0-3]|144|145)$"),_onButtonsCheck:new E.RegExp("^("+E.Object.keys(o._textTagsMap).join("|")+")$","i"),_frontZeroWidthReg:new E.RegExp(t.zeroWidthSpace+"+",""),_keyCodeShortcut:{65:"A",66:"B",83:"S",85:"U",73:"I",89:"Y",90:"Z",219:"[",221:"]"},_shortcutCommand:function(e,i){let s=null;const r=p._keyCodeShortcut[e];switch(r){case"A":s="selectAll";break;case"B":o.shortcutsDisable.indexOf("bold")===-1&&(s="bold");break;case"S":i&&o.shortcutsDisable.indexOf("strike")===-1?s="strike":!i&&o.shortcutsDisable.indexOf("save")===-1&&(s="save");break;case"U":o.shortcutsDisable.indexOf("underline")===-1&&(s="underline");break;case"I":o.shortcutsDisable.indexOf("italic")===-1&&(s="italic");break;case"Z":o.shortcutsDisable.indexOf("undo")===-1&&(i?s="redo":s="undo");break;case"Y":o.shortcutsDisable.indexOf("undo")===-1&&(s="redo");break;case"[":o.shortcutsDisable.indexOf("indent")===-1&&(s=o.rtl?"indent":"outdent");break;case"]":o.shortcutsDisable.indexOf("indent")===-1&&(s=o.rtl?"outdent":"indent");break}return s?(a.commandHandler(a.commandMap[s],s),!0):!!r},_applyTagEffects:function(){if(t.hasClass(n.element.wysiwyg,"se-read-only"))return!1;let e=a.getSelectionNode();if(e===a.effectNode)return;a.effectNode=e;const i=o.rtl?"marginRight":"marginLeft",s=a.commandMap,r=p._onButtonsCheck,c=[],d=[],f=a.activePlugins,u=f.length;let m="";for(;e.firstChild;)e=e.firstChild;for(let h=e;!t.isWysiwygDiv(h)&&h;h=h.parentNode)if(!(h.nodeType!==1||t.isBreak(h))){if(m=h.nodeName.toUpperCase(),d.push(m),!a.isReadOnly)for(let y=0,B;y<u;y++)B=f[y],c.indexOf(B)===-1&&g[B].active.call(a,h)&&c.push(B);if(t.isFormatElement(h)){c.indexOf("OUTDENT")===-1&&s.OUTDENT&&!t.isImportantDisabled(s.OUTDENT)&&(t.isListCell(h)||h.style[i]&&t.getNumber(h.style[i],0)>0)&&(c.push("OUTDENT"),s.OUTDENT.removeAttribute("disabled")),c.indexOf("INDENT")===-1&&s.INDENT&&!t.isImportantDisabled(s.INDENT)&&(c.push("INDENT"),t.isListCell(h)&&!h.previousElementSibling?s.INDENT.setAttribute("disabled",!0):s.INDENT.removeAttribute("disabled"));continue}r&&r.test(m)&&(c.push(m),t.addClass(s[m],"active"))}a._setKeyEffect(c),a._variable.currentNodes=d.reverse(),a._variable.currentNodesMap=c,o.showPathLabel&&(n.element.navigation.textContent=a._variable.currentNodes.join(" > "))},_buttonsEventHandler:function(e){let i=e.target;if(a._bindControllersOff&&e.stopPropagation(),/^(input|textarea|select|option)$/i.test(i.nodeName)?a._antiBlur=!1:e.preventDefault(),t.getParentElement(i,".se-submenu"))e.stopPropagation(),a._notHideToolbar=!0;else{let s=i.getAttribute("data-command"),r=i.className;for(;!s&&!/se-menu-list/.test(r)&&!/sun-editor-common/.test(r);)i=i.parentNode,s=i.getAttribute("data-command"),r=i.className;(s===a._submenuName||s===a._containerName)&&e.stopPropagation()}},addGlobalEvent(e,i,s){return o.iframe&&a._ww.addEventListener(e,i,s),a._w.addEventListener(e,i,s),{type:e,listener:i,useCapture:s}},removeGlobalEvent(e,i,s){e&&(typeof e=="object"&&(i=e.listener,s=e.useCapture,e=e.type),o.iframe&&a._ww.removeEventListener(e,i,s),a._w.removeEventListener(e,i,s))},onClick_toolbar:function(e){let i=e.target,s=i.getAttribute("data-display"),r=i.getAttribute("data-command"),c=i.className;for(a.controllersOff();i.parentNode&&!r&&!/se-menu-list/.test(c)&&!/se-toolbar/.test(c);)i=i.parentNode,r=i.getAttribute("data-command"),s=i.getAttribute("data-display"),c=i.className;!r&&!s||i.disabled||a.actionCall(r,s,i)},__selectionSyncEvent:null,onMouseDown_wysiwyg:function(e){if(a.isReadOnly||t.isNonEditable(n.element.wysiwyg))return;if(t._isExcludeSelectionElement(e.target)){e.preventDefault();return}if(p.removeGlobalEvent(p.__selectionSyncEvent),p.__selectionSyncEvent=p.addGlobalEvent("mouseup",function(){a._editorRange(),p.removeGlobalEvent(p.__selectionSyncEvent)}),typeof F.onMouseDown=="function"&&F.onMouseDown(e,a)===!1)return;const i=t.getParentElement(e.target,t.isCell);if(i){const s=a.plugins.table;s&&i!==s._fixedCell&&!s._shift&&a.callPlugin("table",function(){s.onTableCellMultiSelect.call(a,i,!1)},null)}a._isBalloon&&p._hideToolbar()},onClick_wysiwyg:function(e){const i=e.target;if(a.isReadOnly)return e.preventDefault(),t.isAnchor(i)&&E.open(i.href,i.target),!1;if(t.isNonEditable(n.element.wysiwyg)||typeof F.onClick=="function"&&F.onClick(e,a)===!1)return;const s=a.getFileComponent(i);if(s){e.preventDefault(),a.selectComponent(s.target,s.pluginName);return}const r=t.getParentElement(i,"FIGCAPTION");if(r&&t.isNonEditable(r)&&(e.preventDefault(),r.focus(),a._isInline&&!a._inlineToolbarAttr.isShow)){p._showToolbarInline();const h=function(){p._hideToolbar(),r.removeEventListener("blur",h)};r.addEventListener("blur",h)}if(a._editorRange(),e.detail===3){let h=a.getRange();t.isFormatElement(h.endContainer)&&h.endOffset===0&&(h=a.setRange(h.startContainer,h.startOffset,h.startContainer,h.startContainer.length),a._rangeInfo(h,a.getSelection()))}const c=a.getSelectionNode(),d=t.getFormatElement(c,null),f=t.getRangeFormatElement(c,null);let u=c;for(;u.firstChild;)u=u.firstChild;const m=a.getFileComponent(u);if(m){const h=a.getRange();!f&&h.startContainer===h.endContainer&&a.selectComponent(m.target,m.pluginName)}else a.currentFileComponentInfo&&a.controllersOff();if(!d&&!t.isNonEditable(i)&&!t.isList(f)){const h=a.getRange();if(t.getFormatElement(h.startContainer)===t.getFormatElement(h.endContainer))if(t.isList(f)){e.preventDefault();const y=t.createElement("LI"),B=c.nextElementSibling;y.appendChild(c),f.insertBefore(y,B),a.focus()}else!t.isWysiwygDiv(c)&&!t.isComponent(c)&&(!t.isTable(c)||t.isCell(c))&&a._setDefaultFormat(t.isRangeFormatElement(f)?"DIV":o.defaultTag)!==null?(e.preventDefault(),a.focus()):p._applyTagEffects()}else p._applyTagEffects();a._isBalloon&&E.setTimeout(p._toggleToolbarBalloon)},_balloonDelay:null,_showToolbarBalloonDelay:function(){p._balloonDelay&&E.clearTimeout(p._balloonDelay),p._balloonDelay=E.setTimeout((function(){E.clearTimeout(this._balloonDelay),this._balloonDelay=null,this._showToolbarBalloon()}).bind(p),350)},_toggleToolbarBalloon:function(){a._editorRange();const e=a.getRange();a._bindControllersOff||!a._isBalloonAlways&&e.collapsed?p._hideToolbar():p._showToolbarBalloon(e)},_showToolbarBalloon:function(e){if(!a._isBalloon)return;const i=e||a.getRange(),s=n.element.toolbar,r=n.element.topArea,c=a.getSelection();let d;if(a._isBalloonAlways&&i.collapsed)d=!0;else if(c.focusNode===c.anchorNode)d=c.focusOffset<c.anchorOffset;else{const H=t.getListChildNodes(i.commonAncestorContainer,null);d=t.getArrayIndex(H,c.focusNode)<t.getArrayIndex(H,c.anchorNode)}let f=i.getClientRects();f=f[d?0:f.length-1];const u=a.getGlobalScrollOffset();let m=u.left,h=u.top;const y=r.offsetWidth,B=p._getEditorOffsets(null),b=B.top,S=B.left;if(s.style.top="-10000px",s.style.visibility="hidden",s.style.display="block",!f){const H=a.getSelectionNode();if(t.isFormatElement(H)){const M=t.createTextNode(t.zeroWidthSpace);a.insertNode(M,null,!1),a.setRange(M,1,M,1),a._editorRange(),f=a.getRange().getClientRects(),f=f[d?0:f.length-1]}if(!f){const M=t.getOffset(H,n.element.wysiwygFrame);f={left:M.left,top:M.top,right:M.left,bottom:M.top+H.offsetHeight,noText:!0},m=0,h=0}d=!0}const x=E.Math.round(n.element._arrow.offsetWidth/2),D=s.offsetWidth,z=s.offsetHeight,O=/iframe/i.test(n.element.wysiwygFrame.nodeName)?n.element.wysiwygFrame.getClientRects()[0]:null;if(O&&(f={left:f.left+O.left,top:f.top+O.top,right:f.right+O.right-O.width,bottom:f.bottom+O.bottom-O.height}),p._setToolbarOffset(d,f,s,S,y,m,h,b,x),(D!==s.offsetWidth||z!==s.offsetHeight)&&p._setToolbarOffset(d,f,s,S,y,m,h,b,x),o.toolbarContainer){const H=r.parentElement;let M=o.toolbarContainer,T=M.offsetLeft,k=M.offsetTop;for(;!M.parentElement.contains(H)||!/^(BODY|HTML)$/i.test(M.parentElement.nodeName);)M=M.offsetParent,T+=M.offsetLeft,k+=M.offsetTop;s.style.left=s.offsetLeft-T+r.offsetLeft+"px",s.style.top=s.offsetTop-k+r.offsetTop+"px"}s.style.visibility=""},_setToolbarOffset:function(e,i,s,r,c,d,f,u,m){const y=s.offsetWidth,B=i.noText&&!e?0:s.offsetHeight,b=(e?i.left:i.right)-r-y/2+d,S=b+y-c;let x=(e?i.top-B-m:i.bottom+m)-(i.noText?0:u)+f,D=b<0?1:S<0?b:b-S-1-1,z=!1;const O=x+(e?p._getEditorOffsets(null).top:s.offsetHeight-n.element.wysiwyg.offsetHeight);!e&&O>0&&p._getPageBottomSpace()<O?(e=!0,z=!0):e&&L.documentElement.offsetTop>O&&(e=!1,z=!0),z&&(x=(e?i.top-B-m:i.bottom+m)-(i.noText?0:u)+f),s.style.left=E.Math.floor(D)+"px",s.style.top=E.Math.floor(x)+"px",e?(t.removeClass(n.element._arrow,"se-arrow-up"),t.addClass(n.element._arrow,"se-arrow-down"),n.element._arrow.style.top=B+"px"):(t.removeClass(n.element._arrow,"se-arrow-down"),t.addClass(n.element._arrow,"se-arrow-up"),n.element._arrow.style.top=-m+"px");const H=E.Math.floor(y/2+(b-D));n.element._arrow.style.left=(H+m>s.offsetWidth?s.offsetWidth-m:H<m?m:H)+"px"},_showToolbarInline:function(){if(!a._isInline)return;const e=n.element.toolbar;o.toolbarContainer?e.style.position="relative":e.style.position="absolute",e.style.visibility="hidden",e.style.display="block",a._inlineToolbarAttr.width=e.style.width=o.toolbarWidth,a._inlineToolbarAttr.top=e.style.top=(o.toolbarContainer?0:-1-e.offsetHeight)+"px",typeof F.showInline=="function"&&F.showInline(e,n,a),p.onScroll_window(),a._inlineToolbarAttr.isShow=!0,e.style.visibility=""},_hideToolbar:function(){!a._notHideToolbar&&!a._variable.isFullScreen&&(n.element.toolbar.style.display="none",a._inlineToolbarAttr.isShow=!1)},onInput_wysiwyg:function(e){if(/AUDIO/.test(e.target.nodeName))return!1;if(a.isReadOnly||a.isDisabled)return e.preventDefault(),e.stopPropagation(),a.history.go(a.history.getCurrentIndex()),!1;a._editorRange();const i=(e.data===null?"":e.data===void 0?" ":e.data)||"";if(!a._charCount(i))return e.preventDefault(),e.stopPropagation(),!1;typeof F.onInput=="function"&&F.onInput(e,a)===!1||a.history.push(!0)},_isUneditableNode:function(e,i){const s=i?e.startContainer:e.endContainer,r=i?e.startOffset:e.endOffset,c=i?"previousSibling":"nextSibling",d=s.nodeType===1;let f;return d?(f=p._isUneditableNode_getSibling(s.childNodes[r],c,s),f&&f.nodeType===1&&f.getAttribute("contenteditable")==="false"):(f=p._isUneditableNode_getSibling(s,c,s),a.isEdgePoint(s,r,i?"front":"end")&&f&&f.nodeType===1&&f.getAttribute("contenteditable")==="false")},_isUneditableNode_getSibling:function(e,i,s){if(!e)return null;let r=e[i];if(!r)if(r=t.getFormatElement(s),r=r?r[i]:null,r&&!t.isComponent(r))r=i==="previousSibling"?r.firstChild:r.lastChild;else return null;return r},_onShortcutKey:!1,onKeyDown_wysiwyg:function(e){let i=a.getSelectionNode();if(t.isInputElement(i))return;const s=e.keyCode,r=e.shiftKey,c=e.ctrlKey||e.metaKey||s===91||s===92||s===224,d=e.altKey;if(p._IEisComposing=s===229,!c&&a.isReadOnly&&!p._cursorMoveKeyCode.test(s))return e.preventDefault(),!1;if(a.submenuOff(),a._isBalloon&&p._hideToolbar(),typeof F.onKeyDown=="function"&&F.onKeyDown(e,a)===!1)return;if(c&&p._shortcutCommand(s,r))return p._onShortcutKey=!0,e.preventDefault(),e.stopPropagation(),!1;p._onShortcutKey&&(p._onShortcutKey=!1);const f=a.getRange(),u=!f.collapsed||f.startContainer!==f.endContainer,m=a._fileManager.pluginRegExp.test(a.currentControllerName)?a.currentControllerName:"";let h=t.getFormatElement(i,null)||i,y=t.getRangeFormatElement(h,null);if(!(/37|38|39|40/.test(e.keyCode)&&p._onKeyDown_wysiwyg_arrowKey(e)===!1)){switch(s){case 8:if(!u&&m){e.preventDefault(),e.stopPropagation(),a.plugins[m].destroy.call(a);break}if(u&&p._hardDelete()){e.preventDefault(),e.stopPropagation();break}if(!t.isFormatElement(h)&&!n.element.wysiwyg.firstElementChild&&!t.isComponent(i)&&a._setDefaultFormat(o.defaultTag)!==null)return e.preventDefault(),e.stopPropagation(),!1;if(!u&&!h.previousElementSibling&&f.startOffset===0&&!i.previousSibling&&!t.isListCell(h)&&t.isFormatElement(h)&&(!t.isFreeFormatElement(h)||t.isClosureFreeFormatElement(h))){if(t.isClosureRangeFormatElement(h.parentNode))return e.preventDefault(),e.stopPropagation(),!1;if(t.isWysiwygDiv(h.parentNode)&&h.childNodes.length<=1&&(!h.firstChild||t.onlyZeroWidthSpace(h.textContent))){if(e.preventDefault(),e.stopPropagation(),h.nodeName.toUpperCase()===o.defaultTag.toUpperCase()){h.innerHTML="<br>";const _=h.attributes;for(;_[0];)h.removeAttribute(_[0].name)}else{const _=t.createElement(o.defaultTag);_.innerHTML="<br>",h.parentElement.replaceChild(_,h)}return a.nativeFocus(),!1}}const b=f.startContainer;if(h&&!h.previousElementSibling&&f.startOffset===0&&b.nodeType===3&&!t.isFormatElement(b.parentNode)){let _=b.parentNode.previousSibling;const A=b.parentNode.nextSibling;_||(A?_=A:(_=t.createElement("BR"),h.appendChild(_)));let v=b;for(;h.contains(v)&&!v.previousSibling;)v=v.parentNode;if(!h.contains(v)){b.textContent="",t.removeItemAllParents(b,null,h);break}}if(p._isUneditableNode(f,!0)){e.preventDefault(),e.stopPropagation();break}!u&&a._isEdgeFormat(f.startContainer,f.startOffset,"start")&&t.isFormatElement(h.previousElementSibling)&&(a._formatAttrsTemp=h.previousElementSibling.attributes);const S=f.commonAncestorContainer;if(h=t.getFormatElement(f.startContainer,null),y=t.getRangeFormatElement(h,null),y&&h&&!t.isCell(y)&&!/^FIGCAPTION$/i.test(y.nodeName)){if(t.isListCell(h)&&t.isList(y)&&(t.isListCell(y.parentNode)||h.previousElementSibling)&&(i===h||i.nodeType===3&&(!i.previousSibling||t.isList(i.previousSibling)))&&(t.getFormatElement(f.startContainer,null)!==t.getFormatElement(f.endContainer,null)?y.contains(f.startContainer):f.startOffset===0&&f.collapsed)){if(f.startContainer!==f.endContainer)e.preventDefault(),a.removeNode(),f.startContainer.nodeType===3&&a.setRange(f.startContainer,f.startContainer.textContent.length,f.startContainer,f.startContainer.textContent.length),a.history.push(!0);else{let _=h.previousElementSibling||y.parentNode;if(t.isListCell(_)){e.preventDefault();let A=_;if(!_.contains(h)&&t.isListCell(A)&&t.isList(A.lastElementChild)){for(A=A.lastElementChild.lastElementChild;t.isListCell(A)&&t.isList(A.lastElementChild);)A=A.lastElementChild&&A.lastElementChild.lastElementChild;_=A}let v=_===y.parentNode?y.previousSibling:_.lastChild;v||(v=t.createTextNode(t.zeroWidthSpace),y.parentNode.insertBefore(v,y.parentNode.firstChild));const N=v.nodeType===3?v.textContent.length:1,P=h.childNodes;let V=v,U=P[0];for(;U=P[0];)_.insertBefore(U,V.nextSibling),V=U;t.removeItem(h),y.children.length===0&&t.removeItem(y),a.setRange(v,N,v,N),a.history.push(!0)}}break}if(!u&&f.startOffset===0){let _=!0,A=S;for(;A&&A!==y&&!t.isWysiwygDiv(A);){if(A.previousSibling&&(A.previousSibling.nodeType===1||!t.onlyZeroWidthSpace(A.previousSibling.textContent.trim()))){_=!1;break}A=A.parentNode}if(_&&y.parentNode){e.preventDefault(),a.detachRangeFormatElement(y,t.isListCell(h)?[h]:null,null,!1,!1),a.history.push(!0);break}}}if(!u&&h&&(f.startOffset===0||i===h&&h.childNodes[f.startOffset])){const _=i===h?h.childNodes[f.startOffset]:i,A=h.previousSibling,v=(S.nodeType===3||t.isBreak(S))&&!S.previousSibling&&f.startOffset===0;if(_&&!_.previousSibling&&(S&&t.isComponent(S.previousSibling)||v&&t.isComponent(A))){const N=a.getFileComponent(A);N?(e.preventDefault(),e.stopPropagation(),h.textContent.length===0&&t.removeItem(h),a.selectComponent(N.target,N.pluginName)===!1&&a.blur()):t.isComponent(A)&&(e.preventDefault(),e.stopPropagation(),t.removeItem(A));break}if(_&&t.isNonEditable(_.previousSibling)){e.preventDefault(),e.stopPropagation(),t.removeItem(_.previousSibling);break}}break;case 46:if(m){e.preventDefault(),e.stopPropagation(),a.plugins[m].destroy.call(a);break}if(u&&p._hardDelete()){e.preventDefault(),e.stopPropagation();break}if(p._isUneditableNode(f,!1)){e.preventDefault(),e.stopPropagation();break}if((t.isFormatElement(i)||i.nextSibling===null||t.onlyZeroWidthSpace(i.nextSibling)&&i.nextSibling.nextSibling===null)&&f.startOffset===i.textContent.length){const _=h.nextElementSibling;if(!_)break;if(t.isComponent(_)){if(e.preventDefault(),t.onlyZeroWidthSpace(h)&&(t.removeItem(h),t.isTable(_))){let v=t.getChildElement(_,t.isCell,!1);v=v.firstElementChild||v,a.setRange(v,0,v,0);break}const A=a.getFileComponent(_);A?(e.stopPropagation(),a.selectComponent(A.target,A.pluginName)===!1&&a.blur()):t.isComponent(_)&&(e.stopPropagation(),t.removeItem(_));break}}if(!u&&(a.isEdgePoint(f.endContainer,f.endOffset)||i===h&&h.childNodes[f.startOffset])){const _=i===h&&h.childNodes[f.startOffset]||i;if(_&&t.isNonEditable(_.nextSibling)){e.preventDefault(),e.stopPropagation(),t.removeItem(_.nextSibling);break}else if(t.isComponent(_)){e.preventDefault(),e.stopPropagation(),t.removeItem(_);break}}if(!u&&a._isEdgeFormat(f.endContainer,f.endOffset,"end")&&t.isFormatElement(h.nextElementSibling)&&(a._formatAttrsTemp=h.attributes),h=t.getFormatElement(f.startContainer,null),y=t.getRangeFormatElement(h,null),t.isListCell(h)&&t.isList(y)&&(i===h||i.nodeType===3&&(!i.nextSibling||t.isList(i.nextSibling))&&(t.getFormatElement(f.startContainer,null)!==t.getFormatElement(f.endContainer,null)?y.contains(f.endContainer):f.endOffset===i.textContent.length&&f.collapsed))){f.startContainer!==f.endContainer&&a.removeNode();let _=t.getArrayItem(h.children,t.isList,!1);if(_=_||h.nextElementSibling||y.parentNode.nextElementSibling,_&&(t.isList(_)||t.getArrayItem(_.children,t.isList,!1))){e.preventDefault();let A,v;if(t.isList(_)){const N=_.firstElementChild;for(v=N.childNodes,A=v[0];v[0];)h.insertBefore(v[0],_);t.removeItem(N)}else{for(A=_.firstChild,v=_.childNodes;v[0];)h.appendChild(v[0]);t.removeItem(_)}a.setRange(A,0,A,0),a.history.push(!0)}break}break;case 9:if(m||o.tabDisable||(e.preventDefault(),c||d||t.isWysiwygDiv(i)))break;const x=!f.collapsed||a.isEdgePoint(f.startContainer,f.startOffset),D=a.getSelectedElements(null);i=a.getSelectionNode();const z=[];let O=[],H=t.isListCell(D[0]),M=t.isListCell(D[D.length-1]),T={sc:f.startContainer,so:f.startOffset,ec:f.endContainer,eo:f.endOffset};for(let _=0,A=D.length,v;_<A;_++)if(v=D[_],t.isListCell(v)){if(!v.previousElementSibling&&!r)continue;z.push(v)}else O.push(v);if(z.length>0&&x&&a.plugins.list)T=a.plugins.list.editInsideList.call(a,r,z);else{const _=t.getParentElement(i,t.isCell);if(_&&x){const A=t.getParentElement(_,"table"),v=t.getListChildren(A,t.isCell);let N=r?t.prevIdx(v,_):t.nextIdx(v,_);N===v.length&&!r&&(N=0),N===-1&&r&&(N=v.length-1);let P=v[N];if(!P)break;P=P.firstElementChild||P,a.setRange(P,0,P,0);break}O=O.concat(z),H=M=null}if(O.length>0)if(r){const _=O.length-1;for(let N=0,P;N<=_;N++){P=O[N].childNodes;for(let V=0,U=P.length,Z;V<U&&(Z=P[V],!!Z);V++)if(!t.onlyZeroWidthSpace(Z)){/^\s{1,4}$/.test(Z.textContent)?t.removeItem(Z):/^\s{1,4}/.test(Z.textContent)&&(Z.textContent=Z.textContent.replace(/^\s{1,4}/,""));break}}const A=t.getChildElement(O[0],"text",!1),v=t.getChildElement(O[_],"text",!0);!H&&A&&(T.sc=A,T.so=0),!M&&v&&(T.ec=v,T.eo=v.textContent.length)}else{const _=t.createTextNode(new E.Array(a._variable.tabSize+1).join(" "));if(O.length===1){const A=a.insertNode(_,null,!0);if(!A)return!1;H||(T.sc=_,T.so=A.endOffset),M||(T.ec=_,T.eo=A.endOffset)}else{const A=O.length-1;for(let P=0,V;P<=A;P++)V=O[P].firstChild,V&&(t.isBreak(V)?O[P].insertBefore(_.cloneNode(!1),V):V.textContent=_.textContent+V.textContent);const v=t.getChildElement(O[0],"text",!1),N=t.getChildElement(O[A],"text",!0);!H&&v&&(T.sc=v,T.so=0),!M&&N&&(T.ec=N,T.eo=N.textContent.length)}}a.setRange(T.sc,T.so,T.ec,T.eo),a.history.push(!1);break;case 13:const k=t.getFreeFormatElement(i,null);if(a._charTypeHTML){let _="";if(!r&&k||r?_="<br>":_="<"+h.nodeName+"><br></"+h.nodeName+">",!a.checkCharCount(_,"byte-html"))return e.preventDefault(),!1}if(!r&&!m){const _=a._isEdgeFormat(f.endContainer,f.endOffset,"end"),A=a._isEdgeFormat(f.startContainer,f.startOffset,"start");if(_&&(/^H[1-6]$/i.test(h.nodeName)||/^HR$/i.test(h.nodeName))){p._enterPrevent(e);let v=null;const N=a.appendFormatTag(h,o.defaultTag);if(_&&_.length>0){v=_.pop();const P=v;for(;_.length>0;)v=v.appendChild(_.pop());N.appendChild(P)}if(v=v?v.appendChild(N.firstChild):N.firstChild,t.isBreak(v)){const P=t.createTextNode(t.zeroWidthSpace);v.parentNode.insertBefore(P,v),a.setRange(P,1,P,1)}else a.setRange(v,0,v,0);break}else if(y&&h&&!t.isCell(y)&&!/^FIGCAPTION$/i.test(y.nodeName)){const v=a.getRange();if(a.isEdgePoint(v.endContainer,v.endOffset)&&t.isList(i.nextSibling)){p._enterPrevent(e);const N=t.createElement("LI"),P=t.createElement("BR");N.appendChild(P),h.parentNode.insertBefore(N,h.nextElementSibling),N.appendChild(i.nextSibling),a.setRange(P,1,P,1);break}if((v.commonAncestorContainer.nodeType!==3||!v.commonAncestorContainer.nextElementSibling)&&t.onlyZeroWidthSpace(h.innerText.trim())&&!t.isListCell(h.nextElementSibling)){p._enterPrevent(e);let N=null;if(t.isListCell(y.parentNode)){const P=h.parentNode.parentNode;y=P.parentNode;const V=t.createElement("LI");V.innerHTML="<br>",t.copyTagAttributes(V,h,o.lineAttrReset),N=V,y.insertBefore(N,P.nextElementSibling)}else{const P=t.isCell(y.parentNode)?"DIV":t.isList(y.parentNode)?"LI":t.isFormatElement(y.nextElementSibling)&&!t.isRangeFormatElement(y.nextElementSibling)?y.nextElementSibling.nodeName:t.isFormatElement(y.previousElementSibling)&&!t.isRangeFormatElement(y.previousElementSibling)?y.previousElementSibling.nodeName:o.defaultTag;N=t.createElement(P),t.copyTagAttributes(N,h,o.lineAttrReset);const V=a.detachRangeFormatElement(y,[h],null,!0,!0);V.cc.insertBefore(N,V.ec)}N.innerHTML="<br>",t.removeItemAllParents(h,null,null),a.setRange(N,1,N,1);break}}if(k){p._enterPrevent(e);const v=i===k,N=a.getSelection(),P=i.childNodes,V=N.focusOffset,U=i.previousElementSibling,Z=i.nextSibling;if(!t.isClosureFreeFormatElement(k)&&P&&(v&&f.collapsed&&P.length-1<=V+1&&t.isBreak(P[V])&&(!P[V+1]||(!P[V+2]||t.onlyZeroWidthSpace(P[V+2].textContent))&&P[V+1].nodeType===3&&t.onlyZeroWidthSpace(P[V+1].textContent))&&V>0&&t.isBreak(P[V-1])||!v&&t.onlyZeroWidthSpace(i.textContent)&&t.isBreak(U)&&(t.isBreak(U.previousSibling)||!t.onlyZeroWidthSpace(U.previousSibling.textContent))&&(!Z||!t.isBreak(Z)&&t.onlyZeroWidthSpace(Z.textContent)))){v?t.removeItem(P[V-1]):t.removeItem(i);const W=a.appendFormatTag(k,t.isFormatElement(k.nextElementSibling)&&!t.isRangeFormatElement(k.nextElementSibling)?k.nextElementSibling:null);t.copyFormatAttributes(W,k),a.setRange(W,1,W,1);break}if(v){F.insertHTML(f.collapsed&&t.isBreak(f.startContainer.childNodes[f.startOffset-1])?"<br>":"<br><br>",!0,!1);let W=N.focusNode;const $=N.focusOffset;k===W&&(W=W.childNodes[$-V>1?$-1:$]),a.setRange(W,1,W,1)}else{const W=N.focusNode.nextSibling,$=t.createElement("BR");a.insertNode($,null,!1);const q=$.previousSibling,Y=$.nextSibling;!t.isBreak(W)&&!t.isBreak(q)&&(!Y||t.onlyZeroWidthSpace(Y))?($.parentNode.insertBefore($.cloneNode(!1),$),a.setRange($,1,$,1)):a.setRange(Y,0,Y,0)}p._onShortcutKey=!0;break}if(f.collapsed&&(A||_)){p._enterPrevent(e);const v=t.createElement("BR"),N=t.createElement(h.nodeName);t.copyTagAttributes(N,h,o.lineAttrReset);let P=v;do{if(!t.isBreak(i)&&i.nodeType===1){const V=i.cloneNode(!1);V.appendChild(P),P=V}i=i.parentNode}while(h!==i&&h.contains(i));N.appendChild(P),h.parentNode.insertBefore(N,A&&!_?h:h.nextElementSibling),_&&a.setRange(v,1,v,1);break}if(h){e.stopPropagation();let v,N=0;if(f.collapsed)t.onlyZeroWidthSpace(h)?v=a.appendFormatTag(h,h.cloneNode(!1)):v=t.splitElement(f.endContainer,f.endOffset,t.getElementDepth(h));else{const P=t.getFormatElement(f.startContainer,null)!==t.getFormatElement(f.endContainer,null),V=h.cloneNode(!1);V.innerHTML="<br>";const U=a.removeNode();if(v=t.getFormatElement(U.container,null),!v){t.isWysiwygDiv(U.container)&&(p._enterPrevent(e),n.element.wysiwyg.appendChild(V),v=V,t.copyTagAttributes(v,h,o.lineAttrReset),a.setRange(v,N,v,N));break}const Z=t.getRangeFormatElement(U.container);if(v=v.contains(Z)?t.getChildElement(Z,t.getFormatElement.bind(t)):v,P){if(_&&!A)v.parentNode.insertBefore(V,!U.prevContainer||U.container===U.prevContainer?v.nextElementSibling:v),v=V,N=0;else if(N=U.offset,A){const W=v.parentNode.insertBefore(V,v);_&&(v=W,N=0)}}else _&&A?(v.parentNode.insertBefore(V,U.prevContainer&&U.container===U.prevContainer?v.nextElementSibling:v),v=V,N=0):v=t.splitElement(U.container,U.offset,t.getElementDepth(h))}p._enterPrevent(e),t.copyTagAttributes(v,h,o.lineAttrReset),a.setRange(v,N,v,N);break}}if(u)break;if(y&&t.getParentElement(y,"FIGCAPTION")&&t.getParentElement(y,t.isList)&&(p._enterPrevent(e),h=a.appendFormatTag(h,null),a.setRange(h,0,h,0)),m){e.preventDefault(),e.stopPropagation(),a.containerOff(),a.controllersOff();const _=n[m],A=_._container,v=A.previousElementSibling||A.nextElementSibling;let N=null;t.isListCell(A.parentNode)?N=t.createElement("BR"):(N=t.createElement(t.isFormatElement(v)&&!t.isRangeFormatElement(v)?v.nodeName:o.defaultTag),N.innerHTML="<br>"),r?A.parentNode.insertBefore(N,A):A.parentNode.insertBefore(N,A.nextElementSibling),a.callPlugin(m,function(){a.selectComponent(_._element,m)===!1&&a.blur()},null)}break;case 27:if(m)return e.preventDefault(),e.stopPropagation(),a.controllersOff(),!1;break}if(r&&s===16){e.preventDefault(),e.stopPropagation();const b=a.plugins.table;if(b&&!b._shift&&!b._ref){const S=t.getParentElement(h,t.isCell);if(S){b.onTableCellMultiSelect.call(a,S,!0);return}}}else if(r&&(t.isOSX_IOS?d:c)&&s===32){e.preventDefault(),e.stopPropagation();const b=a.insertNode(t.createTextNode(" "));if(b&&b.container){a.setRange(b.container,b.endOffset,b.container,b.endOffset);return}}if(t.isIE&&!c&&!d&&!u&&!p._nonTextKeyCode.test(s)&&t.isBreak(f.commonAncestorContainer)){const b=t.createTextNode(t.zeroWidthSpace);a.insertNode(b,null,!1),a.setRange(b,1,b,1)}p._directionKeyCode.test(s)&&(a._editorRange(),p._applyTagEffects())}},_onKeyDown_wysiwyg_arrowKey:function(e){if(e.shiftKey)return;let i=a.getSelectionNode();const s=function(d,f=0){if(e.preventDefault(),e.stopPropagation(),!d)return;let u=a.getFileComponent(d);u?a.selectComponent(u.target,u.pluginName):(a.setRange(d,f,d,f),a.controllersOff())},r=t.getParentElement(i,"table");if(r){const d=t.getParentElement(i,"tr"),f=t.getParentElement(i,"td");let u=f,m=f;if(f){for(;u.firstChild;)u=u.firstChild;for(;m.lastChild;)m=m.lastChild}let h=i;for(;h.firstChild;)h=h.firstChild;const y=h===u,B=h===m;let b=null,S=0;if(e.keyCode===38&&y){const x=d&&d.previousElementSibling;for(x?b=x.children[f.cellIndex]:b=t.getPreviousDeepestNode(r,a.context.element.wysiwyg);b.lastChild;)b=b.lastChild;b&&(S=b.textContent.length)}else if(e.keyCode===40&&B){const x=d&&d.nextElementSibling;for(x?b=x.children[f.cellIndex]:b=t.getNextDeepestNode(r,a.context.element.wysiwyg);b.firstChild;)b=b.firstChild}if(b)return s(b,S),!1}const c=a.getFileComponent(i);if(c){const d=/37|38/.test(e.keyCode),f=/39|40/.test(e.keyCode);if(d){const u=t.getPreviousDeepestNode(c.target,a.context.element.wysiwyg);s(u,u&&u.textContent.length)}else if(f){const u=t.getNextDeepestNode(c.target,a.context.element.wysiwyg);s(u)}}},onKeyUp_wysiwyg:function(e){if(p._onShortcutKey)return;a._editorRange();const i=e.keyCode,s=e.ctrlKey||e.metaKey||i===91||i===92||i===224,r=e.altKey;if(a.isReadOnly){!s&&p._cursorMoveKeyCode.test(i)&&p._applyTagEffects();return}const c=a.getRange();let d=a.getSelectionNode();if(a._isBalloon&&(a._isBalloonAlways&&i!==27||!c.collapsed))if(a._isBalloonAlways)i!==27&&p._showToolbarBalloonDelay();else{p._showToolbarBalloon();return}let f=d;for(;f.firstChild;)f=f.firstChild;const u=a.getFileComponent(f);if(!(e.keyCode===16||e.shiftKey)&&u?a.selectComponent(u.target,u.pluginName):a.currentFileComponentInfo&&a.controllersOff(),i===8&&t.isWysiwygDiv(d)&&d.textContent===""&&d.children.length===0){e.preventDefault(),e.stopPropagation(),d.innerHTML="";const b=t.createElement(t.isFormatElement(a._variable.currentNodes[0])?a._variable.currentNodes[0]:o.defaultTag);b.innerHTML="<br>",d.appendChild(b),a.setRange(b,0,b,0),p._applyTagEffects(),a.history.push(!1);return}const m=t.getFormatElement(d,null),h=t.getRangeFormatElement(d,null),y=a._formatAttrsTemp;if(y){for(let b=0,S=y.length;b<S;b++){if(i===13&&/^id$/i.test(y[b].name)){m.removeAttribute("id");continue}m.setAttribute(y[b].name,y[b].value)}a._formatAttrsTemp=null}if(!m&&c.collapsed&&!t.isComponent(d)&&!t.isList(d)&&a._setDefaultFormat(t.isRangeFormatElement(h)?"DIV":o.defaultTag)!==null&&(d=a.getSelectionNode()),!s&&!r&&!p._nonTextKeyCode.test(i)&&d.nodeType===3&&t.zeroWidthRegExp.test(d.textContent)&&!(e.isComposing!==void 0?e.isComposing:p._IEisComposing)){let b=c.startOffset,S=c.endOffset;const x=(d.textContent.substring(0,S).match(p._frontZeroWidthReg)||"").length;b=c.startOffset-x,S=c.endOffset-x,d.textContent=d.textContent.replace(t.zeroWidthRegExp,""),a.setRange(d,b<0?0:b,d,S<0?0:S)}a._charCount(""),!(typeof F.onKeyUp=="function"&&F.onKeyUp(e,a)===!1)&&!s&&!r&&!p._historyIgnoreKeyCode.test(i)&&a.history.push(!0)},onScroll_wysiwyg:function(e){a.controllersOff(),a._isBalloon&&p._hideToolbar(),typeof F.onScroll=="function"&&F.onScroll(e,a)},onFocus_wysiwyg:function(e){a._antiBlur||(a.hasFocus=!0,E.setTimeout(p._applyTagEffects),a._isInline&&p._showToolbarInline(),typeof F.onFocus=="function"&&F.onFocus(e,a))},onBlur_wysiwyg:function(e){a._antiBlur||a._variable.isCodeView||(a.hasFocus=!1,a.effectNode=null,a.controllersOff(),(a._isInline||a._isBalloon)&&p._hideToolbar(),a._setKeyEffect([]),a._variable.currentNodes=[],a._variable.currentNodesMap=[],o.showPathLabel&&(n.element.navigation.textContent=""),typeof F.onBlur=="function"&&F.onBlur(e,a,this))},onMouseDown_resizingBar:function(e){e.stopPropagation(),a.submenuOff(),a.controllersOff(),a._variable.resizeClientY=e.clientY,n.element.resizeBackground.style.display="block";function i(){n.element.resizeBackground.style.display="none",L.removeEventListener("mousemove",p._resize_editor),L.removeEventListener("mouseup",i)}L.addEventListener("mousemove",p._resize_editor),L.addEventListener("mouseup",i)},_resize_editor:function(e){const i=n.element.editorArea.offsetHeight+(e.clientY-a._variable.resizeClientY),s=i<a._variable.minResizingSize?a._variable.minResizingSize:i;n.element.wysiwygFrame.style.height=n.element.code.style.height=s+"px",a._variable.resizeClientY=e.clientY,t.isResizeObserverSupported||a.__callResizeFunction(s,null)},onResize_window:function(){t.isResizeObserverSupported||a.resetResponsiveToolbar();const e=n.element.toolbar,i=e.style.display==="none"||a._isInline&&!a._inlineToolbarAttr.isShow;if(!(e.offsetWidth===0&&!i)){if(n.fileBrowser&&n.fileBrowser.area.style.display==="block"&&(n.fileBrowser.body.style.maxHeight=E.innerHeight-n.fileBrowser.header.offsetHeight-50+"px"),a.submenuActiveButton&&a.submenu&&a._setMenuPosition(a.submenuActiveButton,a.submenu),a._variable.isFullScreen){a._variable.innerHeight_fullScreen+=E.innerHeight-e.offsetHeight-a._variable.innerHeight_fullScreen,n.element.editorArea.style.height=a._variable.innerHeight_fullScreen+"px";return}if(a._variable.isCodeView&&a._isInline){p._showToolbarInline();return}a._iframeAutoHeight(),a._sticky&&(e.style.width=n.element.topArea.offsetWidth-2+"px",p.onScroll_window())}},onScroll_window:function(){if(a._variable.isFullScreen||n.element.toolbar.offsetWidth===0||o.stickyToolbar<0)return;const e=n.element,i=e.editorArea.offsetHeight,s=(this.scrollY||L.documentElement.scrollTop)+o.stickyToolbar,r=p._getEditorOffsets(o.toolbarContainer).top-(a._isInline?e.toolbar.offsetHeight:0),c=a._isInline&&s-r>0?s-r-n.element.toolbar.offsetHeight:0;s<r?p._offStickyToolbar():s+a._variable.minResizingSize>=i+r?(a._sticky||p._onStickyToolbar(c),e.toolbar.style.top=c+i+r+o.stickyToolbar-s-a._variable.minResizingSize+"px"):s>=r&&p._onStickyToolbar(c)},_getEditorOffsets:function(e){let i=e||n.element.topArea,s=0,r=0,c=0;for(;i;)s+=i.offsetTop,r+=i.offsetLeft,c+=i.scrollTop,i=i.offsetParent;return{top:s,left:r,scroll:c}},_getPageBottomSpace:function(){return L.documentElement.scrollHeight-(p._getEditorOffsets(null).top+n.element.topArea.offsetHeight)},_onStickyToolbar:function(e){const i=n.element;!a._isInline&&!o.toolbarContainer&&(i._stickyDummy.style.height=i.toolbar.offsetHeight+"px",i._stickyDummy.style.display="block"),i.toolbar.style.top=o.stickyToolbar+e+"px",i.toolbar.style.width=a._isInline?a._inlineToolbarAttr.width:i.toolbar.offsetWidth+"px",t.addClass(i.toolbar,"se-toolbar-sticky"),a._sticky=!0},_offStickyToolbar:function(){const e=n.element;e._stickyDummy.style.display="none",e.toolbar.style.top=a._isInline?a._inlineToolbarAttr.top:"",e.toolbar.style.width=a._isInline?a._inlineToolbarAttr.width:"",e.editorArea.style.marginTop="",t.removeClass(e.toolbar,"se-toolbar-sticky"),a._sticky=!1},_codeViewAutoHeight:function(){a._variable.isFullScreen||(n.element.code.style.height=n.element.code.scrollHeight+"px")},_hardDelete:function(){const e=a.getRange(),i=e.startContainer,s=e.endContainer,r=t.getRangeFormatElement(i),c=t.getRangeFormatElement(s),d=t.isCell(r),f=t.isCell(c),u=e.commonAncestorContainer;if((d&&!r.previousElementSibling&&!r.parentElement.previousElementSibling||f&&!c.nextElementSibling&&!c.parentElement.nextElementSibling)&&r!==c)if(!d)t.removeItem(t.getParentElement(c,function(y){return u===y.parentNode}));else if(!f)t.removeItem(t.getParentElement(r,function(y){return u===y.parentNode}));else return t.removeItem(t.getParentElement(r,function(y){return u===y.parentNode})),a.nativeFocus(),!0;const m=i.nodeType===1?t.getParentElement(i,".se-component"):null,h=s.nodeType===1?t.getParentElement(s,".se-component"):null;return m&&t.removeItem(m),h&&t.removeItem(h),!1},onPaste_wysiwyg:function(e){const i=t.isIE?E.clipboardData:e.clipboardData;return i?p._dataTransferAction("paste",e,i):!0},_setClipboardComponent:function(e,i,s){e.preventDefault(),e.stopPropagation(),s.setData("text/html",i.component.outerHTML)},onCopy_wysiwyg:function(e){const i=t.isIE?E.clipboardData:e.clipboardData;if(typeof F.onCopy=="function"&&F.onCopy(e,i,a)===!1)return e.preventDefault(),e.stopPropagation(),!1;const s=a.currentFileComponentInfo;s&&!t.isIE&&(p._setClipboardComponent(e,s,i),t.addClass(s.component,"se-component-copy"),E.setTimeout(function(){t.removeClass(s.component,"se-component-copy")},150))},onSave_wysiwyg:function(e){if(typeof F.onSave=="function"){F.onSave(e,a);return}},onCut_wysiwyg:function(e){const i=t.isIE?E.clipboardData:e.clipboardData;if(typeof F.onCut=="function"&&F.onCut(e,i,a)===!1)return e.preventDefault(),e.stopPropagation(),!1;const s=a.currentFileComponentInfo;s&&!t.isIE&&(p._setClipboardComponent(e,s,i),t.removeItem(s.component),a.controllersOff()),E.setTimeout(function(){a.history.push(!1)})},onDrop_wysiwyg:function(e){if(a.isReadOnly||t.isIE)return e.preventDefault(),e.stopPropagation(),!1;const i=e.dataTransfer;return i?(p._setDropLocationSelection(e),a.removeNode(),document.body.contains(a.currentControllerTarget)||a.controllersOff(),p._dataTransferAction("drop",e,i)):!0},_setDropLocationSelection:function(e){const i={startContainer:null,startOffset:null,endContainer:null,endOffset:null};let s=null;if(e.rangeParent?(i.startContainer=e.rangeParent,i.startOffset=e.rangeOffset,i.endContainer=e.rangeParent,i.endOffset=e.rangeOffset):a._wd.caretRangeFromPoint?s=a._wd.caretRangeFromPoint(e.clientX,e.clientY):s=a.getRange(),s&&(i.startContainer=s.startContainer,i.startOffset=s.startOffset,i.endContainer=s.endContainer,i.endOffset=s.endOffset),i.startContainer===i.endContainer){const r=t.getParentElement(i.startContainer,t.isComponent);r&&(i.startContainer=r,i.startOffset=0,i.endContainer=r,i.endOffset=0)}a.setRange(i.startContainer,i.startOffset,i.endContainer,i.endOffset)},_dataTransferAction:function(e,i,s){let r,c;if(t.isIE){r=s.getData("Text");const d=a.getRange(),f=t.createElement("DIV"),u={sc:d.startContainer,so:d.startOffset,ec:d.endContainer,eo:d.endOffset};return f.setAttribute("contenteditable",!0),f.style.cssText="position:absolute; top:0; left:0; width:1px; height:1px; overflow:hidden;",n.element.relative.appendChild(f),f.focus(),E.setTimeout(function(){c=f.innerHTML,t.removeItem(f),a.setRange(u.sc,u.so,u.ec,u.eo),p._setClipboardData(e,i,r,c,s)}),!0}else if(r=s.getData("text/plain"),c=s.getData("text/html"),p._setClipboardData(e,i,r,c,s)===!1)return i.preventDefault(),i.stopPropagation(),!1},_setClipboardData:function(e,i,s,r,c){const d=/class=["']*Mso(Normal|List)/i.test(r)||/content=["']*Word.Document/i.test(r)||/content=["']*OneNote.File/i.test(r)||/content=["']*Excel.Sheet/i.test(r);!r?r=t._HTMLConvertor(s).replace(/\n/g,"<br>"):(r=r.replace(/^<html>\r?\n?<body>\r?\n?\x3C!--StartFragment--\>|\x3C!--EndFragment-->\r?\n?<\/body\>\r?\n?<\/html>$/g,""),d&&(r=r.replace(/\n/g," "),s=s.replace(/\n/g," ")),r=a.cleanHTML(r,a.pasteTagsWhitelistRegExp,a.pasteTagsBlacklistRegExp));const u=a._charCount(a._charTypeHTML?r:s);if(e==="paste"&&typeof F.onPaste=="function"){const h=F.onPaste(i,r,u,a);if(h===!1)return!1;if(typeof h=="string"){if(!h)return!1;r=h}}if(e==="drop"&&typeof F.onDrop=="function"){const h=F.onDrop(i,r,u,a);if(h===!1)return!1;if(typeof h=="string"){if(!h)return!1;r=h}}const m=c.files;if(m.length>0&&!d)return/^image/.test(m[0].type)&&a.plugins.image&&F.insertImage(m),!1;if(!u)return!1;if(r)return F.insertHTML(r,!0,!1),!1},onMouseMove_wysiwyg:function(e){if(a.isDisabled||a.isReadOnly)return!1;const i=t.getParentElement(e.target,t.isComponent),s=a._lineBreaker.style;if(i&&!a.currentControllerName){const r=n.element;let c=0,d=r.wysiwyg;do c+=d.scrollTop,d=d.parentElement;while(d&&!/^(BODY|HTML)$/i.test(d.nodeName));const f=r.wysiwyg.scrollTop,u=p._getEditorOffsets(null),m=t.getOffset(i,r.wysiwygFrame).top+f,h=e.pageY+c+(o.iframe&&!o.toolbarContainer?r.toolbar.offsetHeight:0),y=m+(o.iframe?c:u.top),B=t.isListCell(i.parentNode);let b="",S="";if((B?!i.previousSibling:!t.isFormatElement(i.previousElementSibling))&&h<y+20)S=m,b="t";else if((B?!i.nextSibling:!t.isFormatElement(i.nextElementSibling))&&h>y+i.offsetHeight-20)S=m+i.offsetHeight,b="b";else{s.display="none";return}a._variable._lineBreakComp=i,a._variable._lineBreakDir=b,s.top=S-f+"px",a._lineBreakerButton.style.left=t.getOffset(i).left+i.offsetWidth/2-15+"px",s.display="block"}else s.display!=="none"&&(s.display="none")},_enterPrevent(e){e.preventDefault(),t.isMobile&&a.__focusTemp.focus()},_onMouseDown_lineBreak:function(e){e.preventDefault()},_onLineBreak:function(e){e.preventDefault();const i=a._variable._lineBreakComp,s=this?this:a._variable._lineBreakDir,r=t.isListCell(i.parentNode),c=t.createElement(r?"BR":t.isCell(i.parentNode)?"DIV":o.defaultTag);if(r||(c.innerHTML="<br>"),a._charTypeHTML&&!a.checkCharCount(c.outerHTML,"byte-html"))return;i.parentNode.insertBefore(c,s==="t"?i:i.nextSibling),a._lineBreaker.style.display="none",a._variable._lineBreakComp=null;const d=r?c:c.firstChild;a.setRange(d,1,d,1),a.history.push(!1)},_resizeObserver:null,_toolbarObserver:null,_addEvent:function(){const e=o.iframe?a._ww:n.element.wysiwyg;t.isResizeObserverSupported&&(this._resizeObserver=new E.ResizeObserver(function(i){a.__callResizeFunction(-1,i[0])})),n.element.toolbar.addEventListener("mousedown",p._buttonsEventHandler,!1),n.element._menuTray.addEventListener("mousedown",p._buttonsEventHandler,!1),n.element.toolbar.addEventListener("click",p.onClick_toolbar,!1),e.addEventListener("mousedown",p.onMouseDown_wysiwyg,!1),e.addEventListener("click",p.onClick_wysiwyg,!1),e.addEventListener(t.isIE?"textinput":"input",p.onInput_wysiwyg,!1),e.addEventListener("keydown",p.onKeyDown_wysiwyg,!1),e.addEventListener("keyup",p.onKeyUp_wysiwyg,!1),e.addEventListener("paste",p.onPaste_wysiwyg,!1),e.addEventListener("copy",p.onCopy_wysiwyg,!1),e.addEventListener("cut",p.onCut_wysiwyg,!1),e.addEventListener("drop",p.onDrop_wysiwyg,!1),e.addEventListener("scroll",p.onScroll_wysiwyg,!1),e.addEventListener("focus",p.onFocus_wysiwyg,!1),e.addEventListener("blur",p.onBlur_wysiwyg,!1),p._lineBreakerBind={a:p._onLineBreak.bind(""),t:p._onLineBreak.bind("t"),b:p._onLineBreak.bind("b")},e.addEventListener("mousemove",p.onMouseMove_wysiwyg,!1),a._lineBreakerButton.addEventListener("mousedown",p._onMouseDown_lineBreak,!1),a._lineBreakerButton.addEventListener("click",p._lineBreakerBind.a,!1),n.element.lineBreaker_t.addEventListener("mousedown",p._lineBreakerBind.t,!1),n.element.lineBreaker_b.addEventListener("mousedown",p._lineBreakerBind.b,!1),e.addEventListener("touchstart",p.onMouseDown_wysiwyg,{passive:!0,useCapture:!1}),e.addEventListener("touchend",p.onClick_wysiwyg,{passive:!0,useCapture:!1}),o.height==="auto"&&!o.codeMirrorEditor&&(n.element.code.addEventListener("keydown",p._codeViewAutoHeight,!1),n.element.code.addEventListener("keyup",p._codeViewAutoHeight,!1),n.element.code.addEventListener("paste",p._codeViewAutoHeight,!1)),n.element.resizingBar&&(/\d+/.test(o.height)&&o.resizeEnable?n.element.resizingBar.addEventListener("mousedown",p.onMouseDown_resizingBar,!1):t.addClass(n.element.resizingBar,"se-resizing-none")),p._setResponsiveToolbar(),t.isResizeObserverSupported&&(this._toolbarObserver=new E.ResizeObserver(a.resetResponsiveToolbar)),E.addEventListener("resize",p.onResize_window,!1),o.stickyToolbar>-1&&E.addEventListener("scroll",p.onScroll_window,!1)},_removeEvent:function(){const e=o.iframe?a._ww:n.element.wysiwyg;n.element.toolbar.removeEventListener("mousedown",p._buttonsEventHandler),n.element._menuTray.removeEventListener("mousedown",p._buttonsEventHandler),n.element.toolbar.removeEventListener("click",p.onClick_toolbar),e.removeEventListener("mousedown",p.onMouseDown_wysiwyg),e.removeEventListener("click",p.onClick_wysiwyg),e.removeEventListener(t.isIE?"textinput":"input",p.onInput_wysiwyg),e.removeEventListener("keydown",p.onKeyDown_wysiwyg),e.removeEventListener("keyup",p.onKeyUp_wysiwyg),e.removeEventListener("paste",p.onPaste_wysiwyg),e.removeEventListener("copy",p.onCopy_wysiwyg),e.removeEventListener("cut",p.onCut_wysiwyg),e.removeEventListener("drop",p.onDrop_wysiwyg),e.removeEventListener("scroll",p.onScroll_wysiwyg),e.removeEventListener("mousemove",p.onMouseMove_wysiwyg),a._lineBreakerButton.removeEventListener("mousedown",p._onMouseDown_lineBreak),a._lineBreakerButton.removeEventListener("click",p._lineBreakerBind.a),n.element.lineBreaker_t.removeEventListener("mousedown",p._lineBreakerBind.t),n.element.lineBreaker_b.removeEventListener("mousedown",p._lineBreakerBind.b),p._lineBreakerBind=null,e.removeEventListener("touchstart",p.onMouseDown_wysiwyg,{passive:!0,useCapture:!1}),e.removeEventListener("touchend",p.onClick_wysiwyg,{passive:!0,useCapture:!1}),e.removeEventListener("focus",p.onFocus_wysiwyg),e.removeEventListener("blur",p.onBlur_wysiwyg),n.element.code.removeEventListener("keydown",p._codeViewAutoHeight),n.element.code.removeEventListener("keyup",p._codeViewAutoHeight),n.element.code.removeEventListener("paste",p._codeViewAutoHeight),n.element.resizingBar&&n.element.resizingBar.removeEventListener("mousedown",p.onMouseDown_resizingBar),p._resizeObserver&&(p._resizeObserver.unobserve(n.element.wysiwygFrame),p._resizeObserver=null),p._toolbarObserver&&(p._toolbarObserver.unobserve(n.element._toolbarShadow),p._toolbarObserver=null),E.removeEventListener("resize",p.onResize_window),E.removeEventListener("scroll",p.onScroll_window)},_setResponsiveToolbar:function(){if(w.length===0){w=null;return}p._responsiveCurrentSize="default";const e=p._responsiveButtonSize=[],i=p._responsiveButtons={default:w[0]};for(let s=1,r=w.length,c,d;s<r;s++)d=w[s],c=d[0]*1,e.push(c),i[c]=d[1];e.sort(function(s,r){return s-r}).unshift("default")}},F={core:a,util:t,onload:null,onScroll:null,onMouseDown:null,onClick:null,onInput:null,onKeyDown:null,onKeyUp:null,onCopy:null,onCut:null,onFocus:null,onBlur:null,onChange:null,onSave:null,onDrop:null,onPaste:null,showInline:null,showController:null,toggleCodeView:null,toggleFullScreen:null,imageUploadHandler:null,videoUploadHandler:null,audioUploadHandler:null,onImageUploadBefore:null,onVideoUploadBefore:null,onAudioUploadBefore:null,onImageUpload:null,onVideoUpload:null,onAudioUpload:null,onImageUploadError:null,onVideoUploadError:null,onAudioUploadError:null,onResizeEditor:null,onSetToolbarButtons:null,setToolbarButtons:function(e){a.submenuOff(),a.containerOff(),a.moreLayerOff();const i=be._createToolBar(L,e,a.plugins,o);w=i.responsiveButtons,p._setResponsiveToolbar(),n.element.toolbar.replaceChild(i._buttonTray,n.element._buttonTray);const s=ve(n.element.originElement,a._getConstructed(n.element),o);n.element=s.element,n.tool=s.tool,o.iframe&&(n.element.wysiwyg=a._wd.body),a._recoverButtonStates(),a._cachingButtons(),a.history._resetCachingButton(),a.effectNode=null,a.hasFocus&&p._applyTagEffects(),a.isReadOnly&&t.setDisabledButtons(!0,a.resizingDisabledButtons),typeof F.onSetToolbarButtons=="function"&&F.onSetToolbarButtons(i._buttonTray.querySelectorAll("button"),a)},setOptions:function(e){p._removeEvent(),a._resetComponents(),t.removeClass(a._styleCommandMap.showBlocks,"active"),t.removeClass(a._styleCommandMap.codeView,"active"),a._variable.isCodeView=!1,a._iframeAuto=null,a.plugins=e.plugins||a.plugins;const i=[o,e].reduce(function(d,f){for(let u in f)if(t.hasOwn(f,u))if(u==="plugins"&&f[u]&&d[u]){let m=d[u],h=f[u];m=m.length?m:E.Object.keys(m).map(function(y){return m[y]}),h=h.length?h:E.Object.keys(h).map(function(y){return h[y]}),d[u]=h.filter(function(y){return m.indexOf(y)===-1}).concat(m)}else d[u]=f[u];return d},{}),s=n.element,r=s.wysiwyg.innerHTML,c=be._setOptions(i,n,o);c.callButtons&&(l=c.callButtons,a.initPlugins={}),c.plugins&&(a.plugins=g=c.plugins),s._menuTray.children.length===0&&(this._menuTray={}),w=c.toolbar.responsiveButtons,a.options=o=i,a.lang=C=o.lang,o.iframe&&s.wysiwygFrame.addEventListener("load",function(){t._setIframeDocument(this,o),a._setOptionsInit(s,r)}),s.editorArea.appendChild(s.wysiwygFrame),o.iframe||a._setOptionsInit(s,r)},setDefaultStyle:function(e){const i=o._editorStyles=t._setDefaultOptionStyle(o,e),s=n.element;s.topArea.style.cssText=i.top,s.code.style.cssText=o._editorStyles.frame,s.code.style.display="none",o.height==="auto"?s.code.style.overflow="hidden":s.code.style.overflow="",o.iframe?(s.wysiwygFrame.style.cssText=i.frame,s.wysiwyg.style.cssText=i.editor):s.wysiwygFrame.style.cssText=i.frame+i.editor},noticeOpen:function(e){a.notice.open.call(a,e)},noticeClose:function(){a.notice.close.call(a)},save:function(){const e=a.getContents(!1);n.element.originElement.value=e,p.onSave_wysiwyg(e,a)},getContext:function(){return n},getContents:function(e){return a.getContents(e)},getText:function(){return n.element.wysiwyg.textContent},getCharCount:function(e){return e=typeof e=="string"?e:o.charCounterType,a.getCharLength(a._charTypeHTML?n.element.wysiwyg.innerHTML:n.element.wysiwyg.textContent,e)},getImagesInfo:function(){return n.image?n.image._infoList:[]},getFilesInfo:function(e){return n[e]?n[e]._infoList:[]},insertImage:function(e){!a.plugins.image||!e||(a.initPlugins.image?a.plugins.image.submitAction.call(a,e):a.callPlugin("image",a.plugins.image.submitAction.bind(a,e),null),a.focus())},insertHTML:function(e,i,s,r){if(n.element.wysiwygFrame.contains(a.getSelection().focusNode)||a.focus(),typeof e=="string"){i||(e=a.cleanHTML(e,null,null));try{if(t.isListCell(t.getFormatElement(a.getSelectionNode(),null))){const S=L.createRange().createContextualFragment(e).childNodes;a._isFormatData(S)&&(e=a._convertListCell(S))}const d=L.createRange().createContextualFragment(e).childNodes;if(s){const b=a._charTypeHTML?"outerHTML":"textContent";let S="";for(let x=0,D=d.length;x<D;x++)S+=d[x][b];if(!a.checkCharCount(S,null))return}let f,u,m,h,y;for(;f=d[0];){if(h&&h.nodeType===3&&u&&u.nodeType===1&&t.isBreak(f)){h=f,t.removeItem(f);continue}m=a.insertNode(f,u,!1),u=m.container||m,y||(y=m),h=f}h.nodeType===3&&u.nodeType===1&&(u=h);const B=u.nodeType===3?m.endOffset||u.textContent.length:u.childNodes.length;r?a.setRange(y.container||y,y.startOffset||0,u,B):a.setRange(u,B,u,B)}catch(c){if(a.isDisabled||a.isReadOnly)return;console.warn("[SUNEDITOR.insertHTML.fail] "+c),a.execCommand("insertHTML",!1,e)}}else if(t.isComponent(e))a.insertComponent(e,!1,s,!1);else{let c=null;(t.isFormatElement(e)||t.isMedia(e))&&(c=t.getFormatElement(a.getSelectionNode(),null)),a.insertNode(e,c,s)}a.effectNode=null,a.focus(),a.history.push(!1)},setContents:function(e){a.setContents(e)},appendContents:function(e){const i=a.convertContentsForEditor(e);if(a._variable.isCodeView)a._setCodeView(a._getCodeView()+`
`+a.convertHTMLForCodeView(i,!1));else{const s=t.createElement("DIV");s.innerHTML=i;const r=n.element.wysiwyg,c=s.children;for(let d=0,f=c.length;d<f;d++)c[d]&&r.appendChild(c[d])}a.history.push(!1)},readOnly:function(e){a.isReadOnly=e,t.setDisabledButtons(!!e,a.resizingDisabledButtons),e?(a.controllersOff(),a.submenuActiveButton&&a.submenuActiveButton.disabled&&a.submenuOff(),a._moreLayerActiveButton&&a._moreLayerActiveButton.disabled&&a.moreLayerOff(),a.containerActiveButton&&a.containerActiveButton.disabled&&a.containerOff(),a.modalForm&&a.plugins.dialog.close.call(a),n.element.code.setAttribute("readOnly","true"),t.addClass(n.element.wysiwygFrame,"se-read-only")):(n.element.code.removeAttribute("readOnly"),t.removeClass(n.element.wysiwygFrame,"se-read-only")),o.codeMirrorEditor&&o.codeMirrorEditor.setOption("readOnly",!!e)},disable:function(){this.toolbar.disable(),this.wysiwyg.disable()},disabled:function(){this.disable()},enable:function(){this.toolbar.enable(),this.wysiwyg.enable()},enabled:function(){this.enable()},show:function(){const e=n.element.topArea.style;e.display==="none"&&(e.display=o.display)},hide:function(){n.element.topArea.style.display="none"},destroy:function(){a.submenuOff(),a.containerOff(),a.controllersOff(),a.notice&&a.notice.close.call(a),a.modalForm&&a.plugins.dialog.close.call(a),a.history._destroy(),p._removeEvent(),t.removeItem(n.element.toolbar),t.removeItem(n.element.topArea);for(let e in a.functions)t.hasOwn(a,e)&&delete a.functions[e];for(let e in a)t.hasOwn(a,e)&&delete a[e];for(let e in p)t.hasOwn(p,e)&&delete p[e];for(let e in n)t.hasOwn(n,e)&&delete n[e];for(let e in l)t.hasOwn(l,e)&&delete l[e];for(let e in this)t.hasOwn(this,e)&&delete this[e]},toolbar:{disable:function(){a.submenuOff(),a.moreLayerOff(),a.containerOff(),n.tool.cover.style.display="block"},disabled:function(){this.disable()},enable:function(){n.tool.cover.style.display="none"},enabled:function(){this.enable()},show:function(){a._isInline?p._showToolbarInline():(n.element.toolbar.style.display="",n.element._stickyDummy.style.display=""),p.onResize_window()},hide:function(){a._isInline?p._hideToolbar():(n.element.toolbar.style.display="none",n.element._stickyDummy.style.display="none"),p.onResize_window()}},wysiwyg:{disable:function(){a.controllersOff(),a.modalForm&&a.plugins.dialog.close.call(a),n.element.wysiwyg.setAttribute("contenteditable",!1),a.isDisabled=!0,o.codeMirrorEditor?o.codeMirrorEditor.setOption("readOnly",!0):n.element.code.setAttribute("disabled","disabled")},enable:function(){n.element.wysiwyg.setAttribute("contenteditable",!0),a.isDisabled=!1,o.codeMirrorEditor?o.codeMirrorEditor.setOption("readOnly",!1):n.element.code.removeAttribute("disabled")}}};a.functions=F,a.options=o;let I=n.element,K=I.originElement,G=I.topArea;return K.style.display="none",G.style.display="block",o.iframe&&I.wysiwygFrame.addEventListener("load",function(){t._setIframeDocument(this,o),a._editorInit(!1,o.value),o.value=null}),typeof K.nextElementSibling=="object"?K.parentNode.insertBefore(G,K.nextElementSibling):K.parentNode.appendChild(G),I.editorArea.appendChild(I.wysiwygFrame),I=K=G=null,o.iframe||(a._editorInit(!1,o.value),o.value=null),F}const Ae={init:function(n){return{create:(function(l,g){return this.create(l,g,n)}).bind(this)}},create:function(n,l,g){j._propertiesInit(),typeof l!="object"&&(l={}),g&&(l=[g,l].reduce(function(w,L){for(let E in L)if(j.hasOwn(L,E))if(E==="plugins"&&L[E]&&w[E]){let t=w[E],R=L[E];t=t.length?t:Object.keys(t).map(function(a){return t[a]}),R=R.length?R:Object.keys(R).map(function(a){return R[a]}),w[E]=R.filter(function(a){return t.indexOf(a)===-1}).concat(t)}else w[E]=L[E];return w},{}));const C=typeof n=="string"?document.getElementById(n):n;if(!C)throw Error(typeof n=="string"?'[SUNEDITOR.create.fail] The element for that id was not found (ID:"'+n+'")':"[SUNEDITOR.create.fail] suneditor requires textarea's element or id value");const o=be.init(C,l);if(o.constructed._top.id&&document.getElementById(o.constructed._top.id))throw Error('[SUNEDITOR.create.fail] The ID of the suneditor you are trying to create already exists (ID:"'+o.constructed._top.id+'")');return He(ve(C,o.constructed,o.options),o.pluginCallButtons,o.plugins,o.options.lang,l,o._responsiveButtons)}},Re=Object.freeze(Object.defineProperty({__proto__:null,default:Ae},Symbol.toStringTag,{value:"Module"}));export{Re as s};
