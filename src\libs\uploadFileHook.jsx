// ... existing imports ...
import React from "react";
import { uploadS3FilesProgressAPI } from "Src/services/workOrderService";

export const useFileUpload = () => {
  const [progress, setProgress] = React.useState(0);
  const [error, setError] = React.useState(null);
  const [isUploading, setIsUploading] = React.useState(false);

  const uploadFiles = async (formData) => {
    setIsUploading(true);
    setProgress(0);
    setError(null);

    try {
      const result = await uploadS3FilesProgressAPI(formData, setProgress);

      setIsUploading(false);
      return result;
    } catch (error) {
      setError(error);
      setIsUploading(false);
      throw error;
    }
  };

  return { uploadFiles, progress, error, isUploading };
};

// ... rest of the file ...
