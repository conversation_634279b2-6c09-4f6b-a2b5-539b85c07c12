import React, { useState, useEffect, useCallback, useRef } from "react";
import {
  CardElement,
  useStripe,
  useElements,
  Elements,
} from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";
import MkdSDK from "../../../utils/MkdSDK";
import <PERSON><PERSON><PERSON> from "Utils/TreeSDK";
import { refreshSubscriptionData } from "../../../globalContext";

// Initialize Stripe
const stripePromise = loadStripe(
  "pk_test_51QufojCyqqiotS2DrnWrDsIDJBRyZmFN93rnCuWphXRMZuEW77hYvE8iG7CuP6F5iSmKR8ZmEEdzRDc2NcdmUVDr003Qz6h4o5"
);

// Separate component for the payment form to prevent re-renders
const PaymentForm = React.memo(
  ({
    userDetails,
    selectedPlan,
    selectedInterval,
    getCurrentPrice,
    couponCode,
    setCouponCode,
    error,
    setError,
    processing,
    cardReady,
    setCardReady,
    handlePaymentSubmit,
    cardElementRef,
    couponValidation,
    setCouponValidation,
    getFinalPrice,
    shouldShowDiscount,
  }) => {
    const checkCoupon = async () => {
      if (!couponCode || couponCode.trim() === "") {
        setCouponValidation({
          isValidating: false,
          isValid: null,
          couponData: null,
          error: null,
        });
        return;
      }

      setCouponValidation((prev) => ({
        ...prev,
        isValidating: true,
        error: null,
      }));

      try {
        const tdk = new TreeSDK();
        const result = await tdk.getList("coupon", {
          filter: [`code,eq,${couponCode.trim()}`],
          join: ["coupon|code"],
        });

        console.log("Coupon validation result:", result);

        if (result && result.list && result.list.length > 0) {
          const couponData = result.list[0];

          // Check if coupon is active and not expired
          const now = new Date();
          const expiryDate = couponData.expires_at
            ? new Date(couponData.expires_at)
            : null;

          if (!couponData.active) {
            setCouponValidation({
              isValidating: false,
              isValid: false,
              couponData: null,
              error: "This coupon is no longer active",
            });
          } else if (expiryDate && expiryDate < now) {
            setCouponValidation({
              isValidating: false,
              isValid: false,
              couponData: null,
              error: "This coupon has expired",
            });
          } else {
            setCouponValidation({
              isValidating: false,
              isValid: true,
              couponData: couponData,
              error: null,
            });
          }
        } else {
          setCouponValidation({
            isValidating: false,
            isValid: false,
            couponData: null,
            error: "Invalid coupon code",
          });
        }
      } catch (error) {
        console.error("Error validating coupon:", error);
        setCouponValidation({
          isValidating: false,
          isValid: false,
          couponData: null,
          error: "Failed to validate coupon. Please try again.",
        });
      }
    };

    return (
      <form
        className="flex w-full flex-col gap-4 rounded-[16px]  bg-white p-8 shadow-[0_0_0_4px_#f4ebff,0_1px_2px_0px_rgba(10,13,18,0.06),0_1px_3px_0px_rgba(10,13,18,0.1)] md:w-full"
        onSubmit={handlePaymentSubmit}
        style={{
          pointerEvents: processing ? "none" : "auto",
          opacity: processing ? 0.7 : 1,
        }}
      >
        <div className="flex flex-col gap-6">
          <div>
            <label className="mb-2 block text-sm font-medium text-[#4A5B70]">
              Email
            </label>
            <input
              type="email"
              value={userDetails?.email || ""}
              disabled
              className="w-full rounded-lg border border-[#D1D6DE] bg-white p-3 text-[#414651] outline-none focus:border-primary/70"
            />
          </div>
          <div>
            <label className="mb-2 block text-sm font-medium text-[#4A5B70]">
              Card Details
            </label>
            <div className="rounded-lg border border-[#D1D6DE] bg-white p-4">
              <CardElement
                key="subscription-card-element" // Stable key to prevent unmounting
                options={{
                  hidePostalCode: true,
                  style: {
                    base: {
                      fontSize: "16px",
                      color: "#414651",
                      fontFamily:
                        'Inter, "Helvetica Neue", Helvetica, sans-serif',
                      "::placeholder": { color: "#818E9F" },
                    },
                    invalid: { color: "#fa755a", iconColor: "#fa755a" },
                  },
                }}
                onReady={(element) => {
                  console.log("✅ CardElement is ready");
                  cardElementRef.current = element;
                  setCardReady(true);
                }}
                onChange={(event) => {
                  if (event.error) {
                    console.error("❌ CardElement error:", event.error);
                    setError(event.error.message);
                  } else {
                    setError(""); // Clear error when card is valid
                  }
                }}
              />
            </div>
          </div>
          {/* <div>
            <label className="mb-2 block text-sm font-medium text-[#4A5B70]">
              Country
            </label>
            <input
              type="text"
              value="United States"
              className="w-full rounded-lg border border-[#D1D6DE] focus:border-primary/70 bg-white p-3 text-[#414651] outline-none"
              disabled
            />
          </div> */}
          <div>
            <label className="mb-2 block text-sm font-medium text-[#4A5B70]">
              Coupon Code (Optional)
            </label>
            <div className="relative">
              <input
                type="text"
                value={couponCode}
                onChange={(e) => {
                  setCouponCode(e.target.value);
                  // Reset validation when user types
                  if (couponValidation.isValid !== null) {
                    setCouponValidation({
                      isValidating: false,
                      isValid: null,
                      couponData: null,
                      error: null,
                    });
                  }
                }}
                placeholder="Enter coupon code if you have one"
                className={`w-full rounded-lg border bg-white p-3 text-[#414651] outline-none focus:border-primary/70 ${
                  couponCode.trim() !== "" ? "pr-20" : ""
                } ${
                  couponValidation.isValid === true
                    ? "border-green-500"
                    : couponValidation.isValid === false
                    ? "border-red-500"
                    : "border-[#D1D6DE]"
                }`}
              />
              {/* Validate button only appears when coupon is entered */}
              {couponCode.trim() !== "" && (
                <button
                  type="button"
                  onClick={checkCoupon}
                  disabled={couponValidation.isValidating}
                  className="absolute right-2 top-1/2 -translate-y-1/2 rounded bg-[#3C50E0] px-3 py-1 text-xs font-medium text-white transition hover:bg-[#3C50E0]/90 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  {couponValidation.isValidating ? "..." : "Validate"}
                </button>
              )}
            </div>
            {/* Coupon validation feedback */}
            {couponValidation.isValid === true &&
              couponValidation.couponData && (
                <div className="mt-2 flex items-center gap-2 text-sm text-green-600">
                  <svg
                    className="h-4 w-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                  <span>
                    Valid coupon!{" "}
                    {couponValidation.couponData.discount_type === "percentage"
                      ? `${couponValidation.couponData.discount_value}% off`
                      : `$${couponValidation.couponData.discount_value} off`}
                  </span>
                </div>
              )}
            {couponValidation.error && (
              <div className="mt-2 flex items-center gap-2 text-sm text-red-600">
                <svg
                  className="h-4 w-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
                <span>{couponValidation.error}</span>
              </div>
            )}
          </div>
          {error && <div className="mt-2 text-sm text-red-500">{error}</div>}
        </div>
        <button
          type="submit"
          className="font-inter mt-4 w-full rounded-[99px] border border-[#3C50E0] bg-[#3C50E0]  py-3 text-base  font-semibold text-white transition hover:bg-[#3C50E0]/90 disabled:opacity-60"
          disabled={
            processing ||
            !cardReady ||
            // Only require validation if coupon is entered
            (couponCode.trim() !== "" && couponValidation.isValid !== true)
          }
        >
          {processing ? (
            "Processing..."
          ) : couponCode.trim() !== "" && couponValidation.isValid !== true ? (
            "Please validate coupon first"
          ) : shouldShowDiscount() ? (
            <span className="flex items-center justify-center gap-2">
              Complete Subscription -
              <span className="text-gray-300 line-through">
                ${getCurrentPrice(selectedPlan)}
              </span>
              <span className="text-green-300">
                ${getFinalPrice(selectedPlan).toFixed(2)}
              </span>
              {selectedInterval === "annual" ? "/year" : "/month"}
            </span>
          ) : (
            `Complete Subscription - $${getCurrentPrice(selectedPlan)} ${
              selectedInterval === "annual" ? "/year" : "/month"
            }`
          )}
        </button>
      </form>
    );
  }
);

// Plan data with project ranges and pricing
const PROJECT_RANGES = {
  "1-50": {
    portal: { monthly: 150, annual: 1500 },
    studio: { monthly: 150, annual: 1500 },
    complete: { monthly: 250, annual: 2500 },
  },
  "51-100": {
    portal: { monthly: 175, annual: 1750 },
    studio: { monthly: 175, annual: 1750 },
    complete: { monthly: 275, annual: 2750 },
  },
  "101-150": {
    portal: { monthly: 200, annual: 2000 },
    studio: { monthly: 200, annual: 2000 },
    complete: { monthly: 300, annual: 3000 },
  },
  "151-200": {
    portal: { monthly: 225, annual: 2250 },
    studio: { monthly: 225, annual: 2250 },
    complete: { monthly: 325, annual: 3250 },
  },
  "201+": {
    portal: { monthly: 250, annual: 2500 },
    studio: { monthly: 250, annual: 2500 },
    complete: { monthly: 350, annual: 3500 },
  },
};

const PLAN_FEATURES = {
  portal: [
    "Project Management",
    "Project Calendar",
    "Client Login Portal",
    "Digital 8-count sheets",
    "Automated Music Licenses",
    "Automated Reminder Emails",
    "Automated Music Surveys",
    "Project Edit Management",
    "8-Count Track Management",
    "Custom Email Domain",
  ],
  studio: [
    "Automated Music Surveys",
    "Project Management",
    "Project Calendar",
    "Project Budget Review",
    "Automated Vocal Orders",
    "Excel Style Order View",
    "Automated Reminder Emails",
    "Company Logo Customization",
    "Custom Email Domain",
  ],
  complete: [
    "Everything In The Portal",
    "Everything In The Studio",
    "Priority Support",
    "Dedicated Account Manager",
  ],
};

const PLAN_LABELS = {
  portal: "The Portal",
  studio: "The Studio",
  complete: "Complete Suite",
};

const PLAN_DESCRIPTIONS = {
  portal: "Best for teams who want a client portal and project management.",
  studio: "For studios needing advanced music survey and order tools.",
  complete: "All features, priority support, and dedicated manager.",
};

const PLAN_ICONS = {
  portal: "/figma-icons/icon-sparkles.svg",
  studio: "/figma-icons/icon-file-earmark-text.svg",
  complete: "/figma-icons/icon-credit-card.svg",
};

const PLAN_KEYS = ["portal", "studio", "complete"];

// Figma pixel-perfect plan card styles
const PLAN_CARD_STYLES = {
  portal: {
    border: "border-[#2B3EB4]",
    shadow:
      "shadow-[0_0_0_4px_#f5f5f5,0_1px_2px_0px_rgba(10,13,18,0.06),0_1px_3px_0px_rgba(10,13,18,0.1)]",
    icon: "/figma-icons/plan-portal.png",
  },
  studio: {
    border: "border-[#2B3EB4]",
    shadow:
      "shadow-[0_0_0_4px_#f4ebff,0_1px_2px_0px_rgba(10,13,18,0.06),0_1px_3px_0px_rgba(10,13,18,0.1)]",
    icon: "/figma-icons/plan-studio.png",
  },
  complete: {
    border: "border-[#2B3EB4]",
    shadow:
      "shadow-[0_0_0_4px_#f4ebff,0_1px_2px_0px_rgba(10,13,18,0.06),0_1px_3px_0px_rgba(10,13,18,0.1)]",
    icon: "/figma-icons/plan-complete.png",
  },
};

// Internal component that uses Stripe hooks
const SubscriptionStepInternal = ({
  userDetails,
  onComplete,
  setIsLoading,
  globalDispatch, // Add globalDispatch prop
}) => {
  const stripe = useStripe();
  const elements = useElements();
  const isMountedRef = useRef(true);
  const cardElementRef = useRef(null);
  const [selectedProjectRange, setSelectedProjectRange] = useState("151-200");
  const [selectedInterval, setSelectedInterval] = useState("annual");
  const [selectedPlan, setSelectedPlan] = useState(null); // null = plan list, planKey = show details
  const [showDetails, setShowDetails] = useState(false); // false = plan list, true = details
  const [couponCode, setCouponCode] = useState("");
  const [cardReady, setCardReady] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState("");
  const [currentUserPlanKey, setCurrentUserPlanKey] = useState(null);
  const [couponValidation, setCouponValidation] = useState({
    isValidating: false,
    isValid: null,
    couponData: null,
    error: null,
  });

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Check if user already has a subscription using plan_id (which is price.id)
  // Note: plan_id is the price.id from Stripe, subscription_id is no longer needed
  const hasExistingSubscription = userDetails?.plan_id;

  // Debug logging
  console.log("SubscriptionStep - User Details:", {
    plan_id: userDetails?.plan_id,
    hasExistingSubscription,
  });

  const getCurrentPrice = (planKey = selectedPlan) => {
    if (!planKey) return 0;
    return PROJECT_RANGES[selectedProjectRange][planKey][selectedInterval];
  };

  // Calculate discounted price based on coupon
  const getDiscountedPrice = (originalPrice, couponData) => {
    if (!couponData) return originalPrice;

    if (couponData.discount_type === "percentage") {
      const discountPercent = parseFloat(couponData.discount_amount);
      return originalPrice - (originalPrice * discountPercent) / 100;
    } else if (couponData.discount_type === "fixed_amount") {
      const discountAmount = parseFloat(couponData.discount_amount);
      return Math.max(0, originalPrice - discountAmount);
    }

    return originalPrice;
  };

  // Get final price (discounted if coupon is valid)
  const getFinalPrice = (planKey = selectedPlan) => {
    const originalPrice = getCurrentPrice(planKey);

    if (couponValidation.isValid && couponValidation.couponData) {
      return getDiscountedPrice(originalPrice, couponValidation.couponData);
    }

    return originalPrice;
  };

  // Check if discount should be shown
  const shouldShowDiscount = () => {
    return couponValidation.isValid && couponValidation.couponData;
  };

  const getPlansForCurrentRange = () => {
    return PLAN_KEYS.map((planKey) => ({
      key: planKey,
      label: PLAN_LABELS[planKey],
      icon: PLAN_ICONS[planKey],
      description: PLAN_DESCRIPTIONS[planKey],
      features: PLAN_FEATURES[planKey],
      price: PROJECT_RANGES[selectedProjectRange][planKey],
    }));
  };

  const handlePlanSelect = (plan) => {
    setSelectedPlan(plan.key);
    setShowDetails(true);
  };

  const handleBackToPlans = () => {
    setSelectedPlan(null);
    setShowDetails(false);
    setError("");
  };

  // Determine which plan key matches the user's current plan_id
  useEffect(() => {
    const determineCurrentPlan = async () => {
      if (hasExistingSubscription && userDetails?.plan_id) {
        try {
          const sdk = new MkdSDK();

          // Fetch both products and prices since plan_id is actually price.id
          const [productsResult, pricesResult] = await Promise.all([
            sdk.callRawAPI(
              `/v4/api/records/stripe_product?page=1,100`,
              [],
              "GET"
            ),
            sdk.callRawAPI(
              `/v4/api/records/stripe_price?page=1,100`,
              [],
              "GET"
            ),
          ]);

          if (
            !productsResult.error &&
            !pricesResult.error &&
            productsResult.list &&
            pricesResult.list
          ) {
            // Find the price that matches the user's plan_id (which is price.id)
            const userPrice = pricesResult.list.find(
              (price) => price.id === userDetails.plan_id
            );

            if (userPrice && userPrice.product_id) {
              // Find the product using the product_id from the price
              const userProduct = productsResult.list.find(
                (product) => product.id === userPrice.product_id
              );

              if (userProduct && userProduct.name) {
                // Map product name to plan key
                const planKey = Object.keys(PLAN_LABELS).find(
                  (key) => PLAN_LABELS[key] === userProduct.name
                );

                if (planKey) {
                  setCurrentUserPlanKey(planKey);
                  console.log(
                    "User's current plan key:",
                    planKey,
                    "for product:",
                    userProduct.name,
                    "via price.id:",
                    userDetails.plan_id
                  );
                }
              }
            }
          }
        } catch (error) {
          console.error("Error determining current plan:", error);
        }
      }
    };

    determineCurrentPlan();
  }, [hasExistingSubscription, userDetails?.plan_id]);

  // Handle existing subscription continuation
  const handleExistingSubscriptionContinue = () => {
    // Mark step as complete for existing subscribers
    onComplete({ plan_chosen: true });
  };

  // Payment submission logic - memoized to prevent re-renders
  const handlePaymentSubmit = useCallback(
    async (e) => {
      e.preventDefault();
      console.log("🔄 Starting payment submission...");

      // Early validation to prevent Stripe element issues
      if (!stripe || !elements) {
        console.error("❌ Stripe or Elements not ready:", {
          stripe: !!stripe,
          elements: !!elements,
        });
        setError("Payment system not ready. Please try again.");
        return;
      }
      console.log("✅ Stripe and Elements are ready");

      // Get card element reference - try both stored ref and elements
      let cardElement =
        cardElementRef.current || elements.getElement(CardElement);
      if (!cardElement) {
        console.error("❌ No CardElement found in ref or elements");
        setError("Card information not found. Please refresh and try again.");
        return;
      }
      console.log("✅ CardElement found:", !!cardElement);

      // CREATE TOKEN IMMEDIATELY - before any state changes that could unmount the element
      console.log("🔄 Creating Stripe token IMMEDIATELY...");
      let tokenResult;
      try {
        tokenResult = await stripe.createToken(cardElement);
        console.log("✅ Token created successfully:", !!tokenResult.token);
      } catch (tokenError) {
        console.error(
          "❌ Token creation failed with first element, trying fresh element:",
          tokenError
        );

        // Last resort: try to get a completely fresh element reference
        try {
          const freshElement = elements.getElement(CardElement);
          if (freshElement && freshElement !== cardElement) {
            console.log("🔄 Retrying with fresh CardElement...");
            tokenResult = await stripe.createToken(freshElement);
            console.log(
              "✅ Token created with fresh element:",
              !!tokenResult.token
            );
          } else {
            throw new Error("No fresh element available");
          }
        } catch (retryError) {
          console.error("❌ Retry with fresh element also failed:", retryError);
          setError(
            "Failed to process card information. Please refresh the page and try again."
          );
          return;
        }
      }

      if (tokenResult.error) {
        console.error("❌ Stripe token creation failed:", tokenResult.error);
        setError(tokenResult.error.message);
        return;
      }

      // NOW set processing states after token is safely created
      setProcessing(true);
      setIsLoading(true);
      setError(""); // Clear any previous errors
      console.log("🔄 Processing payment with token...");

      try {
        // Backend integration
        const sdk = new MkdSDK();
        const customerParams = { cardToken: tokenResult.token.id };

        // Get products and find correct product ID
        console.log("🔄 Fetching Stripe products...");
        const productsResult = await sdk.callRawAPI(
          `/v4/api/records/stripe_product?page=1,100`,
          [],
          "GET"
        );

        const pricesResult = await sdk.callRawAPI(
          `/v4/api/records/stripe_price?page=${1},${100}`,
          [],
          "GET"
        );

        let productId = null;
        let currentPrice = null;
        if (!productsResult.error && productsResult.list) {
          console.log("✅ Products fetched:", productsResult.list.length);
          const targetProduct = productsResult.list.find(
            (product) => product.name === PLAN_LABELS[selectedPlan]
          );

          if (targetProduct) {
            productId = targetProduct.id;
            currentPrice = pricesResult.list.find(
              (price) => price.product_id == targetProduct.id
            );
            console.log("✅ Found matching product:", {
              name: targetProduct.name,
              id: productId,
            });
          }
        }

        if (!productId) {
          console.error("❌ Could not find product for plan:", selectedPlan);
          throw new Error("Could not find the selected product");
        }

        let subscriptionResult;

        // Check if user already has a subscription
        if (!hasExistingSubscription) {
          // NEW SUBSCRIPTION FLOW
          console.log(
            "🔄 Creating new subscription - Creating Stripe customer..."
          );
          const customerResult = await sdk.createStripeCustomer(customerParams);
          console.log("✅ Stripe customer created successfully");

          // Create subscription with optional coupon
          const subscriptionPayload = {
            planId: currentPrice?.id,
          };

          // Add coupon to payload only if it's entered and validated
          if (couponCode && couponCode.trim() !== "") {
            if (couponValidation.isValid === true) {
              subscriptionPayload.coupon = couponCode.trim();
              console.log("📋 Using validated coupon code:", couponCode.trim());
            } else {
              // This shouldn't happen due to UI validation, but just in case
              console.warn(
                "⚠️ Coupon entered but not validated - skipping coupon"
              );
            }
          }

          console.log("🔄 Creating new subscription...");
          subscriptionResult = await sdk.createStripeSubscription(
            subscriptionPayload
          );
          console.log(
            "✅ New subscription created successfully:",
            subscriptionResult
          );
        } else {
          // CHANGING EXISTING SUBSCRIPTION FLOW
          console.log("🔄 Changing existing subscription...");

          // Create subscription payload for changing subscription
          // Note: We still need activeSubscriptionId for the API, but plan_id is the price.id
          const subscriptionPayload = {
            newPlanId: currentPrice?.id, // This is the new price.id
            activeSubscriptionId: userDetails?.subscription_id, // Keep this for API compatibility
          };

          // Add coupon to payload only if it's entered and validated
          if (couponCode && couponCode.trim() !== "") {
            if (couponValidation.isValid === true) {
              subscriptionPayload.coupon = couponCode.trim();
              console.log("📋 Using validated coupon code:", couponCode.trim());
            } else {
              // This shouldn't happen due to UI validation, but just in case
              console.warn(
                "⚠️ Coupon entered but not validated - skipping coupon"
              );
            }
          }

          subscriptionResult = await sdk.changeStripeSubscription(
            subscriptionPayload
          );
          console.log(
            "✅ Subscription changed successfully:",
            subscriptionResult
          );
        }

        // On success, refresh subscription data and mark step complete
        console.log(
          "🎉 Payment completed successfully! Refreshing subscription data..."
        );

        // FORCE REFRESH: Update global context with new subscription data
        if (globalDispatch) {
          await refreshSubscriptionData(globalDispatch);
        }

        console.log(
          "✅ Subscription data refreshed, marking step as complete..."
        );
        onComplete({ plan_chosen: true });
      } catch (err) {
        if (isMountedRef.current) {
          setError(err.message || "Payment failed. Please try again.");
        }
      } finally {
        if (isMountedRef.current) {
          setProcessing(false);
          setIsLoading(false);
        }
      }
    },
    [
      stripe,
      elements,
      selectedPlan,
      couponCode,
      couponValidation.isValid,
      onComplete,
      setIsLoading,
      setError,
      setProcessing,
      isMountedRef,
      cardElementRef,
      hasExistingSubscription,
      userDetails?.subscription_id,
      globalDispatch,
    ]
  );

  // Step 1: Plan List View (Figma style)
  if (!showDetails) {
    const plans = getPlansForCurrentRange();
    return (
      <div className=" w-full ">
        <div className="mb-8 text-center">
          <svg
            className="mx-auto mb-4 h-16 w-16"
            width="56"
            height="56"
            viewBox="0 0 56 56"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <rect x="4" y="4" width="48" height="48" rx="24" fill="#E0E6FC" />
            <rect
              x="4"
              y="4"
              width="48"
              height="48"
              rx="24"
              stroke="#F6F7F8"
              stroke-width="8"
            />
            <path
              d="M15.8125 23.3125H40.1875M15.8125 24.25H40.1875M19.5625 30.8125H27.0625M19.5625 33.625H23.3125M18.625 37.375H37.375C38.9283 37.375 40.1875 36.1158 40.1875 34.5625V21.4375C40.1875 19.8842 38.9283 18.625 37.375 18.625H18.625C17.0717 18.625 15.8125 19.8842 15.8125 21.4375V34.5625C15.8125 36.1158 17.0717 37.375 18.625 37.375Z"
              stroke="#3C50E0"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>

          <h2 className="mb-2 font-satoshi text-3xl font-bold text-[#131E2B]">
            Unlock your CheerEQ Potential
          </h2>
          <p className="font-satoshi text-lg text-[#667484]">
            Select the perfect plan designed to fit your program's needs and
            gain full access to our powerful management tools.
          </p>
        </div>
        <div className="mb-8 flex justify-center gap-4">
          <div className="flex items-center gap-2">
            <span
              className="font-m
            edium text-[#4A5B70]"
            >
              Show pricing for
            </span>
            <select
              className="black-select w-[190px] rounded-lg border border-[#D5D7DA] !bg-white px-4 py-2 text-sm font-semibold !text-[#414651] shadow-sm"
              value={selectedProjectRange}
              onChange={(e) => setSelectedProjectRange(e.target.value)}
            >
              {Object.keys(PROJECT_RANGES).map((range) => (
                <option className="text-black" key={range} value={range}>
                  {range} Mix Projects
                </option>
              ))}
            </select>
          </div>
          <div className="flex items-center gap-2">
            <label className="flex cursor-pointer items-center">
              <input
                type="checkbox"
                className="peer sr-only"
                checked={selectedInterval === "annual"}
                onChange={() =>
                  setSelectedInterval(
                    selectedInterval === "annual" ? "monthly" : "annual"
                  )
                }
              />
              <div
                className={`peer relative h-6 w-11 rounded-full  transition-all duration-300 ${
                  selectedInterval === "annual"
                    ? "bg-[#3C50E0]"
                    : "bg-[#E9EBEF]"
                }`}
              >
                <div
                  className={`absolute left-1 top-1 h-4 w-4 rounded-full bg-white shadow-md transition-transform duration-300 ${
                    selectedInterval === "annual" ? "translate-x-5" : ""
                  }`}
                ></div>
              </div>
              <span className="ml-3 text-sm text-[#667484]">
                Bill Annually (Save 15-20%)
              </span>
            </label>
          </div>
        </div>
        <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
          {plans.map((plan) => (
            <div
              key={plan.key}
              className={`relative flex cursor-pointer  flex-col rounded-[16px]   border ${
                hasExistingSubscription && currentUserPlanKey === plan.key
                  ? "border-green-600"
                  : PLAN_CARD_STYLES[plan.key].border
              } bg-white p-8 ${
                hasExistingSubscription && currentUserPlanKey === plan.key
                  ? "shadow-[0_0_0_4px_#dcfce7,0_1px_2px_0px_rgba(10,13,18,0.06),0_1px_3px_0px_rgba(10,13,18,0.1)]"
                  : PLAN_CARD_STYLES[plan.key].shadow
              } transition hover:border-[#3C50E0] hover:shadow-2xl`}
              onClick={() => handlePlanSelect(plan)}
            >
              {/* Current Plan Badge */}
              {hasExistingSubscription && currentUserPlanKey === plan.key && (
                <div className="absolute -top-3 left-4 rounded-full bg-green-600 px-3 py-1 text-xs font-semibold text-white">
                  Current Plan
                </div>
              )}
              <div className="mb-4 flex items-center gap-3">
                {/* <img
                  src={PLAN_CARD_STYLES[plan.key].icon}
                  alt="icon"
                  className="h-8 w-8"
                /> */}
                <span className="font-satoshi text-[24px] font-bold text-[#6F6C90]">
                  {plan.label}
                </span>
              </div>
              {/* <div className="mb-2 min-h-[40px] font-satoshi text-lg text-[#667484]">
                {plan.description}
              </div> */}
              <div className="mb-4 flex items-end gap-1">
                <span className="font-satoshi text-3xl font-bold text-[#3C50E0]">
                  ${plan.price[selectedInterval]}
                </span>
                <span className="font-satoshi text-base text-[#6F6C90]">
                  /{selectedInterval === "annual" ? "year" : "month"}
                </span>
              </div>
              <span className="mt-2 font-satoshi text-[20px] font-bold text-[#6F6C90]">
                Features:
              </span>
              <ul className="mb-6 space-y-2">
                {plan.features.map((feature, i) => (
                  <li
                    key={i}
                    className="flex items-center gap-2 font-satoshi text-sm text-[#131E2B]"
                  >
                    <svg
                      className="h-4 w-4 text-[#3C50E0]"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 16 16"
                    >
                      <path
                        d="M4 8l2 2 4-4"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                    {feature}
                  </li>
                ))}
                {/* {plan.features.length > 5 && (
                  <li className="font-satoshi text-xs text-[#667484]">
                    ...and more
                  </li>
                )} */}
              </ul>
              <button
                className={`font-inter mt-auto w-full rounded-[99px] py-3 text-base font-semibold transition ${
                  hasExistingSubscription && currentUserPlanKey === plan.key
                    ? "border border-green-600 bg-green-600 text-white"
                    : "border border-[#3C50E0] bg-transparent text-[#3C50E0] hover:bg-[#3C50E0] hover:text-white"
                }`}
                onClick={(e) => {
                  e.stopPropagation();
                  if (
                    hasExistingSubscription &&
                    currentUserPlanKey === plan.key
                  ) {
                    // User is already subscribed to this plan - continue setup
                    handleExistingSubscriptionContinue();
                  } else {
                    // User wants to subscribe to this plan or change plan
                    handlePlanSelect(plan);
                  }
                }}
              >
                {hasExistingSubscription && currentUserPlanKey === plan.key
                  ? "Current Plan - Continue"
                  : hasExistingSubscription
                  ? "Change to This Plan"
                  : "Subscribe Now"}
              </button>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Step 2: Plan Details/Payment View (Figma style)
  const selectedPlanData = getPlansForCurrentRange().find(
    (p) => p.key === selectedPlan
  );
  return (
    <div className="w-full">
      <button
        className="mb-6 flex items-center text-[#3C50E0] hover:underline"
        onClick={handleBackToPlans}
      >
        <svg
          className="mr-2 h-5 w-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M15 19l-7-7 7-7"
          />
        </svg>
        Back to Plans
      </button>
      <div className="mb-5 text-center">
        <svg
          className="mx-auto h-16 w-16"
          width="56"
          height="56"
          viewBox="0 0 56 56"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect x="4" y="4" width="48" height="48" rx="24" fill="#E0E6FC" />
          <rect
            x="4"
            y="4"
            width="48"
            height="48"
            rx="24"
            stroke="#F6F7F8"
            stroke-width="8"
          />
          <path
            d="M15.8125 23.3125H40.1875M15.8125 24.25H40.1875M19.5625 30.8125H27.0625M19.5625 33.625H23.3125M18.625 37.375H37.375C38.9283 37.375 40.1875 36.1158 40.1875 34.5625V21.4375C40.1875 19.8842 38.9283 18.625 37.375 18.625H18.625C17.0717 18.625 15.8125 19.8842 15.8125 21.4375V34.5625C15.8125 36.1158 17.0717 37.375 18.625 37.375Z"
            stroke="#3C50E0"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>

        <h2 className="mb-2 font-satoshi text-3xl font-bold text-[#131E2B]">
          Secure Your Payment
        </h2>
        <p className="font-satoshi text-lg text-[#667484]">
          Enter your billing information to complete your subscription and
          activate your chosen plan.
        </p>
        <div className="mb-8 mt-10 flex justify-center gap-4">
          <div className="flex items-center gap-2">
            <span
              className="font-m
            edium text-[#4A5B70]"
            >
              Show pricing for
            </span>
            <select
              className="black-select w-[190px] rounded-lg border border-[#D5D7DA] !bg-white px-4 py-2 text-sm font-semibold !text-[#414651] shadow-sm"
              value={selectedProjectRange}
              onChange={(e) => setSelectedProjectRange(e.target.value)}
            >
              {Object.keys(PROJECT_RANGES).map((range) => (
                <option className="text-black" key={range} value={range}>
                  {range} Mix Projects
                </option>
              ))}
            </select>
          </div>
          <div className="flex items-center gap-2">
            <label className="flex cursor-pointer items-center">
              <input
                type="checkbox"
                className="peer sr-only"
                checked={selectedInterval === "annual"}
                onChange={() =>
                  setSelectedInterval(
                    selectedInterval === "annual" ? "monthly" : "annual"
                  )
                }
              />
              <div
                className={`peer relative h-6 w-11 rounded-full  transition-all duration-300 ${
                  selectedInterval === "annual"
                    ? "bg-[#3C50E0]"
                    : "bg-[#E9EBEF]"
                }`}
              >
                <div
                  className={`absolute left-1 top-1 h-4 w-4 rounded-full bg-white shadow-md transition-transform duration-300 ${
                    selectedInterval === "annual" ? "translate-x-5" : ""
                  }`}
                ></div>
              </div>
              <span className="ml-3 text-sm text-[#667484]">
                Bill Annually (Save 15-20%)
              </span>
            </label>
          </div>
        </div>
      </div>

      <div className="mb-8 flex flex-col items-end justify-end md:flex-row md:gap-12">
        {/* Plan summary card */}
        <div className="mb-8 min-h-[440px] w-[230px] cursor-pointer rounded-[16px] border border-[#2B3EB4]/90 bg-white p-1 px-4 shadow-[0_0_0_4px_#f5f5f5,0_1px_2px_0px_rgba(10,13,18,0.06),0_1px_3px_0px_rgba(10,13,18,0.1)] hover:border-[#2B3EB4] hover:shadow-2xl md:mb-0 md:min-w-[230px]">
          <div className="mb-4 flex items-center gap-3">
            {/* <img
              src={PLAN_CARD_STYLES[selectedPlanData.key].icon}
              alt="icon"
              className="h-8 w-8"
            /> */}
            <span className="font-satoshi text-[24px] font-bold text-[#6F6C90]">
              {selectedPlanData.label}
            </span>
          </div>
          {/* <div className="mb-2 font-satoshi text-lg text-[#667484]">
            {selectedPlanData.description}
          </div> */}
          <div className="mb-4 flex items-end gap-1">
            {shouldShowDiscount() ? (
              <div className="flex flex-col">
                <div className="flex items-end gap-2">
                  <span className="font-satoshi text-lg font-bold text-gray-400 line-through">
                    ${getCurrentPrice(selectedPlan)}
                  </span>
                  <span className="font-satoshi text-3xl font-bold text-green-600">
                    ${getFinalPrice(selectedPlan).toFixed(2)}
                  </span>
                  <span className="font-satoshi text-base text-[#6F6C90]">
                    /{selectedInterval === "annual" ? "year" : "month"}
                  </span>
                </div>
                <span className="font-satoshi text-sm text-green-600">
                  {couponValidation.couponData.discount_type === "percentage"
                    ? `${couponValidation.couponData.discount_amount}% off`
                    : `$${couponValidation.couponData.discount_amount} off`}
                </span>
              </div>
            ) : (
              <>
                <span className="font-satoshi text-3xl font-bold text-[#3C50E0]">
                  ${getCurrentPrice(selectedPlan)}
                </span>
                <span className="font-satoshi text-base text-[#6F6C90]">
                  /{selectedInterval === "annual" ? "year" : "month"}
                </span>
              </>
            )}
          </div>
          <span className="mt-2 font-satoshi text-[20px] font-bold text-[#6F6C90]">
            Features:
          </span>
          <ul className="mb-6 space-y-2">
            {selectedPlanData.features.map((feature, i) => (
              <li
                key={i}
                className="flex items-center gap-2 font-satoshi text-sm text-[#131E2B]"
              >
                <svg
                  className="h-4 w-4 text-[#3C50E0]"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 16 16"
                >
                  <path
                    d="M4 8l2 2 4-4"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                {feature}
              </li>
            ))}
          </ul>
        </div>
        {/* Payment form card */}
        <PaymentForm
          userDetails={userDetails}
          selectedPlan={selectedPlan}
          selectedInterval={selectedInterval}
          getCurrentPrice={getCurrentPrice}
          couponCode={couponCode}
          setCouponCode={setCouponCode}
          error={error}
          setError={setError}
          processing={processing}
          cardReady={cardReady}
          setCardReady={setCardReady}
          handlePaymentSubmit={handlePaymentSubmit}
          cardElementRef={cardElementRef}
          couponValidation={couponValidation}
          setCouponValidation={setCouponValidation}
          getFinalPrice={getFinalPrice}
          shouldShowDiscount={shouldShowDiscount}
        />
      </div>
    </div>
  );
};

// Wrapper component with Elements provider
const SubscriptionStep = (props) => {
  return (
    <Elements stripe={stripePromise}>
      <SubscriptionStepInternal {...props} />
    </Elements>
  );
};

export default SubscriptionStep;
