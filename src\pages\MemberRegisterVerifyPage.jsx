import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import { GlobalContext, showToast } from "../globalContext";
import { AuthContext } from "../authContext";
import MkdSDK from "../utils/MkdSDK";
import { ClipLoader } from "react-spinners";

let sdk = new MkdSDK();

const schema = yup
  .object({
    email: yup.string().email().required(),
    password: yup.string().min(8).required(),
    confirmPassword: yup
      .string()
      .oneOf([yup.ref("password"), null], "Passwords must match")
      .required(),
    first_name: yup.string().required(),
    last_name: yup.string().required(),
  })
  .required();

const MemberRegisterVerifyPage = () => {
  const { dispatch: globalDispatch, state } = React.useContext(GlobalContext);
  const { dispatch: authDispatch } = React.useContext(AuthContext);
  const [currentStep, setCurrentStep] = useState(1); // 1: Register, 2: Verify
  const [submitLoading, setSubmitLoading] = useState(false);
  const [resendLoading, setResendLoading] = useState(false);
  const [verificationStatus, setVerificationStatus] = useState("unverified");
  const [userEmail, setUserEmail] = useState("");
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  useEffect(() => {
    // Check if coming from email verification link with token
    const token = searchParams.get("token");
    if (token) {
      setCurrentStep(2);
      setVerificationStatus("verifying");
      handleEmailVerification(token);
    } else {
      // Check for pending verification user
      const pendingUser = localStorage.getItem("pending_verification_user");
      if (pendingUser) {
        const userData = JSON.parse(pendingUser);
        setUserEmail(userData.email);
        setCurrentStep(2);
        setVerificationStatus("unverified");
      }
    }
  }, []);

  const handleEmailVerification = async (token) => {
    try {
      const result = await sdk.callRawAPI(
        `/v2/api/lambda/verify-email?token=${token}`,
        {},
        "GET"
      );

      if (!result.error) {
        setVerificationStatus("verified");
        showToast(
          globalDispatch,
          "Email verified successfully! Redirecting to dashboard...",
          3000,
          "success"
        );

        // Clear pending user data
        localStorage.removeItem("pending_verification_user");

        // Login the user if login data is provided
        if (result.user) {
          authDispatch({
            type: "LOGIN",
            payload: result,
          });
        }

        // Redirect to dashboard
        setTimeout(() => {
          navigate("/member/dashboard");
        }, 1500);
      } else {
        throw new Error(result.message || "Verification failed");
      }
    } catch (error) {
      console.error("Verification error:", error);
      setVerificationStatus("error");
      showToast(
        globalDispatch,
        error.message || "Email verification failed. Please try again.",
        4000,
        "error"
      );
    }
  };

  const onSubmit = async (data) => {
    try {
      setSubmitLoading(true);

      const result = await sdk.registerMember(
        data.email,
        data.password,
        data.first_name,
        data.last_name
      );

      if (!result.error) {
        showToast(
          globalDispatch,
          "Registration successful! Please check your email for verification.",
          4000,
          "success"
        );

        // Store registration data
        localStorage.setItem("registration_token", result.token);
        localStorage.setItem(
          "pending_verification_user",
          JSON.stringify({
            email: data.email,
            user_id: result.user_id || result.id,
            token: result.token,
          })
        );

        // Move to verification step
        setUserEmail(data.email);
        setCurrentStep(2);
        setVerificationStatus("unverified");
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        } else if (result.message) {
          showToast(globalDispatch, result.message, 4000, "error");
        }
      }
    } catch (error) {
      console.log("Error", error);
      showToast(
        globalDispatch,
        error.message || "Registration failed. Please try again.",
        4000,
        "error"
      );
    } finally {
      setSubmitLoading(false);
    }
  };

  const handleResendVerification = async () => {
    try {
      setResendLoading(true);
      const pendingUser = localStorage.getItem("pending_verification_user");
      if (pendingUser) {
        const userData = JSON.parse(pendingUser);

        const result = await sdk.sendVerificationEmail(userData.email);

        if (!result.error) {
          showToast(
            globalDispatch,
            "Verification email sent! Please check your inbox.",
            4000,
            "success"
          );
        } else {
          showToast(
            globalDispatch,
            result.message ||
              "Failed to resend verification email. Please try again.",
            4000,
            "error"
          );
        }
      }
    } catch (error) {
      console.error("Error resending verification:", error);
      showToast(
        globalDispatch,
        error.message ||
          "Failed to resend verification email. Please try again.",
        4000,
        "error"
      );
    } finally {
      setResendLoading(false);
    }
  };

  const renderRegistrationStep = () => (
    <div className="flex w-full">
      <div className="flex flex-1 flex-col items-center justify-between px-[94px] py-16">
        <div className="flex w-full flex-col items-center">
          {/* Heading Section */}
          <div className="mb-16 flex w-full flex-col items-center">
            <div className="mb-8">
              <img
                crossOrigin="anonymous"
                src={`${window.location.origin}/new/cheerEQ-2-Ed2.png`}
                className="h-auto w-[200px] dark:hidden"
                alt="Logo"
              />
            </div>
            <h1 className="mb-2 text-lg font-semibold text-[#131E2B]">
              Welcome to Cheer EQ!
            </h1>
            <p className="mb-2 text-sm text-[#667484]">Let's Get Started</p>
            <p className="text-center text-sm text-[#667484]">
              Set up your secure account to unlock powerful tools for
              <br />
              managing your cheer programs.
            </p>
          </div>

          {/* Registration Form */}
          <form
            onSubmit={handleSubmit(onSubmit)}
            className="w-full max-w-sm space-y-4"
          >
            {/* First Name and Last Name */}
            <div className="flex gap-3">
              <div className="flex-1">
                <input
                  type="text"
                  placeholder="First Name"
                  {...register("first_name")}
                  className={`w-full rounded-lg border bg-transparent px-4 py-3 text-[#4A5B70] outline-none focus:border-[#3C50E0] ${
                    errors.first_name?.message
                      ? "border-red-500"
                      : "border-[#D1D6DE]"
                  } bg-white`}
                />
                {errors.first_name?.message && (
                  <p className="mt-1 text-sm text-red-500">
                    {errors.first_name.message}
                  </p>
                )}
              </div>
              <div className="flex-1">
                <input
                  type="text"
                  placeholder="Last Name"
                  {...register("last_name")}
                  className={`w-full rounded-lg border bg-transparent px-4 py-3 text-[#4A5B70] outline-none focus:border-[#3C50E0] ${
                    errors.last_name?.message
                      ? "border-red-500"
                      : "border-[#D1D6DE]"
                  } bg-white`}
                />
                {errors.last_name?.message && (
                  <p className="mt-1 text-sm text-red-500">
                    {errors.last_name.message}
                  </p>
                )}
              </div>
            </div>

            {/* Email */}
            <div>
              <input
                type="email"
                placeholder="<EMAIL>"
                {...register("email")}
                className={`w-full rounded-lg border bg-transparent px-4 py-3 text-[#4A5B70] outline-none focus:border-[#3C50E0] ${
                  errors.email?.message ? "border-red-500" : "border-[#D1D6DE]"
                } bg-white`}
              />
              {errors.email?.message && (
                <p className="mt-1 text-sm text-red-500">
                  {errors.email.message}
                </p>
              )}
            </div>

            {/* Password */}
            <div>
              <input
                type="password"
                placeholder="Password"
                {...register("password")}
                className={`w-full rounded-lg border bg-transparent px-4 py-3 text-[#4A5B70] outline-none focus:border-[#3C50E0] ${
                  errors.password?.message
                    ? "border-red-500"
                    : "border-[#D1D6DE]"
                } bg-white`}
              />
              {errors.password?.message && (
                <p className="mt-1 text-sm text-red-500">
                  {errors.password.message}
                </p>
              )}
            </div>

            {/* Confirm Password */}
            <div>
              <input
                type="password"
                placeholder="Confirm Password"
                {...register("confirmPassword")}
                className={`w-full rounded-lg border bg-transparent px-4 py-3 text-[#4A5B70] outline-none focus:border-[#3C50E0] ${
                  errors.confirmPassword?.message
                    ? "border-red-500"
                    : "border-[#D1D6DE]"
                } bg-white`}
              />
              {errors.confirmPassword?.message && (
                <p className="mt-1 text-sm text-red-500">
                  {errors.confirmPassword.message}
                </p>
              )}
            </div>

            {/* Create Account Button */}
            <button
              type="submit"
              disabled={submitLoading}
              className="mt-6 flex w-full items-center justify-center gap-2 rounded-lg bg-[#3C50E0] px-6 py-3 font-medium text-white hover:bg-[#3C50E0]/90 disabled:cursor-not-allowed disabled:opacity-50"
            >
              {submitLoading ? (
                <>
                  <ClipLoader size={16} color="#ffffff" />
                  Creating Account...
                </>
              ) : (
                "Create Account"
              )}
            </button>
          </form>
        </div>
      </div>
    </div>
  );

  const renderVerificationStep = () => {
    if (verificationStatus === "verifying") {
      return (
        <div className="flex w-full">
          <div className="flex flex-1 flex-col items-center px-[94px] py-16">
            <div className="mb-16 flex w-full flex-col items-center">
              <div className="mb-8">
                <div className="flex h-[56px] w-[56px] items-center justify-center rounded-[28px] border-[8px] border-[#F6F7F8] bg-[#E0E6FC]">
                  <ClipLoader size={30} color="#3C50E0" />
                </div>
              </div>
              <h2 className="mb-2 text-[18px] font-semibold leading-[1.555556] text-[#131E2B]">
                Verifying Your Email...
              </h2>
              <p className="text-center text-[14px] leading-[1.428571] text-[#667484]">
                Please wait while we verify your email address.
              </p>
            </div>
          </div>
        </div>
      );
    }

    if (verificationStatus === "verified") {
      return (
        <div className="flex w-full">
          <div className="flex flex-1 flex-col items-center px-[94px] py-16">
            <div className="mb-16 flex w-full flex-col items-center">
              <div className="mb-8">
                <div className="flex h-[56px] w-[56px] items-center justify-center rounded-[28px] bg-[#039855]/10">
                  <svg
                    className="h-[30px] w-[30px] text-[#039855]"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={1.5}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </div>
              </div>
              <h2 className="mb-2 text-[18px] font-semibold leading-[1.555556] text-[#131E2B]">
                Email Verified!
              </h2>
              <p className="text-center text-[14px] leading-[1.428571] text-[#667484]">
                Your email has been successfully verified. You'll be redirected
                to your dashboard.
              </p>
              <div className="mt-6">
                <ClipLoader size={30} color="#3C50E0" />
              </div>
            </div>
          </div>
        </div>
      );
    }

    if (verificationStatus === "error") {
      return (
        <div className="flex w-full">
          <div className="flex flex-1 flex-col items-center px-[94px] py-16">
            <div className="mb-16 flex w-full flex-col items-center">
              <div className="mb-8">
                <div className="flex h-[56px] w-[56px] items-center justify-center rounded-[28px] bg-[#F04438]/10">
                  <svg
                    className="h-[30px] w-[30px] text-[#F04438]"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={1.5}
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                    />
                  </svg>
                </div>
              </div>
              <h2 className="mb-2 text-[18px] font-semibold leading-[1.555556] text-[#131E2B]">
                Verification Error
              </h2>
              <p className="mb-6 text-center text-[14px] leading-[1.428571] text-[#667484]">
                There was an error verifying your email. Please try again or
                contact support.
              </p>
              <button
                onClick={() => window.location.reload()}
                className="w-full max-w-sm rounded-lg bg-[#3C50E0] px-6 py-3 font-medium text-white hover:bg-[#3C50E0]/90"
              >
                Try Again
              </button>
              <div className="mt-4">
                <Link
                  to="/member/register"
                  className="text-[#3C50E0] hover:underline"
                >
                  Back to Registration
                </Link>
              </div>
            </div>
          </div>
        </div>
      );
    }

    // Default unverified state
    return (
      <div className="flex w-full">
        <div className="flex flex-1 flex-col items-center px-[94px] py-16">
          <div className="mb-16 flex w-full flex-col items-center">
            <div className="mb-8">
              <div className="flex h-[56px] w-[56px] items-center justify-center rounded-[28px] border-[8px] border-[#F6F7F8] bg-[#E0E6FC]">
                <svg
                  className="h-[30px] w-[30px] text-[#3C50E0]"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1.5}
                    d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                  />
                </svg>
              </div>
            </div>

            <h2 className="mb-2 text-[18px] font-semibold leading-[1.555556] text-[#131E2B]">
              Please Verify Your Email
            </h2>

            <p className="text-center text-[14px] leading-[1.428571] text-[#667484]">
              We've sent a verification link to your email address:
              <br />
              <span className="my-2 block font-medium text-[#4A5B70]">
                {userEmail}
              </span>
              <br />
              Please click it to activate your account and continue.
            </p>

            {/* <div className="mt-6">
              <button
                onClick={handleResendVerification}
                className="text-[14px] font-normal leading-[1.428571] text-[#667484] hover:text-[#3C50E0]"
                disabled={resendLoading}
              >
                {resendLoading
                  ? "Sending..."
                  : "Didn't get a code? Click to resend"}
              </button>
            </div> */}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-[#23303F] p-4 backdrop-blur-2xl">
      <div className="w-full max-w-5xl">
        <div className="overflow-hidden rounded-2xl bg-white shadow-lg">
          <div className="flex min-h-[600px]">
            {/* Left Sidebar - Setup Roadmap */}
            <div className="w-[424px] border-r border-gray-200 bg-white p-16">
              <div className="mb-6">
                <h2 className="mb-2 text-lg font-semibold text-[#131E2B]">
                  Your Setup Roadmap
                </h2>
                <p className="text-sm text-[#667484]">
                  See what's next to unlock your app's full power.
                </p>
              </div>

              <div className="mb-6">
                <div className="mb-3 text-sm font-medium text-[#131E2B]">
                  Instant Kick-Off
                </div>
                <div className="h-2 w-full rounded-full bg-[#E9EBEF]">
                  <div className="h-2 w-[14%] rounded-full bg-[#3C50E0]"></div>
                </div>
              </div>

              <div className="space-y-4">
                {/* Step 1 - Create Account */}
                <div
                  className={`flex items-center justify-between gap-3 rounded-lg p-3 ${
                    currentStep === 1
                      ? "border border-[#3C50E0] bg-[#F5F7FF]"
                      : "border border-[#3C50E0] bg-[#F5F7FF]"
                  }`}
                >
                  <div className="flex items-center gap-4">
                    <div className="flex h-7 w-7 flex-shrink-0 items-center justify-center rounded-full bg-[#E0E6FC]">
                      <svg
                        className="h-[18px] w-[18px] text-[#3C50E0]"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={1.5}
                          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                        />
                      </svg>
                    </div>
                    <div className="flex-1">
                      <h3 className="text-sm font-medium text-[#1F2E8A]">
                        Create Your Account
                      </h3>
                    </div>
                  </div>
                  {currentStep > 1 && (
                    <div className="flex h-4 w-4 flex-shrink-0 items-center justify-center rounded-full bg-[#3C50E0]">
                      <svg
                        className="h-3 w-3 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                    </div>
                  )}
                </div>

                {/* Step 2 - Verify Email */}
                <div
                  className={`flex items-center justify-between gap-3 rounded-lg p-3 ${
                    currentStep === 2
                      ? "border border-[#3C50E0] bg-[#F5F7FF]"
                      : "border border-[#3C50E0] bg-[white] py-6"
                  }`}
                >
                  <div className="flex items-center gap-4">
                    <div className="flex h-7 w-7 flex-shrink-0 items-center justify-center rounded-full bg-[#E0E6FC]">
                      <svg
                        className="h-[18px] w-[18px] text-[#3C50E0]"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={1.5}
                          d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                        />
                      </svg>
                    </div>
                    <div className="flex-1">
                      <h3 className="text-sm font-medium text-[#1F2E8A]">
                        Verify Your Email
                      </h3>
                      <p className="text-xs text-[#3C50E0]">
                        Confirm your email address.
                      </p>
                    </div>
                  </div>
                  {verificationStatus === "verified" && (
                    <div className="flex h-4 w-4 flex-shrink-0 items-center justify-center rounded-full bg-[#3C50E0]">
                      <svg
                        className="h-3 w-3 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Right Content Area */}
            <div className="flex-1">
              {currentStep === 1
                ? renderRegistrationStep()
                : renderVerificationStep()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MemberRegisterVerifyPage;
