import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { AuthContext } from "Src/authContext";
import { GlobalContext, showToast } from "Src/globalContext";
import {
  getUserDetailsByIdAPI,
  updateUserDetailsAPI,
} from "Src/services/userService";
import { ClipLoader } from "react-spinners";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

const StripeOnboardingCompletePage = () => {
  const navigate = useNavigate();
  const { state } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [isLoading, setIsLoading] = useState(true);
  const [isSuccess, setIsSuccess] = useState(false);
  const [message, setMessage] = useState("Verifying your Stripe setup...");

  // Function to update onboarding steps when Strip<PERSON> is verified
  const updateOnboardingSteps = async (userId, userDetails) => {
    try {
      // Parse existing steps or create new object
      let stepData = {};
      if (userDetails.steps) {
        try {
          stepData = JSON.parse(userDetails.steps);
        } catch (e) {
          console.error("Error parsing existing step data:", e);
          stepData = {};
        }
      }

      // Update the payment billing step as completed
      const updatedStepData = {
        ...stepData,
        payment_billing_complete: true,
        stripe_setup_completed: true,
        stripe_setup_completed_at: new Date().toISOString(),
      };

      // Prepare payload with all existing user fields plus updated steps
      const payload = {
        id: parseInt(userId),
        // Include all existing user fields to avoid overwriting
        first_name: userDetails.first_name || "",
        last_name: userDetails.last_name || "",
        email: userDetails.email || "",
        company_name: userDetails.company_name || "",
        company_address: userDetails.company_address || "",
        office_email: userDetails.office_email || "",
        phone: userDetails.phone || "",
        company_logo: userDetails.company_logo || "",
        license_company_logo: userDetails.license_company_logo || "",
        deposit_percent: userDetails.deposit_percent || 0,
        contract_agreement: userDetails.contract_agreement || "",
        survey: userDetails.survey ? userDetails.survey : "",
        routine_submission_date: userDetails.routine_submission_date
          ? userDetails.routine_submission_date
          : "",
        estimated_delivery: userDetails?.estimated_delivery
          ? JSON.stringify(userDetails.estimated_delivery)
          : "",
        edit_policy_link: userDetails.edit_policy_link || "",
        // Update the steps field with payment completion
        steps: JSON.stringify(updatedStepData),
      };

      const updateResult = await updateUserDetailsAPI(payload);

      if (updateResult.error) {
        throw new Error(
          updateResult.message || "Failed to update onboarding steps"
        );
      }

      return updatedStepData;
    } catch (error) {
      console.error("Error updating onboarding steps:", error);
      throw error;
    }
  };

  // Function to determine where to redirect based on onboarding status
  const determineRedirectPath = (stepData, userDetails) => {
    // Check if onboarding is complete

    return "/member/dashboard";
  };

  useEffect(() => {
    const handleStripeComplete = async () => {
      try {
        setIsLoading(true);

        // Get current user ID
        const userId = localStorage.getItem("user");
        if (!userId) {
          showToast(globalDispatch, "User session not found", 4000, "error");
          navigate(
            state.role === "manager" ? "/manager/login" : "/member/login"
          );
          return;
        }

        // Wait a moment for Stripe to process the completion
        await new Promise((resolve) => setTimeout(resolve, 2000));

        // Fetch updated user details to check Stripe status
        const userResult = await getUserDetailsByIdAPI(userId);

        if (userResult.error) {
          showToast(
            globalDispatch,
            "Failed to fetch user details",
            4000,
            "error"
          );
          navigate(
            state.role === "manager" ? "/manager/projects" : "/member/dashboard"
          );
          return;
        }

        const userDetails = userResult.model;

        // Check if Stripe setup is now complete
        if (userDetails.has_stripe === true) {
          setIsSuccess(true);
          setMessage("Stripe setup completed successfully!");
          showToast(
            globalDispatch,
            "Payment system setup completed successfully!",
            4000,
            "success"
          );

          // Update onboarding steps to mark payment step as complete
          try {
            const updatedStepData = await updateOnboardingSteps(
              userId,
              userDetails
            );

            setTimeout(() => {
              // Check user role and redirect appropriately
              if (state.role === "manager") {
                // Managers don't need onboarding - go directly to projects
                navigate("/manager/projects");
              } else {
                // Determine redirect path based on onboarding status
                const redirectPath = determineRedirectPath(
                  updatedStepData,
                  userDetails
                );
                navigate(redirectPath);
              }
            }, 3000);
          } catch (error) {
            console.error("Error updating onboarding steps:", error);
            showToast(
              globalDispatch,
              "Payment verified but failed to update progress",
              4000,
              "warning"
            );

            // Still redirect even if step update failed
            setTimeout(() => {
              if (state.role === "manager") {
                navigate("/manager/projects");
              } else {
                navigate("/member/dashboard");
              }
            }, 3000);
          }
        } else {
          // Stripe setup might still be processing or failed
          setMessage(
            "Verification in process. Please check your profile in 24 hours "
          );
          setIsLoading(false);

          showToast(
            globalDispatch,
            "Stripe verification is still in process. Please check your profile in 24 minutes.",
            8000,
            "info"
          );

          // Try again after a longer delay
          setTimeout(async () => {
            try {
              const retryResult = await getUserDetailsByIdAPI(userId);
              if (!retryResult.error && retryResult.model.has_stripe === true) {
                setIsSuccess(true);
                setMessage("Stripe setup completed successfully!");
                showToast(
                  globalDispatch,
                  "Payment system setup completed successfully!",
                  4000,
                  "success"
                );

                // Update onboarding steps to mark payment step as complete
                try {
                  const updatedStepData = await updateOnboardingSteps(
                    userId,
                    retryResult.model
                  );

                  setTimeout(() => {
                    if (state.role === "manager") {
                      navigate("/manager/projects");
                    } else {
                      const redirectPath = determineRedirectPath(
                        updatedStepData,
                        retryResult.model
                      );
                      navigate(redirectPath);
                    }
                  }, 2000);
                } catch (error) {
                  console.error("Error updating onboarding steps:", error);
                  showToast(
                    globalDispatch,
                    "Payment verified but failed to update progress",
                    4000,
                    "warning"
                  );

                  setTimeout(() => {
                    if (state.role === "manager") {
                      navigate("/manager/projects");
                    } else {
                      navigate("/member/dashboard");
                    }
                  }, 2000);
                }
              } else {
                setMessage(
                  "Stripe setup incomplete. Redirecting to dashboard..."
                );
                showToast(
                  globalDispatch,
                  "Stripe setup may need additional steps",
                  4000,
                  "warning"
                );

                setTimeout(() => {
                  if (state.role === "manager") {
                    navigate("/manager/projects");
                  } else {
                    // For members, check if they have any onboarding steps
                    let redirectPath = "/member/dashboard";
                    if (retryResult.model.steps) {
                      try {
                        const stepData = JSON.parse(retryResult.model.steps);
                        redirectPath = determineRedirectPath(
                          stepData,
                          retryResult.model
                        );
                      } catch (e) {
                        console.error("Error parsing step data:", e);
                      }
                    }
                    navigate(redirectPath);
                  }
                }, 2000);
              }
            } catch (error) {
              console.error("Error on retry:", error);
              navigate(
                state.role === "manager"
                  ? "/manager/projects"
                  : "/member/dashboard"
              );
            }
          }, 5000);
        }
      } catch (error) {
        console.error("Error handling Stripe completion:", error);
        showToast(
          globalDispatch,
          "An error occurred during Stripe setup verification",
          4000,
          "error"
        );
        navigate(
          state.role === "manager" ? "/manager/projects" : "/member/dashboard"
        );
      } finally {
        setIsLoading(false);
      }
    };

    handleStripeComplete();
  }, [navigate, globalDispatch, state.role]);

  return (
    <div className="flex h-screen items-center justify-center bg-boxdark-2">
      <div className="text-center">
        <div className="mb-6">
          {isSuccess ? (
            <FontAwesomeIcon
              icon="fa-solid fa-check-circle"
              className="text-6xl text-primary"
            />
          ) : (
            <ClipLoader size={50} color="#3C50E0" />
          )}
        </div>
        <h2 className="mb-4 text-2xl font-bold text-white">
          {isSuccess ? "Setup Complete!" : "Processing..."}
        </h2>
        <p className="text-lg text-bodydark">{message}</p>
        <div className="mt-6">
          <p className="text-sm text-bodydark2">
            {isSuccess
              ? "You will be redirected shortly..."
              : "Please wait while we verify your Stripe setup..."}
          </p>
        </div>
      </div>
    </div>
  );
};

export default StripeOnboardingCompletePage;
