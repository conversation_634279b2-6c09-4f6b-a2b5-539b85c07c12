import React from "react";
import { GlobalContext } from "../globalContext";
import {
  getUserSubscriptionProductId,
  getSubscriptionTypeFromProductId,
  hasAdvancedFeatures,
  hasSubscriptionAccess,
  getProjectLimit,
  getSubscriptionDisplayName,
} from "../utils/subscriptionUtils";

/**
 * Custom hook for managing subscription data throughout the application
 * Provides a consistent interface for accessing subscription information
 */
export const useSubscription = () => {
  const { state: globalState } = React.useContext(GlobalContext);
  const [subscriptionProductId, setSubscriptionProductId] =
    React.useState(null);

  // Use cached user details from global state instead of making API calls
  const userDetails = globalState.userDetails?.data;

  // Get subscription product ID from cached user details or global state
  React.useEffect(() => {
    const getSubscriptionProductId = () => {
      console.log("🔍 useSubscription: Starting subscription detection...");
      console.log("🔍 userDetails:", userDetails);
      console.log("🔍 globalState.subscription:", globalState.subscription);

      try {
        // Get prices from global state for conversion
        const prices = globalState.subscription?.prices || [];
        console.log("🔍 prices array length:", prices.length);
        console.log("🔍 prices array:", prices);

        // First try to get from cached user details
        if (userDetails) {
          console.log("🔍 Processing userDetails...");
          console.log("🔍 userDetails.plan_id:", userDetails.plan_id);
          console.log(
            "🔍 userDetails.subscription_id:",
            userDetails.subscription_id
          );
          console.log("🔍 userDetails.subscription:", userDetails.subscription);

          const productId = getUserSubscriptionProductId(userDetails, prices);
          console.log("🔍 getUserSubscriptionProductId returned:", productId);

          if (userDetails.plan_id && prices.length > 0) {
            const matchingPrice = prices.find(
              (p) => p.id === userDetails.plan_id
            );
            console.log(
              "🔍 Matching price for plan_id",
              userDetails.plan_id,
              ":",
              matchingPrice
            );
          }

          setSubscriptionProductId(productId);
          return;
        }

        console.log("🔍 No userDetails, checking global subscription state...");

        // Fallback to global subscription state
        if (globalState.subscription?.currentSubscription) {
          console.log(
            "🔍 Found currentSubscription:",
            globalState.subscription.currentSubscription
          );
          // If we have currentSubscription with planId (price.id), convert to product_id
          const planId = globalState.subscription.currentSubscription.planId;
          console.log("🔍 currentSubscription planId:", planId);

          if (planId && prices.length > 0) {
            const priceObject = prices.find((price) => price.id === planId);
            console.log("🔍 Found price object for planId:", priceObject);
            if (priceObject) {
              console.log(
                "🔍 Setting subscriptionProductId to product_id:",
                priceObject.product_id
              );
              setSubscriptionProductId(priceObject.product_id);
              return;
            }
          }
          // Fallback to planId if no conversion possible
          console.log(
            "🔍 Fallback: setting subscriptionProductId to planId:",
            planId
          );
          setSubscriptionProductId(planId);
          return;
        }

        console.log("🔍 Checking localStorage fallback...");
        // Final fallback to localStorage for backward compatibility
        const legacySubscription = localStorage.getItem("UserSubscription");
        console.log(
          "🔍 legacySubscription from localStorage:",
          legacySubscription
        );

        if (legacySubscription) {
          const productId = getUserSubscriptionProductId(
            {
              subscription: parseInt(legacySubscription),
            },
            prices
          );
          console.log("🔍 Legacy subscription productId:", productId);
          setSubscriptionProductId(productId);
        } else {
          console.log("🔍 No subscription found anywhere - setting to null");
          setSubscriptionProductId(null);
        }
      } catch (error) {
        console.error("🔍 Error getting user subscription details:", error);

        // Fallback to global state on error
        if (globalState.subscription?.currentSubscription) {
          const planId = globalState.subscription.currentSubscription.planId;
          console.log("🔍 Error fallback: setting to planId:", planId);
          setSubscriptionProductId(planId);
        }
      }
    };

    getSubscriptionProductId();
  }, [userDetails, globalState.subscription]);

  // Computed values based on subscription
  const subscriptionType = React.useMemo(() => {
    const result = getSubscriptionTypeFromProductId(subscriptionProductId);
    console.log(
      "🎯 subscriptionType for productId",
      subscriptionProductId,
      ":",
      result
    );
    return result;
  }, [subscriptionProductId]);

  const hasAdvanced = React.useMemo(() => {
    const result = hasAdvancedFeatures(subscriptionProductId);
    console.log(
      "🎯 hasAdvanced for productId",
      subscriptionProductId,
      ":",
      result
    );
    return result;
  }, [subscriptionProductId]);

  const projectLimit = React.useMemo(() => {
    const result = getProjectLimit(subscriptionProductId);
    console.log(
      "🎯 projectLimit for productId",
      subscriptionProductId,
      ":",
      result
    );
    return result;
  }, [subscriptionProductId]);

  const displayName = React.useMemo(() => {
    const result = getSubscriptionDisplayName(subscriptionProductId);
    console.log(
      "🎯 displayName for productId",
      subscriptionProductId,
      ":",
      result
    );
    return result;
  }, [subscriptionProductId]);

  // Feature access helpers
  const hasStudioAccess = React.useMemo(() => {
    const result = hasSubscriptionAccess(subscriptionProductId, "studio");
    console.log(
      "🎯 hasStudioAccess for productId",
      subscriptionProductId,
      ":",
      result
    );
    return result;
  }, [subscriptionProductId]);

  const hasPortalAccess = React.useMemo(() => {
    const result = hasSubscriptionAccess(subscriptionProductId, "portal");
    console.log(
      "🎯 hasPortalAccess for productId",
      subscriptionProductId,
      ":",
      result
    );
    return result;
  }, [subscriptionProductId]);

  const hasCompleteAccess = React.useMemo(() => {
    const result = hasSubscriptionAccess(subscriptionProductId, "complete");
    console.log(
      "🎯 hasCompleteAccess for productId",
      subscriptionProductId,
      ":",
      result
    );
    return result;
  }, [subscriptionProductId]);

  // Helper functions for membership status
  const isMainMember = React.useMemo(() => {
    return (
      !userDetails?.main_user_details || userDetails.main_user_details.is_self
    );
  }, [userDetails]);

  const isSubMember = React.useMemo(() => {
    return (
      userDetails?.main_user_details && !userDetails.main_user_details.is_self
    );
  }, [userDetails]);

  return {
    // Core subscription data
    subscriptionProductId,
    subscriptionType, // For backward compatibility (1, 2, 3)
    displayName,

    // Feature access
    hasAdvanced, // Replaces parseInt(SubscriptionType) > 1
    hasStudioAccess,
    hasPortalAccess,
    hasCompleteAccess,

    // Project limits
    projectLimit,

    // User details and membership status
    userDetails,
    isMainMember,
    isSubMember,

    // Global subscription state
    globalSubscription: globalState.subscription,

    // Utility functions
    checkFeatureAccess: (feature) =>
      hasSubscriptionAccess(subscriptionProductId, feature),
  };
};
