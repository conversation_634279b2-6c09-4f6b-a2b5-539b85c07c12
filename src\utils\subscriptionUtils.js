/**
 * Subscription utilities for handling the new subscription system
 * Maps old subscription types (1,2,3) to new Stripe product IDs
 */

// Mapping from old subscription types to new product IDs
export const SUBSCRIPTION_MAPPING = {
  // Old system -> New system
  1: 6, // "Vocal Production Subscription" -> "The Studio" (ID 3)
  2: 7, // "Producer/Client Platform Subscription" -> "The Portal" (ID 2)
  3: 8, // "Both (Vocal production and producer/client platform)" -> "Complete Suite" (ID 4)
};

// Reverse mapping for backward compatibility
export const REVERSE_SUBSCRIPTION_MAPPING = {
  6: 2, // "The Portal" -> equivalent to old subscription 2
  7: 1, // "The Studio" -> equivalent to old subscription 1
  8: 3, // "Complete Suite" -> equivalent to old subscription 3
};

// Product names mapping
export const PRODUCT_NAMES = {
  6: "The Portal",
  7: "The Studio",
  8: "Complete Suite",
};

// Legacy subscription names
export const LEGACY_SUBSCRIPTION_NAMES = {
  1: "Vocal Production Subscription",
  2: "Producer/Client Platform Subscription",
  3: "Both (Vocal production and producer/client platform)",
};

/**
 * Get the current user's subscription product ID
 * Handles both main members and sub-members
 * Note: This function now needs price data to convert plan_id (price.id) to product_id
 * @param {Object} userDetails - User details from API
 * @param {Array} prices - Array of price objects from Stripe API (optional)
 * @returns {number|null} - Product ID or null if no subscription
 */
export const getUserSubscriptionProductId = (userDetails, prices = null) => {
  console.log("🔧 getUserSubscriptionProductId called with:");
  console.log("🔧 userDetails:", userDetails);
  console.log("🔧 prices length:", prices?.length || 0);

  if (!userDetails) {
    console.log("🔧 No userDetails provided, returning null");
    return null;
  }

  // Check if user is a sub-member
  const isSubMember =
    userDetails.main_user_details && !userDetails.main_user_details.is_self;
  console.log("🔧 isSubMember:", isSubMember);

  let planId = null;

  if (isSubMember) {
    // For sub-members, use main member's plan_id (which is price.id)
    planId = userDetails.main_user_details.plan_id;
    console.log("🔧 Sub-member planId:", planId);
  } else {
    // For main members, check if they have a plan_id from Stripe subscription
    planId = userDetails.plan_id;
    console.log("🔧 Main member planId:", planId);
  }

  // If we have a plan_id (price.id) and prices array, convert to product_id
  if (planId && prices && prices.length > 0) {
    console.log(
      "🔧 Converting planId",
      planId,
      "to product_id using prices array"
    );
    const priceObject = prices.find((price) => price.id === planId);
    console.log("🔧 Found price object:", priceObject);
    if (priceObject) {
      console.log("🔧 Returning product_id:", priceObject.product_id);
      return priceObject.product_id;
    } else {
      console.log("🔧 No matching price found for planId:", planId);
    }
  }

  // If we have plan_id but no prices array, return plan_id for backward compatibility
  if (planId) {
    console.log("🔧 No prices array, returning planId as fallback:", planId);
    return planId;
  }

  // Fallback: convert legacy subscription to new product ID
  const legacySubscription = userDetails.subscription;
  console.log("🔧 Checking legacy subscription:", legacySubscription);
  if (legacySubscription && SUBSCRIPTION_MAPPING[legacySubscription]) {
    const mappedProductId = SUBSCRIPTION_MAPPING[legacySubscription];
    console.log(
      "🔧 Legacy subscription mapped to product_id:",
      mappedProductId
    );
    return mappedProductId;
  }

  console.log("🔧 No subscription found, returning null");
  return null;
};

/**
 * Get subscription type for backward compatibility
 * Converts new product ID back to old subscription type (1,2,3)
 * @param {number} productId - Stripe product ID
 * @returns {number|null} - Legacy subscription type or null
 */
export const getSubscriptionTypeFromProductId = (productId) => {
  return REVERSE_SUBSCRIPTION_MAPPING[productId] || null;
};

/**
 * Check if user has access to features based on subscription
 * @param {number} productId - User's subscription product ID
 * @param {string} feature - Feature to check ('portal', 'studio', 'complete')
 * @returns {boolean} - Whether user has access
 */
export const hasSubscriptionAccess = (productId, feature) => {
  if (!productId) return false;

  switch (feature) {
    case "studio":
      // Studio features available in "The Studio" (3) and "Complete Suite" (4)
      return productId == 7 || productId == 8;
    case "portal":
      // Portal features available in "The Portal" (2) and "Complete Suite" (4)
      return productId == 6 || productId == 8;
    case "complete":
      // Complete suite features only in "Complete Suite" (4)
      return productId === 8;
    default:
      return false;
  }
};

/**
 * Check if subscription requires advanced features (equivalent to old subscription > 1)
 * @param {number} productId - User's subscription product ID
 * @returns {boolean} - Whether subscription has advanced features
 */
export const hasAdvancedFeatures = (productId) => {
  // Advanced features available in "The Portal" (2) and "Complete Suite" (4)
  // This replaces the old parseInt(SubscriptionType) > 1 checks
  return productId == 6 || productId == 8;
};

/**
 * Get project limit based on subscription product ID
 * @param {number} productId - User's subscription product ID
 * @returns {number} - Project limit
 */
export const getProjectLimit = (productId) => {
  // This logic should match what's currently in AddProjectPage.jsx
  switch (productId) {
    case 6: // The Portal (was subscription 2)
      return 100;
    case 7: // The Studio (was subscription 1)
      return 50;
    case 8: // Complete Suite (was subscription 3)
      return 150;
    default:
      return 50; // Default limit
  }
};

/**
 * Get subscription display name
 * @param {number} productId - User's subscription product ID
 * @returns {string} - Display name
 */
export const getSubscriptionDisplayName = (productId) => {
  return PRODUCT_NAMES[productId] || "Unknown Plan";
};
