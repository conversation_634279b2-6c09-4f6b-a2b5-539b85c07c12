import React, { useState } from "react";
import { useNavigate } from "react-router";
import { AuthContext } from "Src/authContext";
import { GlobalContext, showToast } from "Src/globalContext";
import { uploadFilesDataAPI } from "Src/services/workOrderService";
import WorkOrderUploadedDemo from "../WorkOrderUploadedDemo";
import WorkOrderEmptyDemo from "../WorkOrderEmptyDemo";
import WorkOrderLyrics from "../WorkOrderLyrics";
import AdminInstrumentals from "Components/AdminInstrumentals";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import IdeasNotesModal from "Components/ideaNotes";
import { useS3Upload } from "Src/libs/uploads3Hook";
import { Link } from "react-router-dom";

const WriterSubProject = ({
  subProject,
  workOrderDetails,
  uploadedDemoFiles,
  setLyrics,
  setDeleteFileId,
  setSubProjectDetails,
}) => {
  const navigate = useNavigate();

  const { state: authState } = React.useContext(AuthContext);
  const { dispatch: globalDispatch, state } = React.useContext(GlobalContext);
  const [showIdeasNotesModal, setShowIdeasNotesModal] = React.useState(false);
  const { uploadS3FilesAPI, progress, error, isUploading } = useS3Upload();

  const { songSubProjectsEditWorkOrder, subproject_update } = state;

  const [employeeType, setEmployeeType] = React.useState("");
  const [fileType, setFileType] = React.useState("");
  const [employeeId, setEmployeeId] = React.useState(null);

  const [songTitle, setSongTitle] = React.useState(subProject.type);
  const [bpm, setBpm] = React.useState(subProject.bpm);
  const [key, setKey] = React.useState(subProject.song_key);

  const [activeTab, setActiveTab] = useState("demos");

  const handleOnChangeTitle = (e) => {
    e.preventDefault();

    const songIndex = songSubProjectsEditWorkOrder.findIndex(
      (song) => song.subproject_id === subProject.id
    );

    if (songIndex > -1) {
      const updatedSongs = [...songSubProjectsEditWorkOrder];
      updatedSongs[songIndex].type_name = e.target.value;
      updatedSongs[songIndex].is_song = 1;
      globalDispatch({
        type: "SET_SONG_SUB_PROJECTS_EDIT_WORK_ORDER",
        payload: updatedSongs,
      });
    } else {
      globalDispatch({
        type: "SET_SONG_SUB_PROJECTS_EDIT_WORK_ORDER",
        payload: [
          ...songSubProjectsEditWorkOrder,
          {
            subproject_id: subProject.id,
            type_name: e.target.value,
            is_song: 1,
          },
        ],
      });
    }
  };

  const handleOnChangeBpm = (e) => {
    e.preventDefault();

    const songIndex = songSubProjectsEditWorkOrder.findIndex(
      (song) => song.subproject_id === subProject.id
    );

    if (songIndex > -1) {
      const updatedSongs = [...songSubProjectsEditWorkOrder];
      updatedSongs[songIndex].bpm = e.target.value;
      updatedSongs[songIndex].is_song = 1;
      globalDispatch({
        type: "SET_SONG_SUB_PROJECTS_EDIT_WORK_ORDER",
        payload: updatedSongs,
      });
    } else {
      globalDispatch({
        type: "SET_SONG_SUB_PROJECTS_EDIT_WORK_ORDER",
        payload: [
          ...songSubProjectsEditWorkOrder,
          {
            subproject_id: subProject.id,
            bpm: e.target.value,
            is_song: 1,
          },
        ],
      });
    }
  };

  const handleOnChangeKey = (e) => {
    e.preventDefault();

    const songIndex = songSubProjectsEditWorkOrder.findIndex(
      (song) => song.subproject_id === subProject.id
    );

    if (songIndex > -1) {
      const updatedSongs = [...songSubProjectsEditWorkOrder];
      updatedSongs[songIndex].song_key = e.target.value;
      updatedSongs[songIndex].is_song = 1;
      globalDispatch({
        type: "SET_SONG_SUB_PROJECTS_EDIT_WORK_ORDER",
        payload: updatedSongs,
      });
    } else {
      globalDispatch({
        type: "SET_SONG_SUB_PROJECTS_EDIT_WORK_ORDER",
        payload: [
          ...songSubProjectsEditWorkOrder,
          {
            subproject_id: subProject.id,
            song_key: e.target.value,
            is_song: 1,
          },
        ],
      });
    }
  };

  const handleUpdateLyrics = (lyrics) => {
    setLyrics({
      subproject_id: subProject.id,
      lyrics: lyrics,
    });
  };

  const handleEmployeeType = (employeeType) => {
    setEmployeeType(employeeType);
    if (employeeType === "writer") {
      setEmployeeId(Number(workOrderDetails.writer_id));
    }
  };

  const handleUploadFileType = (fileType) => {
    setFileType(fileType);
  };

  const handleSubProjectDetails = (e) => {
    e.preventDefault();
    if (songTitle === "") {
      showToast(globalDispatch, "Please enter song title", 5000, "error");
      return;
    }
    if (bpm === "") {
      showToast(globalDispatch, "Please enter bpm", 5000, "error");
      return;
    }
    if (key === "") {
      showToast(globalDispatch, "Please enter key", 5000, "error");
      return;
    }

    setSubProjectDetails({
      subproject_id: Number(subProject.id),
      type_name: songTitle,
      bpm: bpm,
      song_key: key,
      is_song: 1,
    });
  };

  const handleDemoUploads = async (formData) => {
    try {
      // setIsLoading(true);
      const result = await uploadS3FilesAPI(formData);
      if (!result.error) {
        const payload = {
          project_id: subProject.project_id
            ? Number(subProject.project_id)
            : null,
          subproject_id: subProject.id ? Number(subProject.id) : null,
          workorder_id: Number(subProject.workorder_id),
          employee_id: Number(workOrderDetails.writer_id),
          employee_type: "writer",
          type: "demo",
          attachments: result.attachments,
          is_from_admin: 1,
        };

        const fileUploadDataResult = await uploadFilesDataAPI(payload);
        if (!fileUploadDataResult.error) {
          showToast(globalDispatch, "Files updated successfully", 5000);
          globalDispatch({
            type: "SET_SUBPROJECT_UPDATE",
            payload: !subproject_update,
          });
        } else {
          showToast(
            globalDispatch,
            fileUploadDataResult.message,
            5000,
            "error"
          );
        }
        // setIsLoading(false);
      } else {
        showToast(globalDispatch, result.message, 5000, "error");
        // setIsLoading(false);
      }
    } catch (error) {
      showToast(globalDispatch, error.message, 5000, "error");
    }
  };

  React.useEffect(() => {
    if (subProject) {
      setSongTitle(subProject.type_name);
      setBpm(subProject.bpm);
      setKey(subProject.song_key);
    }
  }, [subProject]);

  const handleWriterNotesModalClose = () => {
    setShowIdeasNotesModal(false);
  };
  console.log(subProject);

  return (
    <div className="shadow-default rounded border border-strokedark bg-boxdark p-4">
      {/* Header Section */}
      <div className="mb-4 flex items-center justify-between border-b border-strokedark pb-4">
        <div className="flex flex-col">
          <div className="flex items-center gap-3">
            <h4 className="text-lg font-semibold text-white">
              <span>{subProject.type}:</span>
              <span className="ml-2">{subProject.program_name}</span>
            </h4>
            <Link
              to={`/${authState.role}/view-project/${subProject.project_id}`}
              className="cursor-pointer text-primary hover:underline"
              onClick={() => {
                localStorage.setItem("projectClientId", "");
                localStorage.setItem("projectTeamName", "");
                localStorage.setItem("projectMixTypeId", "");
                localStorage.setItem("projectMixDateStart", "");
                localStorage.setItem("projectMixDateEnd", "");
                localStorage.setItem("projectPageSize", "");
              }}
            >
              {subProject.team_name}
            </Link>
          </div>
          <span className="mt-1 text-sm text-bodydark2">
            Team Type: {subProject.team_type === 1 ? "All Girl" : "Coed"}
          </span>
        </div>

        <FontAwesomeIcon
          className="cursor-pointer text-2xl text-white hover:text-primary"
          icon="fa-solid fa-book"
          onClick={() => setShowIdeasNotesModal(true)}
        />
      </div>

      {/* Tabs Navigation */}
      <div className="mb-6 flex gap-2 border-b border-stroke">
        <button
          className={`px-4 py-2 text-sm font-semibold ${
            activeTab === "demos"
              ? "border-b-2 border-white text-primary"
              : "text-bodydark hover:text-white"
          }`}
          onClick={() => setActiveTab("demos")}
        >
          Demo Files
        </button>
        <button
          className={`px-4 py-2 text-sm font-semibold ${
            activeTab === "lyrics"
              ? "border-b-2 border-white text-primary"
              : "text-bodydark hover:text-white"
          }`}
          onClick={() => setActiveTab("lyrics")}
        >
          Lyrics
        </button>
        {subProject.admin_writer_instrumentals?.length > 0 && (
          <button
            className={`px-4 py-2 text-sm font-semibold ${
              activeTab === "instrumentals"
                ? "border-b-2 border-white text-primary"
                : "text-bodydark hover:text-white"
            }`}
            onClick={() => setActiveTab("instrumentals")}
          >
            Instrumentals
          </button>
        )}
        {subProject.is_song ? (
          <button
            className={`px-4 py-2 text-sm font-semibold ${
              activeTab === "details"
                ? "border-b-2 border-white text-primary"
                : "text-bodydark hover:text-white"
            }`}
            onClick={() => setActiveTab("details")}
          >
            Song Details
          </button>
        ) : null}
      </div>

      {/* Tab Content */}
      <div className="mt-4">
        {activeTab === "details" && subProject.is_song ? (
          <div className="min-h-[350px] border border-form-strokedark bg-form-input p-4">
            <div className="flex flex-col gap-4">
              <div className="w-[50%]">
                <label className="mb-2.5 block text-sm font-medium text-white">
                  Song Title
                </label>
                <input
                  type="text"
                  placeholder="Enter Song Title"
                  defaultValue={subProject.type_name}
                  onChange={(e) => {
                    setSongTitle(e.target.value);
                    handleOnChangeTitle(e);
                  }}
                  className="w-full rounded-md border border-form-strokedark bg-form-input px-4 py-2.5 text-white focus:border-white focus:outline-none"
                />
              </div>
              <div className="flex w-[50%] flex-row gap-3">
                <div className="basis-1/2">
                  <label className="mb-2.5 block text-sm font-medium text-white">
                    BPM
                  </label>
                  <input
                    type="text"
                    placeholder="Enter BPM"
                    defaultValue={subProject.bpm}
                    onChange={(e) => {
                      setBpm(e.target.value);
                      handleOnChangeBpm(e);
                    }}
                    className="w-full rounded-md border border-form-strokedark bg-form-input px-4 py-2.5 text-white focus:border-white focus:outline-none"
                  />
                </div>
                <div className="basis-1/2">
                  <label className="mb-2.5 block text-sm font-medium text-white">
                    Key
                  </label>
                  <input
                    type="text"
                    placeholder="Enter Key"
                    defaultValue={subProject.song_key}
                    onChange={(e) => {
                      setKey(e.target.value);
                      handleOnChangeKey(e);
                    }}
                    className="w-full rounded-md border border-form-strokedark bg-form-input px-4 py-2.5 text-white focus:border-white focus:outline-none"
                  />
                </div>
              </div>
              <div className="col-span-12">
                <button
                  className="rounded-md bg-primary px-4 py-2 text-sm font-semibold text-white hover:bg-opacity-90"
                  type="button"
                  onClick={handleSubProjectDetails}
                >
                  Update Song
                </button>
              </div>
            </div>
          </div>
        ) : null}

        {activeTab === "demos" && (
          <div className="rounded border border-form-strokedark bg-form-input p-4">
            {uploadedDemoFiles.length > 0 ? (
              <WorkOrderUploadedDemo
                uploadedFilesProgressData={{ progress, error, isUploading }}
                uploadedFiles={uploadedDemoFiles}
                setDeleteFileId={setDeleteFileId}
                setEmployeeType={handleEmployeeType}
                setFileUploadType={handleUploadFileType}
                setFormData={handleDemoUploads}
              />
            ) : (
              <WorkOrderEmptyDemo
                uploadedFilesProgressData={{ progress, error, isUploading }}
                setEmployeeType={handleEmployeeType}
                setFileUploadType={handleUploadFileType}
                setFormData={handleDemoUploads}
              />
            )}
          </div>
        )}

        {activeTab === "lyrics" && (
          <div className="min-h-[350px] rounded border border-form-strokedark bg-form-input p-4">
            <WorkOrderLyrics
              subProjectId={subProject.id}
              lyrics={subProject.lyrics}
              setLyrics={handleUpdateLyrics}
            />
          </div>
        )}

        {activeTab === "instrumentals" && (
          <div className="min-h-[350px] rounded border border-form-strokedark bg-form-input p-4">
            <AdminInstrumentals
              uploadedFiles={subProject.admin_writer_instrumentals}
            />
          </div>
        )}
      </div>

      {showIdeasNotesModal && (
        <IdeasNotesModal
          theme={subProject.survey.theme_of_the_routine}
          ideas={subProject.ideas}
          setModalClose={handleWriterNotesModalClose}
          notes={subProject.notes}
        />
      )}
    </div>
  );
};

export default WriterSubProject;
