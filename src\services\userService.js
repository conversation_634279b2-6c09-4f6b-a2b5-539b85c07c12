import axios from "axios";
import MkdSDK from "../utils/MkdSDK";
let sdk = new MkdSDK();

export const addUserAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/user/add`;
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const retrieveAllUserAPI = async (page, limit, filter) => {
  try {
    const payload = {
      page: page,
      limit: limit,
      filter: filter,
    };
    const uri = `/v3/api/custom/equality_record/user/retrieve`;
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const getUserDetailsByIdAPI = async (id) => {
  try {
    const uri = `/v3/api/custom/equality_record/user/view/${id}`;
    const res = await sdk.callRawAPI(uri, {}, "GET");
    return res;
  } catch (error) {
    return error;
  }
};

export const updateUserDetailsAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/user/${payload.id}`;
    const res = await sdk.callRawAPI(uri, payload, "PUT");
    return res;
  } catch (error) {
    return error;
  }
};

export const getUserSubsAPI = async () => {
  try {
    const uri = `/v3/api/custom/equality_record/user/subscription`;
    const res = await sdk.callRawAPI(uri, [], "GET");
    return res;
  } catch (error) {
    return error;
  }
};

export const deleteUserAPI = async (id, email) => {
  try {
    const uri = `/v3/api/custom/equality_record/user/${id}`;
    const res = await sdk.callRawAPI(uri, { email }, "DELETE");
    return res;
  } catch (error) {
    return error;
  }
};

export const uploadUserPhotoAPI = async (formData) => {
  try {
    const uri = `https://equalitydev.manaknightdigital.com/v3/api/custom/equality_record/user/upload/file`;
    const res = await axios.post(uri, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
        "x-project":
          "ZXF1YWxpdHlyZWNvcmQ6cDV3YjJnd2M5aWVyZjhzem5ldDZibnNjcTA3bnVyYQ==",
        Authorization: `Bearer ${localStorage.getItem("token")}`,
      },
    });

    return res.data;
  } catch (error) {
    return error;
  }
};

export const createOrUpdateUserProfilePhotoAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/user/photo/${payload.id}`;
    const res = await sdk.callRawAPI(uri, payload, "PUT");
    return res;
  } catch (error) {
    return error;
  }
};
