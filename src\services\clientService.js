import MkdSDK from "../utils/MkdSDK";

let sdk = new MkdSDK();

export const retrieveAllClientAPI = async (page, limit, filter) => {
  try {
    const payload = {
      page: page,
      limit: limit,
      filter: filter,
    };
    const uri = "/v3/api/custom/equality_record/client/retrieve";
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const getAllProjectsClientAPI = async () => {
  try {
    const uri = "/v3/api/custom/equality_record/project/client/get_all";
    const res = await sdk.callRawAPI(uri, [], "GET");
    return res;
  } catch (error) {
    return error;
  }
};

export const retrieveAllForClientForMember = async (page, limit, filter) => {
  try {
    const payload = {
      page,
      limit,
      filter: filter,
    };
    const uri = `/v3/api/custom/equality_record/client/retrieve`;
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const getAllClientsAPI = async () => {
  try {
    const uri = "/v3/api/custom/equality_record/user/view/all/client";
    const res = await sdk.callRawAPI(uri, [], "GET");
    return res;
  } catch (error) {
    return error;
  }
};

export const getAllClientsFilterAdminAPI = async () => {
  try {
    const uri = "/v3/api/custom/equality_record/user/view/all/client/project";
    const res = await sdk.callRawAPI(uri, [], "GET");
    return res;
  } catch (error) {
    return error;
  }
};

export const getAllClientsFilterManagerAPI = async (page, limit, filter) => {
  const payload = {
    page,
    limit,
    filter: filter,
  };
  try {
    const uri =
      "/v3/api/custom/equality_record/client/manager/retrieve/project";
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const getAllClientsFilterMemberAPI = async () => {
  try {
    const uri = "/v3/api/custom/equality_record/client/get_all/project";
    const res = await sdk.callRawAPI(uri, [], "GET");
    return res;
  } catch (error) {
    return error;
  }
};

export const retrieveAllForClientForAdmin = async (page, limit, filter) => {
  try {
    const payload = {
      page,
      limit,
      filter: filter,
    };
    const uri = `/v3/api/custom/equality_record/client/retrieve`;
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const AssignClient = async (payload, id) => {
  try {
    const uri = `/v3/api/custom/equality_record/user/client/assign_to_member/${id}`;
    const res = await sdk.callRawAPI(uri, payload, "PUT");
    return res;
  } catch (error) {
    return error;
  }
};

export const getAllMemberAssignedToClient = async (id) => {
  try {
    const uri = `/v3/api/custom/equality_record/client/members/${id}`;
    const res = await sdk.callRawAPI(uri, [], "GET");
    return res;
  } catch (error) {
    return error;
  }
};

export const UnAssignClient = async (payload, id) => {
  try {
    const uri = `/v3/api/custom/equality_record/user/client/assign_to_member/${id}`;
    const res = await sdk.callRawAPI(uri, payload, "PUT");
    return res;
  } catch (error) {
    return error;
  }
};

export const viewClientDetails = async (id) => {
  try {
    const uri = `/v3/api/custom/equality_record/client/${id}`;
    const res = await sdk.callRawAPI(uri, [], "GET");
    return res;
  } catch (error) {
    return error;
  }
};

export const myClients = async (id) => {
  try {
    const uri = `/v3/api/custom/equality_record/user/view/clients/${id}`;
    const res = await sdk.callRawAPI(uri, [], "GET");
    return res;
  } catch (error) {
    return error;
  }
};

export const getAllProducersAPI = async () => {
  try {
    const uri = "/v3/api/custom/equality_record/client/get_all/producers";
    const res = await sdk.callRawAPI(uri, [], "GET");
    return res;
  } catch (error) {
    return error;
  }
};

export const getAllClientAPI = async () => {
  try {
    const uri = "/v3/api/custom/equality_record/client/get_all";
    const res = await sdk.callRawAPI(uri, [], "GET");
    return res;
  } catch (error) {
    return error;
  }
};

export const getClientDetailsAPI = async (id) => {
  try {
    const uri = `/v3/api/custom/equality_record/client/${id}`;
    const res = await sdk.callRawAPI(uri, [], "GET");
    return res;
  } catch (error) {
    return error;
  }
};

export const addClientAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/client/add`;
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const updateClientAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/client/${payload.id}`;
    const res = await sdk.callRawAPI(uri, payload, "PUT");
    return res;
  } catch (error) {
    return error;
  }
};

export const deleteClientAPI = async (id) => {
  try {
    const uri = `/v3/api/custom/equality_record/client/${id}`;
    const res = await sdk.callRawAPI(uri, [], "DELETE");
    return res;
  } catch (error) {
    return error;
  }
};

export const retrieveAllProjectsForClientAPI = async (page, limit, filter) => {
  try {
    const payload = {
      page: page,
      limit: limit,
      filter: filter,
    };
    const uri = "/v3/api/custom/equality_record/project/client/retrieve_all";
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};
