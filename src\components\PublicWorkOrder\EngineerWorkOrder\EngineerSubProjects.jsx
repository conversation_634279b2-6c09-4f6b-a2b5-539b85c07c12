import React from "react";
import EngineerSubProject from "./EngineerSubProject";

const EngineerSubProjects = ({
  canUpload = true,
  isPublic,
  subProjects,
  workOrderDetails,
  setDeleteFileId,
}) => {
  console.log("hiii6");
  console.log(subProjects.length);
  return (
    <>
      {subProjects && subProjects.length > 0 ? (
        subProjects.map((subProject, index) => {
          return (
            <EngineerSubProject
              key={index}
              isPublic={isPublic}
              canUpload={canUpload}
              workOrderDetails={workOrderDetails}
              subProject={subProject}
              uploadedFiles={subProject.masters}
              setDeleteFileId={setDeleteFileId}
            />
          );
        })
      ) : (
        <div className="flex flex-1 items-center justify-center">
          <div className="text-center">
            <svg
              className="mx-auto mb-4 h-12 w-12 text-boxdark-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
              />
            </svg>
            <p className="text-base font-medium text-bodydark">
              No Master Files Uploaded
            </p>
          </div>
        </div>
      )}
    </>
  );
};

export default EngineerSubProjects;
