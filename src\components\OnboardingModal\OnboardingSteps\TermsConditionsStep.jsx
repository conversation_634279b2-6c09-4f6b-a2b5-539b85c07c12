import React, { useState, useEffect } from "react";
import { <PERSON>lipLoader } from "react-spinners";
import MkdSDK from "../../../utils/MkdSDK";

const TermsConditionsStep = ({ stepData, userDetails, onComplete }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [privacyAccepted, setPrivacyAccepted] = useState(false);
  const [legalDocuments, setLegalDocuments] = useState({
    termsOfService: "",
    privacyPolicy: "",
  });
  const [showTermsAccordion, setShowTermsAccordion] = useState(false);
  const [showPrivacyAccordion, setShowPrivacyAccordion] = useState(false);

  // Fetch legal documents
  const fetchLegalDocuments = async () => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        "/v3/api/custom/equality_record/user/legal-documents",
        [],
        "GET"
      );

      if (!response.error && response.data && response.data.length > 0) {
        const legalDoc = response.data[0];
        setLegalDocuments({
          termsOfService: legalDoc.terms_of_service || "",
          privacyPolicy: legalDoc.privacy_policy || "",
        });
      } else {
        console.error("Error fetching legal documents:", response.message);
      }
    } catch (err) {
      console.error("Error fetching legal documents:", err);
    }
  };

  // Fetch legal documents on component mount
  useEffect(() => {
    fetchLegalDocuments();
  }, []);

  const handleSubmit = async () => {
    if (!termsAccepted || !privacyAccepted) {
      return;
    }

    try {
      setIsLoading(true);

      const termsData = {
        terms_accepted: true,
        privacy_accepted: true,
        terms_accepted_at: new Date().toISOString(),
        privacy_accepted_at: new Date().toISOString(),
      };

      onComplete(termsData);
    } catch (error) {
      console.error("Error saving terms acceptance:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const isFormValid = termsAccepted && privacyAccepted;

  return (
    <div className="mx-auto flex w-full max-w-[397px] flex-col items-center bg-white p-0">
      <div className="flex w-full flex-col items-center">
        <svg
          className="mx-auto h-16 w-16"
          width="57"
          height="56"
          viewBox="0 0 57 56"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect x="4.5" y="4" width="48" height="48" rx="24" fill="#E0E6FC" />
          <rect
            x="4.5"
            y="4"
            width="48"
            height="48"
            rx="24"
            stroke="#F6F7F8"
            stroke-width="8"
          />
          <path
            d="M24.75 26.5C24.5511 26.5 24.3603 26.579 24.2197 26.7197C24.079 26.8603 24 27.0511 24 27.25C24 27.4489 24.079 27.6397 24.2197 27.7803C24.3603 27.921 24.5511 28 24.75 28H32.25C32.4489 28 32.6397 27.921 32.7803 27.7803C32.921 27.6397 33 27.4489 33 27.25C33 27.0511 32.921 26.8603 32.7803 26.7197C32.6397 26.579 32.4489 26.5 32.25 26.5H24.75ZM24 30.25C24 30.0511 24.079 29.8603 24.2197 29.7197C24.3603 29.579 24.5511 29.5 24.75 29.5H32.25C32.4489 29.5 32.6397 29.579 32.7803 29.7197C32.921 29.8603 33 30.0511 33 30.25C33 30.4489 32.921 30.6397 32.7803 30.7803C32.6397 30.921 32.4489 31 32.25 31H24.75C24.5511 31 24.3603 30.921 24.2197 30.7803C24.079 30.6397 24 30.4489 24 30.25ZM24 33.25C24 33.0511 24.079 32.8603 24.2197 32.7197C24.3603 32.579 24.5511 32.5 24.75 32.5H27.75C27.9489 32.5 28.1397 32.579 28.2803 32.7197C28.421 32.8603 28.5 33.0511 28.5 33.25C28.5 33.4489 28.421 33.6397 28.2803 33.7803C28.1397 33.921 27.9489 34 27.75 34H24.75C24.5511 34 24.3603 33.921 24.2197 33.7803C24.079 33.6397 24 33.4489 24 33.25Z"
            fill="#3C50E0"
          />
          <path
            d="M30.75 16H22.5C21.7044 16 20.9413 16.3161 20.3787 16.8787C19.8161 17.4413 19.5 18.2044 19.5 19V37C19.5 37.7956 19.8161 38.5587 20.3787 39.1213C20.9413 39.6839 21.7044 40 22.5 40H34.5C35.2956 40 36.0587 39.6839 36.6213 39.1213C37.1839 38.5587 37.5 37.7956 37.5 37V22.75L30.75 16ZM30.75 17.5V20.5C30.75 21.0967 30.9871 21.669 31.409 22.091C31.831 22.5129 32.4033 22.75 33 22.75H36V37C36 37.3978 35.842 37.7794 35.5607 38.0607C35.2794 38.342 34.8978 38.5 34.5 38.5H22.5C22.1022 38.5 21.7206 38.342 21.4393 38.0607C21.158 37.7794 21 37.3978 21 37V19C21 18.6022 21.158 18.2206 21.4393 17.9393C21.7206 17.658 22.1022 17.5 22.5 17.5H30.75Z"
            fill="#3C50E0"
          />
        </svg>

        <h2 className="mb-[52px] font-satoshi text-2xl font-bold text-[#131E2B]">
          Review Our Legal Agreements
        </h2>
        <p className="max-w-md text-center font-satoshi text-base text-[#667484]">
          To continue setting up your cheer music business with CheerEQ, please
          take a moment to review our essential agreements. <br /> <br /> Your
          understanding and agreement are required to proceed.
        </p>
      </div>
      <div
        className="mt-8 flex w-full flex-col items-center"
        style={{ gap: 24 }}
      >
        {/* Link area */}
        <div className="flex flex-col items-center" style={{ gap: 23 }}>
          <button
            type="button"
            className="font-roboto flex items-center gap-2 text-[16px] font-medium text-black underline hover:text-[#3C50E0]"
            onClick={() => setShowTermsAccordion(!showTermsAccordion)}
          >
            Terms of Service
            <svg
              className={`h-4 w-4 transition-transform ${
                showTermsAccordion ? "rotate-180" : ""
              }`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </button>
          <button
            type="button"
            className="font-roboto flex items-center gap-2 text-[16px] font-medium text-black underline hover:text-[#3C50E0]"
            onClick={() => setShowPrivacyAccordion(!showPrivacyAccordion)}
          >
            Privacy Policy
            <svg
              className={`h-4 w-4 transition-transform ${
                showPrivacyAccordion ? "rotate-180" : ""
              }`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </button>
        </div>

        {/* Terms of Service Accordion */}
        {showTermsAccordion && (
          <div className="w-full max-w-md rounded-lg border border-gray-200 bg-gray-50 p-4">
            <h3 className="font-roboto mb-3 text-sm font-semibold text-[#131E2B]">
              Terms of Service
            </h3>
            <div className="max-h-60 overflow-y-auto">
              {legalDocuments.termsOfService ? (
                <div
                  className="font-roboto text-xs leading-relaxed text-gray-700"
                  dangerouslySetInnerHTML={{
                    __html: legalDocuments.termsOfService,
                  }}
                />
              ) : (
                <p className="font-roboto text-xs text-gray-500">
                  Loading Terms of Service...
                </p>
              )}
            </div>
          </div>
        )}

        {/* Privacy Policy Accordion */}
        {showPrivacyAccordion && (
          <div className="w-full max-w-md rounded-lg border border-gray-200 bg-gray-50 p-4">
            <h3 className="font-roboto mb-3 text-sm font-semibold text-[#131E2B]">
              Privacy Policy
            </h3>
            <div className="max-h-60 overflow-y-auto">
              {legalDocuments.privacyPolicy ? (
                <div
                  className="font-roboto text-xs leading-relaxed text-gray-700"
                  dangerouslySetInnerHTML={{
                    __html: legalDocuments.privacyPolicy,
                  }}
                />
              ) : (
                <p className="font-roboto text-xs text-gray-500">
                  Loading Privacy Policy...
                </p>
              )}
            </div>
          </div>
        )}

        {/* Checkbox area */}
        <div
          className="mt-6 flex w-full flex-col items-start"
          style={{ gap: 9 }}
        >
          <label className="flex cursor-pointer items-center gap-2">
            <input
              type="checkbox"
              id="terms"
              checked={termsAccepted}
              onChange={(e) => setTermsAccepted(e.target.checked)}
              className="h-4 w-4 rounded border border-[#3C50E0] text-[#3C50E0] focus:ring-[#3C50E0]"
            />
            <span className="font-roboto text-[12px] tracking-wider text-black">
              I have read and agree to the Terms of Service.
            </span>
          </label>
          <label className="flex cursor-pointer items-center gap-2">
            <input
              type="checkbox"
              id="privacy"
              checked={privacyAccepted}
              onChange={(e) => setPrivacyAccepted(e.target.checked)}
              className="h-4 w-4 rounded border border-[#3C50E0] text-[#3C50E0] focus:ring-[#3C50E0]"
            />
            <span className="font-roboto text-[12px] tracking-wider text-black">
              I have read and agree to the Privacy Policy.
            </span>
          </label>
        </div>
      </div>
      <button
        onClick={handleSubmit}
        disabled={!isFormValid || isLoading}
        className="font-inter mt-8 w-full rounded-[12px] bg-[#3C50E0] py-3 text-base font-semibold text-[#F5F7FF] transition hover:bg-[#2B3EB4] disabled:opacity-50"
        type="button"
      >
        {isLoading ? (
          <div className="flex items-center justify-center gap-2">
            <ClipLoader size={16} color="#fff" />
            Accepting...
          </div>
        ) : (
          "Agree & Continue"
        )}
      </button>
    </div>
  );
};

export default TermsConditionsStep;
