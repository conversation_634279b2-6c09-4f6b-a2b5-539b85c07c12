import React from "react";

const BusinessInfoSection = () => {
  return (
    <div className="mx-auto flex w-full max-w-[397px] flex-col items-center gap-8 rounded-[16px] bg-white p-0 shadow-none">
      <div className="flex w-full flex-col items-center" style={{ gap: 64 }}>
        <h2 className="font-inter mb-1 text-center text-[20px] font-bold leading-[28px] text-[#131E2B]">
          Your Business Identity
        </h2>
        <p className="font-inter mb-2 max-w-[350px] text-center text-[14px] font-normal leading-[20px] text-[#667484]">
          Personalize your CheerEQ account with your business details. This
          information will be used for your profile, billing, and client
          communications.
        </p>
      </div>
      <form className="mt-8 flex w-full flex-col items-center gap-6">
        <div className="flex w-full flex-col gap-4">
          <label className="font-inter mb-1 text-[14px] font-medium text-[#4A5B70]">
            Company Name
          </label>
          <input
            type="text"
            className="font-inter w-full rounded-lg border border-[#D1D6DE] bg-white p-3 text-[14px] text-[#414651] outline-none focus:border-primary/70"
            placeholder="Enter your company name"
          />
        </div>
        <div className="flex w-full flex-col gap-4">
          <label className="font-inter mb-1 text-[14px] font-medium text-[#4A5B70]">
            Company Address
          </label>
          <input
            type="text"
            className="font-inter w-full rounded-lg border border-[#D1D6DE] bg-white p-3 text-[14px] text-[#414651] outline-none focus:border-primary/70"
            placeholder="Enter your company address"
          />
        </div>
        <div className="flex w-full flex-col gap-4">
          <label className="font-inter mb-1 text-[14px] font-medium text-[#4A5B70]">
            Office Email
          </label>
          <input
            type="email"
            className="font-inter w-full rounded-lg border border-[#D1D6DE] bg-white p-3 text-[14px] text-[#414651] outline-none focus:border-primary/70"
            placeholder="Enter your office email"
          />
        </div>
        <div className="flex w-full flex-col gap-4">
          <label className="font-inter mb-1 text-[14px] font-medium text-[#4A5B70]">
            Phone Number
          </label>
          <input
            type="tel"
            className="font-inter w-full rounded-lg border border-[#D1D6DE] bg-white p-3 text-[14px] text-[#414651] outline-none focus:border-primary/70"
            placeholder="Enter your phone number"
          />
        </div>
        <div className="flex w-full flex-col gap-4">
          <label className="font-inter mb-1 text-[14px] font-medium text-[#4A5B70]">
            Company Logo
          </label>
          <div className="flex w-full items-center gap-3">
            <div className="flex h-16 w-16 items-center justify-center rounded-[12px] bg-[#E0E6FC]">
              <img
                src="/public/1717617310029-myeq_logo.png"
                alt="Company Logo"
                className="h-12 w-12 object-contain"
              />
            </div>
            <button
              type="button"
              className="rounded-full bg-[#3C50E0] px-4 py-2 text-sm font-semibold text-white hover:bg-[#2B3EB4]"
            >
              Upload
            </button>
          </div>
        </div>
        <div className="flex w-full flex-col gap-4">
          <label className="font-inter mb-1 text-[14px] font-medium text-[#4A5B70]">
            License Logo
          </label>
          <div className="flex w-full items-center gap-3">
            <div className="flex h-16 w-16 items-center justify-center rounded-[12px] bg-[#E0E6FC]">
              <img
                src="/public/1717617310029-myeq_logo.png"
                alt="License Logo"
                className="h-12 w-12 object-contain"
              />
            </div>
            <button
              type="button"
              className="rounded-full bg-[#3C50E0] px-4 py-2 text-sm font-semibold text-white hover:bg-[#2B3EB4]"
            >
              Upload
            </button>
          </div>
        </div>
        <div className="mt-8 flex w-full gap-3">
          <button
            type="submit"
            className="font-inter flex-1 rounded-[8px] bg-[#3C50E0] py-3 text-base font-semibold text-white transition hover:bg-[#2B3EB4]"
          >
            Save & Continue
          </button>
        </div>
      </form>
    </div>
  );
};

export default BusinessInfoSection;
