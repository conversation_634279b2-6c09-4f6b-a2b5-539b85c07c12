import{r as a,b as qt,e as Zt}from"./vendor-94843817.js";import{u as H,P as k,d as Qt,c as Jt,S as en,a as tn}from"./@radix-ui/react-progress-96a44bf4.js";import{j as R}from"./@floating-ui/react-bfff8aa2.js";import{_ as j,a as dt,b as nn}from"./@react-pdf/renderer-8ed2c300.js";var Xe=1,rn=.9,on=.8,an=.17,Ce=.1,Re=.999,un=.9999,cn=.99,sn=/[\\\/_+.#"@\[\(\{&]/,ln=/[\\\/_+.#"@\[\(\{&]/g,dn=/[\s-]/,ft=/[\s-]/g;function Oe(e,t,n,r,o,i,c){if(i===t.length)return o===e.length?Xe:cn;var u=`${o},${i}`;if(c[u]!==void 0)return c[u];for(var m=r.charAt(i),s=n.indexOf(m,o),d=0,f,h,E,A;s>=0;)f=Oe(e,t,n,r,s+1,i+1,c),f>d&&(s===o?f*=Xe:sn.test(e.charAt(s-1))?(f*=on,E=e.slice(o,s-1).match(ln),E&&o>0&&(f*=Math.pow(Re,E.length))):dn.test(e.charAt(s-1))?(f*=rn,A=e.slice(o,s-1).match(ft),A&&o>0&&(f*=Math.pow(Re,A.length))):(f*=an,o>0&&(f*=Math.pow(Re,s-o))),e.charAt(s)!==t.charAt(i)&&(f*=un)),(f<Ce&&n.charAt(s-1)===r.charAt(i+1)||r.charAt(i+1)===r.charAt(i)&&n.charAt(s-1)!==r.charAt(i))&&(h=Oe(e,t,n,r,s+1,i+2,c),h*Ce>f&&(f=h*Ce)),f>d&&(d=f),s=n.indexOf(m,s+1);return c[u]=d,d}function qe(e){return e.toLowerCase().replace(ft," ")}function fn(e,t,n){return e=n&&n.length>0?`${e+" "+n.join(" ")}`:e,Oe(e,t,qe(e),qe(t),0,0,{})}function B(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}var me=globalThis!=null&&globalThis.document?a.useLayoutEffect:()=>{},vn=qt["useId".toString()]||(()=>{}),mn=0;function F(e){const[t,n]=a.useState(vn());return me(()=>{e||n(r=>r??String(mn++))},[e]),e||(t?`radix-${t}`:"")}function V(e){const t=a.useRef(e);return a.useEffect(()=>{t.current=e}),a.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function hn({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=pn({defaultProp:t,onChange:n}),i=e!==void 0,c=i?e:r,u=V(n),m=a.useCallback(s=>{if(i){const f=typeof s=="function"?s(e):s;f!==e&&u(f)}else o(s)},[i,e,o,u]);return[c,m]}function pn({defaultProp:e,onChange:t}){const n=a.useState(e),[r]=n,o=a.useRef(r),i=V(t);return a.useEffect(()=>{o.current!==r&&(i(r),o.current=r)},[r,o,i]),n}function gn(e,t=globalThis==null?void 0:globalThis.document){const n=V(e);a.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var En="DismissableLayer",Le="dismissableLayer.update",yn="dismissableLayer.pointerDownOutside",bn="dismissableLayer.focusOutside",Ze,vt=a.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),mt=a.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:i,onInteractOutside:c,onDismiss:u,...m}=e,s=a.useContext(vt),[d,f]=a.useState(null),h=(d==null?void 0:d.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,E]=a.useState({}),A=H(t,b=>f(b)),v=Array.from(s.layers),[g]=[...s.layersWithOutsidePointerEventsDisabled].slice(-1),S=v.indexOf(g),I=d?v.indexOf(d):-1,x=s.layersWithOutsidePointerEventsDisabled.size>0,P=I>=S,D=Cn(b=>{const T=b.target,_=[...s.branches].some(U=>U.contains(T));!P||_||(o==null||o(b),c==null||c(b),b.defaultPrevented||u==null||u())},h),C=Rn(b=>{const T=b.target;[...s.branches].some(U=>U.contains(T))||(i==null||i(b),c==null||c(b),b.defaultPrevented||u==null||u())},h);return gn(b=>{I===s.layers.size-1&&(r==null||r(b),!b.defaultPrevented&&u&&(b.preventDefault(),u()))},h),a.useEffect(()=>{if(d)return n&&(s.layersWithOutsidePointerEventsDisabled.size===0&&(Ze=h.body.style.pointerEvents,h.body.style.pointerEvents="none"),s.layersWithOutsidePointerEventsDisabled.add(d)),s.layers.add(d),Qe(),()=>{n&&s.layersWithOutsidePointerEventsDisabled.size===1&&(h.body.style.pointerEvents=Ze)}},[d,h,n,s]),a.useEffect(()=>()=>{d&&(s.layers.delete(d),s.layersWithOutsidePointerEventsDisabled.delete(d),Qe())},[d,s]),a.useEffect(()=>{const b=()=>E({});return document.addEventListener(Le,b),()=>document.removeEventListener(Le,b)},[]),R.jsx(k.div,{...m,ref:A,style:{pointerEvents:x?P?"auto":"none":void 0,...e.style},onFocusCapture:B(e.onFocusCapture,C.onFocusCapture),onBlurCapture:B(e.onBlurCapture,C.onBlurCapture),onPointerDownCapture:B(e.onPointerDownCapture,D.onPointerDownCapture)})});mt.displayName=En;var Sn="DismissableLayerBranch",wn=a.forwardRef((e,t)=>{const n=a.useContext(vt),r=a.useRef(null),o=H(t,r);return a.useEffect(()=>{const i=r.current;if(i)return n.branches.add(i),()=>{n.branches.delete(i)}},[n.branches]),R.jsx(k.div,{...e,ref:o})});wn.displayName=Sn;function Cn(e,t=globalThis==null?void 0:globalThis.document){const n=V(e),r=a.useRef(!1),o=a.useRef(()=>{});return a.useEffect(()=>{const i=u=>{if(u.target&&!r.current){let m=function(){ht(yn,n,s,{discrete:!0})};const s={originalEvent:u};u.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=m,t.addEventListener("click",o.current,{once:!0})):m()}else t.removeEventListener("click",o.current);r.current=!1},c=window.setTimeout(()=>{t.addEventListener("pointerdown",i)},0);return()=>{window.clearTimeout(c),t.removeEventListener("pointerdown",i),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function Rn(e,t=globalThis==null?void 0:globalThis.document){const n=V(e),r=a.useRef(!1);return a.useEffect(()=>{const o=i=>{i.target&&!r.current&&ht(bn,n,{originalEvent:i},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function Qe(){const e=new CustomEvent(Le);document.dispatchEvent(e)}function ht(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Qt(o,i):o.dispatchEvent(i)}var De="focusScope.autoFocusOnMount",xe="focusScope.autoFocusOnUnmount",Je={bubbles:!1,cancelable:!0},Dn="FocusScope",pt=a.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...c}=e,[u,m]=a.useState(null),s=V(o),d=V(i),f=a.useRef(null),h=H(t,v=>m(v)),E=a.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;a.useEffect(()=>{if(r){let v=function(x){if(E.paused||!u)return;const P=x.target;u.contains(P)?f.current=P:W(f.current,{select:!0})},g=function(x){if(E.paused||!u)return;const P=x.relatedTarget;P!==null&&(u.contains(P)||W(f.current,{select:!0}))},S=function(x){if(document.activeElement===document.body)for(const D of x)D.removedNodes.length>0&&W(u)};document.addEventListener("focusin",v),document.addEventListener("focusout",g);const I=new MutationObserver(S);return u&&I.observe(u,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",v),document.removeEventListener("focusout",g),I.disconnect()}}},[r,u,E.paused]),a.useEffect(()=>{if(u){tt.add(E);const v=document.activeElement;if(!u.contains(v)){const S=new CustomEvent(De,Je);u.addEventListener(De,s),u.dispatchEvent(S),S.defaultPrevented||(xn(In(gt(u)),{select:!0}),document.activeElement===v&&W(u))}return()=>{u.removeEventListener(De,s),setTimeout(()=>{const S=new CustomEvent(xe,Je);u.addEventListener(xe,d),u.dispatchEvent(S),S.defaultPrevented||W(v??document.body,{select:!0}),u.removeEventListener(xe,d),tt.remove(E)},0)}}},[u,s,d,E]);const A=a.useCallback(v=>{if(!n&&!r||E.paused)return;const g=v.key==="Tab"&&!v.altKey&&!v.ctrlKey&&!v.metaKey,S=document.activeElement;if(g&&S){const I=v.currentTarget,[x,P]=Pn(I);x&&P?!v.shiftKey&&S===P?(v.preventDefault(),n&&W(x,{select:!0})):v.shiftKey&&S===x&&(v.preventDefault(),n&&W(P,{select:!0})):S===I&&v.preventDefault()}},[n,r,E.paused]);return R.jsx(k.div,{tabIndex:-1,...c,ref:h,onKeyDown:A})});pt.displayName=Dn;function xn(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(W(r,{select:t}),document.activeElement!==n)return}function Pn(e){const t=gt(e),n=et(t,e),r=et(t.reverse(),e);return[n,r]}function gt(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function et(e,t){for(const n of e)if(!An(n,{upTo:t}))return n}function An(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function Nn(e){return e instanceof HTMLInputElement&&"select"in e}function W(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&Nn(e)&&t&&e.select()}}var tt=Mn();function Mn(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=nt(e,t),e.unshift(t)},remove(t){var n;e=nt(e,t),(n=e[0])==null||n.resume()}}}function nt(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function In(e){return e.filter(t=>t.tagName!=="A")}var kn="Portal",Et=a.forwardRef((e,t)=>{var u;const{container:n,...r}=e,[o,i]=a.useState(!1);me(()=>i(!0),[]);const c=n||o&&((u=globalThis==null?void 0:globalThis.document)==null?void 0:u.body);return c?Zt.createPortal(R.jsx(k.div,{...r,ref:t}),c):null});Et.displayName=kn;function Tn(e,t){return a.useReducer((n,r)=>t[n][r]??n,e)}var pe=e=>{const{present:t,children:n}=e,r=On(t),o=typeof n=="function"?n({present:r.isPresent}):a.Children.only(n),i=H(r.ref,Ln(o));return typeof n=="function"||r.isPresent?a.cloneElement(o,{ref:i}):null};pe.displayName="Presence";function On(e){const[t,n]=a.useState(),r=a.useRef({}),o=a.useRef(e),i=a.useRef("none"),c=e?"mounted":"unmounted",[u,m]=Tn(c,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return a.useEffect(()=>{const s=ue(r.current);i.current=u==="mounted"?s:"none"},[u]),me(()=>{const s=r.current,d=o.current;if(d!==e){const h=i.current,E=ue(s);e?m("MOUNT"):E==="none"||(s==null?void 0:s.display)==="none"?m("UNMOUNT"):m(d&&h!==E?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,m]),me(()=>{if(t){let s;const d=t.ownerDocument.defaultView??window,f=E=>{const v=ue(r.current).includes(E.animationName);if(E.target===t&&v&&(m("ANIMATION_END"),!o.current)){const g=t.style.animationFillMode;t.style.animationFillMode="forwards",s=d.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=g)})}},h=E=>{E.target===t&&(i.current=ue(r.current))};return t.addEventListener("animationstart",h),t.addEventListener("animationcancel",f),t.addEventListener("animationend",f),()=>{d.clearTimeout(s),t.removeEventListener("animationstart",h),t.removeEventListener("animationcancel",f),t.removeEventListener("animationend",f)}}else m("ANIMATION_END")},[t,m]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:a.useCallback(s=>{s&&(r.current=getComputedStyle(s)),n(s)},[])}}function ue(e){return(e==null?void 0:e.animationName)||"none"}function Ln(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var Pe=0;function Fn(){a.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??rt()),document.body.insertAdjacentElement("beforeend",e[1]??rt()),Pe++,()=>{Pe===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),Pe--}},[])}function rt(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var fe="right-scroll-bar-position",ve="width-before-scroll-bar",_n="with-scroll-bars-hidden",Wn="--removed-body-scroll-bar-size";function Ae(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function jn(e,t){var n=a.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var Bn=typeof window<"u"?a.useLayoutEffect:a.useEffect,ot=new WeakMap;function Un(e,t){var n=jn(t||null,function(r){return e.forEach(function(o){return Ae(o,r)})});return Bn(function(){var r=ot.get(n);if(r){var o=new Set(r),i=new Set(e),c=n.current;o.forEach(function(u){i.has(u)||Ae(u,null)}),i.forEach(function(u){o.has(u)||Ae(u,c)})}ot.set(n,e)},[e]),n}function $n(e){return e}function Kn(e,t){t===void 0&&(t=$n);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(i){var c=t(i,r);return n.push(c),function(){n=n.filter(function(u){return u!==c})}},assignSyncMedium:function(i){for(r=!0;n.length;){var c=n;n=[],c.forEach(i)}n={push:function(u){return i(u)},filter:function(){return n}}},assignMedium:function(i){r=!0;var c=[];if(n.length){var u=n;n=[],u.forEach(i),c=n}var m=function(){var d=c;c=[],d.forEach(i)},s=function(){return Promise.resolve().then(m)};s(),n={push:function(d){c.push(d),s()},filter:function(d){return c=c.filter(d),n}}}};return o}function Vn(e){e===void 0&&(e={});var t=Kn(null);return t.options=j({async:!0,ssr:!1},e),t}var yt=function(e){var t=e.sideCar,n=dt(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return a.createElement(r,j({},n))};yt.isSideCarExport=!0;function Gn(e,t){return e.useMedium(t),yt}var bt=Vn(),Ne=function(){},ge=a.forwardRef(function(e,t){var n=a.useRef(null),r=a.useState({onScrollCapture:Ne,onWheelCapture:Ne,onTouchMoveCapture:Ne}),o=r[0],i=r[1],c=e.forwardProps,u=e.children,m=e.className,s=e.removeScrollBar,d=e.enabled,f=e.shards,h=e.sideCar,E=e.noIsolation,A=e.inert,v=e.allowPinchZoom,g=e.as,S=g===void 0?"div":g,I=e.gapMode,x=dt(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),P=h,D=Un([n,t]),C=j(j({},x),o);return a.createElement(a.Fragment,null,d&&a.createElement(P,{sideCar:bt,removeScrollBar:s,shards:f,noIsolation:E,inert:A,setCallbacks:i,allowPinchZoom:!!v,lockRef:n,gapMode:I}),c?a.cloneElement(a.Children.only(u),j(j({},C),{ref:D})):a.createElement(S,j({},C,{className:m,ref:D}),u))});ge.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};ge.classNames={fullWidth:ve,zeroRight:fe};var at,Yn=function(){if(at)return at;if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function zn(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=Yn();return t&&e.setAttribute("nonce",t),e}function Hn(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function Xn(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var qn=function(){var e=0,t=null;return{add:function(n){e==0&&(t=zn())&&(Hn(t,n),Xn(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},Zn=function(){var e=qn();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},St=function(){var e=Zn(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},Qn={left:0,top:0,right:0,gap:0},Me=function(e){return parseInt(e||"",10)||0},Jn=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[Me(n),Me(r),Me(o)]},er=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return Qn;var t=Jn(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},tr=St(),J="data-scroll-locked",nr=function(e,t,n,r){var o=e.left,i=e.top,c=e.right,u=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(_n,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(u,"px ").concat(r,`;
  }
  body[`).concat(J,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(i,`px;
    padding-right: `).concat(c,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(u,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(u,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(fe,` {
    right: `).concat(u,"px ").concat(r,`;
  }
  
  .`).concat(ve,` {
    margin-right: `).concat(u,"px ").concat(r,`;
  }
  
  .`).concat(fe," .").concat(fe,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(ve," .").concat(ve,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(J,`] {
    `).concat(Wn,": ").concat(u,`px;
  }
`)},it=function(){var e=parseInt(document.body.getAttribute(J)||"0",10);return isFinite(e)?e:0},rr=function(){a.useEffect(function(){return document.body.setAttribute(J,(it()+1).toString()),function(){var e=it()-1;e<=0?document.body.removeAttribute(J):document.body.setAttribute(J,e.toString())}},[])},or=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;rr();var i=a.useMemo(function(){return er(o)},[o]);return a.createElement(tr,{styles:nr(i,!t,o,n?"":"!important")})},Fe=!1;if(typeof window<"u")try{var ce=Object.defineProperty({},"passive",{get:function(){return Fe=!0,!0}});window.addEventListener("test",ce,ce),window.removeEventListener("test",ce,ce)}catch{Fe=!1}var X=Fe?{passive:!1}:!1,ar=function(e){return e.tagName==="TEXTAREA"},wt=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!ar(e)&&n[t]==="visible")},ir=function(e){return wt(e,"overflowY")},ur=function(e){return wt(e,"overflowX")},ut=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=Ct(e,r);if(o){var i=Rt(e,r),c=i[1],u=i[2];if(c>u)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},cr=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},sr=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},Ct=function(e,t){return e==="v"?ir(t):ur(t)},Rt=function(e,t){return e==="v"?cr(t):sr(t)},lr=function(e,t){return e==="h"&&t==="rtl"?-1:1},dr=function(e,t,n,r,o){var i=lr(e,window.getComputedStyle(t).direction),c=i*r,u=n.target,m=t.contains(u),s=!1,d=c>0,f=0,h=0;do{var E=Rt(e,u),A=E[0],v=E[1],g=E[2],S=v-g-i*A;(A||S)&&Ct(e,u)&&(f+=S,h+=A),u instanceof ShadowRoot?u=u.host:u=u.parentNode}while(!m&&u!==document.body||m&&(t.contains(u)||t===u));return(d&&(o&&Math.abs(f)<1||!o&&c>f)||!d&&(o&&Math.abs(h)<1||!o&&-c>h))&&(s=!0),s},se=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},ct=function(e){return[e.deltaX,e.deltaY]},st=function(e){return e&&"current"in e?e.current:e},fr=function(e,t){return e[0]===t[0]&&e[1]===t[1]},vr=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},mr=0,q=[];function hr(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(mr++)[0],i=a.useState(St)[0],c=a.useRef(e);a.useEffect(function(){c.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var v=nn([e.lockRef.current],(e.shards||[]).map(st),!0).filter(Boolean);return v.forEach(function(g){return g.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),v.forEach(function(g){return g.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=a.useCallback(function(v,g){if("touches"in v&&v.touches.length===2||v.type==="wheel"&&v.ctrlKey)return!c.current.allowPinchZoom;var S=se(v),I=n.current,x="deltaX"in v?v.deltaX:I[0]-S[0],P="deltaY"in v?v.deltaY:I[1]-S[1],D,C=v.target,b=Math.abs(x)>Math.abs(P)?"h":"v";if("touches"in v&&b==="h"&&C.type==="range")return!1;var T=ut(b,C);if(!T)return!0;if(T?D=b:(D=b==="v"?"h":"v",T=ut(b,C)),!T)return!1;if(!r.current&&"changedTouches"in v&&(x||P)&&(r.current=D),!D)return!0;var _=r.current||D;return dr(_,g,v,_==="h"?x:P,!0)},[]),m=a.useCallback(function(v){var g=v;if(!(!q.length||q[q.length-1]!==i)){var S="deltaY"in g?ct(g):se(g),I=t.current.filter(function(D){return D.name===g.type&&(D.target===g.target||g.target===D.shadowParent)&&fr(D.delta,S)})[0];if(I&&I.should){g.cancelable&&g.preventDefault();return}if(!I){var x=(c.current.shards||[]).map(st).filter(Boolean).filter(function(D){return D.contains(g.target)}),P=x.length>0?u(g,x[0]):!c.current.noIsolation;P&&g.cancelable&&g.preventDefault()}}},[]),s=a.useCallback(function(v,g,S,I){var x={name:v,delta:g,target:S,should:I,shadowParent:pr(S)};t.current.push(x),setTimeout(function(){t.current=t.current.filter(function(P){return P!==x})},1)},[]),d=a.useCallback(function(v){n.current=se(v),r.current=void 0},[]),f=a.useCallback(function(v){s(v.type,ct(v),v.target,u(v,e.lockRef.current))},[]),h=a.useCallback(function(v){s(v.type,se(v),v.target,u(v,e.lockRef.current))},[]);a.useEffect(function(){return q.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:h}),document.addEventListener("wheel",m,X),document.addEventListener("touchmove",m,X),document.addEventListener("touchstart",d,X),function(){q=q.filter(function(v){return v!==i}),document.removeEventListener("wheel",m,X),document.removeEventListener("touchmove",m,X),document.removeEventListener("touchstart",d,X)}},[]);var E=e.removeScrollBar,A=e.inert;return a.createElement(a.Fragment,null,A?a.createElement(i,{styles:vr(o)}):null,E?a.createElement(or,{gapMode:e.gapMode}):null)}function pr(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const gr=Gn(bt,hr);var Dt=a.forwardRef(function(e,t){return a.createElement(ge,j({},e,{ref:t,sideCar:gr}))});Dt.classNames=ge.classNames;const Er=Dt;var yr=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},Z=new WeakMap,le=new WeakMap,de={},Ie=0,xt=function(e){return e&&(e.host||xt(e.parentNode))},br=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=xt(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},Sr=function(e,t,n,r){var o=br(t,Array.isArray(e)?e:[e]);de[n]||(de[n]=new WeakMap);var i=de[n],c=[],u=new Set,m=new Set(o),s=function(f){!f||u.has(f)||(u.add(f),s(f.parentNode))};o.forEach(s);var d=function(f){!f||m.has(f)||Array.prototype.forEach.call(f.children,function(h){if(u.has(h))d(h);else try{var E=h.getAttribute(r),A=E!==null&&E!=="false",v=(Z.get(h)||0)+1,g=(i.get(h)||0)+1;Z.set(h,v),i.set(h,g),c.push(h),v===1&&A&&le.set(h,!0),g===1&&h.setAttribute(n,"true"),A||h.setAttribute(r,"true")}catch(S){console.error("aria-hidden: cannot operate on ",h,S)}})};return d(t),u.clear(),Ie++,function(){c.forEach(function(f){var h=Z.get(f)-1,E=i.get(f)-1;Z.set(f,h),i.set(f,E),h||(le.has(f)||f.removeAttribute(r),le.delete(f)),E||f.removeAttribute(n)}),Ie--,Ie||(Z=new WeakMap,Z=new WeakMap,le=new WeakMap,de={})}},wr=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||yr(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),Sr(r,o,n,"aria-hidden")):function(){return null}},We="Dialog",[Pt,fo]=Jt(We),[Cr,L]=Pt(We),At=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:i,modal:c=!0}=e,u=a.useRef(null),m=a.useRef(null),[s=!1,d]=hn({prop:r,defaultProp:o,onChange:i});return R.jsx(Cr,{scope:t,triggerRef:u,contentRef:m,contentId:F(),titleId:F(),descriptionId:F(),open:s,onOpenChange:d,onOpenToggle:a.useCallback(()=>d(f=>!f),[d]),modal:c,children:n})};At.displayName=We;var Nt="DialogTrigger",Rr=a.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=L(Nt,n),i=H(t,o.triggerRef);return R.jsx(k.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":Ue(o.open),...r,ref:i,onClick:B(e.onClick,o.onOpenToggle)})});Rr.displayName=Nt;var je="DialogPortal",[Dr,Mt]=Pt(je,{forceMount:void 0}),It=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:o}=e,i=L(je,t);return R.jsx(Dr,{scope:t,forceMount:n,children:a.Children.map(r,c=>R.jsx(pe,{present:n||i.open,children:R.jsx(Et,{asChild:!0,container:o,children:c})}))})};It.displayName=je;var he="DialogOverlay",kt=a.forwardRef((e,t)=>{const n=Mt(he,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=L(he,e.__scopeDialog);return i.modal?R.jsx(pe,{present:r||i.open,children:R.jsx(xr,{...o,ref:t})}):null});kt.displayName=he;var xr=a.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=L(he,n);return R.jsx(Er,{as:en,allowPinchZoom:!0,shards:[o.contentRef],children:R.jsx(k.div,{"data-state":Ue(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),G="DialogContent",Tt=a.forwardRef((e,t)=>{const n=Mt(G,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=L(G,e.__scopeDialog);return R.jsx(pe,{present:r||i.open,children:i.modal?R.jsx(Pr,{...o,ref:t}):R.jsx(Ar,{...o,ref:t})})});Tt.displayName=G;var Pr=a.forwardRef((e,t)=>{const n=L(G,e.__scopeDialog),r=a.useRef(null),o=H(t,n.contentRef,r);return a.useEffect(()=>{const i=r.current;if(i)return wr(i)},[]),R.jsx(Ot,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:B(e.onCloseAutoFocus,i=>{var c;i.preventDefault(),(c=n.triggerRef.current)==null||c.focus()}),onPointerDownOutside:B(e.onPointerDownOutside,i=>{const c=i.detail.originalEvent,u=c.button===0&&c.ctrlKey===!0;(c.button===2||u)&&i.preventDefault()}),onFocusOutside:B(e.onFocusOutside,i=>i.preventDefault())})}),Ar=a.forwardRef((e,t)=>{const n=L(G,e.__scopeDialog),r=a.useRef(!1),o=a.useRef(!1);return R.jsx(Ot,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:i=>{var c,u;(c=e.onCloseAutoFocus)==null||c.call(e,i),i.defaultPrevented||(r.current||(u=n.triggerRef.current)==null||u.focus(),i.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:i=>{var m,s;(m=e.onInteractOutside)==null||m.call(e,i),i.defaultPrevented||(r.current=!0,i.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const c=i.target;((s=n.triggerRef.current)==null?void 0:s.contains(c))&&i.preventDefault(),i.detail.originalEvent.type==="focusin"&&o.current&&i.preventDefault()}})}),Ot=a.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:i,...c}=e,u=L(G,n),m=a.useRef(null),s=H(t,m);return Fn(),R.jsxs(R.Fragment,{children:[R.jsx(pt,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:i,children:R.jsx(mt,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":Ue(u.open),...c,ref:s,onDismiss:()=>u.onOpenChange(!1)})}),R.jsxs(R.Fragment,{children:[R.jsx(Nr,{titleId:u.titleId}),R.jsx(Ir,{contentRef:m,descriptionId:u.descriptionId})]})]})}),Be="DialogTitle",Lt=a.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=L(Be,n);return R.jsx(k.h2,{id:o.titleId,...r,ref:t})});Lt.displayName=Be;var Ft="DialogDescription",_t=a.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=L(Ft,n);return R.jsx(k.p,{id:o.descriptionId,...r,ref:t})});_t.displayName=Ft;var Wt="DialogClose",jt=a.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=L(Wt,n);return R.jsx(k.button,{type:"button",...r,ref:t,onClick:B(e.onClick,()=>o.onOpenChange(!1))})});jt.displayName=Wt;function Ue(e){return e?"open":"closed"}var Bt="DialogTitleWarning",[vo,Ut]=tn(Bt,{contentName:G,titleName:Be,docsSlug:"dialog"}),Nr=({titleId:e})=>{const t=Ut(Bt),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return a.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},Mr="DialogDescriptionWarning",Ir=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Ut(Mr).contentName}}.`;return a.useEffect(()=>{var i;const o=(i=e.current)==null?void 0:i.getAttribute("aria-describedby");t&&o&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},kr=At,Tr=It,Or=kt,Lr=Tt,mo=Lt,ho=_t,po=jt,$t={exports:{}},Kt={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ee=a;function Fr(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var _r=typeof Object.is=="function"?Object.is:Fr,Wr=ee.useState,jr=ee.useEffect,Br=ee.useLayoutEffect,Ur=ee.useDebugValue;function $r(e,t){var n=t(),r=Wr({inst:{value:n,getSnapshot:t}}),o=r[0].inst,i=r[1];return Br(function(){o.value=n,o.getSnapshot=t,ke(o)&&i({inst:o})},[e,n,t]),jr(function(){return ke(o)&&i({inst:o}),e(function(){ke(o)&&i({inst:o})})},[e]),Ur(n),n}function ke(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!_r(e,n)}catch{return!0}}function Kr(e,t){return t()}var Vr=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?Kr:$r;Kt.useSyncExternalStore=ee.useSyncExternalStore!==void 0?ee.useSyncExternalStore:Vr;$t.exports=Kt;var Gr=$t.exports,re='[cmdk-group=""]',Te='[cmdk-group-items=""]',Yr='[cmdk-group-heading=""]',$e='[cmdk-item=""]',lt=`${$e}:not([aria-disabled="true"])`,_e="cmdk-item-select",K="data-value",zr=(e,t,n)=>fn(e,t,n),Vt=a.createContext(void 0),ae=()=>a.useContext(Vt),Gt=a.createContext(void 0),Ke=()=>a.useContext(Gt),Yt=a.createContext(void 0),zt=a.forwardRef((e,t)=>{let n=Q(()=>{var l,y;return{search:"",value:(y=(l=e.value)!=null?l:e.defaultValue)!=null?y:"",filtered:{count:0,items:new Map,groups:new Set}}}),r=Q(()=>new Set),o=Q(()=>new Map),i=Q(()=>new Map),c=Q(()=>new Set),u=Ht(e),{label:m,children:s,value:d,onValueChange:f,filter:h,shouldFilter:E,loop:A,disablePointerSelection:v=!1,vimBindings:g=!0,...S}=e,I=F(),x=F(),P=F(),D=a.useRef(null),C=oo();Y(()=>{if(d!==void 0){let l=d.trim();n.current.value=l,b.emit()}},[d]),Y(()=>{C(6,Ve)},[]);let b=a.useMemo(()=>({subscribe:l=>(c.current.add(l),()=>c.current.delete(l)),snapshot:()=>n.current,setState:(l,y,w)=>{var p,N,M;if(!Object.is(n.current[l],y)){if(n.current[l]=y,l==="search")be(),U(),C(1,ye);else if(l==="value"&&(w||C(5,Ve),((p=u.current)==null?void 0:p.value)!==void 0)){let O=y??"";(M=(N=u.current).onValueChange)==null||M.call(N,O);return}b.emit()}},emit:()=>{c.current.forEach(l=>l())}}),[]),T=a.useMemo(()=>({value:(l,y,w)=>{var p;y!==((p=i.current.get(l))==null?void 0:p.value)&&(i.current.set(l,{value:y,keywords:w}),n.current.filtered.items.set(l,_(y,w)),C(2,()=>{U(),b.emit()}))},item:(l,y)=>(r.current.add(l),y&&(o.current.has(y)?o.current.get(y).add(l):o.current.set(y,new Set([l]))),C(3,()=>{be(),U(),n.current.value||ye(),b.emit()}),()=>{i.current.delete(l),r.current.delete(l),n.current.filtered.items.delete(l);let w=te();C(4,()=>{be(),(w==null?void 0:w.getAttribute("id"))===l&&ye(),b.emit()})}),group:l=>(o.current.has(l)||o.current.set(l,new Set),()=>{i.current.delete(l),o.current.delete(l)}),filter:()=>u.current.shouldFilter,label:m||e["aria-label"],getDisablePointerSelection:()=>u.current.disablePointerSelection,listId:I,inputId:P,labelId:x,listInnerRef:D}),[]);function _(l,y){var w,p;let N=(p=(w=u.current)==null?void 0:w.filter)!=null?p:zr;return l?N(l,n.current.search,y):0}function U(){if(!n.current.search||u.current.shouldFilter===!1)return;let l=n.current.filtered.items,y=[];n.current.filtered.groups.forEach(p=>{let N=o.current.get(p),M=0;N.forEach(O=>{let $=l.get(O);M=Math.max($,M)}),y.push([p,M])});let w=D.current;ne().sort((p,N)=>{var M,O;let $=p.getAttribute("id"),ie=N.getAttribute("id");return((M=l.get(ie))!=null?M:0)-((O=l.get($))!=null?O:0)}).forEach(p=>{let N=p.closest(Te);N?N.appendChild(p.parentElement===N?p:p.closest(`${Te} > *`)):w.appendChild(p.parentElement===w?p:p.closest(`${Te} > *`))}),y.sort((p,N)=>N[1]-p[1]).forEach(p=>{var N;let M=(N=D.current)==null?void 0:N.querySelector(`${re}[${K}="${encodeURIComponent(p[0])}"]`);M==null||M.parentElement.appendChild(M)})}function ye(){let l=ne().find(w=>w.getAttribute("aria-disabled")!=="true"),y=l==null?void 0:l.getAttribute(K);b.setState("value",y||void 0)}function be(){var l,y,w,p;if(!n.current.search||u.current.shouldFilter===!1){n.current.filtered.count=r.current.size;return}n.current.filtered.groups=new Set;let N=0;for(let M of r.current){let O=(y=(l=i.current.get(M))==null?void 0:l.value)!=null?y:"",$=(p=(w=i.current.get(M))==null?void 0:w.keywords)!=null?p:[],ie=_(O,$);n.current.filtered.items.set(M,ie),ie>0&&N++}for(let[M,O]of o.current)for(let $ of O)if(n.current.filtered.items.get($)>0){n.current.filtered.groups.add(M);break}n.current.filtered.count=N}function Ve(){var l,y,w;let p=te();p&&(((l=p.parentElement)==null?void 0:l.firstChild)===p&&((w=(y=p.closest(re))==null?void 0:y.querySelector(Yr))==null||w.scrollIntoView({block:"nearest"})),p.scrollIntoView({block:"nearest"}))}function te(){var l;return(l=D.current)==null?void 0:l.querySelector(`${$e}[aria-selected="true"]`)}function ne(){var l;return Array.from(((l=D.current)==null?void 0:l.querySelectorAll(lt))||[])}function Se(l){let y=ne()[l];y&&b.setState("value",y.getAttribute(K))}function we(l){var y;let w=te(),p=ne(),N=p.findIndex(O=>O===w),M=p[N+l];(y=u.current)!=null&&y.loop&&(M=N+l<0?p[p.length-1]:N+l===p.length?p[0]:p[N+l]),M&&b.setState("value",M.getAttribute(K))}function Ge(l){let y=te(),w=y==null?void 0:y.closest(re),p;for(;w&&!p;)w=l>0?no(w,re):ro(w,re),p=w==null?void 0:w.querySelector(lt);p?b.setState("value",p.getAttribute(K)):we(l)}let Ye=()=>Se(ne().length-1),ze=l=>{l.preventDefault(),l.metaKey?Ye():l.altKey?Ge(1):we(1)},He=l=>{l.preventDefault(),l.metaKey?Se(0):l.altKey?Ge(-1):we(-1)};return a.createElement(k.div,{ref:t,tabIndex:-1,...S,"cmdk-root":"",onKeyDown:l=>{var y;if((y=S.onKeyDown)==null||y.call(S,l),!l.defaultPrevented)switch(l.key){case"n":case"j":{g&&l.ctrlKey&&ze(l);break}case"ArrowDown":{ze(l);break}case"p":case"k":{g&&l.ctrlKey&&He(l);break}case"ArrowUp":{He(l);break}case"Home":{l.preventDefault(),Se(0);break}case"End":{l.preventDefault(),Ye();break}case"Enter":if(!l.nativeEvent.isComposing&&l.keyCode!==229){l.preventDefault();let w=te();if(w){let p=new Event(_e);w.dispatchEvent(p)}}}}},a.createElement("label",{"cmdk-label":"",htmlFor:T.inputId,id:T.labelId,style:io},m),Ee(e,l=>a.createElement(Gt.Provider,{value:b},a.createElement(Vt.Provider,{value:T},l))))}),Hr=a.forwardRef((e,t)=>{var n,r;let o=F(),i=a.useRef(null),c=a.useContext(Yt),u=ae(),m=Ht(e),s=(r=(n=m.current)==null?void 0:n.forceMount)!=null?r:c==null?void 0:c.forceMount;Y(()=>{if(!s)return u.item(o,c==null?void 0:c.id)},[s]);let d=Xt(o,i,[e.value,e.children,i],e.keywords),f=Ke(),h=z(C=>C.value&&C.value===d.current),E=z(C=>s||u.filter()===!1?!0:C.search?C.filtered.items.get(o)>0:!0);a.useEffect(()=>{let C=i.current;if(!(!C||e.disabled))return C.addEventListener(_e,A),()=>C.removeEventListener(_e,A)},[E,e.onSelect,e.disabled]);function A(){var C,b;v(),(b=(C=m.current).onSelect)==null||b.call(C,d.current)}function v(){f.setState("value",d.current,!0)}if(!E)return null;let{disabled:g,value:S,onSelect:I,forceMount:x,keywords:P,...D}=e;return a.createElement(k.div,{ref:oe([i,t]),...D,id:o,"cmdk-item":"",role:"option","aria-disabled":!!g,"aria-selected":!!h,"data-disabled":!!g,"data-selected":!!h,onPointerMove:g||u.getDisablePointerSelection()?void 0:v,onClick:g?void 0:A},e.children)}),Xr=a.forwardRef((e,t)=>{let{heading:n,children:r,forceMount:o,...i}=e,c=F(),u=a.useRef(null),m=a.useRef(null),s=F(),d=ae(),f=z(E=>o||d.filter()===!1?!0:E.search?E.filtered.groups.has(c):!0);Y(()=>d.group(c),[]),Xt(c,u,[e.value,e.heading,m]);let h=a.useMemo(()=>({id:c,forceMount:o}),[o]);return a.createElement(k.div,{ref:oe([u,t]),...i,"cmdk-group":"",role:"presentation",hidden:f?void 0:!0},n&&a.createElement("div",{ref:m,"cmdk-group-heading":"","aria-hidden":!0,id:s},n),Ee(e,E=>a.createElement("div",{"cmdk-group-items":"",role:"group","aria-labelledby":n?s:void 0},a.createElement(Yt.Provider,{value:h},E))))}),qr=a.forwardRef((e,t)=>{let{alwaysRender:n,...r}=e,o=a.useRef(null),i=z(c=>!c.search);return!n&&!i?null:a.createElement(k.div,{ref:oe([o,t]),...r,"cmdk-separator":"",role:"separator"})}),Zr=a.forwardRef((e,t)=>{let{onValueChange:n,...r}=e,o=e.value!=null,i=Ke(),c=z(d=>d.search),u=z(d=>d.value),m=ae(),s=a.useMemo(()=>{var d;let f=(d=m.listInnerRef.current)==null?void 0:d.querySelector(`${$e}[${K}="${encodeURIComponent(u)}"]`);return f==null?void 0:f.getAttribute("id")},[]);return a.useEffect(()=>{e.value!=null&&i.setState("search",e.value)},[e.value]),a.createElement(k.input,{ref:t,...r,"cmdk-input":"",autoComplete:"off",autoCorrect:"off",spellCheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":m.listId,"aria-labelledby":m.labelId,"aria-activedescendant":s,id:m.inputId,type:"text",value:o?e.value:c,onChange:d=>{o||i.setState("search",d.target.value),n==null||n(d.target.value)}})}),Qr=a.forwardRef((e,t)=>{let{children:n,label:r="Suggestions",...o}=e,i=a.useRef(null),c=a.useRef(null),u=ae();return a.useEffect(()=>{if(c.current&&i.current){let m=c.current,s=i.current,d,f=new ResizeObserver(()=>{d=requestAnimationFrame(()=>{let h=m.offsetHeight;s.style.setProperty("--cmdk-list-height",h.toFixed(1)+"px")})});return f.observe(m),()=>{cancelAnimationFrame(d),f.unobserve(m)}}},[]),a.createElement(k.div,{ref:oe([i,t]),...o,"cmdk-list":"",role:"listbox","aria-label":r,id:u.listId},Ee(e,m=>a.createElement("div",{ref:oe([c,u.listInnerRef]),"cmdk-list-sizer":""},m)))}),Jr=a.forwardRef((e,t)=>{let{open:n,onOpenChange:r,overlayClassName:o,contentClassName:i,container:c,...u}=e;return a.createElement(kr,{open:n,onOpenChange:r},a.createElement(Tr,{container:c},a.createElement(Or,{"cmdk-overlay":"",className:o}),a.createElement(Lr,{"aria-label":e.label,"cmdk-dialog":"",className:i},a.createElement(zt,{ref:t,...u}))))}),eo=a.forwardRef((e,t)=>z(n=>n.filtered.count===0)?a.createElement(k.div,{ref:t,...e,"cmdk-empty":"",role:"presentation"}):null),to=a.forwardRef((e,t)=>{let{progress:n,children:r,label:o="Loading...",...i}=e;return a.createElement(k.div,{ref:t,...i,"cmdk-loading":"",role:"progressbar","aria-valuenow":n,"aria-valuemin":0,"aria-valuemax":100,"aria-label":o},Ee(e,c=>a.createElement("div",{"aria-hidden":!0},c)))}),go=Object.assign(zt,{List:Qr,Item:Hr,Input:Zr,Group:Xr,Separator:qr,Dialog:Jr,Empty:eo,Loading:to});function no(e,t){let n=e.nextElementSibling;for(;n;){if(n.matches(t))return n;n=n.nextElementSibling}}function ro(e,t){let n=e.previousElementSibling;for(;n;){if(n.matches(t))return n;n=n.previousElementSibling}}function Ht(e){let t=a.useRef(e);return Y(()=>{t.current=e}),t}var Y=typeof window>"u"?a.useEffect:a.useLayoutEffect;function Q(e){let t=a.useRef();return t.current===void 0&&(t.current=e()),t}function oe(e){return t=>{e.forEach(n=>{typeof n=="function"?n(t):n!=null&&(n.current=t)})}}function z(e){let t=Ke(),n=()=>e(t.snapshot());return Gr.useSyncExternalStore(t.subscribe,n,n)}function Xt(e,t,n,r=[]){let o=a.useRef(),i=ae();return Y(()=>{var c;let u=(()=>{var s;for(let d of n){if(typeof d=="string")return d.trim();if(typeof d=="object"&&"current"in d)return d.current?(s=d.current.textContent)==null?void 0:s.trim():o.current}})(),m=r.map(s=>s.trim());i.value(e,u,m),(c=t.current)==null||c.setAttribute(K,u),o.current=u}),o}var oo=()=>{let[e,t]=a.useState(),n=Q(()=>new Map);return Y(()=>{n.current.forEach(r=>r()),n.current=new Map},[e]),(r,o)=>{n.current.set(r,o),t({})}};function ao(e){let t=e.type;return typeof t=="function"?t(e.props):"render"in t?t.render(e.props):e}function Ee({asChild:e,children:t},n){return e&&a.isValidElement(t)?a.cloneElement(ao(t),{ref:t.ref},n(t.props.children)):n(t)}var io={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"};export{Lr as C,ho as D,Or as O,Tr as P,mo as T,go as V,po as a};
