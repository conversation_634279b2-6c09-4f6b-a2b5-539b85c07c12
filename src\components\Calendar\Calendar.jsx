// import React, { useEffect } from "react";
// import { useNavigate } from "react-router-dom";
// import FullCalendar from "@fullcalendar/react";
// import dayGridPlugin from "@fullcalendar/daygrid";
// import { AuthContext } from "Src/authContext";
// import { formatCalendarEvents } from "Utils/utils";

// const Calendar = ({ events }) => {
//   const navigate = useNavigate();

//   const { state: authState } = React.useContext(AuthContext);
//   const [formattedEvents, setFormattedEvents] = React.useState([]);

//   function renderEventContent(eventInfo) {
//     return (
//       <>
//         <b
//           className={`cursor-pointer`}
//           onClick={() => {
//             localStorage.setItem("projectClientId", "");
//             localStorage.setItem("projectTeamName", "");
//             localStorage.setItem("projectMixTypeId", "");
//             localStorage.setItem("projectMixDateStart", "");
//             localStorage.setItem("projectMixDateEnd", "");
//             localStorage.setItem("projectPageSize", "");
//             navigate(`/${authState.role}/view-project/${eventInfo.event.id}`);
//           }}
//           style={{
//             backgroundColor: eventInfo.event.backgroundColor ?? "white",
//             color: !eventInfo.event.backgroundColor ? "white" : "black",
//             width: "100%",
//           }}
//         >
//           {eventInfo.event.title}
//         </b>
//       </>
//     );
//   }

//   useEffect(() => {
//     if (events && events.length > 0) {
//       const formattedEvents = formatCalendarEvents(events);
//       setFormattedEvents(formattedEvents);
//     }
//   }, [events]);

//   return (
//     <div className="p-5 my-2 text-white rounded-md border border-gray-500 bg-neutral-700">
//       <FullCalendar
//         plugins={[dayGridPlugin]}
//         initialView="dayGridMonth"
//         weekends={true}
//         // dateClick={(e) => handleDateClick(e)}
//         events={formattedEvents}
//         eventContent={renderEventContent}
//       />
//     </div>
//   );
// };

// export default Calendar;
import React, { useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import { AuthContext } from "Src/authContext";
import { formatCalendarEvents } from "Utils/utils";
import { ClipLoader } from "react-spinners";
import CustomSelect from "Components/CustomSelect";
import CustomSelect2 from "Components/CustomSelect2";

const Calendar = ({
  events,
  isLoading = false,
  members = null,
  selectedMemberId = null,
  setSelectedMemberId = null,
}) => {
  const navigate = useNavigate();

  const { state: authState } = React.useContext(AuthContext);

  const [formattedEvents, setFormattedEvents] = React.useState([]);
  console.log(formattedEvents, "formattedEvents");

  function renderEventContent(eventInfo) {
    console.log(eventInfo);
    return (
      <>
        <div className="group relative">
          <Link
            to={`/${authState.role}/view-project/${eventInfo.event.id}`}
            style={{
              borderLeft: `4px solid ${eventInfo.event.backgroundColor}`,
            }}
            className={`inline-block w-full cursor-pointer overflow-x-hidden truncate rounded border-l-4 bg-meta-4   p-1.5 text-sm`}
            onClick={() => {
              localStorage.setItem("projectClientId", "");
              localStorage.setItem("projectTeamName", "");
              localStorage.setItem("projectMixTypeId", "");
              localStorage.setItem("projectMixDateStart", "");
              localStorage.setItem("projectMixDateEnd", "");
              localStorage.setItem("projectPageSize", "");
              navigate(``);
            }}
          >
            {eventInfo.event.title}
          </Link>

          <div className="pointer-events-none absolute left-1/2 top-0 z-50 -translate-x-1/2 -translate-y-full whitespace-nowrap rounded bg-black px-2 py-1 text-sm text-white opacity-0 transition-opacity group-hover:opacity-100">
            {eventInfo.event.title}
          </div>
        </div>
      </>
    );
  }

  useEffect(() => {
    if (events && events.length > 0) {
      const formattedEvents = formatCalendarEvents(events);
      setFormattedEvents(formattedEvents);
    }
  }, [events]);

  return (
    <div className="shadow-default w-full max-w-full rounded border border-strokedark bg-boxdark p-[30px] pt-[10px] dark:border-strokedark dark:bg-boxdark">
      <div className="mb-1 pb-[10px]">
        <h2 className="text-2xl text-white">Project Calendar</h2>
        {authState?.role == "manager" || authState?.role == "admin" ? (
          <div className="my-[20px] min-w-[220px]">
            <CustomSelect2
              className="!w-full"
              position="up"
              label="Select Producer"
              options={
                members && members.length > 0
                  ? [
                      { value: "", label: "All Members" },
                      ...members.map((member) => ({
                        value: member.id,
                        label: member.user_name,
                      })),
                    ]
                  : []
              }
              value={selectedMemberId}
              onChange={setSelectedMemberId}
            />
          </div>
        ) : null}
      </div>
      {isLoading ? (
        <div className="flex h-[calc(100vh-160px)] w-full items-center justify-center">
          <ClipLoader color="#fff" size={30} />
        </div>
      ) : (
        <FullCalendar
          plugins={[dayGridPlugin]}
          initialView="dayGridMonth"
          weekends={true}
          contentHeight={500}
          events={formattedEvents}
          eventContent={renderEventContent}
          // headerToolbar={{
          //   left: "",
          //   center: "title",
          //   right: "prev,next",
          // }}
          // Custom styling
          // height="auto"
          // contentHeight="auto"
          dayCellClassNames="ease relative h-10 max-h-20 cursor-pointer border !border-strokedark p-2 transition duration-500 hover:bg-strokedark/60 dark:border-strokedark dark:hover:bg-meta-4 md:h-18 md:p-6 xl:h-20"
          dayHeaderClassNames="bg-primary text-white !border-0 py-[40px] column-lol"
          viewClassNames="bg-boxdark dark:bg-boxdark "
          // Custom view options
          dayHeaderFormat={{ weekday: "long" }}
          // Responsive settings for day header
          // dayHeaderContent={({ date }) => {
          //   const day = date.toLocaleDateString("en-US", { weekday: "long" });
          //   return (
          //     <>
          //       <span className="hidden lg:block">{day}</span>
          //       <span className="block lg:hidden">{day.slice(0, 3)}</span>
          //     </>
          //   );
          // }}
        />
      )}
    </div>
  );
};

export default Calendar;
