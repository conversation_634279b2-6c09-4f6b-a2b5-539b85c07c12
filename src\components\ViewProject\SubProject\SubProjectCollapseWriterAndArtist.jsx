import React from "react";
import SimpleFileUpload from "Components/FileUpload/SimpleFileUpload";
import UploadedAdminInstrumentals from "Components/PublicWorkOrder/WriterWorkOrder/UploadedAdminInstrumentals2";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import UploadedAdminInstrumentals2 from "Components/PublicWorkOrder/WriterWorkOrder/UploadedAdminInstrumentals2";

const SubProjectCollapseWriterAndArtist = ({
  adminWriterInstrumentals,
  surveySubmitStatus,
  assignedIdeas,
  subProjectId,
  setDeleteFileId,
  setShowAssignIdeaModal,
  setSelectedSubProjectId,
  handleInstrumentalUploads,
  handleShowAddIdeaModalOpen,
  uploadedFilesProgressData = {},
}) => {
  const [activeTab, setActiveTab] = React.useState("instrumentals&ideas");

  const tabs = [
    {
      id: "instrumentals&ideas",
      label: "Survey & Instrumentals",
    },
  ];

  return (
    <div className="p-4 w-full rounded border shadow-default border-strokedark bg-boxdark">
      {/* Tabs Navigation */}
      <div className="flex gap-2 mb-4 border-b border-stroke">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            className={`px-4 py-2 text-sm font-semibold ${
              activeTab === tab.id
                ? "border-b-2 border-white text-primary"
                : "text-bodydark hover:text-white"
            }`}
            onClick={() => setActiveTab(tab.id)}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="mt-4">
        {activeTab === "instrumentals&ideas" && (
          <div className="flex justify-between items-start p-4 rounded border border-form-strokedark bg-form-input">
            <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
              <div className="flex gap-2 items-center">
                <FontAwesomeIcon
                  icon="fa-solid fa-circle-check"
                  color={assignedIdeas?.length > 0 ? "#00FF00" : "#cccccc"}
                />
                <span className="text-white">
                  {assignedIdeas?.length > 0
                    ? "Ideas assigned"
                    : "Ideas not assigned"}
                </span>
              </div>

              <div className="flex flex-wrap gap-3 items-center">
                <button
                  className="inline-flex items-center justify-center rounded-sm bg-primary px-3 py-1.5 text-center text-sm font-medium text-white hover:bg-opacity-90"
                  onClick={() => {
                    setShowAssignIdeaModal(true);
                    setSelectedSubProjectId(subProjectId);
                  }}
                >
                  Assign New Idea
                </button>
                <button
                  className="inline-flex items-center justify-center rounded-sm bg-primary px-3 py-1.5 text-center text-sm font-medium text-white hover:bg-opacity-90"
                  onClick={handleShowAddIdeaModalOpen}
                >
                  Add New Idea
                </button>
              </div>
            </div>
            <div className="custom-overflow h-[220px] w-1/2 overflow-y-auto rounded border border-form-strokedark bg-form-input p-4">
              <div className="space-y-4">
                <SimpleFileUpload
                  maxFileSize={15}
                  label={"Instrumental"}
                  uploadedFilesProgressData={uploadedFilesProgressData}
                  setFormData={handleInstrumentalUploads}
                />
                {adminWriterInstrumentals?.length > 0 && (
                  <div className="mt-4">
                    <UploadedAdminInstrumentals2
                      uploadedFiles={adminWriterInstrumentals}
                      setDeleteFileId={setDeleteFileId}
                    />
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SubProjectCollapseWriterAndArtist;
