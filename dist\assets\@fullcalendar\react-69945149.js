import{r as i,R as r,a as o}from"../vendor-94843817.js";import{U as u,X as h}from"./core-ff88745d.js";const l=parseInt(String(r.version).split(".")[0]),m=l<18;class p extends i.Component{constructor(){super(...arguments),this.elRef=i.createRef(),this.isUpdating=!1,this.isUnmounting=!1,this.state={customRenderingMap:new Map},this.requestResize=()=>{this.isUnmounting||(this.cancelResize(),this.resizeId=requestAnimationFrame(()=>{this.doResize()}))}}render(){const e=[];for(const t of this.state.customRenderingMap.values())e.push(r.createElement(g,{key:t.id,customRendering:t}));return r.createElement("div",{ref:this.elRef},e)}componentDidMount(){this.isUnmounting=!1;const e=new u;this.handleCustomRendering=e.handle.bind(e),this.calendar=new h(this.elRef.current,Object.assign(Object.assign({},this.props),{handleCustomRendering:this.handleCustomRendering})),this.calendar.render(),this.calendar.on("_beforeprint",()=>{o.flushSync(()=>{})});let t;e.subscribe(s=>{const a=Date.now(),c=!t;(m||c||this.isUpdating||this.isUnmounting||a-t<100?d:o.flushSync)(()=>{this.setState({customRenderingMap:s},()=>{t=a,c?this.doResize():this.requestResize()})})})}componentDidUpdate(){this.isUpdating=!0,this.calendar.resetOptions(Object.assign(Object.assign({},this.props),{handleCustomRendering:this.handleCustomRendering})),this.isUpdating=!1}componentWillUnmount(){this.isUnmounting=!0,this.cancelResize(),this.calendar.destroy()}doResize(){this.calendar.updateSize()}cancelResize(){this.resizeId!==void 0&&(cancelAnimationFrame(this.resizeId),this.resizeId=void 0)}getApi(){return this.calendar}}p.act=d;class g extends i.PureComponent{render(){const{customRendering:e}=this.props,{generatorMeta:t}=e,s=typeof t=="function"?t(e.renderProps):t;return o.createPortal(s,e.containerEl)}}function d(n){n()}export{p as F};
