import React from 'react';

const LiveDateTime = () => {
  const [, setTime] = React.useState(new Date());

  React.useEffect(() => {
    const interval = setInterval(() => setTime(new Date()), 1000);
    return () => {
      clearInterval(interval);
    };
  }, []);

  return (
    <span className='ml-2 text-base font-medium text-white'>
      {new Date().toLocaleString('en-US', {
        timeZone: 'America/New_York',
      })}
    </span>
  );
};

export default LiveDateTime;
