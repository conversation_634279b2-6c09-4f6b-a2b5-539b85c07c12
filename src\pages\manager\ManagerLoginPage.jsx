import { yupResolver } from "@hookform/resolvers/yup";
import { InteractiveButton } from "Components/InteractiveButton";
import React from "react";
import { useForm } from "react-hook-form";
import { Link, useLocation, useNavigate } from "react-router-dom";
import * as yup from "yup";
import { AuthContext } from "../../authContext";
import { GlobalContext, showToast } from "../../globalContext";
import MkdSDK from "../../utils/MkdSDK";
import StripeOnboardingModal from "../../components/StripeOnboardingModal";

let sdk = new MkdSDK();

const ManagerLoginPage = () => {
  const schema = yup
    .object({
      email: yup.string().email().required(),
      password: yup.string().required(),
    })
    .required();

  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch, state } = React.useContext(GlobalContext);

  const [submitLoading, setSubmitLoading] = React.useState(false);
  const [showStripeModal, setShowStripeModal] = React.useState(false);
  const [stripeModalLoading, setStripeModalLoading] = React.useState(false);
  const [userDetailsForStripe, setUserDetailsForStripe] = React.useState(null);
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const redirect_uri = searchParams.get("redirect_uri");
  const navigate = useNavigate();
  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  // Helper function to check if manager needs Stripe onboarding
  const checkManagerStripeOnboarding = (loginResult) => {
    console.log("🔍 Manager Stripe Check:");
    console.log("- has_stripe === false:", loginResult.has_stripe === false);
    console.log(
      "- stripe_onboard_url exists:",
      !!loginResult.stripe_onboard_url
    );

    // For managers, ALWAYS show Stripe modal if:
    // 1. Manager doesn't have Stripe setup (has_stripe: false)
    // 2. Manager has a stripe onboard URL available
    // Note: Unlike members, we don't check if they're main member - ALL managers need Stripe
    if (loginResult.has_stripe === false && loginResult.stripe_onboard_url) {
      console.log("✅ Manager needs Stripe setup - showing modal");
      return { shouldShow: true, loginResult };
    }

    console.log("❌ Manager doesn't need Stripe setup");
    return null;
  };

  // Handle Stripe setup button click
  const handleStripeSetup = async () => {
    if (!userDetailsForStripe?.stripe_onboard_url) {
      showToast(
        globalDispatch,
        "Stripe setup URL not available",
        4000,
        "error"
      );
      return;
    }

    setStripeModalLoading(true);

    // Redirect to Stripe onboarding
    window.location.href = userDetailsForStripe.stripe_onboard_url;
  };

  // Handle skip Stripe setup
  const handleSkipStripe = () => {
    setShowStripeModal(false);

    // NOW dispatch LOGIN since manager chose to skip Stripe
    if (userDetailsForStripe) {
      console.log("🔄 Manager skipped Stripe - dispatching LOGIN now");
      dispatch({
        type: "LOGIN",
        payload: userDetailsForStripe,
      });

      // Managers don't need subscription onboarding - go directly to projects
      navigate(redirect_uri ?? "/manager/projects");
    }
  };

  const onSubmit = async (data) => {
    try {
      setSubmitLoading(true);
      const result = await sdk.login(data.email, data.password, "manager");
      if (!result.error) {
        // Clear manager-specific localStorage items
        localStorage.removeItem("managerTypeViewEdits");
        localStorage.removeItem("managerCompletedViewEdits");
        localStorage.removeItem("managerPendingViewEdits");
        localStorage.removeItem("managerSelectedMemberId");

        // Check if manager needs Stripe onboarding BEFORE dispatching LOGIN
        const stripeCheck = checkManagerStripeOnboarding(result);

        if (stripeCheck && stripeCheck.shouldShow) {
          console.log(
            "🎯 Manager needs Stripe setup - showing modal (NO LOGIN DISPATCH YET)"
          );
          setUserDetailsForStripe(stripeCheck.loginResult);
          setShowStripeModal(true);
          setSubmitLoading(false);
          showToast(globalDispatch, "Successfully Logged In", 4000, "success");
          localStorage.setItem("photo", result?.photo);
          localStorage.setItem("companyName", result?.company_name);
          localStorage.setItem("userName", result?.user_name);
          return; // Stop here - LOGIN dispatch will happen when manager chooses skip or after Stripe
        }

        // If no Stripe modal needed, proceed with normal LOGIN dispatch
        dispatch({
          type: "LOGIN",
          payload: result,
        });

        showToast(globalDispatch, "Successfully Logged In", 4000, "success");
        localStorage.setItem("photo", result?.photo);
        localStorage.setItem("companyName", result?.company_name);
        localStorage.setItem("userName", result?.user_name);
        navigate(redirect_uri ?? "/manager/projects");
      } else {
        setSubmitLoading(false);
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
      }
    } catch (error) {
      setSubmitLoading(false);

      showToast(globalDispatch, error.message, 4000, "error");
      setError("email", {
        type: "manual",
        message: error.response.data.message
          ? error.response.data.message
          : error.message,
      });
    }
  };

  return (
    <>
      <StripeOnboardingModal
        isOpen={showStripeModal}
        onClose={() => setShowStripeModal(false)}
        onSetupPayment={handleStripeSetup}
        onSkip={handleSkipStripe}
        isLoading={stripeModalLoading}
      />
      <div className="max-w-screen flex h-full">
        <div className="shadow-default flex min-h-screen w-full items-center justify-center rounded border border-form-strokedark bg-boxdark dark:border-form-strokedark dark:bg-boxdark">
          <div className="flex w-full flex-wrap items-center">
            {/* Left Side - Image */}
            <div className="hidden w-full xl:block xl:w-1/2">
              <div className="py-17.5 px-26 text-center">
                <Link className="mb-5.5 inline-block" to="/">
                  <img
                    crossOrigin="anonymous"
                    src={
                      state.siteLogo ??
                      `${window.location.origin}/new/cheerEQ-2-Ed2.png`
                    }
                    className="h-auto w-[300px] dark:hidden"
                    alt="Logo"
                  />
                </Link>

                <p className="2xl:px-20">
                  Welcome back! Please sign in to access your account.
                </p>
              </div>
            </div>

            {/* Right Side - Login Form */}
            <div className="w-full border-form-strokedark px-12 xl:w-1/2 xl:border-l-2 dark:border-form-strokedark">
              <div className="sm:p-12.5 xl:p-17.5 w-full p-4">
                <h2 className="mb-9 text-2xl font-bold text-white sm:text-title-xl2 dark:text-white">
                  Sign In to Your Account
                </h2>

                <form onSubmit={handleSubmit(onSubmit)}>
                  <div className="mb-4">
                    <label className="mb-2.5 block font-medium text-white dark:text-white">
                      Email
                    </label>
                    <div className="relative">
                      <input
                        type="email"
                        placeholder="Enter your email"
                        {...register("email")}
                        className="w-full rounded-lg border border-form-strokedark bg-form-input py-4 pl-6 pr-10 text-white outline-none focus:border-primary focus-visible:shadow-none dark:border-form-strokedark dark:bg-form-input dark:text-white dark:focus:border-primary"
                      />
                      {errors.email && (
                        <span className="mt-1 text-sm text-red-500">
                          {errors.email.message}
                        </span>
                      )}
                    </div>
                  </div>

                  <div className="mb-6">
                    <label className="mb-2.5 block font-medium text-white dark:text-white">
                      Password
                    </label>
                    <div className="relative">
                      <input
                        type="password"
                        placeholder="Enter your password"
                        {...register("password")}
                        className="w-full rounded-lg border border-form-strokedark bg-form-input py-4 pl-6 pr-10 text-white outline-none focus:border-primary focus-visible:shadow-none dark:border-form-strokedark dark:bg-form-input dark:text-white dark:focus:border-primary"
                      />
                      {errors.password && (
                        <span className="mt-1 text-sm text-red-500">
                          {errors.password.message}
                        </span>
                      )}
                    </div>
                  </div>

                  <div className="mb-5">
                    <InteractiveButton
                      type="submit"
                      disabled={submitLoading}
                      className="w-full cursor-pointer rounded-lg border border-primary bg-primary p-4 text-white transition hover:bg-opacity-90 disabled:opacity-50"
                      loading={submitLoading}
                    >
                      Sign In
                    </InteractiveButton>
                  </div>

                  <div className="mt-6 text-center">
                    <Link
                      to="/manager/forgot"
                      className="text-primary hover:underline"
                    >
                      Forgot Password?
                    </Link>
                  </div>

                  <div className="mt-6 text-center">
                    <Link to="/" className="text-white hover:text-primary">
                      Back to Home
                    </Link>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ManagerLoginPage;
