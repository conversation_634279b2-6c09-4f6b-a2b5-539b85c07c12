import React from "react";
import { useNavigate, useParams } from "react-router-dom";
import { AuthContext, tokenExpireError } from "../authContext";
import { GlobalContext, showToast } from "../globalContext";
import ConfirmModal from "Components/Modal/ConfirmModal";
import {
  getMixSeasonDetailsAPI,
  deleteMixSeasonAPI,
} from "Src/services/mixSeasonService";

const ViewMixSeasonPage = () => {
  const { dispatch, state: authState } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);

  const [viewModel, setViewModel] = React.useState({});
  const [deleteItemId, setDeleteItemId] = React.useState(null);
  const [showDeleteMixSeasonModal, setShowDeleteMixSeasonModal] =
    React.useState(false);

  const params = useParams();
  const navigate = useNavigate();

  const handleDeleteMixSeason = async () => {
    try {
      const result = await deleteMixSeasonAPI(deleteItemId);
      if (!result.error) {
        setShowDeleteMixSeasonModal(false);
        navigate(`/${authState.role}/mix-seasons`);
        showToast(globalDispatch, "Mix season deleted successfully", 4000);
      } else {
        showToast(globalDispatch, result.message, 4000, "error");
        setShowDeleteMixSeasonModal(false);
      }
    } catch (err) {
      setShowDeleteMixSeasonModal(false);
      tokenExpireError(dispatch, err.message);
    }
  };

  const handleDeleteMixSeasonModalClose = () => {
    setShowDeleteMixSeasonModal(false);
  };

  React.useEffect(function () {
    (async function () {
      try {
        const result = await getMixSeasonDetailsAPI(Number(params?.id));
        if (!result.error) {
          setViewModel(result.model);
        }
      } catch (error) {
        tokenExpireError(dispatch, error.message);
      }
    })();
  }, []);

  return (
    <>
      <div className="max-w-screen h-full p-4 md:p-4 lg:p-6 xl:p-8">
        <div className="shadow-default rounded border border-strokedark bg-boxdark dark:border-strokedark dark:bg-boxdark">
          <div className="sm:px-6.5 border-b border-strokedark px-4 py-4 dark:border-strokedark">
            <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
              <h3 className="text-xl font-medium text-white">
                View Mix Season
              </h3>
              <div className="flex flex-wrap items-center gap-3">
                <button
                  onClick={() => navigate(-1)}
                  className="inline-flex items-center justify-center rounded-md border border-strokedark bg-meta-4 px-6 py-2.5 text-center font-medium text-white hover:bg-opacity-90"
                >
                  Back
                </button>
                <button
                  onClick={() => {
                    navigate(
                      `/${authState.role}/edit-mix-season/` + params?.id,
                      {
                        state: params?.id,
                      }
                    );
                  }}
                  className="inline-flex items-center justify-center rounded-md bg-primary px-6 py-2.5 text-center font-medium text-white hover:bg-opacity-90"
                >
                  Edit
                </button>
                <button
                  onClick={() => {
                    setShowDeleteMixSeasonModal(true);
                    setDeleteItemId(params?.id);
                  }}
                  className="inline-flex items-center justify-center rounded-md bg-danger px-6 py-2.5 text-center font-medium text-white hover:bg-opacity-90"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>

          <div className="p-4 md:p-6 2xl:p-10">
            <div className="mb-8">
              <div className="space-y-4">
                <div className="border-b border-strokedark pb-4 dark:border-strokedark">
                  <div className="grid grid-cols-1 gap-2 sm:grid-cols-2">
                    <div>
                      <span className="text-sm text-gray-400">Name</span>
                      <p className="text-base font-medium text-white">
                        {viewModel?.name || "N/A"}
                      </p>
                    </div>
                    <div>
                      <span className="text-sm text-gray-400">Username</span>
                      <p className="text-base font-medium text-white">
                        {viewModel?.user_name || "N/A"}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="border-b border-strokedark pb-4 dark:border-strokedark">
                  <div className="grid grid-cols-1 gap-2 sm:grid-cols-2">
                    <div>
                      <span className="text-sm text-gray-400">Status</span>
                      <p className="text-base font-medium text-white">
                        {viewModel.status === 1 ? (
                          <span className="inline-flex items-center gap-2">
                            <span className="inline-block h-2 w-2 rounded-full bg-success"></span>
                            Active
                          </span>
                        ) : (
                          <span className="inline-flex items-center gap-2">
                            <span className="inline-block h-2 w-2 rounded-full bg-danger"></span>
                            Inactive
                          </span>
                        )}
                      </p>
                    </div>
                    {/* <div>
                      <span className="text-sm text-gray-400">
                        Created Date
                      </span>
                      <p className="text-base font-medium text-white">
                        {viewModel?.created_at
                          ? new Date(viewModel.created_at).toLocaleDateString()
                          : "N/A"}
                      </p>
                    </div> */}
                  </div>
                </div>
              </div>
            </div>

            {/* Additional Sections if needed */}
            {/* <div className="mb-8">
              <h4 className="mb-4 text-lg font-semibold text-white">Additional Information</h4>
              <div className="space-y-4">
                ... Additional content ...
              </div>
            </div> */}
          </div>
        </div>
      </div>

      {showDeleteMixSeasonModal && (
        <ConfirmModal
          confirmText="Are you sure you want to delete this mix season?"
          setModalClose={handleDeleteMixSeasonModalClose}
          setFormYes={handleDeleteMixSeason}
        />
      )}
    </>
  );
};

export default ViewMixSeasonPage;
