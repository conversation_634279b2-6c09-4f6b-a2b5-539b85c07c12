import React from "react";

const features = [
  {
    icon: "/figma-icons/icon-sparkles.svg",
    title: "Project Management",
    desc: "Organize, track, and manage all your music projects in one place.",
  },
  {
    icon: "/figma-icons/icon-file-earmark-text.svg",
    title: "Client Portal",
    desc: "Give your clients a seamless, branded experience for project collaboration.",
  },
  {
    icon: "/figma-icons/icon-credit-card.svg",
    title: "Automated Billing",
    desc: "Streamline payments and invoicing with built-in automation.",
  },
  {
    icon: "/figma-icons/icon-building-office-2.svg",
    title: "Team Collaboration",
    desc: "Empower your team with shared calendars, reminders, and more.",
  },
];

const ExploreCheerEQSection = () => {
  return (
    <div className="mx-auto max-w-4xl rounded-2xl border border-[#D1D6DE] bg-white p-10 shadow-lg">
      <h2 className="mb-2 text-center text-2xl font-bold text-[#131E2B]">
        Explore CheerEQ Features
      </h2>
      <p className="mb-8 text-center text-base text-[#667484]">
        Discover the powerful tools and features that make CheerEQ the best
        platform for music teams and studios.
      </p>
      <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
        {features.map((feature, idx) => (
          <div
            key={idx}
            className="flex items-start gap-4 rounded-xl border border-[#E9EBEF] bg-[#F5F7FF] p-6"
          >
            <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-[#E0E6FC]">
              <img src={feature.icon} alt="icon" className="h-8 w-8" />
            </div>
            <div>
              <h3 className="mb-1 text-lg font-semibold text-[#2B3EB4]">
                {feature.title}
              </h3>
              <p className="text-sm text-[#667484]">{feature.desc}</p>
            </div>
          </div>
        ))}
      </div>
      <div className="mt-10 flex justify-center">
        <button
          className="rounded-full bg-[#3C50E0] px-8 py-3 text-base font-semibold text-white transition hover:bg-[#2B3EB4]"
          type="button"
        >
          Continue
        </button>
      </div>
    </div>
  );
};

export default ExploreCheerEQSection;
