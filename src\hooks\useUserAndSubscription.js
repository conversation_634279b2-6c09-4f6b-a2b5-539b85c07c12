import React from "react";
import { useUserDetails } from "./useUserDetails";
import { useSubscription } from "./useSubscription";
import {
  GlobalContext,
  setGLobalProperty,
  setProjectCounts,
} from "../globalContext";
import MkdSDK from "../utils/MkdSDK";

/**
 * Combined hook for user details and subscription data
 * Optimizes API calls by managing both user and subscription state together
 */
export const useUserAndSubscription = (options = {}) => {
  const {
    autoFetch = true,
    cacheTimeout = 5 * 60 * 1000, // 5 minutes
    shouldFetchSubscriptionData = true,
  } = options;

  const globalContext = React.useContext(GlobalContext);
  const [subscriptionLoading, setSubscriptionLoading] = React.useState(false);
  const [lastSubscriptionFetch, setLastSubscriptionFetch] =
    React.useState(null);

  const { dispatch: globalDispatch } = globalContext || { dispatch: () => {} };
  const contextAvailable = !!globalContext;

  // Use existing hooks
  const userDetailsHook = useUserDetails({
    autoFetch,
    cacheTimeout,
    forceRefresh: false,
  });

  const subscriptionHook = useSubscription();

  const sdk = new MkdSDK();

  // Check if subscription data is fresh
  const isSubscriptionDataFresh = React.useMemo(() => {
    if (!lastSubscriptionFetch) return false;
    const now = Date.now();
    return now - lastSubscriptionFetch < cacheTimeout;
  }, [lastSubscriptionFetch, cacheTimeout]);

  // Fetch subscription data
  const fetchSubscriptionData = React.useCallback(async () => {
    if (!shouldFetchSubscriptionData) return;

    // Prevent multiple simultaneous calls
    if (subscriptionLoading) {
      console.log("Subscription data already loading, skipping duplicate call");
      return;
    }

    try {
      setSubscriptionLoading(true);

      // Fetch products and prices in parallel
      const [productsResult, pricesResult] = await Promise.all([
        sdk.callRawAPI(`/v4/api/records/stripe_product?page=1,100`, [], "GET"),
        sdk.callRawAPI(`/v4/api/records/stripe_price?page=1,100`, [], "GET"),
      ]);

      // Store products
      if (!productsResult.error) {
        const filteredProducts = productsResult.list
          .filter((product) => product.name !== "plan A")
          .sort((a, b) => a.id - b.id);

        setGLobalProperty(
          globalDispatch,
          filteredProducts,
          "subscription.products"
        );
      }

      // Store prices
      if (!pricesResult.error) {
        setGLobalProperty(
          globalDispatch,
          pricesResult.list,
          "subscription.prices"
        );
      }

      // Fetch current subscription
      const subscriptionResult = await sdk.getCustomerStripeSubscription();
      if (!subscriptionResult.error) {
        // Note: planId is actually the price.id, not product_id
        const currentPrice = pricesResult.list.find(
          (price) => price.id === subscriptionResult?.customer?.planId
        );

        const enhancedSubscription = {
          ...subscriptionResult?.customer,
          plan_name:
            currentPrice?.name || subscriptionResult?.customer?.plan_name,
          amount: currentPrice?.amount || subscriptionResult?.customer?.amount,
          interval: currentPrice?.object
            ? JSON.parse(currentPrice.object)?.recurring?.interval
            : "month",
          priceId: currentPrice?.id || null,
          productId: currentPrice?.product_id || null, // Keep product_id separate if needed
        };

        setGLobalProperty(
          globalDispatch,
          enhancedSubscription,
          "subscription.currentSubscription"
        );
      }

      // Fetch project counts
      const projectCountsResult = await sdk.callRawAPI(
        "/v3/api/custom/equality_record/project/subscription/project-counts",
        [],
        "GET"
      );

      if (!projectCountsResult.error) {
        setProjectCounts(
          globalDispatch,
          projectCountsResult,
          projectCountsResult.subscription_period
        );
      }

      setLastSubscriptionFetch(Date.now());
    } catch (error) {
      console.error("Error fetching subscription data:", error);
    } finally {
      setSubscriptionLoading(false);
    }
  }, [globalDispatch, shouldFetchSubscriptionData, subscriptionLoading]);

  // Initialize data on mount
  // Remove fetchSubscriptionData from dependencies to prevent multiple calls
  React.useEffect(() => {
    if (autoFetch && !isSubscriptionDataFresh && !subscriptionLoading) {
      // Only fetch if not already loading to prevent multiple simultaneous calls
      fetchSubscriptionData();
    }
  }, [autoFetch, isSubscriptionDataFresh, subscriptionLoading]);

  // Refresh both user and subscription data
  const refreshAll = React.useCallback(async () => {
    await Promise.all([
      userDetailsHook.refreshUserDetails(),
      fetchSubscriptionData(),
    ]);
  }, [userDetailsHook.refreshUserDetails, fetchSubscriptionData]);

  // Return safe fallback if context not available
  if (!contextAvailable) {
    console.warn("GlobalContext not available in useUserAndSubscription");
    return {
      userDetails: null,
      userLoading: false,
      userError: "Context not available",
      subscription: {},
      subscriptionLoading: false,
      isLoading: false,
      refreshUserDetails: () => Promise.resolve(),
      refreshSubscription: () => Promise.resolve(),
      refreshAll: () => Promise.resolve(),
      updateUserDetailsCache: () => {},
      isDataFresh: false,
    };
  }

  return {
    // User details
    userDetails: userDetailsHook.userDetails,
    userLoading: userDetailsHook.isLoading,
    userError: userDetailsHook.error,

    // Subscription data
    subscription: subscriptionHook,
    subscriptionLoading,

    // Combined loading state
    isLoading: userDetailsHook.isLoading || subscriptionLoading,

    // Actions
    refreshUserDetails: userDetailsHook.refreshUserDetails,
    refreshSubscription: fetchSubscriptionData,
    refreshAll,
    updateUserDetailsCache: userDetailsHook.updateUserDetailsCache,

    // Utilities
    isDataFresh: userDetailsHook.isDataFresh && isSubscriptionDataFresh,
  };
};

export default useUserAndSubscription;
