import MkdSDK from '../utils/MkdSDK';

let sdk = new MkdSDK();

export const retrieveAllProjectAdminAPI = async (page, limit, filter) => {
  try {
    const payload = {
      page: page,
      limit: limit,
      filter: filter,
    };
    const uri = `/v3/api/custom/equality_record/project/admin/retrieve_all`;
    const res = await sdk.callRawAPI(uri, payload, 'POST');
    return res;
  } catch (error) {
    return error;
  }
};

export const retrieveAllForProjectCalendarForAdmin = async (
  page,
  limit,
  filter
) => {
  try {
    const payload = {
      page: page,
      limit: limit,
      filter: filter,
    };
    const uri = `/v3/api/custom/equality_record/project/calendar/admin/retrieve_all`;
    const res = await sdk.callRawAPI(uri, payload, 'POST');
    return res;
  } catch (error) {
    return error;
  }
};

export const getAllManagerPermissionAdmin = async () => {
  try {
    // const payload = {
    //   page: page,
    //   limit: limit,
    //   filter: filter,
    // };
    const uri = `/v3/api/custom/equality_record/manager_permission/get_all`;
    const res = await sdk.callRawAPI(uri, [], 'GET');
    return res;
  } catch (error) {
    return error;
  }
};

export const retrieveAllManagerPermissionForAdmin = async (
  page,
  limit,
  filter
) => {
  try {
    const payload = {
      page: page,
      limit: limit,
      filter: filter,
    };
    const uri = `/v3/api/custom/equality_record/manager_permission/retrieve_all`;
    const res = await sdk.callRawAPI(uri, payload, 'POST');
    return res;
  } catch (error) {
    return error;
  }
};

export const createOrUpdateMangerPermissionByManagerPermissionIdForAdmin =
  async (filter, id) => {
    try {
      const payload = {
        member_ids: filter,
      };
      const uri = `/v3/api/custom/equality_record/manager_permission/create_or_update_one/${id}`;
      const res = await sdk.callRawAPI(uri, payload, 'PUT');
      return res;
    } catch (error) {
      return error;
    }
  };

export const getOneManagerPermissionForAdmin = async (id) => {
  try {
    const uri = `/v3/api/custom/equality_record/manager_permission/create_or_update_one/${id}`;
    const res = await sdk.callRawAPI(uri, [], 'GET');
    return res;
  } catch (error) {
    return error;
  }
};
