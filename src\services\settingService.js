import axios from "axios";
import MkdSDK from "../utils/MkdSDK";

let sdk = new MkdSDK();

export const retrieveAllSettingsAPI = async (payload) => {
  try {
    const uri = "/v3/api/custom/equality_record/setting/retrieve_all";
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {}
};

export const createOrUpdateAllSettingsAPI = async (payload) => {
  try {
    const url = `/v3/api/custom/equality_record/setting/update_all`;
    const res = await sdk.callRawAPI(url, payload, "PUT");
    return res;
  } catch (error) {
    return error;
  }
};

export const uploadSettingFileAPI = async (formData) => {
  try {
    const uri = `https://app.equalityrecords.com/v3/api/custom/equality_record/setting/upload/file`;
    const res = await axios.post(uri, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
        "x-project":
          "ZXF1YWxpdHlyZWNvcmQ6cDV3YjJnd2M5aWVyZjhzem5ldDZibnNjcTA3bnVyYQ==",
        Authorization: `Bearer ${localStorage.getItem("token")}`,
      },
    });

    return res.data;
  } catch (error) {
    return error;
  }
};

export const updateSettingAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/setting/create_or_update_one`;
    const res = await sdk.callRawAPI(uri, payload, "PUT");
    return res;
  } catch (error) {
    return error;
  }
};

export const getSiteImagesAPI = async () => {
  try {
    const uri = `/v3/api/custom/equality_record/setting/public/site_images`;
    const res = await sdk.callRawAPI(uri, [], "GET");
    return res;
  } catch (error) {
    return error;
  }
};

export const retrieveSummaryAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/setting/summary`;
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};
