import React from "react";
import FileUpload from "Components/FileUpload/FileUpload";

const EmptyMaster = ({
  canUpload = true,
  setFormData,
  uploadedFilesProgressData = {},
}) => {
  return (
    <div className="w-full p-5 mb-2 bg-gray-800 rounded-md shadow">
      {canUpload && (
        <div className="flex flex-col">
          <div className="mb-2 text-xl font-semibold text-center text-white">
            Upload Master Files
          </div>

          <FileUpload
            justify="center"
            items="center"
            maxFileSize={500}
            setFormData={setFormData}
            uploadedFilesProgressData={uploadedFilesProgressData}
          />
        </div>
      )}
    </div>
  );
};

export default EmptyMaster;
