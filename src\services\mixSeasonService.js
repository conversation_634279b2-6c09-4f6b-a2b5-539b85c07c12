import MkdSDK from '../utils/MkdSDK';

let sdk = new MkdSDK();

export const retrieveAllMixSeasonsAPI = async (page, limit, filter) => {
  try {
    const payload = {
      page: page,
      limit: limit,
      filter: filter,
    };
    const uri = `/v3/api/custom/equality_record/mix_season/retrieve`;
    const res = await sdk.callRawAPI(uri, payload, 'POST');
    return res;
  } catch (error) {
    return error;
  }
};

export const getAllMixSeasonAPI = async () => {
  try {
    const uri = `/v3/api/custom/equality_record/mix_season/get_all`;
    const res = await sdk.callRawAPI(uri, [], 'GET');
    return res;
  } catch (error) {
    return error;
  }
};

export const getMixSeasonDetailsAPI = async (id) => {
  try {
    const uri = `/v3/api/custom/equality_record/mix_season/${id}`;
    const res = await sdk.callRawAPI(uri, [], 'GET');
    return res;
  } catch (error) {
    return error;
  }
};

export const addMixSeasonAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/mix_season/add`;
    const res = await sdk.callRawAPI(uri, payload, 'POST');
    return res;
  } catch (error) {
    return error;
  }
};

export const updateMixSeasonAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/mix_season/${payload.id}`;
    const res = await sdk.callRawAPI(uri, payload, 'PUT');
    return res;
  } catch (error) {
    return error;
  }
};

export const deleteMixSeasonAPI = async (id) => {
  try {
    const uri = `/v3/api/custom/equality_record/mix_season/${id}`;
    const res = await sdk.callRawAPI(uri, [], 'DELETE');
    return res;
  } catch (error) {
    return error;
  }
};
