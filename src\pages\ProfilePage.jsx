import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "../utils/MkdSDK";
import { GlobalContext, showToast } from "../globalContext";
import { AuthContext, tokenExpireError } from "../authContext";
import { InteractiveButton } from "Components/InteractiveButton";
import {
  createOrUpdateUserProfilePhotoAPI,
  updateUserDetailsAPI,
} from "Src/services/userService";
import { useUserDetails } from "../hooks/useUserDetails";
import { uploadS3FilesAPI } from "Src/services/workOrderService";

import PhotoUpload from "Components/PhotoUpload";
import { useSubscription } from "../hooks/useSubscription";

let sdk = new MkdSDK();

const ProfilePage = () => {
  const schema = yup.object().shape({
    first_name: yup.string().required(),
    last_name: yup.string().required(),
    email: yup.string().email().required(),
    company_name: yup.string().required(),
    company_address: yup.string(),
    phone: yup.string(),
    password: yup.string(),
  });

  const { dispatch } = React.useContext(AuthContext);
  const { state: authState } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { subscriptionProductId, displayName } = useSubscription();

  // Use optimized user details hook
  const { userDetails, refetchUserDetails } = useUserDetails({
    autoFetch: true,
    cacheTimeout: 5 * 60 * 1000, // 5 minutes cache
  });

  const [oldEmail, setOldEmail] = useState("");
  const [submitLoading, setSubmitLoading] = useState(false);
  const [userId, setUserId] = React.useState(null);
  const [photoUrl, setPhotoUrl] = React.useState(null);
  const [companyName, setCompanyName] = React.useState(null);

  const {
    register,
    handleSubmit,
    setError,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "profile",
      },
    });

    const userId = localStorage.getItem("user");
    setUserId(userId);
  }, [globalDispatch]);

  // Separate effect to handle user details when they're available
  React.useEffect(() => {
    if (userDetails) {
      setValue("email", userDetails.email || "");
      setValue("first_name", userDetails.first_name || "");
      setValue("last_name", userDetails.last_name || "");
      setValue("company_name", userDetails.company_name || "");
      setValue("company_address", userDetails.company_address || "");
      setValue("phone", userDetails.phone || "");
      setOldEmail(userDetails.email || "");
      setPhotoUrl(userDetails.photo || "");
      setCompanyName(userDetails.company_name || "");

      dispatch({
        type: "SET_PROFILE",
        payload: {
          photo: userDetails.photo,
          companyName: userDetails.company_name,
        },
      });
    }
  }, [userDetails, setValue, dispatch]);

  const onSubmit = async (data) => {
    try {
      setSubmitLoading(true);
      if (oldEmail !== data.email) {
        const emailResult = await sdk.updateEmail(data.email);
        if (!emailResult.error) {
          showToast(dispatch, "Email Updated", 2000);
        } else {
          if (emailResult.validation) {
            const keys = Object.keys(emailResult.validation);
            for (let i = 0; i < keys.length; i++) {
              const field = keys[i];
              setError(field, {
                type: "manual",
                message: emailResult.validation[field],
              });
            }
          }
        }
      }

      if (data.password.length > 0) {
        const passwordResult = await sdk.updatePassword(data.password);
        if (!passwordResult.error) {
          showToast(globalDispatch, "Password Updated", 2000);
        } else {
          if (passwordResult.validation) {
            const keys = Object.keys(passwordResult.validation);
            for (let i = 0; i < keys.length; i++) {
              const field = keys[i];
              setError(field, {
                type: "manual",
                message: passwordResult.validation[field],
              });
            }
          }
        }
      }

      if (
        data.first_name.length > 0 ||
        data.last_name.length > 0 ||
        data.company_name.length > 0 ||
        data.company_address ||
        data.phone
      ) {
        // Create comprehensive payload with all necessary fields to prevent data loss
        const profilePayload = {
          id: parseInt(userId),
          first_name: data.first_name,
          last_name: data.last_name,
          company_name: data.company_name,
          company_address: data.company_address,
          phone: data.phone,
          // Include existing data to prevent loss
          deposit_percent: userDetails?.deposit_percent || 50,
          contract_agreement: userDetails?.contract_agreement || "",
          survey: userDetails?.survey
            ? typeof userDetails.survey === "string"
              ? userDetails.survey
              : JSON.stringify(userDetails.survey)
            : JSON.stringify({ weeks: 8, day: "Monday" }),
          routine_submission_date: userDetails?.routine_submission_date
            ? typeof userDetails.routine_submission_date === "string"
              ? userDetails.routine_submission_date
              : JSON.stringify(userDetails.routine_submission_date)
            : JSON.stringify({ weeks: 1, day: "Monday" }),
          estimated_delivery: userDetails?.estimated_delivery
            ? typeof userDetails.estimated_delivery === "string"
              ? userDetails.estimated_delivery
              : JSON.stringify(userDetails.estimated_delivery)
            : JSON.stringify({ weeks: 1, day: "Friday" }),
          license_company_logo: userDetails?.license_company_logo || "",
          company_logo: userDetails?.company_logo || photoUrl || "",
          office_email: userDetails?.office_email || "",
          edit_policy_link: userDetails?.edit_policy_link || "",
        };

        const profileResult = await updateUserDetailsAPI(profilePayload);
        if (!profileResult.error) {
          // Update auth context with new profile data
          dispatch({
            type: "SET_PROFILE",
            payload: {
              photo: photoUrl,
              companyName: data.company_name,
            },
          });

          // Refresh user details to get updated data from server
          await refetchUserDetails();

          showToast(globalDispatch, "Profile Updated", 2000);
        } else {
          showToast(globalDispatch, "Profile Update Failed", 2000, "error");
        }
      }

      setSubmitLoading(false);
    } catch (error) {
      setSubmitLoading(false);

      setError("email", {
        type: "manual",
        message: error.message,
      });
      tokenExpireError(
        dispatch,
        error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  };

  const handlePhotoUpload = async (formData) => {
    try {
      const result = await uploadS3FilesAPI(formData);
      if (!result.error) {
        let attachmentsArr = JSON.parse(result.attachments);
        const payload = {
          id: userId,
          photo: attachmentsArr[0],
        };
        const photoResult = await createOrUpdateUserProfilePhotoAPI(payload);

        if (!photoResult.error) {
          setPhotoUrl(attachmentsArr[0]);
          showToast(globalDispatch, photoResult.message, 5000);

          // Update auth context with new photo
          dispatch({
            type: "SET_PROFILE",
            payload: {
              photo: attachmentsArr[0],
              companyName: companyName,
            },
          });

          // Refresh user details to get updated data from server
          await refetchUserDetails();

          // window.location.reload();
        } else {
          showToast(globalDispatch, photoResult.message, 3000, "error");
          return;
        }
      } else {
        showToast(globalDispatch, result.message, 2000, "error");
        return;
      }
    } catch (error) {
      tokenExpireError(dispatch, error.message, 4000);
    }
  };

  // Subscription display is now handled by the useSubscription hook

  return (
    <div className="max-w-screen h-full p-4 md:p-4 lg:p-6 xl:p-8">
      <div className="grid grid-cols-5 gap-8">
        {/* Left Column - Personal Information */}
        <div className="col-span-5 xl:col-span-3">
          <div className="shadow-default rounded border border-strokedark bg-boxdark dark:border-strokedark dark:bg-boxdark">
            <div className="border-b border-strokedark px-7 py-4 dark:border-strokedark">
              <h3 className="font-medium text-white dark:text-white">
                Personal Information
              </h3>
            </div>

            <div className="p-7">
              <form onSubmit={handleSubmit(onSubmit)}>
                <div className="mb-5 flex flex-col justify-between gap-5 sm:flex-row">
                  <div className="w-full sm:w-[45%]">
                    <label className="mb-3 block text-sm font-medium text-white dark:text-white">
                      First Name
                    </label>
                    <div className="relative">
                      <span className="left-4.5 absolute top-4">
                        <svg
                          className="fill-current"
                          width="20"
                          height="20"
                          viewBox="0 0 20 20"
                        >
                          {/* User icon SVG path */}
                        </svg>
                      </span>
                      <input
                        className="pl-11.5 pr-4.5 w-full rounded border border-form-strokedark bg-form-input py-3 text-white focus:border-primary focus-visible:outline-none dark:border-strokedark dark:bg-meta-4 dark:text-white dark:focus:border-primary"
                        type="text"
                        {...register("first_name")}
                        placeholder="First Name"
                      />
                    </div>
                    <p className="mt-1 text-sm text-danger">
                      {errors.first_name?.message}
                    </p>
                  </div>

                  <div className="w-full sm:w-[45%]">
                    <label className="mb-3 block text-sm font-medium text-white dark:text-white">
                      Last Name
                    </label>
                    <input
                      className="px-4.5 w-full rounded border border-form-strokedark bg-form-input py-3 text-white focus:border-primary focus-visible:outline-none dark:border-strokedark dark:bg-meta-4 dark:text-white dark:focus:border-primary"
                      type="text"
                      {...register("last_name")}
                      placeholder="Last Name"
                    />
                    <p className="mt-1 text-sm text-danger">
                      {errors.last_name?.message}
                    </p>
                  </div>
                </div>

                {/* Company Name */}
                <div className="mb-5">
                  <label className="mb-3 block text-sm font-medium text-white dark:text-white">
                    Company Name
                  </label>
                  <input
                    className="px-4.5 w-full rounded border border-form-strokedark bg-form-input py-3 text-white focus:border-primary focus-visible:outline-none dark:border-strokedark dark:bg-meta-4 dark:text-white dark:focus:border-primary"
                    type="text"
                    {...register("company_name")}
                    placeholder="Company Name"
                  />
                  <p className="mt-1 text-sm text-danger">
                    {errors.company_name?.message}
                  </p>
                </div>

                {/* Company Address */}
                <div className="mb-5">
                  <label className="mb-3 block text-sm font-medium text-white dark:text-white">
                    Company Address
                  </label>
                  <input
                    className="px-4.5 w-full rounded border border-form-strokedark bg-form-input py-3 text-white focus:border-primary focus-visible:outline-none dark:border-strokedark dark:bg-meta-4 dark:text-white dark:focus:border-primary"
                    type="text"
                    {...register("company_address")}
                    placeholder="Company Address"
                  />
                  <p className="mt-1 text-sm text-danger">
                    {errors.company_address?.message}
                  </p>
                </div>

                {/* Phone */}
                <div className="mb-5">
                  <label className="mb-3 block text-sm font-medium text-white dark:text-white">
                    Phone Number
                  </label>
                  <input
                    className="px-4.5 w-full rounded border border-form-strokedark bg-form-input py-3 text-white focus:border-primary focus-visible:outline-none dark:border-strokedark dark:bg-meta-4 dark:text-white dark:focus:border-primary"
                    type="tel"
                    {...register("phone")}
                    placeholder="Phone Number"
                  />
                  <p className="mt-1 text-sm text-danger">
                    {errors.phone?.message}
                  </p>
                </div>

                {/* Email */}
                <div className="mb-5">
                  <label className="mb-3 block text-sm font-medium text-white dark:text-white">
                    Email Address
                  </label>
                  <div className="relative">
                    <span className="left-4.5 absolute top-4">
                      <svg
                        className="fill-current"
                        width="20"
                        height="20"
                        viewBox="0 0 20 20"
                      >
                        {/* Email icon SVG path */}
                      </svg>
                    </span>
                    <input
                      className="pl-11.5 pr-4.5 w-full rounded border border-form-strokedark bg-form-input py-3 text-white focus:border-primary focus-visible:outline-none dark:border-strokedark dark:bg-meta-4 dark:text-white dark:focus:border-primary"
                      type="email"
                      {...register("email")}
                      readOnly={true}
                      placeholder="Email Address"
                    />
                  </div>
                  <p className="mt-1 text-sm text-danger">
                    {errors.email?.message}
                  </p>
                </div>

                {/* Password */}
                <div className="mb-5">
                  <label className="mb-3 block text-sm font-medium text-white dark:text-white">
                    Password
                  </label>
                  <input
                    className="px-4.5 w-full rounded border border-form-strokedark bg-form-input py-3 text-white focus:border-primary focus-visible:outline-none dark:border-strokedark dark:bg-meta-4 dark:text-white dark:focus:border-primary"
                    type="password"
                    {...register("password")}
                    placeholder="Enter password"
                  />
                  <p className="mt-1 text-sm text-danger">
                    {errors.password?.message}
                  </p>
                </div>

                {/* Subscription (if member) */}
                {authState?.role === "member" && subscriptionProductId && (
                  <div className="mb-5">
                    <label className="mb-3 block text-sm font-medium text-white dark:text-white">
                      Subscription
                    </label>
                    <input
                      className="px-4.5 w-full rounded border border-form-strokedark bg-form-input py-3 text-white focus:border-primary focus-visible:outline-none dark:border-strokedark dark:bg-meta-4 dark:text-white dark:focus:border-primary"
                      type="text"
                      readOnly
                      value={displayName || ""}
                    />
                  </div>
                )}

                {/* Buttons */}
                <div className="gap-4.5 mt-5 flex justify-end">
                  <button
                    className="flex justify-center rounded border border-strokedark px-6 py-2 font-medium text-white hover:shadow-1 dark:border-strokedark dark:text-white"
                    type="button"
                  >
                    Cancel
                  </button>
                  <InteractiveButton
                    loading={submitLoading}
                    disabled={submitLoading}
                    type="submit"
                    className="flex justify-center rounded bg-primary px-6 py-2 font-medium text-gray hover:bg-opacity-90"
                  >
                    Save
                  </InteractiveButton>
                </div>
              </form>
            </div>
          </div>
        </div>

        {/* Right Column - Photo Upload */}
        <div className="col-span-5 xl:col-span-2">
          <div className="shadow-default rounded border border-strokedark bg-boxdark dark:border-strokedark dark:bg-boxdark">
            <div className="border-b border-strokedark px-7 py-4 dark:border-strokedark">
              <h3 className="font-medium text-white dark:text-white">
                Your Photo
              </h3>
            </div>
            <div className="p-7">
              <div className="mb-4 flex items-center gap-3">
                {photoUrl && (
                  <div className="h-14 w-14 rounded-full">
                    <img
                      src={photoUrl}
                      alt="Profile"
                      className="h-full w-full rounded-full object-cover"
                    />
                  </div>
                )}
                <div>
                  <span className="mb-1.5 text-white dark:text-white">
                    Edit your photo
                  </span>
                </div>
              </div>

              <PhotoUpload maxFileSize={2} setFileUpload={handlePhotoUpload} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
