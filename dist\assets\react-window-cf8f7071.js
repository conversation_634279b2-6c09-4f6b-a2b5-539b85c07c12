import{j as Y}from"./@react-pdf/renderer-8ed2c300.js";import{_ as Z,a as D}from"./react-select-64a59aa2.js";import{r as P}from"./vendor-94843817.js";function tt(n,e){n.prototype=Object.create(e.prototype),n.prototype.constructor=n,Z(n,e)}var A=Number.isNaN||function(e){return typeof e=="number"&&e!==e};function et(n,e){return!!(n===e||A(n)&&A(e))}function rt(n,e){if(n.length!==e.length)return!1;for(var o=0;o<n.length;o++)if(!et(n[o],e[o]))return!1;return!0}function W(n,e){e===void 0&&(e=rt);var o,c=[],d,O=!1;function T(){for(var v=[],g=0;g<arguments.length;g++)v[g]=arguments[g];return O&&o===this&&e(v,c)||(d=n.apply(this,v),O=!0,o=this,c=v),d}return T}var it=typeof performance=="object"&&typeof performance.now=="function",U=it?function(){return performance.now()}:function(){return Date.now()};function q(n){cancelAnimationFrame(n.id)}function nt(n,e){var o=U();function c(){U()-o>=e?n.call(null):d.id=requestAnimationFrame(c)}var d={id:requestAnimationFrame(c)};return d}var F=-1;function $(n){if(n===void 0&&(n=!1),F===-1||n){var e=document.createElement("div"),o=e.style;o.width="50px",o.height="50px",o.overflow="scroll",document.body.appendChild(e),F=e.offsetWidth-e.clientWidth,document.body.removeChild(e)}return F}var w=null;function H(n){if(n===void 0&&(n=!1),w===null||n){var e=document.createElement("div"),o=e.style;o.width="50px",o.height="50px",o.overflow="scroll",o.direction="rtl";var c=document.createElement("div"),d=c.style;return d.width="100px",d.height="100px",e.appendChild(c),document.body.appendChild(e),e.scrollLeft>0?w="positive-descending":(e.scrollLeft=1,e.scrollLeft===0?w="negative":w="positive-ascending"),document.body.removeChild(e),w}return w}var ot=150,lt=function(e,o){return e};function st(n){var e,o=n.getItemOffset,c=n.getEstimatedTotalSize,d=n.getItemSize,O=n.getOffsetForIndexAndAlignment,T=n.getStartIndexForOffset,v=n.getStopIndexForStartIndex,g=n.initInstanceProps,C=n.shouldResetStyleCacheOnItemSizeChange,y=n.validateProps;return e=function(_){tt(z,_);function z(m){var t;return t=_.call(this,m)||this,t._instanceProps=g(t.props,D(t)),t._outerRef=void 0,t._resetIsScrollingTimeoutId=null,t.state={instance:D(t),isScrolling:!1,scrollDirection:"forward",scrollOffset:typeof t.props.initialScrollOffset=="number"?t.props.initialScrollOffset:0,scrollUpdateWasRequested:!1},t._callOnItemsRendered=void 0,t._callOnItemsRendered=W(function(r,i,l,a){return t.props.onItemsRendered({overscanStartIndex:r,overscanStopIndex:i,visibleStartIndex:l,visibleStopIndex:a})}),t._callOnScroll=void 0,t._callOnScroll=W(function(r,i,l){return t.props.onScroll({scrollDirection:r,scrollOffset:i,scrollUpdateWasRequested:l})}),t._getItemStyle=void 0,t._getItemStyle=function(r){var i=t.props,l=i.direction,a=i.itemSize,f=i.layout,s=t._getItemStyleCache(C&&a,C&&f,C&&l),u;if(s.hasOwnProperty(r))u=s[r];else{var h=o(t.props,r,t._instanceProps),S=d(t.props,r,t._instanceProps),I=l==="horizontal"||f==="horizontal",b=l==="rtl",R=I?h:0;s[r]=u={position:"absolute",left:b?void 0:R,right:b?R:void 0,top:I?0:h,height:I?"100%":S,width:I?S:"100%"}}return u},t._getItemStyleCache=void 0,t._getItemStyleCache=W(function(r,i,l){return{}}),t._onScrollHorizontal=function(r){var i=r.currentTarget,l=i.clientWidth,a=i.scrollLeft,f=i.scrollWidth;t.setState(function(s){if(s.scrollOffset===a)return null;var u=t.props.direction,h=a;if(u==="rtl")switch(H()){case"negative":h=-a;break;case"positive-descending":h=f-l-a;break}return h=Math.max(0,Math.min(h,f-l)),{isScrolling:!0,scrollDirection:s.scrollOffset<h?"forward":"backward",scrollOffset:h,scrollUpdateWasRequested:!1}},t._resetIsScrollingDebounced)},t._onScrollVertical=function(r){var i=r.currentTarget,l=i.clientHeight,a=i.scrollHeight,f=i.scrollTop;t.setState(function(s){if(s.scrollOffset===f)return null;var u=Math.max(0,Math.min(f,a-l));return{isScrolling:!0,scrollDirection:s.scrollOffset<u?"forward":"backward",scrollOffset:u,scrollUpdateWasRequested:!1}},t._resetIsScrollingDebounced)},t._outerRefSetter=function(r){var i=t.props.outerRef;t._outerRef=r,typeof i=="function"?i(r):i!=null&&typeof i=="object"&&i.hasOwnProperty("current")&&(i.current=r)},t._resetIsScrollingDebounced=function(){t._resetIsScrollingTimeoutId!==null&&q(t._resetIsScrollingTimeoutId),t._resetIsScrollingTimeoutId=nt(t._resetIsScrolling,ot)},t._resetIsScrolling=function(){t._resetIsScrollingTimeoutId=null,t.setState({isScrolling:!1},function(){t._getItemStyleCache(-1,null)})},t}z.getDerivedStateFromProps=function(t,r){return at(t,r),y(t),null};var p=z.prototype;return p.scrollTo=function(t){t=Math.max(0,t),this.setState(function(r){return r.scrollOffset===t?null:{scrollDirection:r.scrollOffset<t?"forward":"backward",scrollOffset:t,scrollUpdateWasRequested:!0}},this._resetIsScrollingDebounced)},p.scrollToItem=function(t,r){r===void 0&&(r="auto");var i=this.props,l=i.itemCount,a=i.layout,f=this.state.scrollOffset;t=Math.max(0,Math.min(t,l-1));var s=0;if(this._outerRef){var u=this._outerRef;a==="vertical"?s=u.scrollWidth>u.clientWidth?$():0:s=u.scrollHeight>u.clientHeight?$():0}this.scrollTo(O(this.props,t,r,f,this._instanceProps,s))},p.componentDidMount=function(){var t=this.props,r=t.direction,i=t.initialScrollOffset,l=t.layout;if(typeof i=="number"&&this._outerRef!=null){var a=this._outerRef;r==="horizontal"||l==="horizontal"?a.scrollLeft=i:a.scrollTop=i}this._callPropsCallbacks()},p.componentDidUpdate=function(){var t=this.props,r=t.direction,i=t.layout,l=this.state,a=l.scrollOffset,f=l.scrollUpdateWasRequested;if(f&&this._outerRef!=null){var s=this._outerRef;if(r==="horizontal"||i==="horizontal")if(r==="rtl")switch(H()){case"negative":s.scrollLeft=-a;break;case"positive-ascending":s.scrollLeft=a;break;default:var u=s.clientWidth,h=s.scrollWidth;s.scrollLeft=h-u-a;break}else s.scrollLeft=a;else s.scrollTop=a}this._callPropsCallbacks()},p.componentWillUnmount=function(){this._resetIsScrollingTimeoutId!==null&&q(this._resetIsScrollingTimeoutId)},p.render=function(){var t=this.props,r=t.children,i=t.className,l=t.direction,a=t.height,f=t.innerRef,s=t.innerElementType,u=t.innerTagName,h=t.itemCount,S=t.itemData,I=t.itemKey,b=I===void 0?lt:I,R=t.layout,K=t.outerElementType,j=t.outerTagName,V=t.style,B=t.useIsScrolling,G=t.width,E=this.state.isScrolling,M=l==="horizontal"||R==="horizontal",J=M?this._onScrollHorizontal:this._onScrollVertical,L=this._getRangeToRender(),Q=L[0],X=L[1],N=[];if(h>0)for(var x=Q;x<=X;x++)N.push(P.createElement(r,{data:S,key:b(x,S),index:x,isScrolling:B?E:void 0,style:this._getItemStyle(x)}));var k=c(this.props,this._instanceProps);return P.createElement(K||j||"div",{className:i,onScroll:J,ref:this._outerRefSetter,style:Y({position:"relative",height:a,width:G,overflow:"auto",WebkitOverflowScrolling:"touch",willChange:"transform",direction:l},V)},P.createElement(s||u||"div",{children:N,ref:f,style:{height:M?"100%":k,pointerEvents:E?"none":void 0,width:M?k:"100%"}}))},p._callPropsCallbacks=function(){if(typeof this.props.onItemsRendered=="function"){var t=this.props.itemCount;if(t>0){var r=this._getRangeToRender(),i=r[0],l=r[1],a=r[2],f=r[3];this._callOnItemsRendered(i,l,a,f)}}if(typeof this.props.onScroll=="function"){var s=this.state,u=s.scrollDirection,h=s.scrollOffset,S=s.scrollUpdateWasRequested;this._callOnScroll(u,h,S)}},p._getRangeToRender=function(){var t=this.props,r=t.itemCount,i=t.overscanCount,l=this.state,a=l.isScrolling,f=l.scrollDirection,s=l.scrollOffset;if(r===0)return[0,0,0,0];var u=T(this.props,s,this._instanceProps),h=v(this.props,u,s,this._instanceProps),S=!a||f==="backward"?Math.max(1,i):1,I=!a||f==="forward"?Math.max(1,i):1;return[Math.max(0,u-S),Math.max(0,Math.min(r-1,h+I)),u,h]},z}(P.PureComponent),e.defaultProps={direction:"ltr",itemData:void 0,layout:"vertical",overscanCount:2,useIsScrolling:!1},e}var at=function(e,o){e.children,e.direction,e.height,e.layout,e.innerTagName,e.outerTagName,e.width,o.instance},ft=st({getItemOffset:function(e,o){var c=e.itemSize;return o*c},getItemSize:function(e,o){var c=e.itemSize;return c},getEstimatedTotalSize:function(e){var o=e.itemCount,c=e.itemSize;return c*o},getOffsetForIndexAndAlignment:function(e,o,c,d,O,T){var v=e.direction,g=e.height,C=e.itemCount,y=e.itemSize,_=e.layout,z=e.width,p=v==="horizontal"||_==="horizontal",m=p?z:g,t=Math.max(0,C*y-m),r=Math.min(t,o*y),i=Math.max(0,o*y-m+y+T);switch(c==="smart"&&(d>=i-m&&d<=r+m?c="auto":c="center"),c){case"start":return r;case"end":return i;case"center":{var l=Math.round(i+(r-i)/2);return l<Math.ceil(m/2)?0:l>t+Math.floor(m/2)?t:l}case"auto":default:return d>=i&&d<=r?d:d<i?i:r}},getStartIndexForOffset:function(e,o){var c=e.itemCount,d=e.itemSize;return Math.max(0,Math.min(c-1,Math.floor(o/d)))},getStopIndexForStartIndex:function(e,o,c){var d=e.direction,O=e.height,T=e.itemCount,v=e.itemSize,g=e.layout,C=e.width,y=d==="horizontal"||g==="horizontal",_=o*v,z=y?C:O,p=Math.ceil((z+c-_)/v);return Math.max(0,Math.min(T-1,o+p-1))},initInstanceProps:function(e){},shouldResetStyleCacheOnItemSizeChange:!0,validateProps:function(e){e.itemSize}});export{ft as F};
