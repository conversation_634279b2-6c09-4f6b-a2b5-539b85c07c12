import React from "react";

const AgreementTermsSection = () => {
  return (
    <div className="mx-auto max-w-2xl rounded-2xl bg-white p-10 ">
      <h2 className="mb-4 text-2xl font-bold text-[#131E2B]">
        Legal Agreements
      </h2>
      <p className="mb-6 text-base text-[#667484]">
        Please review and agree to our Terms of Service and Privacy Policy to
        continue using CheerEQ.
      </p>
      <div className="mb-6">
        <div className="mb-4 flex items-start">
          <input
            type="checkbox"
            id="terms"
            className="mr-3 mt-1 h-5 w-5 rounded border border-[#D1D6DE] text-[#3C50E0] focus:ring-[#3C50E0]"
          />
          <label
            htmlFor="terms"
            className="cursor-pointer text-base text-[#131E2B]"
          >
            I agree to the{" "}
            <a href="#" className="text-[#3C50E0] underline">
              Terms of Service
            </a>
          </label>
        </div>
        <div className="flex items-start">
          <input
            type="checkbox"
            id="privacy"
            className="mr-3 mt-1 h-5 w-5 rounded border border-[#D1D6DE] text-[#3C50E0] focus:ring-[#3C50E0]"
          />
          <label
            htmlFor="privacy"
            className="cursor-pointer text-base text-[#131E2B]"
          >
            I have read the{" "}
            <a href="#" className="text-[#3C50E0] underline">
              Privacy Policy
            </a>
          </label>
        </div>
      </div>
      <button
        className="w-full rounded-full bg-[#3C50E0] py-3 text-base font-semibold text-white transition hover:bg-[#2B3EB4]"
        type="button"
      >
        Agree & Continue
      </button>
    </div>
  );
};

export default AgreementTermsSection;
