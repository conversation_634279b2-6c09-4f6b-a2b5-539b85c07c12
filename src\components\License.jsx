import React from "react";

export const License = ({
  program,
  team_name,
  mixSeasonName,
  logo,
  member_name,
  company_name,
  id,
}) => {
  function shortenYearRange(yearRange) {
    if (!yearRange) {
      return "mixSeason";
    } else if (!/\d{4}-\d{4}/.test(yearRange)) {
      return yearRange;
    }
    // Split the year range by "-"
    const [startYear, endYear] = yearRange?.split("-");

    // Extract the last two digits of each year
    const shortStartYear = startYear.slice(2);
    const shortEndYear = endYear.slice(2);

    // Concatenate the shortened years
    return shortStartYear + "-" + shortEndYear;
  }

  return (
    <div id="pop" className="absolute left-[-9999px] top-[-9999px]">
      <section
        id={`printable-component-${id}`}
        className="printable-component flex w-full flex-col justify-center bg-white px-[100px] py-[50px] text-xl text-black"
      >
        <div className="flex w-full justify-center">
          {logo ? (
            <img
              crossOrigin="anonymous"
              // crossOrigin='anonymous'
              // absolute left-[-9999px] top-[-9999px]
              className="h-[150px] max-h-[150px] w-[400px] max-w-[400px] object-cover "
              src={logo}
              alt=""
            />
          ) : (
            <h2 className="text-center text-4xl font-semibold ">
              COMPANY LOGO
            </h2>
          )}
        </div>
        <h3 className="mt-10 text-center text-4xl font-semibold ">
          License for use of Music
        </h3>
        <div className="mx-auto my-7 h-[2px] w-[90%] bg-gray-800"></div>
        <h5 className="mb-7 text-center text-3xl font-medium">
          This certifies that
        </h5>
        <span className="mx-auto mb-7 flex w-max  items-center justify-center  pb-[2px]  text-center text-3xl font-semibold underline underline-offset-8">
          {program}
          &nbsp;-&nbsp;
          {team_name}
        </span>
        <p className="mx-auto mb-7 max-w-[550px] text-center text-xl font-medium leading-7">
          is licensed to use their mix compeleted by{" "}
          {company_name ? (
            <b>{company_name}</b>
          ) : (
            <b className="font-bold">Company Name</b>
          )}{" "}
          during the &nbsp;
          <b className="font-bold">{shortenYearRange(mixSeasonName)}</b>{" "}
          Cheerleading Season. All content within the mix is legally licensed
          from Licensed Music Catalogs, from the producer's Personal Catalog,
          and/or Original Content
        </p>

        <div className="mx-auto flex w-max flex-col items-center justify-center">
          <h4 className=" mx-auto flex w-full items-center justify-center text-center font-Script text-[28px] font-bold tracking-wider underline  underline-offset-8">
            {member_name}
          </h4>

          <div className="mt-[2px] w-full text-center text-2xl font-medium">
            {member_name}{" "}
          </div>
        </div>
      </section>
    </div>
  );
};

export default License;
