import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import CustomSelect2 from "Components/CustomSelect2";

const ModifyEngineerModal = ({
  engineers,
  selectedEngineerId,
  setModalClose,
  setFormSubmit,
}) => {
  const [engineerCost, setEngineerCost] = React.useState(0);
  const [localSelectedEngineerId, setLocalSelectedEngineerId] =
    React.useState(selectedEngineerId);

  const handleEngineerChange = (value) => {
    if (value) {
      setLocalSelectedEngineerId(value);
      if (engineers) {
        const selectedEngineer = engineers.filter(
          (engineer) => engineer.id === Number(value)
        );
        setEngineerCost(selectedEngineer[0].engineer_cost);
      }
    } else {
      setLocalSelectedEngineerId(null);
      setEngineerCost(0);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const payload = {
      engineer_id: localSelectedEngineerId,
      engineer_cost: engineerCost,
    };
    setFormSubmit(payload);
    setModalClose(false);
  };

  React.useEffect(() => {
    if (engineers && engineers.length > 0) {
      const selectedEngineer = engineers.filter(
        (engineer) => engineer.id === Number(selectedEngineerId)
      );
      setEngineerCost(selectedEngineer[0].engineer_cost);
    }
  }, [engineers]);

  return (
    <div className="flex fixed inset-0 z-50 justify-center items-center">
      <div
        className="fixed inset-0 backdrop-blur-sm bg-black/50"
        onClick={() => setModalClose(false)}
      />
      <div className="w-full max-w-xl rounded border transition-all transform shadow-default border-strokedark bg-boxdark">
        {/* Modal Header */}
        <div className="flex justify-between items-center px-6 py-4 border-b border-stroke">
          <div className="flex gap-3 items-center">
            <FontAwesomeIcon
              icon="fa-solid fa-user-gear"
              className="text-xl text-primary"
            />
            <h3 className="text-xl font-medium text-white">Update Engineer</h3>
          </div>
          <button
            onClick={() => setModalClose(false)}
            className="hover:text-primary"
          >
            <FontAwesomeIcon icon="fa-solid fa-xmark" className="text-2xl" />
          </button>
        </div>

        <div className="px-6 py-4">
          <form className="space-y-4">
            <div className="flex flex-col">
              <label
                className="mb-2.5 block text-sm font-medium text-white"
                htmlFor="engineer"
              >
                Engineer
              </label>
              <CustomSelect2
                className="w-full rounded border border-stroke bg-boxdark-2 px-4 py-2.5 text-sm text-bodydark2 focus:border-primary focus-visible:outline-none"
                name="engineer"
                id="engineer"
                value={localSelectedEngineerId}
                defaultValue={selectedEngineerId}
                onChange={(value) => {
                  handleEngineerChange(value);
                }}
              >
                <option value="">Select Engineer</option>
                {engineers &&
                  engineers.length > 0 &&
                  engineers?.map((engineer) => (
                    <option key={engineer.id} value={engineer.id}>
                      {engineer.name}
                    </option>
                  ))}
              </CustomSelect2>
            </div>

            <div className="flex flex-col">
              <label
                className="mb-2.5 block text-sm font-medium text-white"
                htmlFor="engineer_cost"
              >
                Cost (default)
              </label>
              <input
                type="number"
                id="engineer_cost"
                className="w-full rounded border border-form-strokedark bg-form-input px-4 py-2.5 text-sm text-bodydark2 focus:border-primary focus-visible:outline-none"
                placeholder="Enter cost"
                value={engineerCost}
                min="0"
                onChange={(e) => setEngineerCost(e.target.value)}
              />
            </div>
          </form>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-stroke">
          <div className="flex gap-2">
            <button
              onClick={(e) => handleSubmit(e)}
              className="flex justify-center items-center px-6 py-2 w-full text-sm font-medium text-white rounded-sm bg-primary hover:bg-opacity-90"
            >
              Save Changes
            </button>
            <button
              onClick={() => setModalClose(false)}
              className="flex justify-center items-center px-6 py-2 w-full text-sm font-medium text-white rounded-sm bg-danger hover:bg-opacity-90"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModifyEngineerModal;
