import React, { useState, useEffect, useCallback } from "react";
import { useUserAndSubscription } from "../../hooks/useUserAndSubscription";
import { getUserDetailsByIdAPI } from "../../services/userService";
import OnboardingProgressTracker from "./OnboardingProgressTracker";
import NewOnboardingModal from "../OnboardingModal/NewOnboardingModal";

const GlobalOnboardingTracker = () => {
  // Use comprehensive hook for user and subscription data - SINGLE API CALL!
  const {
    userDetails: cachedUserDetails,
    subscription,
    isLoading: userDetailsLoading,
    refreshUserDetails,
  } = useUserAndSubscription({
    autoFetch: true,
    cacheTimeout: 5 * 60 * 1000, // 5 minutes cache
  });

  const [userDetails, setUserDetails] = useState(null);
  const [onboardingStepData, setOnboardingStepData] = useState({});
  const [showOnboardingModal, setShowOnboardingModal] = useState(false);
  const [onboardingMinimized, setOnboardingMinimized] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [currentUserId, setCurrentUserId] = useState(null);

  // Function to fetch fresh user details (similar to NewOnboardingModal)
  const loadFreshUserDetails = useCallback(async () => {
    try {
      const userId = localStorage.getItem("user");
      if (userId) {
        console.log(
          "🔄 GlobalOnboardingTracker - Loading fresh user details for user:",
          userId
        );
        const result = await getUserDetailsByIdAPI(userId);
        if (!result.error && result.model) {
          setUserDetails(result.model);
          return result.model;
        } else {
          console.error("Failed to fetch fresh user details:", result.error);
        }
      }
    } catch (error) {
      console.error("Error loading fresh user details:", error);
    }
    return null;
  }, []);

  // Detect user changes and force fresh data fetch
  useEffect(() => {
    const userId = localStorage.getItem("user");

    // If user changed or we don't have user details yet, fetch fresh data
    if (userId && (userId !== currentUserId || !userDetails)) {
      console.log(
        "🔄 GlobalOnboardingTracker - User change detected or no data:",
        {
          newUserId: userId,
          previousUserId: currentUserId,
          hasUserDetails: !!userDetails,
        }
      );

      setCurrentUserId(userId);
      loadFreshUserDetails();
    }
  }, [currentUserId, userDetails, loadFreshUserDetails]);

  // Fallback to cached data if fresh data is not available
  useEffect(() => {
    if (!userDetails && cachedUserDetails) {
      console.log(
        "🔄 GlobalOnboardingTracker - Using cached user details as fallback"
      );
      setUserDetails(cachedUserDetails);
    }
  }, [userDetails, cachedUserDetails]);

  // Check if onboarding is needed using fresh user details
  const checkOnboardingStatus = React.useCallback(() => {
    const needsOnboarding = localStorage.getItem("needs_onboarding");
    const userId = localStorage.getItem("user");

    // Check if user is a sub-member FIRST - skip onboarding entirely for sub-members
    if (userDetails && userDetails.main_user_details) {
      const isSubMember = !userDetails.main_user_details.is_self;

      if (isSubMember) {
        // Sub-members don't need onboarding since main member has already completed it
        console.log(
          "GlobalOnboardingTracker - Skipping onboarding for sub-member"
        );
        localStorage.removeItem("needs_onboarding");
        setIsLoading(false);
        setShowOnboardingModal(false);
        setOnboardingMinimized(false);
        return;
      }
    }

    if (needsOnboarding === "true" && userId && userDetails) {
      setIsLoading(true);

      try {
        // Use cached user details - NO API CALL!

        // Check onboarding status from cached user data
        let onboardingComplete = false;
        let stepData = {};

        if (userDetails.steps) {
          try {
            stepData = JSON.parse(userDetails.steps);
            onboardingComplete = stepData.onboarding_complete === true;
            console.log(
              "🔄 GlobalOnboardingTracker - Using FRESH user details for step data:",
              {
                userId: userDetails.id,
                hasSteps: !!userDetails.steps,
                stepDataKeys: Object.keys(stepData),
              }
            );
          } catch (e) {
            console.error("Error parsing step data:", e);
          }
        } else {
          console.log(
            "🔄 GlobalOnboardingTracker - No steps found in user details:",
            {
              userId: userDetails.id,
              userDetails: userDetails,
            }
          );
        }

        setOnboardingStepData(stepData);

        // Check if user has ANY new onboarding structure fields (same logic as MemberLoginPage)
        const hasAnyNewFields =
          stepData.hasOwnProperty("plan_chosen") ||
          stepData.hasOwnProperty("legal_agreements_complete") ||
          stepData.hasOwnProperty("business_profile_complete") ||
          stepData.hasOwnProperty("subscription_complete") ||
          stepData.hasOwnProperty("feature_tour_complete") ||
          stepData.hasOwnProperty("business_identity_complete") ||
          stepData.hasOwnProperty("mandatory_completion_screen_viewed");

        // Check if user has COMPLETE new step structure
        const hasCompleteNewStepStructure =
          stepData.hasOwnProperty("legal_agreements_complete") &&
          stepData.hasOwnProperty("business_profile_complete") &&
          stepData.hasOwnProperty("subscription_complete") &&
          stepData.hasOwnProperty("feature_tour_complete") &&
          stepData.hasOwnProperty("business_identity_complete") &&
          stepData.hasOwnProperty("mandatory_completion_screen_viewed");

        // Check if all mandatory steps are complete
        const allMandatoryComplete =
          stepData.legal_agreements_complete &&
          stepData.business_profile_complete &&
          stepData.plan_chosen &&
          stepData.feature_tour_complete &&
          stepData.business_identity_complete;
        const completionScreenViewed =
          stepData.mandatory_completion_screen_viewed === true;

        console.log("GlobalOnboardingTracker - Onboarding check:", {
          hasAnyNewFields,
          hasCompleteNewStepStructure,
          onboardingComplete,
          allMandatoryComplete,
          completionScreenViewed,
          stepData,
        });

        // Check if ALL mandatory steps are complete AND completion screen viewed
        if (allMandatoryComplete && completionScreenViewed) {
          // Mandatory onboarding is complete, check if optional steps are available
          const optionalStepsKeys = [
            "payment_billing_complete",
            "client_service_agreement_complete",
            "edit_policy_complete",
            "project_management_complete",
            "project_timelines_complete",
            "contact_details_complete",
          ];

          const hasIncompleteOptionalSteps = optionalStepsKeys.some(
            (key) => !stepData[key]
          );

          console.log("GlobalOnboardingTracker - Optional steps check:", {
            optionalStepsKeys,
            hasIncompleteOptionalSteps,
            optionalStepsStatus: optionalStepsKeys.map((key) => ({
              [key]: stepData[key],
            })),
          });

          if (hasIncompleteOptionalSteps) {
            // Show progress tracker for optional steps ONLY
            console.log(
              "GlobalOnboardingTracker - Showing minimized tracker for optional steps"
            );
            setOnboardingMinimized(true);
            setShowOnboardingModal(false);
          } else {
            // All optional steps complete - hide tracker completely
            console.log(
              "GlobalOnboardingTracker - All steps complete, hiding tracker"
            );
            setOnboardingMinimized(false);
            setShowOnboardingModal(false);
            localStorage.removeItem("needs_onboarding");
          }
        } else {
          // Check if user needs onboarding (mandatory steps not complete)
          const needsOnboardingCheck =
            !userDetails.steps ||
            (hasAnyNewFields && !hasCompleteNewStepStructure) || // Partial new structure - force complete onboarding
            !hasAnyNewFields || // Old structure only - needs new onboarding
            !onboardingComplete ||
            !allMandatoryComplete ||
            (allMandatoryComplete && !completionScreenViewed);

          console.log(
            "GlobalOnboardingTracker - Mandatory onboarding needed:",
            {
              needsOnboarding: needsOnboardingCheck,
              reason: {
                noSteps: !userDetails.steps,
                partialNewStructure:
                  hasAnyNewFields && !hasCompleteNewStepStructure,
                oldStructureOnly: !hasAnyNewFields,
                notComplete: !onboardingComplete,
                mandatoryIncomplete: !allMandatoryComplete,
                completionScreenNotViewed:
                  allMandatoryComplete && !completionScreenViewed,
              },
            }
          );

          if (needsOnboardingCheck) {
            // Show appropriate onboarding UI based on progress
            if (!allMandatoryComplete) {
              console.log(
                "GlobalOnboardingTracker - Showing full onboarding modal"
              );
              setShowOnboardingModal(true);
              setOnboardingMinimized(false);
            } else if (allMandatoryComplete && !completionScreenViewed) {
              console.log(
                "GlobalOnboardingTracker - Showing minimized tracker for completion screen"
              );
              setOnboardingMinimized(true);
              setShowOnboardingModal(false);
            }
          } else {
            // Onboarding is actually complete, clean up flag
            console.log(
              "GlobalOnboardingTracker - Onboarding complete, cleaning up"
            );
            localStorage.removeItem("needs_onboarding");
            setShowOnboardingModal(false);
            setOnboardingMinimized(false);
          }
        }
      } catch (error) {
        console.error("Error checking onboarding status:", error);
      } finally {
        setIsLoading(false);
      }
    }
  }, [userDetails]);

  // Handle onboarding completion
  const handleOnboardingComplete = () => {
    setShowOnboardingModal(false);
    setOnboardingMinimized(false);
    localStorage.removeItem("needs_onboarding");
    // Reload the page to refresh the state
    window.location.reload();
  };

  // Handle onboarding minimize
  const handleOnboardingMinimize = () => {
    setShowOnboardingModal(false);
    setOnboardingMinimized(true);
  };

  // Handle onboarding reopen
  const handleOnboardingReopen = () => {
    setOnboardingMinimized(false);
    setShowOnboardingModal(true);
  };

  // Handle step data updates from NewOnboardingModal
  const handleStepDataUpdate = useCallback(
    async (updatedStepData) => {
      console.log(
        "🎯 GlobalOnboardingTracker - Received step data update:",
        updatedStepData
      );
      console.log(
        "🎯 GlobalOnboardingTracker - Previous step data:",
        onboardingStepData
      );
      setOnboardingStepData(updatedStepData);
      console.log("🎯 GlobalOnboardingTracker - Step data state updated");

      // Also refresh user details to ensure cache consistency
      try {
        const freshUserDetails = await loadFreshUserDetails();
        if (freshUserDetails) {
          console.log(
            "🎯 GlobalOnboardingTracker - Refreshed user details after step update"
          );
        }
      } catch (error) {
        console.error(
          "Error refreshing user details after step update:",
          error
        );
      }

      // Check if we need to update the UI state based on new step data
      const allMandatoryComplete =
        updatedStepData.legal_agreements_complete &&
        updatedStepData.business_profile_complete &&
        updatedStepData.plan_chosen &&
        updatedStepData.feature_tour_complete &&
        updatedStepData.business_identity_complete;

      const completionScreenViewed =
        updatedStepData.mandatory_completion_screen_viewed === true;

      // If all mandatory steps are complete and completion screen viewed,
      // check if we should show minimized tracker for optional steps
      if (allMandatoryComplete && completionScreenViewed) {
        const optionalStepsKeys = [
          "payment_billing_complete",
          "client_service_agreement_complete",
          "edit_policy_complete",
          "project_management_complete",
          "project_timelines_complete",
          "contact_details_complete",
        ];

        const hasIncompleteOptionalSteps = optionalStepsKeys.some(
          (key) => !updatedStepData[key]
        );

        if (hasIncompleteOptionalSteps) {
          // Show minimized tracker for optional steps
          setOnboardingMinimized(false);
          setShowOnboardingModal(true);
        } else {
          // All steps complete - hide everything
          setOnboardingMinimized(false);
          setShowOnboardingModal(false);
          localStorage.removeItem("needs_onboarding");
        }
      }
    },
    [onboardingStepData, loadFreshUserDetails]
  );

  // Check onboarding status when user details are available
  useEffect(() => {
    if (userDetails && !userDetailsLoading) {
      console.log("GlobalOnboardingTracker - User details received:", {
        userDetails,
        hasMainUserDetails: !!userDetails.main_user_details,
        steps: userDetails.steps,
        isMainMember: userDetails.main_user_details?.is_self,
      });

      // Only call checkOnboardingStatus if user is NOT currently in full modal
      // This prevents overriding the current state when user is actively working on optional steps
      if (!showOnboardingModal) {
        checkOnboardingStatus();
      } else {
        console.log(
          "GlobalOnboardingTracker - Skipping checkOnboardingStatus because user is in full modal"
        );
      }
    }
  }, [
    userDetails,
    userDetailsLoading,
    checkOnboardingStatus,
    showOnboardingModal,
  ]);

  // Don't render anything if loading or no onboarding needed
  if (isLoading || userDetailsLoading) return null;

  return (
    <>
      {/* Onboarding Modal */}
      {showOnboardingModal && userDetails && (
        <NewOnboardingModal
          userDetails={userDetails}
          onComplete={handleOnboardingComplete}
          onMinimize={handleOnboardingMinimize}
          onStepDataUpdate={handleStepDataUpdate}
        />
      )}

      {/* Onboarding Progress Tracker */}
      {onboardingMinimized && (
        <OnboardingProgressTracker
          stepData={onboardingStepData}
          onReopen={handleOnboardingReopen}
        />
      )}
    </>
  );
};

export default GlobalOnboardingTracker;
