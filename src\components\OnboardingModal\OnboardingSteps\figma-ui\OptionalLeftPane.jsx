import React from "react";

const OptionalLeftPane = () => {
  return (
    <div className="flex w-full max-w-[424px] flex-col items-center gap-8 rounded-l-[16px] bg-[#F5F7FF] px-10 py-16 shadow-none">
      <div className="flex w-full flex-col items-center gap-2">
        <h2 className="font-inter text-center text-[18px] font-semibold leading-[28px] text-[#131E2B]">
          Empower Your Business Operations
        </h2>
        <p className="font-inter text-center text-[14px] font-normal leading-[20px] text-[#667484]">
          Small steps, monumental impact.
        </p>
      </div>
      <div className="mt-4 flex w-full flex-col gap-4">
        {/* Checklist items - pixel-perfect, Figma-extracted text */}
        <div className="mb-1 flex items-center justify-between rounded-[12px] border border-[#3C50E0] bg-[#F5F7FF] px-4 py-3">
          <div className="flex flex-col gap-1">
            <span className="font-inter text-[14px] font-medium text-[#1F2E8A]">
              Secure Your Financials
            </span>
            <span className="font-inter text-[14px] font-normal text-[#3C50E0]">
              Integrate Stripe for direct payments and streamlined billing.
            </span>
          </div>
          <input
            type="checkbox"
            className="h-4 w-4 rounded border border-[#3C50E0]"
            disabled
          />
        </div>
        <div className="mb-1 flex items-center justify-between rounded-[12px] border border-[#D1D6DE] bg-[#F5F7FF] px-4 py-3">
          <div className="flex flex-col gap-1">
            <span className="font-inter text-[14px] font-medium text-[#1F2E8A]">
              Personalize Your Brand
            </span>
            <span className="font-inter text-[14px] font-normal text-[#3C50E0]">
              Upload your company and license logos for a professional touch.
            </span>
          </div>
          <input
            type="checkbox"
            className="h-4 w-4 rounded border border-[#D1D6DE]"
            disabled
          />
        </div>
        <div className="mb-1 flex items-center justify-between rounded-[12px] border border-[#D1D6DE] bg-white px-4 py-3">
          <div className="flex flex-col gap-1">
            <span className="font-inter text-[14px] font-medium text-[#1F2E8A]">
              Invite Your Team
            </span>
            <span className="font-inter text-[14px] font-normal text-[#3C50E0]">
              Add team members to collaborate and assign roles.
            </span>
          </div>
          <input
            type="checkbox"
            className="h-4 w-4 rounded border border-[#D1D6DE]"
            disabled
          />
        </div>
        <div className="mb-1 flex items-center justify-between rounded-[12px] border border-[#D1D6DE] bg-white px-4 py-3">
          <div className="flex flex-col gap-1">
            <span className="font-inter text-[14px] font-medium text-[#1F2E8A]">
              Set Up Your First Project
            </span>
            <span className="font-inter text-[14px] font-normal text-[#3C50E0]">
              Kick off your first cheer music project in minutes.
            </span>
          </div>
          <input
            type="checkbox"
            className="h-4 w-4 rounded border border-[#D1D6DE]"
            disabled
          />
        </div>
        <div className="mb-1 flex items-center justify-between rounded-[12px] border border-[#D1D6DE] bg-white px-4 py-3">
          <div className="flex flex-col gap-1">
            <span className="font-inter text-[14px] font-medium text-[#1F2E8A]">
              Automate Client Communication
            </span>
            <span className="font-inter text-[14px] font-normal text-[#3C50E0]">
              Enable reminders, surveys, and client portal access.
            </span>
          </div>
          <input
            type="checkbox"
            className="h-4 w-4 rounded border border-[#D1D6DE]"
            disabled
          />
        </div>
        <div className="flex items-center justify-between rounded-[12px] border border-[#D1D6DE] bg-white px-4 py-3">
          <div className="flex flex-col gap-1">
            <span className="font-inter text-[14px] font-medium text-[#1F2E8A]">
              Streamline Work Orders
            </span>
            <span className="font-inter text-[14px] font-normal text-[#3C50E0]">
              Assign, track, and auto-approve tasks for efficiency.
            </span>
          </div>
          <input
            type="checkbox"
            className="h-4 w-4 rounded border border-[#D1D6DE]"
            disabled
          />
        </div>
      </div>
      <p className="font-inter mt-8 text-center text-[12px] font-normal leading-[18px] text-[#667484]">
        Your progress is saved, so feel free to return to this optional setup
        via the 'Onboarding Checklist' widget on your dashboard.
      </p>
    </div>
  );
};

export default OptionalLeftPane;
