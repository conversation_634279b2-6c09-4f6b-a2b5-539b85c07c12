import{d as vt,g as jr}from"../vendor-94843817.js";var Et,ur;function sr(){if(ur)return Et;ur=1;function r(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}return Et=r,Et}var Ot,lr;function si(){if(lr)return Ot;lr=1;var r=typeof vt=="object"&&vt&&vt.Object===Object&&vt;return Ot=r,Ot}var Ut,cr;function Ar(){if(cr)return Ut;cr=1;var r=si(),e=typeof self=="object"&&self&&self.Object===Object&&self,t=r||e||Function("return this")();return Ut=t,Ut}var Rt,dr;function ni(){if(dr)return Rt;dr=1;var r=Ar(),e=function(){return r.Date.now()};return Rt=e,Rt}var $t,pr;function oi(){if(pr)return $t;pr=1;var r=/\s/;function e(t){for(var i=t.length;i--&&r.test(t.charAt(i)););return i}return $t=e,$t}var kt,hr;function ai(){if(hr)return kt;hr=1;var r=oi(),e=/^\s+/;function t(i){return i&&i.slice(0,r(i)+1).replace(e,"")}return kt=t,kt}var Ct,fr;function Mr(){if(fr)return Ct;fr=1;var r=Ar(),e=r.Symbol;return Ct=e,Ct}var Ft,mr;function ui(){if(mr)return Ft;mr=1;var r=Mr(),e=Object.prototype,t=e.hasOwnProperty,i=e.toString,s=r?r.toStringTag:void 0;function n(o){var a=t.call(o,s),u=o[s];try{o[s]=void 0;var l=!0}catch{}var c=i.call(o);return l&&(a?o[s]=u:delete o[s]),c}return Ft=n,Ft}var jt,yr;function li(){if(yr)return jt;yr=1;var r=Object.prototype,e=r.toString;function t(i){return e.call(i)}return jt=t,jt}var At,vr;function ci(){if(vr)return At;vr=1;var r=Mr(),e=ui(),t=li(),i="[object Null]",s="[object Undefined]",n=r?r.toStringTag:void 0;function o(a){return a==null?a===void 0?s:i:n&&n in Object(a)?e(a):t(a)}return At=o,At}var Mt,gr;function di(){if(gr)return Mt;gr=1;function r(e){return e!=null&&typeof e=="object"}return Mt=r,Mt}var Lt,br;function pi(){if(br)return Lt;br=1;var r=ci(),e=di(),t="[object Symbol]";function i(s){return typeof s=="symbol"||e(s)&&r(s)==t}return Lt=i,Lt}var xt,wr;function hi(){if(wr)return xt;wr=1;var r=ai(),e=sr(),t=pi(),i=0/0,s=/^[-+]0x[0-9a-f]+$/i,n=/^0b[01]+$/i,o=/^0o[0-7]+$/i,a=parseInt;function u(l){if(typeof l=="number")return l;if(t(l))return i;if(e(l)){var c=typeof l.valueOf=="function"?l.valueOf():l;l=e(c)?c+"":c}if(typeof l!="string")return l===0?l:+l;l=r(l);var d=n.test(l);return d||o.test(l)?a(l.slice(2),d?2:8):s.test(l)?i:+l}return xt=u,xt}var qt,Pr;function fi(){if(Pr)return qt;Pr=1;var r=sr(),e=ni(),t=hi(),i="Expected a function",s=Math.max,n=Math.min;function o(a,u,l){var c,d,f,p,m,g,w=0,P=!1,k=!1,D=!0;if(typeof a!="function")throw new TypeError(i);u=t(u)||0,r(l)&&(P=!!l.leading,k="maxWait"in l,f=k?s(t(l.maxWait)||0,u):f,D="trailing"in l?!!l.trailing:D);function O(S){var K=c,ye=d;return c=d=void 0,w=S,p=a.apply(ye,K),p}function se(S){return w=S,m=setTimeout(_,u),P?O(S):p}function fe(S){var K=S-g,ye=S-w,Ie=u-K;return k?n(Ie,f-ye):Ie}function T(S){var K=S-g,ye=S-w;return g===void 0||K>=u||K<0||k&&ye>=f}function _(){var S=e();if(T(S))return z(S);m=setTimeout(_,fe(S))}function z(S){return m=void 0,D&&c?O(S):(c=d=void 0,p)}function me(){m!==void 0&&clearTimeout(m),w=0,c=g=d=m=void 0}function Tt(){return m===void 0?p:z(e())}function qe(){var S=e(),K=T(S);if(c=arguments,d=this,g=S,K){if(m===void 0)return se(g);if(k)return clearTimeout(m),m=setTimeout(_,u),O(g)}return m===void 0&&(m=setTimeout(_,u)),p}return qe.cancel=me,qe.flush=Tt,qe}return qt=o,qt}var It,_r;function mi(){if(_r)return It;_r=1;var r=fi(),e=sr(),t="Expected a function";function i(s,n,o){var a=!0,u=!0;if(typeof s!="function")throw new TypeError(t);return e(o)&&(a="leading"in o?!!o.leading:a,u="trailing"in o?!!o.trailing:u),r(s,n,{leading:a,maxWait:n,trailing:u})}return It=i,It}function gt(r,e){if(!Object.prototype.hasOwnProperty.call(r,e))throw new TypeError("attempted to use private field on non-instance");return r}var yi=0;function Lr(r){return"__private_"+yi+++"_"+r}function vi(r,e,t){const i=[];return r.forEach(s=>typeof s!="string"?i.push(s):e[Symbol.split](s).forEach((n,o,a)=>{n!==""&&i.push(n),o<a.length-1&&i.push(t)})),i}/**
 * Takes a string with placeholder variables like `%{smart_count} file selected`
 * and replaces it with values from options `{smart_count: 5}`
 *
 * @license https://github.com/airbnb/polyglot.js/blob/master/LICENSE
 * taken from https://github.com/airbnb/polyglot.js/blob/master/lib/polyglot.js#L299
 *
 * @param phrase that needs interpolation, with placeholders
 * @param options with values that will be used to replace placeholders
 */function Tr(r,e){const t=/\$/g,i="$$$$";let s=[r];if(e==null)return s;for(const n of Object.keys(e))if(n!=="_"){let o=e[n];typeof o=="string"&&(o=t[Symbol.replace](o,i)),s=vi(s,new RegExp(`%\\{${n}\\}`,"g"),o)}return s}const gi=r=>{throw new Error(`missing string: ${r}`)};var He=Lr("onMissingKey"),Ne=Lr("apply");class bi{constructor(e,t){let{onMissingKey:i=gi}=t===void 0?{}:t;Object.defineProperty(this,Ne,{value:wi}),Object.defineProperty(this,He,{writable:!0,value:void 0}),this.locale={strings:{},pluralize(s){return s===1?0:1}},Array.isArray(e)?e.forEach(gt(this,Ne)[Ne],this):gt(this,Ne)[Ne](e),gt(this,He)[He]=i}translate(e,t){return this.translateArray(e,t).join("")}translateArray(e,t){let i=this.locale.strings[e];if(i==null&&(gt(this,He)[He](e),i=e),typeof i=="object"){if(t&&typeof t.smart_count<"u"){const n=this.locale.pluralize(t.smart_count);return Tr(i[n],t)}throw new Error("Attempted to use a string with plural forms, but no value was given for %{smart_count}")}if(typeof i!="string")throw new Error("string was not a string");return Tr(i,t)}}function wi(r){if(!(r!=null&&r.strings))return;const e=this.locale;Object.assign(this.locale,{strings:{...e.strings,...r.strings},pluralize:r.pluralize||e.pluralize})}let Pi="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict",_i=(r=21)=>{let e="",t=r;for(;t--;)e+=Pi[Math.random()*64|0];return e};var Ti=mi();const Si=jr(Ti);class xr{constructor(e,t){this.uppy=e,this.opts=t??{}}getPluginState(){const{plugins:e}=this.uppy.getState();return(e==null?void 0:e[this.id])||{}}setPluginState(e){const{plugins:t}=this.uppy.getState();this.uppy.setState({plugins:{...t,[this.id]:{...t[this.id],...e}}})}setOptions(e){this.opts={...this.opts,...e},this.setPluginState(void 0),this.i18nInit()}i18nInit(){const e=new bi([this.defaultLocale,this.uppy.locale,this.opts.locale]);this.i18n=e.translate.bind(e),this.i18nArray=e.translateArray.bind(e),this.setPluginState(void 0)}addTarget(e){throw new Error("Extend the addTarget method to add your plugin to another plugin's target")}install(){}uninstall(){}update(e){}afterUpdate(){}}class Ei extends Error{constructor(){super(...arguments),this.name="UserFacingApiError"}}var qr={};function Z(r,e){typeof e=="boolean"&&(e={forever:e}),this._originalTimeouts=JSON.parse(JSON.stringify(r)),this._timeouts=r,this._options=e||{},this._maxRetryTime=e&&e.maxRetryTime||1/0,this._fn=null,this._errors=[],this._attempts=1,this._operationTimeout=null,this._operationTimeoutCb=null,this._timeout=null,this._operationStart=null,this._timer=null,this._options.forever&&(this._cachedTimeouts=this._timeouts.slice(0))}var Oi=Z;Z.prototype.reset=function(){this._attempts=1,this._timeouts=this._originalTimeouts.slice(0)};Z.prototype.stop=function(){this._timeout&&clearTimeout(this._timeout),this._timer&&clearTimeout(this._timer),this._timeouts=[],this._cachedTimeouts=null};Z.prototype.retry=function(r){if(this._timeout&&clearTimeout(this._timeout),!r)return!1;var e=new Date().getTime();if(r&&e-this._operationStart>=this._maxRetryTime)return this._errors.push(r),this._errors.unshift(new Error("RetryOperation timeout occurred")),!1;this._errors.push(r);var t=this._timeouts.shift();if(t===void 0)if(this._cachedTimeouts)this._errors.splice(0,this._errors.length-1),t=this._cachedTimeouts.slice(-1);else return!1;var i=this;return this._timer=setTimeout(function(){i._attempts++,i._operationTimeoutCb&&(i._timeout=setTimeout(function(){i._operationTimeoutCb(i._attempts)},i._operationTimeout),i._options.unref&&i._timeout.unref()),i._fn(i._attempts)},t),this._options.unref&&this._timer.unref(),!0};Z.prototype.attempt=function(r,e){this._fn=r,e&&(e.timeout&&(this._operationTimeout=e.timeout),e.cb&&(this._operationTimeoutCb=e.cb));var t=this;this._operationTimeoutCb&&(this._timeout=setTimeout(function(){t._operationTimeoutCb()},t._operationTimeout)),this._operationStart=new Date().getTime(),this._fn(this._attempts)};Z.prototype.try=function(r){console.log("Using RetryOperation.try() is deprecated"),this.attempt(r)};Z.prototype.start=function(r){console.log("Using RetryOperation.start() is deprecated"),this.attempt(r)};Z.prototype.start=Z.prototype.try;Z.prototype.errors=function(){return this._errors};Z.prototype.attempts=function(){return this._attempts};Z.prototype.mainError=function(){if(this._errors.length===0)return null;for(var r={},e=null,t=0,i=0;i<this._errors.length;i++){var s=this._errors[i],n=s.message,o=(r[n]||0)+1;r[n]=o,o>=t&&(e=s,t=o)}return e};(function(r){var e=Oi;r.operation=function(t){var i=r.timeouts(t);return new e(i,{forever:t&&(t.forever||t.retries===1/0),unref:t&&t.unref,maxRetryTime:t&&t.maxRetryTime})},r.timeouts=function(t){if(t instanceof Array)return[].concat(t);var i={retries:10,factor:2,minTimeout:1*1e3,maxTimeout:1/0,randomize:!1};for(var s in t)i[s]=t[s];if(i.minTimeout>i.maxTimeout)throw new Error("minTimeout is greater than maxTimeout");for(var n=[],o=0;o<i.retries;o++)n.push(this.createTimeout(o,i));return t&&t.forever&&!n.length&&n.push(this.createTimeout(o,i)),n.sort(function(a,u){return a-u}),n},r.createTimeout=function(t,i){var s=i.randomize?Math.random()+1:1,n=Math.round(s*Math.max(i.minTimeout,1)*Math.pow(i.factor,t));return n=Math.min(n,i.maxTimeout),n},r.wrap=function(t,i,s){if(i instanceof Array&&(s=i,i=null),!s){s=[];for(var n in t)typeof t[n]=="function"&&s.push(n)}for(var o=0;o<s.length;o++){var a=s[o],u=t[a];t[a]=(function(c){var d=r.operation(i),f=Array.prototype.slice.call(arguments,1),p=f.pop();f.push(function(m){d.retry(m)||(m&&(arguments[0]=d.mainError()),p.apply(this,arguments))}),d.attempt(function(){c.apply(t,f)})}).bind(t,u),t[a].options=i}}})(qr);var Ui=qr;const Ri=jr(Ui),$i=Object.prototype.toString,ki=r=>$i.call(r)==="[object Error]",Ci=new Set(["network error","Failed to fetch","NetworkError when attempting to fetch resource.","The Internet connection appears to be offline.","Load failed","Network request failed","fetch failed","terminated"]);function Fi(r){return r&&ki(r)&&r.name==="TypeError"&&typeof r.message=="string"?r.message==="Load failed"?r.stack===void 0:Ci.has(r.message):!1}class tr extends Error{constructor(e){super(),e instanceof Error?(this.originalError=e,{message:e}=e):(this.originalError=new Error(e),this.originalError.stack=this.stack),this.name="AbortError",this.message=e}}const Sr=(r,e,t)=>{const i=t.retries-(e-1);return r.attemptNumber=e,r.retriesLeft=i,r};async function Ir(r,e){return new Promise((t,i)=>{e={...e},e.onFailedAttempt??(e.onFailedAttempt=()=>{}),e.shouldRetry??(e.shouldRetry=()=>!0),e.retries??(e.retries=10);const s=Ri.operation(e),n=()=>{var a;s.stop(),i((a=e.signal)==null?void 0:a.reason)};e.signal&&!e.signal.aborted&&e.signal.addEventListener("abort",n,{once:!0});const o=()=>{var a;(a=e.signal)==null||a.removeEventListener("abort",n),s.stop()};s.attempt(async a=>{try{const u=await r(a);o(),t(u)}catch(u){try{if(!(u instanceof Error))throw new TypeError(`Non-error was thrown: "${u}". You should only throw errors.`);if(u instanceof tr)throw u.originalError;if(u instanceof TypeError&&!Fi(u))throw u;if(Sr(u,a,e),await e.shouldRetry(u)||(s.stop(),i(u)),await e.onFailedAttempt(u),!s.retry(u))throw s.mainError()}catch(l){Sr(l,a,e),o(),i(l)}}})})}class Hr extends Error{constructor(e,t){t===void 0&&(t=null),super("This looks like a network error, the endpoint might be blocked by an internet provider or a firewall."),this.cause=e,this.isNetworkError=!0,this.request=t}}function ji(){return fetch(...arguments).catch(r=>{throw r.name==="AbortError"?r:new Hr(r)})}function Nr(r,e){return Object.prototype.hasOwnProperty.call(r,e)}class zr extends Error{constructor(e,t){super(e),this.cause=t==null?void 0:t.cause,this.cause&&Nr(this.cause,"isNetworkError")?this.isNetworkError=this.cause.isNetworkError:this.isNetworkError=!1}}function Ai(r,e,t){const{progress:i,bytesUploaded:s,bytesTotal:n}=e;i&&(r.uppy.log(`Upload progress: ${i}`),r.uppy.emit("upload-progress",t,{uploader:r,bytesUploaded:s,bytesTotal:n}))}const Mi=Si(Ai,300,{leading:!0,trailing:!0});function Li(r){var e;const i=(e=/^(?:https?:\/\/|\/\/)?(?:[^@\n]+@)?(?:www\.)?([^\n]+)/i.exec(r))==null?void 0:e[1];return`${/^http:\/\//i.test(r)?"ws":"wss"}://${i}`}class xi extends Error{constructor(){super("Authorization required"),this.name="AuthError",this.isAuthError=!0}}let Kr;function ne(r,e){if(!Object.prototype.hasOwnProperty.call(r,e))throw new TypeError("attempted to use private field on non-instance");return r}var qi=0;function wt(r){return"__private_"+qi+++"_"+r}const Ii={version:"3.8.2"};function Hi(r){return r.replace(/\/$/,"")}const Br=10,Ni=5*60*1e3,Dr=401;class zi extends Error{constructor(e){let{statusCode:t,message:i}=e;super(i),this.name="HttpError",this.statusCode=t}}async function Ki(r){if(r.status===Dr)throw new xi;if(r.ok)return r.json();let e=`Failed request with status: ${r.status}. ${r.statusText}`,t;try{t=await r.json(),t.message&&(e=`${e} message: ${t.message}`),t.requestId&&(e=`${e} request-Id: ${t.requestId}`)}catch(i){throw new Error(e,{cause:i})}throw r.status>=400&&r.status<=499&&t.message?new Ei(t.message):new zi({statusCode:r.status,message:e})}var oe=wt("companionHeaders"),ze=wt("getUrl"),Ht=wt("requestSocketToken"),Ke=wt("awaitRemoteFileUpload");Kr=Symbol.for("uppy test: getCompanionHeaders");class ft{constructor(e,t){Object.defineProperty(this,Ke,{value:Di}),Object.defineProperty(this,ze,{value:Bi}),Object.defineProperty(this,oe,{writable:!0,value:void 0}),Object.defineProperty(this,Ht,{writable:!0,value:async i=>{var s;let{file:n,postBody:o,signal:a}=i;if(((s=n.remote)==null?void 0:s.url)==null)throw new Error("Cannot connect to an undefined URL");return(await this.post(n.remote.url,{...n.remote.body,...o},{signal:a})).token}}),this.uppy=e,this.opts=t,this.onReceiveResponse=this.onReceiveResponse.bind(this),ne(this,oe)[oe]=t==null?void 0:t.companionHeaders}setCompanionHeaders(e){ne(this,oe)[oe]=e}[Kr](){return ne(this,oe)[oe]}get hostname(){const{companion:e}=this.uppy.getState(),t=this.opts.companionUrl;return Hi(e&&e[t]?e[t]:t)}async headers(e){return e===void 0&&(e=!1),{...{Accept:"application/json",...e?void 0:{"Content-Type":"application/json"}},...ne(this,oe)[oe]}}onReceiveResponse(e){const{headers:t}=e,s=this.uppy.getState().companion||{},n=this.opts.companionUrl;t.has("i-am")&&t.get("i-am")!==s[n]&&this.uppy.setState({companion:{...s,[n]:t.get("i-am")}})}async request(e){let{path:t,method:i="GET",data:s,skipPostResponse:n,signal:o}=e;try{const a=await this.headers(!s),u=await ji(ne(this,ze)[ze](t),{method:i,signal:o,headers:a,credentials:this.opts.companionCookiesRule||"same-origin",body:s?JSON.stringify(s):null});return n||this.onReceiveResponse(u),await Ki(u)}catch(a){throw a.isAuthError||a.name==="UserFacingApiError"||a.name==="AbortError"?a:new zr(`Could not ${i} ${ne(this,ze)[ze](t)}`,{cause:a})}}async get(e,t){return typeof t=="boolean"&&(t={skipPostResponse:t}),this.request({...t,path:e})}async post(e,t,i){return typeof i=="boolean"&&(i={skipPostResponse:i}),this.request({...i,path:e,method:"POST",data:t})}async delete(e,t,i){return typeof i=="boolean"&&(i={skipPostResponse:i}),this.request({...i,path:e,method:"DELETE",data:t})}async uploadRemoteFile(e,t,i){var s=this;try{const{signal:n,getQueue:o}=i||{};return await Ir(async()=>{var a;const u=(a=this.uppy.getFile(e.id))==null?void 0:a.serverToken;if(u!=null)return this.uppy.log(`Connecting to exiting websocket ${u}`),ne(this,Ke)[Ke]({file:e,queue:o(),signal:n});const c=await o().wrapPromiseFunction(async function(){try{return await ne(s,Ht)[Ht](...arguments)}catch(d){if(d.isAuthError)throw new tr(d);if(d.cause==null)throw d;const f=d.cause,p=()=>[408,409,429,418,423].includes(f.statusCode)||f.statusCode>=500&&f.statusCode<=599&&![501,505].includes(f.statusCode);throw f.name==="HttpError"&&!p()?new tr(f):f}},{priority:-1})({file:e,postBody:t,signal:n}).abortOn(n);if(this.uppy.getFile(e.id))return this.uppy.setFileState(e.id,{serverToken:c}),ne(this,Ke)[Ke]({file:this.uppy.getFile(e.id),queue:o(),signal:n})},{retries:Br,signal:n,onFailedAttempt:a=>this.uppy.log(`Retrying upload due to: ${a.message}`,"warning")})}catch(n){if(n.name==="AbortError")return;throw this.uppy.emit("upload-error",e,n),n}}}function Bi(r){return/^(https?:|)\/\//.test(r)?r:`${this.hostname}/${r}`}async function Di(r){let{file:e,queue:t,signal:i}=r,s;const{capabilities:n}=this.uppy.getState();try{return await new Promise((o,a)=>{const u=e.serverToken,l=Li(e.remote.companionUrl);let c,d,f,{isPaused:p}=e;const m=(T,_)=>{if(c==null||c.readyState!==c.OPEN){var z;this.uppy.log(`Cannot send "${T}" to socket ${e.id} because the socket state was ${String((z=c)==null?void 0:z.readyState)}`,"warning");return}c.send(JSON.stringify({action:T,payload:_??{}}))};function g(){n.resumableUploads&&m(p?"pause":"resume")}const w=async()=>{d&&d.abort(),d=new AbortController;const T=z=>{var me;this.uppy.setFileState(e.id,{serverToken:null}),(me=d)==null||me.abort==null||me.abort(),a(z)};function _(){clearTimeout(f),!p&&(f=setTimeout(()=>T(new Error("Timeout waiting for message from Companion socket")),Ni))}try{await t.wrapPromiseFunction(async()=>{await Ir(async()=>new Promise((me,Tt)=>{c=new WebSocket(`${l}/api/${u}`),_(),c.addEventListener("close",()=>{c=void 0,Tt(new Error("Socket closed unexpectedly"))}),c.addEventListener("error",S=>{var K;this.uppy.log(`Companion socket error ${JSON.stringify(S)}, closing socket`,"warning"),(K=c)==null||K.close()}),c.addEventListener("open",()=>{g()}),c.addEventListener("message",S=>{_();try{const{action:mt,payload:Me}=JSON.parse(S.data);switch(mt){case"progress":{Mi(this,Me,this.uppy.getFile(e.id));break}case"success":{var K,ye,Ie,St;const yt=(K=Me.response)==null?void 0:K.responseText;this.uppy.emit("upload-success",this.uppy.getFile(e.id),{uploadURL:Me.url,status:(ye=(Ie=Me.response)==null?void 0:Ie.status)!=null?ye:200,body:yt?JSON.parse(yt):void 0}),(St=d)==null||St.abort==null||St.abort(),o();break}case"error":{const{message:yt}=Me.error;throw Object.assign(new Error(yt),{cause:Me.error})}default:this.uppy.log(`Companion socket unknown action ${mt}`,"warning")}}catch(mt){T(mt)}});const qe=()=>{this.uppy.log(`Closing socket ${e.id}`,"info"),clearTimeout(f),c&&c.close(),c=void 0};d.signal.addEventListener("abort",()=>{qe()})}),{retries:Br,signal:d.signal,onFailedAttempt:()=>{d.signal.aborted||this.uppy.log(`Retrying websocket ${e.id}`,"info")}})})().abortOn(d.signal)}catch(z){if(d.signal.aborted)return;T(z)}},P=T=>{if(n.resumableUploads)if(p=T,c&&g(),T){var _;(_=d)==null||_.abort==null||_.abort()}else w()},k=T=>{var _;n.individualCancellation&&T.id===e.id&&(m("cancel"),(_=d)==null||_.abort==null||_.abort(),this.uppy.log(`upload ${e.id} was removed`,"info"),o())},D=T=>{var _;let{reason:z}=T;z==="user"&&m("cancel"),(_=d)==null||_.abort==null||_.abort(),this.uppy.log(`upload ${e.id} was canceled`,"info"),o()},O=(T,_)=>{T===e.id&&P(_)},se=()=>P(!0),fe=()=>P(!1);this.uppy.on("file-removed",k),this.uppy.on("cancel-all",D),this.uppy.on("upload-pause",O),this.uppy.on("pause-all",se),this.uppy.on("resume-all",fe),s=()=>{this.uppy.off("file-removed",k),this.uppy.off("cancel-all",D),this.uppy.off("upload-pause",O),this.uppy.off("pause-all",se),this.uppy.off("resume-all",fe)},i.addEventListener("abort",()=>{var T;(T=d)==null||T.abort()}),w()})}finally{s==null||s()}}ft.VERSION=Ii.version;function Y(r,e){if(!Object.prototype.hasOwnProperty.call(r,e))throw new TypeError("attempted to use private field on non-instance");return r}var Wi=0;function nr(r){return"__private_"+Wi+++"_"+r}const Xi=r=>r.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ");function Ji(){return location.origin}function Er(r){if(typeof r=="string")return new RegExp(`^${r}$`);if(r instanceof RegExp)return r}function Gi(r,e){return(Array.isArray(e)?e.map(Er):[Er(e)]).some(i=>(i==null?void 0:i.test(r))||(i==null?void 0:i.test(`${r}/`)))}var te=nr("refreshingTokenPromise"),Be=nr("getAuthToken"),ie=nr("getPlugin");class sn extends ft{constructor(e,t){var i;super(e,t),Object.defineProperty(this,ie,{value:Vi}),Object.defineProperty(this,Be,{value:Qi}),Object.defineProperty(this,te,{writable:!0,value:void 0}),this.provider=t.provider,this.id=this.provider,this.name=this.opts.name||Xi(this.id),this.pluginId=this.opts.pluginId,this.tokenKey=`companion-${this.pluginId}-auth-token`,this.companionKeysParams=this.opts.companionKeysParams,this.preAuthToken=null,this.supportsRefreshToken=(i=t.supportsRefreshToken)!=null?i:!0}async headers(){const[e,t]=await Promise.all([super.headers(),Y(this,Be)[Be]()]),i={};return t&&(i["uppy-auth-token"]=t),this.companionKeysParams&&(i["uppy-credentials-params"]=btoa(JSON.stringify({params:this.companionKeysParams}))),{...e,...i}}onReceiveResponse(e){super.onReceiveResponse(e);const t=Y(this,ie)[ie](),s=t.getPluginState().authenticated?e.status!==Dr:e.status<400;return t.setPluginState({authenticated:s}),e}async setAuthToken(e){return Y(this,ie)[ie]().storage.setItem(this.tokenKey,e)}async removeAuthToken(){return Y(this,ie)[ie]().storage.removeItem(this.tokenKey)}async ensurePreAuth(){if(this.companionKeysParams&&!this.preAuthToken&&(await this.fetchPreAuthToken(),!this.preAuthToken))throw new Error("Could not load authentication data required for third-party login. Please try again later.")}authQuery(e){return{}}authUrl(e){let{authFormData:t,query:i}=e;const s=new URLSearchParams({...i,state:btoa(JSON.stringify({origin:Ji()})),...this.authQuery({authFormData:t})});return this.preAuthToken&&s.set("uppyPreAuthToken",this.preAuthToken),`${this.hostname}/${this.id}/connect?${s}`}async loginSimpleAuth(e){let{uppyVersions:t,authFormData:i,signal:s}=e;const n=await this.post(`${this.id}/simple-auth`,{form:i},{qs:{uppyVersions:t},signal:s});this.setAuthToken(n.uppyAuthToken)}async loginOAuth(e){let{uppyVersions:t,authFormData:i,signal:s}=e;return await this.ensurePreAuth(),s.throwIfAborted(),new Promise((n,o)=>{const a=this.authUrl({query:{uppyVersions:t},authFormData:i}),u=window.open(a,"_blank");let l;const c=d=>{if(d.source!==u){let m="";try{m=JSON.stringify(d.data)}catch{}this.uppy.log(`ignoring event from unknown source ${m}`,"warning");return}const{companionAllowedHosts:f}=Y(this,ie)[ie]().opts;if(!Gi(d.origin,f)){o(new Error(`rejecting event from ${d.origin} vs allowed pattern ${f}`));return}const p=typeof d.data=="string"?JSON.parse(d.data):d.data;if(p.error){const{uppy:m}=this,g=m.i18n("authAborted");m.info({message:g},"warning",5e3),o(new Error("auth aborted"));return}if(!p.token){o(new Error("did not receive token from auth window"));return}l(),n(this.setAuthToken(p.token))};l=()=>{u==null||u.close(),window.removeEventListener("message",c),s.removeEventListener("abort",l)},s.addEventListener("abort",l),window.addEventListener("message",c)})}async login(e){let{uppyVersions:t,authFormData:i,signal:s}=e;return this.loginOAuth({uppyVersions:t,authFormData:i,signal:s})}refreshTokenUrl(){return`${this.hostname}/${this.id}/refresh-token`}fileUrl(e){return`${this.hostname}/${this.id}/get/${e}`}async request(){await Y(this,te)[te];try{return await super.request(...arguments)}catch(e){if(!this.supportsRefreshToken)throw e;const t=await Y(this,Be)[Be]();if(!e.isAuthError||!t)throw e;return Y(this,te)[te]==null&&(Y(this,te)[te]=(async()=>{try{this.uppy.log("[CompanionClient] Refreshing expired auth token","info");const i=await super.request({path:this.refreshTokenUrl(),method:"POST"});await this.setAuthToken(i.uppyAuthToken)}catch(i){throw i.isAuthError&&await this.removeAuthToken(),e}finally{Y(this,te)[te]=void 0}})()),await Y(this,te)[te],super.request(...arguments)}}async fetchPreAuthToken(){if(this.companionKeysParams)try{const e=await this.post(`${this.id}/preauth/`,{params:this.companionKeysParams});this.preAuthToken=e.token}catch(e){this.uppy.log(`[CompanionClient] unable to fetch preAuthToken ${e}`,"warning")}}list(e,t){return this.get(`${this.id}/list/${e||""}`,t)}async logout(e){const t=await this.get(`${this.id}/logout`,e);return await this.removeAuthToken(),t}}async function Qi(){return Y(this,ie)[ie]().storage.getItem(this.tokenKey)}function Vi(){const r=this.uppy.getPlugin(this.pluginId);if(r==null)throw new Error("Plugin was nullish");return r}const Yi=r=>r.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ");class nn extends ft{constructor(e,t){super(e,t),this.provider=t.provider,this.id=this.provider,this.name=this.opts.name||Yi(this.id),this.pluginId=this.opts.pluginId}fileUrl(e){return`${this.hostname}/search/${this.id}/get/${e}`}search(e,t){return this.get(`search/${this.id}/list?q=${encodeURIComponent(e)}${t?`&${t}`:""}`)}}function on(r,e){if(r){if(typeof r!="string"&&!Array.isArray(r)&&!(r instanceof RegExp))throw new TypeError('The option "companionAllowedHosts" must be one of string, Array, RegExp');return r}return/^(?!https?:\/\/).*$/i.test(e)?`https://${e.replace(/^\/\//,"")}`:new URL(e).origin}function Zi(r,e){return new Promise(t=>{localStorage.setItem(r,e),t()})}function es(r){return Promise.resolve(localStorage.getItem(r))}function ts(r){return new Promise(e=>{localStorage.removeItem(r),e()})}const an=Object.freeze(Object.defineProperty({__proto__:null,getItem:es,removeItem:ts,setItem:Zi},Symbol.toStringTag,{value:"Module"}));function ae(r,e){if(!Object.prototype.hasOwnProperty.call(r,e))throw new TypeError("attempted to use private field on non-instance");return r}var rs=0;function Wr(r){return"__private_"+rs+++"_"+r}var L=Wr("uppy"),De=Wr("events");let is=class{constructor(e){Object.defineProperty(this,L,{writable:!0,value:void 0}),Object.defineProperty(this,De,{writable:!0,value:[]}),ae(this,L)[L]=e}on(e,t){return ae(this,De)[De].push([e,t]),ae(this,L)[L].on(e,t)}remove(){for(const[e,t]of ae(this,De)[De].splice(0))ae(this,L)[L].off(e,t)}onFilePause(e,t){this.on("upload-pause",(i,s)=>{e===i&&t(s)})}onFileRemove(e,t){this.on("file-removed",i=>{e===i.id&&t(i.id)})}onPause(e,t){this.on("upload-pause",(i,s)=>{e===i&&t(s)})}onRetry(e,t){this.on("upload-retry",i=>{e===i&&t()})}onRetryAll(e,t){this.on("retry-all",()=>{ae(this,L)[L].getFile(e)&&t()})}onPauseAll(e,t){this.on("pause-all",()=>{ae(this,L)[L].getFile(e)&&t()})}onCancelAll(e,t){var i=this;this.on("cancel-all",function(){ae(i,L)[L].getFile(e)&&t(...arguments)})}onResumeAll(e,t){this.on("resume-all",()=>{ae(this,L)[L].getFile(e)&&t()})}};function y(r,e){if(!Object.prototype.hasOwnProperty.call(r,e))throw new TypeError("attempted to use private field on non-instance");return r}var ss=0;function B(r){return"__private_"+ss+++"_"+r}function ns(r){return new Error("Cancelled",{cause:r})}function Or(r){if(r!=null){var e;const t=()=>this.abort(r.reason);r.addEventListener("abort",t,{once:!0});const i=()=>{r.removeEventListener("abort",t)};(e=this.then)==null||e.call(this,i,i)}return this}var J=B("activeRequests"),H=B("queuedHandlers"),X=B("paused"),ve=B("pauseTimer"),x=B("downLimit"),ge=B("upperLimit"),ue=B("rateLimitingTimer"),ct=B("call"),he=B("queueNext"),rr=B("next"),Nt=B("queue"),ir=B("dequeue"),zt=B("resume"),be=B("increaseLimit");class Xr{constructor(e){Object.defineProperty(this,ir,{value:cs}),Object.defineProperty(this,Nt,{value:ls}),Object.defineProperty(this,rr,{value:us}),Object.defineProperty(this,he,{value:as}),Object.defineProperty(this,ct,{value:os}),Object.defineProperty(this,J,{writable:!0,value:0}),Object.defineProperty(this,H,{writable:!0,value:[]}),Object.defineProperty(this,X,{writable:!0,value:!1}),Object.defineProperty(this,ve,{writable:!0,value:void 0}),Object.defineProperty(this,x,{writable:!0,value:1}),Object.defineProperty(this,ge,{writable:!0,value:void 0}),Object.defineProperty(this,ue,{writable:!0,value:void 0}),Object.defineProperty(this,zt,{writable:!0,value:()=>this.resume()}),Object.defineProperty(this,be,{writable:!0,value:()=>{if(y(this,X)[X]){y(this,ue)[ue]=setTimeout(y(this,be)[be],0);return}y(this,x)[x]=this.limit,this.limit=Math.ceil((y(this,ge)[ge]+y(this,x)[x])/2);for(let t=y(this,x)[x];t<=this.limit;t++)y(this,he)[he]();y(this,ge)[ge]-y(this,x)[x]>3?y(this,ue)[ue]=setTimeout(y(this,be)[be],2e3):y(this,x)[x]=Math.floor(y(this,x)[x]/2)}}),typeof e!="number"||e===0?this.limit=1/0:this.limit=e}run(e,t){return!y(this,X)[X]&&y(this,J)[J]<this.limit?y(this,ct)[ct](e):y(this,Nt)[Nt](e,t)}wrapSyncFunction(e,t){var i=this;return function(){for(var s=arguments.length,n=new Array(s),o=0;o<s;o++)n[o]=arguments[o];const a=i.run(()=>(e(...n),queueMicrotask(()=>a.done()),()=>{}),t);return{abortOn:Or,abort(){a.abort()}}}}wrapPromiseFunction(e,t){var i=this;return function(){for(var s=arguments.length,n=new Array(s),o=0;o<s;o++)n[o]=arguments[o];let a;const u=new Promise((l,c)=>{a=i.run(()=>{let d,f;try{f=Promise.resolve(e(...n))}catch(p){f=Promise.reject(p)}return f.then(p=>{d?c(d):(a.done(),l(p))},p=>{d?c(d):(a.done(),c(p))}),p=>{d=ns(p)}},t)});return u.abort=l=>{a.abort(l)},u.abortOn=Or,u}}resume(){y(this,X)[X]=!1,clearTimeout(y(this,ve)[ve]);for(let e=0;e<this.limit;e++)y(this,he)[he]()}pause(e){e===void 0&&(e=null),y(this,X)[X]=!0,clearTimeout(y(this,ve)[ve]),e!=null&&(y(this,ve)[ve]=setTimeout(y(this,zt)[zt],e))}rateLimit(e){clearTimeout(y(this,ue)[ue]),this.pause(e),this.limit>1&&Number.isFinite(this.limit)&&(y(this,ge)[ge]=this.limit-1,this.limit=y(this,x)[x],y(this,ue)[ue]=setTimeout(y(this,be)[be],e))}get isPaused(){return y(this,X)[X]}}function os(r){y(this,J)[J]+=1;let e=!1,t;try{t=r()}catch(i){throw y(this,J)[J]-=1,i}return{abort:i=>{e||(e=!0,y(this,J)[J]-=1,t==null||t(i),y(this,he)[he]())},done:()=>{e||(e=!0,y(this,J)[J]-=1,y(this,he)[he]())}}}function as(){queueMicrotask(()=>y(this,rr)[rr]())}function us(){if(y(this,X)[X]||y(this,J)[J]>=this.limit||y(this,H)[H].length===0)return;const r=y(this,H)[H].shift();if(r==null)throw new Error("Invariant violation: next is null");const e=y(this,ct)[ct](r.fn);r.abort=e.abort,r.done=e.done}function ls(r,e){const t={fn:r,priority:(e==null?void 0:e.priority)||0,abort:()=>{y(this,ir)[ir](t)},done:()=>{throw new Error("Cannot mark a queued request as done: this indicates a bug")}},i=y(this,H)[H].findIndex(s=>t.priority>s.priority);return i===-1?y(this,H)[H].push(t):y(this,H)[H].splice(i,0,t),t}function cs(r){const e=y(this,H)[H].indexOf(r);e!==-1&&y(this,H)[H].splice(e,1)}const Jr=Symbol("__queue");function Gr(r){const e=t=>"error"in t&&!!t.error;return r.filter(t=>!e(t))}function Qr(r){return r.filter(e=>{var t;return!((t=e.progress)!=null&&t.uploadStarted)||!e.isRestored})}const{AbortController:Kt}=globalThis,Vr=function(r,e){r===void 0&&(r="Aborted");const t=new DOMException(r,"AbortError");return e!=null&&Nr(e,"cause")&&Object.defineProperty(t,"cause",{__proto__:null,configurable:!0,writable:!0,value:e.cause}),t};function h(r,e){if(!Object.prototype.hasOwnProperty.call(r,e))throw new TypeError("attempted to use private field on non-instance");return r}var ds=0;function $(r){return"__private_"+ds+++"_"+r}const ps=1024*1024,Ur={getChunkSize(r){return Math.ceil(r.size/1e4)},onProgress(){},onPartComplete(){},onSuccess(){},onError(r){throw r}};function hs(r){if(typeof r=="string")return parseInt(r,10);if(typeof r=="number")return r;throw new TypeError("Expected a number")}const bt=Symbol("pausing upload, not an actual error");var A=$("abortController"),R=$("chunks"),I=$("chunkState"),G=$("data"),Q=$("file"),dt=$("uploadHasStarted"),We=$("onError"),Ae=$("onSuccess"),pe=$("shouldUseMultipart"),$e=$("isRestoring"),ht=$("onReject"),Ue=$("maxMultipartParts"),ot=$("minPartSize"),Bt=$("initChunks"),Dt=$("createUpload"),Xe=$("resumeUpload"),at=$("onPartProgress"),ut=$("onPartComplete"),Wt=$("abortUpload");class fs{constructor(e,t){var i,s;Object.defineProperty(this,Wt,{value:gs}),Object.defineProperty(this,Xe,{value:vs}),Object.defineProperty(this,Dt,{value:ys}),Object.defineProperty(this,Bt,{value:ms}),Object.defineProperty(this,A,{writable:!0,value:new Kt}),Object.defineProperty(this,R,{writable:!0,value:void 0}),Object.defineProperty(this,I,{writable:!0,value:void 0}),Object.defineProperty(this,G,{writable:!0,value:void 0}),Object.defineProperty(this,Q,{writable:!0,value:void 0}),Object.defineProperty(this,dt,{writable:!0,value:!1}),Object.defineProperty(this,We,{writable:!0,value:void 0}),Object.defineProperty(this,Ae,{writable:!0,value:void 0}),Object.defineProperty(this,pe,{writable:!0,value:void 0}),Object.defineProperty(this,$e,{writable:!0,value:void 0}),Object.defineProperty(this,ht,{writable:!0,value:n=>(n==null?void 0:n.cause)===bt?null:h(this,We)[We](n)}),Object.defineProperty(this,Ue,{writable:!0,value:1e4}),Object.defineProperty(this,ot,{writable:!0,value:5*ps}),Object.defineProperty(this,at,{writable:!0,value:n=>o=>{if(!o.lengthComputable)return;h(this,I)[I][n].uploaded=hs(o.loaded);const a=h(this,I)[I].reduce((u,l)=>u+l.uploaded,0);this.options.onProgress(a,h(this,G)[G].size)}}),Object.defineProperty(this,ut,{writable:!0,value:n=>o=>{h(this,R)[R][n]=null,h(this,I)[I][n].etag=o,h(this,I)[I][n].done=!0;const a={PartNumber:n+1,ETag:o};this.options.onPartComplete(a)}}),this.options={...Ur,...t},(s=(i=this.options).getChunkSize)!=null||(i.getChunkSize=Ur.getChunkSize),h(this,G)[G]=e,h(this,Q)[Q]=t.file,h(this,Ae)[Ae]=this.options.onSuccess,h(this,We)[We]=this.options.onError,h(this,pe)[pe]=this.options.shouldUseMultipart,h(this,$e)[$e]=t.uploadId&&t.key,h(this,Bt)[Bt]()}start(){h(this,dt)[dt]?(h(this,A)[A].signal.aborted||h(this,A)[A].abort(bt),h(this,A)[A]=new Kt,h(this,Xe)[Xe]()):h(this,$e)[$e]?(this.options.companionComm.restoreUploadFile(h(this,Q)[Q],{uploadId:this.options.uploadId,key:this.options.key}),h(this,Xe)[Xe]()):h(this,Dt)[Dt]()}pause(){h(this,A)[A].abort(bt),h(this,A)[A]=new Kt}abort(e){e!=null&&e.really?h(this,Wt)[Wt]():this.pause()}get chunkState(){return h(this,I)[I]}}function ms(){const r=h(this,G)[G].size,e=typeof h(this,pe)[pe]=="function"?h(this,pe)[pe](h(this,Q)[Q]):!!h(this,pe)[pe];if(e&&r>h(this,ot)[ot]){let t=Math.max(this.options.getChunkSize(h(this,G)[G]),h(this,ot)[ot]),i=Math.floor(r/t);i>h(this,Ue)[Ue]&&(i=h(this,Ue)[Ue],t=r/h(this,Ue)[Ue]),h(this,R)[R]=Array(i);for(let s=0,n=0;s<r;s+=t,n++){const o=Math.min(r,s+t),a=()=>{const u=s;return h(this,G)[G].slice(u,o)};if(h(this,R)[R][n]={getData:a,onProgress:h(this,at)[at](n),onComplete:h(this,ut)[ut](n),shouldUseMultipart:e},h(this,$e)[$e]){const u=s+t>r?r-s:t;h(this,R)[R][n].setAsUploaded=()=>{h(this,R)[R][n]=null,h(this,I)[I][n].uploaded=u}}}}else h(this,R)[R]=[{getData:()=>h(this,G)[G],onProgress:h(this,at)[at](0),onComplete:h(this,ut)[ut](0),shouldUseMultipart:e}];h(this,I)[I]=h(this,R)[R].map(()=>({uploaded:0}))}function ys(){this.options.companionComm.uploadFile(h(this,Q)[Q],h(this,R)[R],h(this,A)[A].signal).then(h(this,Ae)[Ae],h(this,ht)[ht]),h(this,dt)[dt]=!0}function vs(){this.options.companionComm.resumeUploadFile(h(this,Q)[Q],h(this,R)[R],h(this,A)[A].signal).then(h(this,Ae)[Ae],h(this,ht)[ht])}function gs(){h(this,A)[A].abort(),this.options.companionComm.abortFileUpload(h(this,Q)[Q]).catch(r=>this.options.log(r))}function M(r){if(r!=null&&r.aborted)throw Vr("The operation was aborted",{cause:r.reason})}function bs(r){let{method:e="PUT",CanonicalUri:t="/",CanonicalQueryString:i="",SignedHeaders:s,HashedPayload:n}=r;const o=Object.keys(s).map(a=>a.toLowerCase()).sort();return[e,t,i,...o.map(a=>`${a}:${s[a]}`),"",o.join(";"),n].join(`
`)}const or=new TextEncoder,ar={name:"HMAC",hash:"SHA-256"};async function ws(r){const{subtle:e}=globalThis.crypto;return e.digest(ar.hash,or.encode(r))}async function Ps(r){const{subtle:e}=globalThis.crypto;return e.importKey("raw",typeof r=="string"?or.encode(r):r,ar,!1,["sign"])}function Rr(r){const e=new Uint8Array(r);let t="";for(let i=0;i<e.length;i++)t+=e[i].toString(16).padStart(2,"0");return t}async function Je(r,e){const{subtle:t}=globalThis.crypto;return t.sign(ar,await Ps(r),or.encode(e))}async function _s(r){let{accountKey:e,accountSecret:t,sessionToken:i,bucketName:s,Key:n,Region:o,expires:a,uploadId:u,partNumber:l}=r;const c="s3",d=`${s}.${c}.${o}.amazonaws.com`,f=`/${encodeURI(n).replace(/[;?:@&=+$,#!'()*]/g,me=>`%${me.charCodeAt(0).toString(16).toUpperCase()}`)}`,p="UNSIGNED-PAYLOAD",m=new Date().toISOString().replace(/[-:]|\.\d+/g,""),g=m.slice(0,8),w=`${g}/${o}/${c}/aws4_request`,P=new URL(`https://${d}${f}`);P.searchParams.set("X-Amz-Algorithm","AWS4-HMAC-SHA256"),P.searchParams.set("X-Amz-Content-Sha256",p),P.searchParams.set("X-Amz-Credential",`${e}/${w}`),P.searchParams.set("X-Amz-Date",m),P.searchParams.set("X-Amz-Expires",a),P.searchParams.set("X-Amz-Security-Token",i),P.searchParams.set("X-Amz-SignedHeaders","host"),l&&P.searchParams.set("partNumber",l),u&&P.searchParams.set("uploadId",u),P.searchParams.set("x-id",l&&u?"UploadPart":"PutObject");const k=bs({CanonicalUri:f,CanonicalQueryString:P.search.slice(1),SignedHeaders:{host:d},HashedPayload:p}),D=Rr(await ws(k)),O=["AWS4-HMAC-SHA256",m,w,D].join(`
`),se=await Je(`AWS4${t}`,g),fe=await Je(se,o),T=await Je(fe,c),_=await Je(T,"aws4_request"),z=Rr(await Je(_,O));return P.searchParams.set("X-Amz-Signature",z),P}function v(r,e){if(!Object.prototype.hasOwnProperty.call(r,e))throw new TypeError("attempted to use private field on non-instance");return r}var Ts=0;function N(r){return"__private_"+Ts+++"_"+r}function Ss(r){const e=new URL(r);return e.search="",e.hash="",e.href}var Ge=N("abortMultipartUpload"),C=N("cache"),Qe=N("createMultipartUpload"),Ve=N("fetchSignature"),pt=N("getUploadParameters"),Ye=N("listParts"),Re=N("previousRetryDelay"),ke=N("requests"),we=N("retryDelays"),Pe=N("sendCompletionRequest"),_e=N("setS3MultipartState"),Ce=N("uploadPartBytes"),j=N("getFile"),Xt=N("shouldRetry"),Ze=N("nonMultipartUpload");class Es{constructor(e,t,i,s){Object.defineProperty(this,Ze,{value:Us}),Object.defineProperty(this,Xt,{value:Os}),Object.defineProperty(this,Ge,{writable:!0,value:void 0}),Object.defineProperty(this,C,{writable:!0,value:new WeakMap}),Object.defineProperty(this,Qe,{writable:!0,value:void 0}),Object.defineProperty(this,Ve,{writable:!0,value:void 0}),Object.defineProperty(this,pt,{writable:!0,value:void 0}),Object.defineProperty(this,Ye,{writable:!0,value:void 0}),Object.defineProperty(this,Re,{writable:!0,value:void 0}),Object.defineProperty(this,ke,{writable:!0,value:void 0}),Object.defineProperty(this,we,{writable:!0,value:void 0}),Object.defineProperty(this,Pe,{writable:!0,value:void 0}),Object.defineProperty(this,_e,{writable:!0,value:void 0}),Object.defineProperty(this,Ce,{writable:!0,value:void 0}),Object.defineProperty(this,j,{writable:!0,value:void 0}),v(this,ke)[ke]=e,v(this,_e)[_e]=i,v(this,j)[j]=s,this.setOptions(t)}setOptions(e){const t=v(this,ke)[ke];if("abortMultipartUpload"in e&&(v(this,Ge)[Ge]=t.wrapPromiseFunction(e.abortMultipartUpload,{priority:1})),"createMultipartUpload"in e&&(v(this,Qe)[Qe]=t.wrapPromiseFunction(e.createMultipartUpload,{priority:-1})),"signPart"in e&&(v(this,Ve)[Ve]=t.wrapPromiseFunction(e.signPart)),"listParts"in e&&(v(this,Ye)[Ye]=t.wrapPromiseFunction(e.listParts)),"completeMultipartUpload"in e&&(v(this,Pe)[Pe]=t.wrapPromiseFunction(e.completeMultipartUpload,{priority:1})),"retryDelays"in e){var i;v(this,we)[we]=(i=e.retryDelays)!=null?i:[]}"uploadPartBytes"in e&&(v(this,Ce)[Ce]=t.wrapPromiseFunction(e.uploadPartBytes,{priority:1/0})),"getUploadParameters"in e&&(v(this,pt)[pt]=t.wrapPromiseFunction(e.getUploadParameters))}async getUploadId(e,t){let i;for(;(i=v(this,C)[C].get(e.data))!=null;)try{return await i}catch{}const s=v(this,Qe)[Qe](v(this,j)[j](e),t),n=()=>{s.abort(t.reason),v(this,C)[C].delete(e.data)};return t.addEventListener("abort",n,{once:!0}),v(this,C)[C].set(e.data,s),s.then(async o=>{t.removeEventListener("abort",n),v(this,_e)[_e](e,o),v(this,C)[C].set(e.data,o)},()=>{t.removeEventListener("abort",n),v(this,C)[C].delete(e.data)}),s}async abortFileUpload(e){const t=v(this,C)[C].get(e.data);if(t==null)return;v(this,C)[C].delete(e.data),v(this,_e)[_e](e,Object.create(null));let i;try{i=await t}catch{return}await v(this,Ge)[Ge](v(this,j)[j](e),i)}async uploadFile(e,t,i){if(M(i),t.length===1&&!t[0].shouldUseMultipart)return v(this,Ze)[Ze](e,t[0],i);const{uploadId:s,key:n}=await this.getUploadId(e,i);M(i);try{const o=await Promise.all(t.map((a,u)=>this.uploadChunk(e,u+1,a,i)));return M(i),await v(this,Pe)[Pe](v(this,j)[j](e),{key:n,uploadId:s,parts:o,signal:i},i).abortOn(i)}catch(o){throw(o==null?void 0:o.cause)!==bt&&(o==null?void 0:o.name)!=="AbortError"&&this.abortFileUpload(e),o}}restoreUploadFile(e,t){v(this,C)[C].set(e.data,t)}async resumeUploadFile(e,t,i){if(M(i),t.length===1&&t[0]!=null&&!t[0].shouldUseMultipart)return v(this,Ze)[Ze](e,t[0],i);const{uploadId:s,key:n}=await this.getUploadId(e,i);M(i);const o=await v(this,Ye)[Ye](v(this,j)[j](e),{uploadId:s,key:n,signal:i},i).abortOn(i);M(i);const a=await Promise.all(t.map((u,l)=>{const c=l+1,d=o.find(f=>{let{PartNumber:p}=f;return p===c});return d==null?this.uploadChunk(e,c,u,i):(u==null||u.setAsUploaded==null||u.setAsUploaded(),{PartNumber:c,ETag:d.ETag})}));return M(i),v(this,Pe)[Pe](v(this,j)[j](e),{key:n,uploadId:s,parts:a,signal:i},i).abortOn(i)}async uploadChunk(e,t,i,s){M(s);const{uploadId:n,key:o}=await this.getUploadId(e,s),a=v(this,we)[we].values(),u=v(this,we)[we].values(),l=()=>{const c=a.next();return c==null||c.done?null:c.value};for(;;){M(s);const c=i.getData(),{onProgress:d,onComplete:f}=i;let p;try{p=await v(this,Ve)[Ve](v(this,j)[j](e),{uploadId:n,key:o,partNumber:t,body:c,signal:s}).abortOn(s)}catch(m){const g=l();if(g==null||s.aborted)throw m;await new Promise(w=>setTimeout(w,g));continue}M(s);try{return{PartNumber:t,...await v(this,Ce)[Ce]({signature:p,body:c,size:c.size,onProgress:d,onComplete:f,signal:s}).abortOn(s)}}catch(m){if(!await v(this,Xt)[Xt](m,u))throw m}}}}async function Os(r,e){var t;const i=v(this,ke)[ke],s=r==null||(t=r.source)==null?void 0:t.status;if(s==null)return!1;if(s===403&&r.message==="Request has expired"){if(!i.isPaused){if(i.limit===1||v(this,Re)[Re]==null){const n=e.next();if(n==null||n.done)return!1;v(this,Re)[Re]=n.value}i.rateLimit(0),await new Promise(n=>setTimeout(n,v(this,Re)[Re]))}}else if(s===429){if(!i.isPaused){const n=e.next();if(n==null||n.done)return!1;i.rateLimit(n.value)}}else{if(s>400&&s<500&&s!==409)return!1;if(typeof navigator<"u"&&navigator.onLine===!1)i.isPaused||(i.pause(),window.addEventListener("online",()=>{i.resume()},{once:!0}));else{const n=e.next();if(n==null||n.done)return!1;await new Promise(o=>setTimeout(o,n.value))}}return!0}async function Us(r,e,t){const{method:i="POST",url:s,fields:n,headers:o}=await v(this,pt)[pt](v(this,j)[j](r),{signal:t}).abortOn(t);let a;const u=e.getData();if(i.toUpperCase()==="POST"){const f=new FormData;Object.entries(n).forEach(p=>{let[m,g]=p;return f.set(m,g)}),f.set("file",u),a=f}else a=u;const{onProgress:l,onComplete:c}=e,d=await v(this,Ce)[Ce]({signature:{url:s,headers:o,method:i},body:a,size:u.size,onProgress:l,onComplete:c,signal:t}).abortOn(t);return"location"in d?d:{location:Ss(s),...d}}let Yr;function b(r,e){if(!Object.prototype.hasOwnProperty.call(r,e))throw new TypeError("attempted to use private field on non-instance");return r}var Rs=0;function ee(r){return"__private_"+Rs+++"_"+r}const $s={version:"3.12.0"};function Le(r){if(r!=null&&r.error){const e=new Error(r.message);throw Object.assign(e,r.error),e}return r}function Zr(r){const e=r.Expiration;if(e){const t=Math.floor((new Date(e)-Date.now())/1e3);if(t>9)return t}}function $r(r){let{meta:e,allowedMetaFields:t,querify:i=!1}=r;const s=t??Object.keys(e);return e?Object.fromEntries(s.filter(n=>e[n]!=null).map(n=>{const o=i?`metadata[${n}]`:n,a=String(e[n]);return[o,a]})):{}}const ks={allowedMetaFields:null,limit:6,getTemporarySecurityCredentials:!1,shouldUseMultipart:r=>r.size!==0,retryDelays:[0,1e3,3e3,5e3],companionHeaders:{}};var Fe=ee("companionCommunicationQueue"),E=ee("client"),F=ee("cachedTemporaryCredentials"),Jt=ee("getTemporarySecurityCredentials"),Gt=ee("setS3MultipartState"),je=ee("getFile"),Qt=ee("uploadLocalFile"),Vt=ee("getCompanionClientArgs"),et=ee("upload"),Te=ee("setCompanionHeaders"),le=ee("setResumableUploadsCapability"),tt=ee("resetResumableCapability");Yr=Symbol.for("uppy test: getClient");class Pt extends xr{constructor(e,t){var i;super(e,{...ks,uploadPartBytes:Pt.uploadPartBytes,createMultipartUpload:null,listParts:null,abortMultipartUpload:null,completeMultipartUpload:null,signPart:null,getUploadParameters:null,...t}),Object.defineProperty(this,Vt,{value:js}),Object.defineProperty(this,Qt,{value:Fs}),Object.defineProperty(this,Jt,{value:Cs}),Object.defineProperty(this,Fe,{writable:!0,value:void 0}),Object.defineProperty(this,E,{writable:!0,value:void 0}),Object.defineProperty(this,F,{writable:!0,value:void 0}),Object.defineProperty(this,Gt,{writable:!0,value:(n,o)=>{let{key:a,uploadId:u}=o;const l=this.uppy.getFile(n.id);l!=null&&this.uppy.setFileState(n.id,{s3Multipart:{...l.s3Multipart,key:a,uploadId:u}})}}),Object.defineProperty(this,je,{writable:!0,value:n=>this.uppy.getFile(n.id)||n}),Object.defineProperty(this,et,{writable:!0,value:async n=>{if(n.length===0)return;const o=this.uppy.getFilesByIds(n),a=Gr(o),u=Qr(a);this.uppy.emit("upload-start",u);const l=a.map(d=>{if(d.isRemote){const f=()=>this.requests;b(this,le)[le](!1);const p=new AbortController,m=w=>{w.id===d.id&&p.abort()};this.uppy.on("file-removed",m);const g=this.uppy.getRequestClientForFile(d).uploadRemoteFile(d,b(this,Vt)[Vt](d),{signal:p.signal,getQueue:f});return this.requests.wrapSyncFunction(()=>{this.uppy.off("file-removed",m)},{priority:-1})(),g}return b(this,Qt)[Qt](d)}),c=await Promise.all(l);return b(this,le)[le](!0),c}}),Object.defineProperty(this,Te,{writable:!0,value:()=>{b(this,E)[E].setCompanionHeaders(this.opts.companionHeaders)}}),Object.defineProperty(this,le,{writable:!0,value:n=>{const{capabilities:o}=this.uppy.getState();this.uppy.setState({capabilities:{...o,resumableUploads:n}})}}),Object.defineProperty(this,tt,{writable:!0,value:()=>{b(this,le)[le](!0)}}),this.type="uploader",this.id=this.opts.id||"AwsS3Multipart",this.title="AWS S3 Multipart",b(this,E)[E]=new ft(e,t);const s={createMultipartUpload:this.createMultipartUpload,listParts:this.listParts,abortMultipartUpload:this.abortMultipartUpload,completeMultipartUpload:this.completeMultipartUpload,signPart:t!=null&&t.getTemporarySecurityCredentials?this.createSignedURL:this.signPart,getUploadParameters:t!=null&&t.getTemporarySecurityCredentials?this.createSignedURL:this.getUploadParameters};for(const n of Object.keys(s))this.opts[n]==null&&(this.opts[n]=s[n].bind(this));(t==null?void 0:t.prepareUploadParts)!=null&&t.signPart==null&&(this.opts.signPart=async(n,o)=>{let{uploadId:a,key:u,partNumber:l,body:c,signal:d}=o;const{presignedUrls:f,headers:p}=await t.prepareUploadParts(n,{uploadId:a,key:u,parts:[{number:l,chunk:c}],signal:d});return{url:f==null?void 0:f[l],headers:p==null?void 0:p[l]}}),this.requests=(i=this.opts.rateLimitedQueue)!=null?i:new Xr(this.opts.limit),b(this,Fe)[Fe]=new Es(this.requests,this.opts,b(this,Gt)[Gt],b(this,je)[je]),this.uploaders=Object.create(null),this.uploaderEvents=Object.create(null),this.uploaderSockets=Object.create(null)}[Yr](){return b(this,E)[E]}setOptions(e){b(this,Fe)[Fe].setOptions(e),super.setOptions(e),b(this,Te)[Te]()}resetUploaderReferences(e,t){this.uploaders[e]&&(this.uploaders[e].abort({really:(t==null?void 0:t.abort)||!1}),this.uploaders[e]=null),this.uploaderEvents[e]&&(this.uploaderEvents[e].remove(),this.uploaderEvents[e]=null),this.uploaderSockets[e]&&(this.uploaderSockets[e].close(),this.uploaderSockets[e]=null)}assertHost(e){if(!this.opts.companionUrl)throw new Error(`Expected a \`companionUrl\` option containing a Companion address, or if you are not using Companion, a custom \`${e}\` implementation.`)}createMultipartUpload(e,t){this.assertHost("createMultipartUpload"),M(t);const i=$r({meta:e.meta,allowedMetaFields:this.opts.allowedMetaFields});return b(this,E)[E].post("s3/multipart",{filename:e.name,type:e.type,metadata:i},{signal:t}).then(Le)}listParts(e,t,i){var s;let{key:n,uploadId:o,signal:a}=t;(s=a)!=null||(a=i),this.assertHost("listParts"),M(a);const u=encodeURIComponent(n);return b(this,E)[E].get(`s3/multipart/${o}?key=${u}`,{signal:a}).then(Le)}completeMultipartUpload(e,t,i){var s;let{key:n,uploadId:o,parts:a,signal:u}=t;(s=u)!=null||(u=i),this.assertHost("completeMultipartUpload"),M(u);const l=encodeURIComponent(n),c=encodeURIComponent(o);return b(this,E)[E].post(`s3/multipart/${c}/complete?key=${l}`,{parts:a},{signal:u}).then(Le)}async createSignedURL(e,t){const i=await b(this,Jt)[Jt](t),s=Zr(i.credentials)||604800,{uploadId:n,key:o,partNumber:a}=t;return{method:"PUT",expires:s,fields:{},url:`${await _s({accountKey:i.credentials.AccessKeyId,accountSecret:i.credentials.SecretAccessKey,sessionToken:i.credentials.SessionToken,expires:s,bucketName:i.bucket,Region:i.region,Key:o??`${crypto.randomUUID()}-${e.name}`,uploadId:n,partNumber:a})}`,headers:{"Content-Type":e.type}}}signPart(e,t){let{uploadId:i,key:s,partNumber:n,signal:o}=t;if(this.assertHost("signPart"),M(o),i==null||s==null||n==null)throw new Error("Cannot sign without a key, an uploadId, and a partNumber");const a=encodeURIComponent(s);return b(this,E)[E].get(`s3/multipart/${i}/${n}?key=${a}`,{signal:o}).then(Le)}abortMultipartUpload(e,t,i){var s;let{key:n,uploadId:o,signal:a}=t;(s=a)!=null||(a=i),this.assertHost("abortMultipartUpload");const u=encodeURIComponent(n),l=encodeURIComponent(o);return b(this,E)[E].delete(`s3/multipart/${l}?key=${u}`,void 0,{signal:a}).then(Le)}getUploadParameters(e,t){const{meta:i}=e,{type:s,name:n}=i,o=$r({meta:i,allowedMetaFields:this.opts.allowedMetaFields,querify:!0}),a=new URLSearchParams({filename:n,type:s,...o});return b(this,E)[E].get(`s3/params?${a}`,t)}static async uploadPartBytes(e){let{signature:{url:t,expires:i,headers:s,method:n="PUT"},body:o,size:a=o.size,onProgress:u,onComplete:l,signal:c}=e;if(M(c),t==null)throw new Error("Cannot upload to an undefined URL");return new Promise((d,f)=>{const p=new XMLHttpRequest;p.open(n,t,!0),s&&Object.keys(s).forEach(w=>{p.setRequestHeader(w,s[w])}),p.responseType="text",typeof i=="number"&&(p.timeout=i*1e3);function m(){p.abort()}function g(){c==null||c.removeEventListener("abort",m)}c==null||c.addEventListener("abort",m),p.upload.addEventListener("progress",w=>{u(w)}),p.addEventListener("abort",()=>{g(),f(Vr())}),p.addEventListener("timeout",()=>{g();const w=new Error("Request has expired");w.source={status:403},f(w)}),p.addEventListener("load",()=>{if(g(),p.status===403&&p.responseText.includes("<Message>Request has expired</Message>")){const O=new Error("Request has expired");O.source=p,f(O);return}if(p.status<200||p.status>=300){const O=new Error("Non 2xx");O.source=p,f(O);return}u==null||u({loaded:a,lengthComputable:!0});const w=p.getAllResponseHeaders().trim().split(/[\r\n]+/),P={__proto__:null};for(const O of w){const se=O.split(": "),fe=se.shift(),T=se.join(": ");P[fe]=T}const{etag:k,location:D}=P;if(n.toUpperCase()==="POST"&&D===null&&console.warn("AwsS3/Multipart: Could not read the Location header. This likely means CORS is not configured correctly on the S3 Bucket. See https://uppy.io/docs/aws-s3-multipart#S3-Bucket-Configuration for instructions."),k===null){f(new Error("AwsS3/Multipart: Could not read the ETag header. This likely means CORS is not configured correctly on the S3 Bucket. See https://uppy.io/docs/aws-s3-multipart#S3-Bucket-Configuration for instructions."));return}l==null||l(k),d({...P,ETag:k})}),p.addEventListener("error",w=>{g();const P=new Error("Unknown error");P.source=w.target,f(P)}),p.send(o)})}install(){b(this,le)[le](!0),this.uppy.addPreProcessor(b(this,Te)[Te]),this.uppy.addUploader(b(this,et)[et]),this.uppy.on("cancel-all",b(this,tt)[tt])}uninstall(){this.uppy.removePreProcessor(b(this,Te)[Te]),this.uppy.removeUploader(b(this,et)[et]),this.uppy.off("cancel-all",b(this,tt)[tt])}}async function Cs(r){return M(r==null?void 0:r.signal),b(this,F)[F]==null&&(this.opts.getTemporarySecurityCredentials===!0?(this.assertHost("getTemporarySecurityCredentials"),b(this,F)[F]=b(this,E)[E].get("s3/sts",r).then(Le)):b(this,F)[F]=this.opts.getTemporarySecurityCredentials(r),b(this,F)[F]=await b(this,F)[F],setTimeout(()=>{b(this,F)[F]=null},(Zr(b(this,F)[F].credentials)||0)*500)),b(this,F)[F]}function Fs(r){var e=this;return new Promise((t,i)=>{const s=(l,c)=>{this.uppy.emit("upload-progress",this.uppy.getFile(r.id),{uploader:this,bytesUploaded:l,bytesTotal:c})},n=l=>{this.uppy.log(l),this.uppy.emit("upload-error",r,l),this.resetUploaderReferences(r.id),i(l)},o=l=>{const c={body:{...l},status:200,uploadURL:l.location};this.resetUploaderReferences(r.id),this.uppy.emit("upload-success",b(this,je)[je](r),c),l.location&&this.uppy.log(`Download ${r.name} from ${l.location}`),t()},a=new fs(r.data,{companionComm:b(this,Fe)[Fe],log:function(){return e.uppy.log(...arguments)},getChunkSize:this.opts.getChunkSize?this.opts.getChunkSize.bind(this):null,onProgress:s,onError:n,onSuccess:o,onPartComplete:l=>{this.uppy.emit("s3-multipart:part-uploaded",b(this,je)[je](r),l)},file:r,shouldUseMultipart:this.opts.shouldUseMultipart,...r.s3Multipart});this.uploaders[r.id]=a;const u=new is(this.uppy);this.uploaderEvents[r.id]=u,u.onFileRemove(r.id,l=>{a.abort(),this.resetUploaderReferences(r.id,{abort:!0}),t(`upload ${l} was removed`)}),u.onCancelAll(r.id,l=>{(l==null?void 0:l.reason)==="user"&&(a.abort(),this.resetUploaderReferences(r.id,{abort:!0})),t(`upload ${r.id} was canceled`)}),u.onFilePause(r.id,l=>{l?a.pause():a.start()}),u.onPauseAll(r.id,()=>{a.pause()}),u.onResumeAll(r.id,()=>{a.start()}),a.start()})}function js(r){var e;return{...(e=r.remote)==null?void 0:e.body,protocol:"s3-multipart",size:r.data.size,metadata:r.meta}}Pt.VERSION=$s.version;function ce(r,e){if(!Object.prototype.hasOwnProperty.call(r,e))throw new TypeError("attempted to use private field on non-instance");return r}var As=0;function ei(r){return"__private_"+As+++"_"+r}var q=ei("uppy"),rt=ei("events");class Ms{constructor(e){Object.defineProperty(this,q,{writable:!0,value:void 0}),Object.defineProperty(this,rt,{writable:!0,value:[]}),ce(this,q)[q]=e}on(e,t){return ce(this,rt)[rt].push([e,t]),ce(this,q)[q].on(e,t)}remove(){for(const[e,t]of ce(this,rt)[rt].splice(0))ce(this,q)[q].off(e,t)}onFilePause(e,t){this.on("upload-pause",(i,s)=>{e===i&&t(s)})}onFileRemove(e,t){this.on("file-removed",i=>{e===i.id&&t(i.id)})}onPause(e,t){this.on("upload-pause",(i,s)=>{e===i&&t(s)})}onRetry(e,t){this.on("upload-retry",i=>{e===i&&t()})}onRetryAll(e,t){this.on("retry-all",()=>{ce(this,q)[q].getFile(e)&&t()})}onPauseAll(e,t){this.on("pause-all",()=>{ce(this,q)[q].getFile(e)&&t()})}onCancelAll(e,t){var i=this;this.on("cancel-all",function(){ce(i,q)[q].getFile(e)&&t(...arguments)})}onResumeAll(e,t){this.on("resume-all",()=>{ce(this,q)[q].getFile(e)&&t()})}}function V(r,e){if(!Object.prototype.hasOwnProperty.call(r,e))throw new TypeError("attempted to use private field on non-instance");return r}var Ls=0;function _t(r){return"__private_"+Ls+++"_"+r}var de=_t("aliveTimer"),Se=_t("isDone"),it=_t("onTimedOut"),Ee=_t("timeout");class xs{constructor(e,t){Object.defineProperty(this,de,{writable:!0,value:void 0}),Object.defineProperty(this,Se,{writable:!0,value:!1}),Object.defineProperty(this,it,{writable:!0,value:void 0}),Object.defineProperty(this,Ee,{writable:!0,value:void 0}),V(this,Ee)[Ee]=e,V(this,it)[it]=()=>t(e)}progress(){V(this,Se)[Se]||V(this,Ee)[Ee]>0&&(clearTimeout(V(this,de)[de]),V(this,de)[de]=setTimeout(V(this,it)[it],V(this,Ee)[Ee]))}done(){V(this,Se)[Se]||(clearTimeout(V(this,de)[de]),V(this,de)[de]=void 0,V(this,Se)[Se]=!0)}}function qs(r){return r?r.readyState!==0&&r.readyState!==4||r.status===0:!1}function kr(r,e){if(!Object.prototype.hasOwnProperty.call(r,e))throw new TypeError("attempted to use private field on non-instance");return r}var Is=0;function ti(r){return"__private_"+Is+++"_"+r}function Cr(r,e){if(qs(r))return new Hr(e,r);const t=new zr("Upload error",{cause:e});return t.request=r,t}function Hs(r){return r.data.slice(0,r.data.size,r.meta.type)}function Ns(r,e,t){(Array.isArray(t.allowedMetaFields)?t.allowedMetaFields:Object.keys(e)).forEach(s=>{r.append(s,e[s])})}function zs(r,e){const t=new FormData;Ns(t,r.meta,e);const i=Hs(r);return r.name?t.append(e.fieldName,i,r.meta.name):t.append(e.fieldName,i),t}const Ks=r=>r.data;var Yt=ti("addEventHandlerForFile"),Zt=ti("addEventHandlerIfFileStillExists");class Bs{constructor(e,t){Object.defineProperty(this,Zt,{value:Ws}),Object.defineProperty(this,Yt,{value:Ds}),this.uppy=e,this.opts={validateStatus(i){return i>=200&&i<300},...t},this.requests=t[Jr],this.uploaderEvents=Object.create(null),this.i18n=t.i18n}getOptions(e){var t;const{uppy:i}=this,s=i.getState().xhrUpload;return{...this.opts,...s||{},...e.xhrUpload||{},headers:{...this.opts.headers,...s==null?void 0:s.headers,...(t=e.xhrUpload)==null?void 0:t.headers}}}uploadLocalFile(e){const t=this.getOptions(e);return new Promise((i,s)=>{const n=t.formData?zs(e,t):Ks(e),o=new XMLHttpRequest;this.uploaderEvents[e.id]=new Ms(this.uppy);const a=new xs(t.timeout,()=>{o.abort(),l.done();const c=new Error(this.i18n("timedOut",{seconds:Math.ceil(t.timeout/1e3)}));this.uppy.emit("upload-error",e,c),s(c)}),u=_i();o.upload.addEventListener("loadstart",()=>{this.uppy.log(`[AwsS3/XHRUpload] ${u} started`)}),o.upload.addEventListener("progress",c=>{this.uppy.log(`[AwsS3/XHRUpload] ${u} progress: ${c.loaded} / ${c.total}`),a.progress(),c.lengthComputable&&this.uppy.emit("upload-progress",this.uppy.getFile(e.id),{uploader:this,bytesUploaded:c.loaded,bytesTotal:c.total})}),o.addEventListener("load",c=>{if(this.uppy.log(`[AwsS3/XHRUpload] ${u} finished`),a.done(),l.done(),this.uploaderEvents[e.id]&&(this.uploaderEvents[e.id].remove(),this.uploaderEvents[e.id]=null),t.validateStatus(c.target.status,o.responseText,o)){const m=t.getResponseData(o.responseText,o),g=m[t.responseUrlFieldName],w={status:c.target.status,body:m,uploadURL:g};return this.uppy.emit("upload-success",this.uppy.getFile(e.id),w),g&&this.uppy.log(`Download ${e.name} from ${g}`),i(e)}const d=t.getResponseData(o.responseText,o),f=Cr(o,t.getResponseError(o.responseText,o)),p={status:c.target.status,body:d};return this.uppy.emit("upload-error",e,f,p),s(f)}),o.addEventListener("error",()=>{this.uppy.log(`[AwsS3/XHRUpload] ${u} errored`),a.done(),l.done(),this.uploaderEvents[e.id]&&(this.uploaderEvents[e.id].remove(),this.uploaderEvents[e.id]=null);const c=Cr(o,t.getResponseError(o.responseText,o));return this.uppy.emit("upload-error",e,c),s(c)}),o.open(t.method.toUpperCase(),t.endpoint,!0),o.withCredentials=!!t.withCredentials,t.responseType!==""&&(o.responseType=t.responseType),Object.keys(t.headers).forEach(c=>{o.setRequestHeader(c,t.headers[c])});const l=this.requests.run(()=>(o.send(n),()=>{a.done(),o.abort()}),{priority:1});kr(this,Yt)[Yt]("file-removed",e.id,()=>{l.abort(),s(new Error("File removed"))}),kr(this,Zt)[Zt]("cancel-all",e.id,function(c){let{reason:d}=c===void 0?{}:c;d==="user"&&l.abort(),s(new Error("Upload cancelled"))})})}}function Ds(r,e,t){this.uploaderEvents[e].on(r,i=>{var s;const n=(s=i==null?void 0:i.id)!=null?s:i;e===n&&t()})}function Ws(r,e,t){var i=this;this.uploaderEvents[e].on(r,function(){i.uppy.getFile(e)&&t(...arguments)})}function Xs(r){return r.replace(/;.*$/,"")}function ri(r,e){const t=e.headers?e.headers["content-type"]:e.getResponseHeader("Content-Type");if(typeof t=="string"){const i=Xs(t).toLowerCase();if(i==="application/xml"||i==="text/xml"||i==="text/html"&&/^<\?xml /.test(r))return!0}return!1}const Js={strings:{timedOut:"Upload stalled for %{seconds} seconds, aborting."}};let ii;function U(r,e){if(!Object.prototype.hasOwnProperty.call(r,e))throw new TypeError("attempted to use private field on non-instance");return r}var Gs=0;function xe(r){return"__private_"+Gs+++"_"+r}const Qs={version:"3.6.2"};function Vs(r,e){return!r&&!e.startsWith("https://")&&!e.startsWith("http://")&&(e=`https://${e}`),new URL(e,r||void 0).toString()}function lt(r,e){const t=r.indexOf(`<${e}>`),i=r.indexOf(`</${e}>`,t);return t!==-1&&i!==-1?r.slice(t+e.length+2,i):""}function Ys(r){if(r&&r.error){const e=new Error(r.message);throw Object.assign(e,r.error),e}return r}function Zs(r,e){if(!(e!=null&&typeof e.url=="string"&&(typeof e.fields=="object"||e.fields==null)))throw new TypeError(`AwsS3: got incorrect result from 'getUploadParameters()' for file '${r.name}', expected an object '{ url, method, fields, headers }' but got '${JSON.stringify(e)}' instead.
See https://uppy.io/docs/aws-s3/#getUploadParameters-file for more on the expected format.`);if(!(e.method==null||/^p(u|os)t$/i.test(e.method)))throw new TypeError(`AwsS3: got incorrect method from 'getUploadParameters()' for file '${r.name}', expected  'PUT' or 'POST' but got '${e.method}' instead.
See https://uppy.io/docs/aws-s3/#getUploadParameters-file for more on the expected format.`)}function en(r,e){if(!ri(r,e))return;const t=lt(r,"Message");return new Error(t)}let Fr=!1;var W=xe("client"),re=xe("requests"),Oe=xe("uploader"),st=xe("handleUpload"),nt=xe("setCompanionHeaders"),er=xe("getCompanionClientArgs");ii=Symbol.for("uppy test: getClient");class tn extends xr{constructor(e,t){if((t==null?void 0:t.shouldUseMultipart)!=null)return new Pt(e,t);super(e,t),Object.defineProperty(this,W,{writable:!0,value:void 0}),Object.defineProperty(this,re,{writable:!0,value:void 0}),Object.defineProperty(this,Oe,{writable:!0,value:void 0}),Object.defineProperty(this,st,{writable:!0,value:async s=>{const n=Object.create(null);function o(f){var p;const{id:m}=f;(p=n[m])==null||p.abort()}this.uppy.on("file-removed",o);const a=this.uppy.getFilesByIds(s),u=Gr(a),l=Qr(u);this.uppy.emit("upload-start",l);const c=U(this,re)[re].wrapPromiseFunction(f=>this.opts.getUploadParameters(f)),d=s.length;return Promise.allSettled(s.map((f,p)=>(n[f]=c(this.uppy.getFile(f)),n[f].then(m=>{delete n[f];const g=this.uppy.getFile(f);Zs(g,m);const{method:w="POST",url:P,fields:k,headers:D}=m,O={method:w,formData:w.toUpperCase()==="POST",endpoint:P,allowedMetaFields:k?Object.keys(k):[]};return D&&(O.headers=D),this.uppy.setFileState(g.id,{meta:{...g.meta,...k},xhrUpload:O}),this.uploadFile(g.id,p,d)}).catch(m=>{delete n[f];const g=this.uppy.getFile(f);return this.uppy.emit("upload-error",g,m),Promise.reject(m)})))).finally(()=>{this.uppy.off("file-removed",o)})}}),Object.defineProperty(this,nt,{writable:!0,value:()=>(U(this,W)[W].setCompanionHeaders(this.opts.companionHeaders),Promise.resolve())}),Object.defineProperty(this,er,{writable:!0,value:s=>{const n=U(this,Oe)[Oe].getOptions(s),o=Array.isArray(n.allowedMetaFields)?n.allowedMetaFields:Object.keys(s.meta);return{...s.remote.body,protocol:"multipart",endpoint:n.endpoint,size:s.data.size,fieldname:n.fieldName,metadata:Object.fromEntries(o.map(a=>[a,s.meta[a]])),httpMethod:n.method,useFormData:n.formData,headers:typeof n.headers=="function"?n.headers(s):n.headers}}}),this.type="uploader",this.id=this.opts.id||"AwsS3",this.title="AWS S3",this.defaultLocale=Js;const i={timeout:30*1e3,limit:0,allowedMetaFields:[],getUploadParameters:this.getUploadParameters.bind(this),shouldUseMultipart:!1,companionHeaders:{}};if(this.opts={...i,...t},(t==null?void 0:t.allowedMetaFields)===void 0&&"metaFields"in this.opts)throw new Error("The `metaFields` option has been renamed to `allowedMetaFields`.");this.i18nInit(),U(this,W)[W]=new ft(e,t),U(this,re)[re]=new Xr(this.opts.limit)}[ii](){return U(this,W)[W]}get client(){return U(this,W)[W]}set client(e){U(this,W)[W]=e}getUploadParameters(e){if(!this.opts.companionUrl)throw new Error("Expected a `companionUrl` option containing a Companion address.");const t=e.meta.name,{type:i}=e.meta,s=Object.fromEntries(this.opts.allowedMetaFields.filter(o=>e.meta[o]!=null).map(o=>[`metadata[${o}]`,e.meta[o].toString()])),n=new URLSearchParams({filename:t,type:i,...s});return U(this,W)[W].get(`s3/params?${n}`).then(Ys)}uploadFile(e,t,i){const s=this.uppy.getFile(e);if(this.uppy.log(`uploading ${t} of ${i}`),s.error)throw new Error(s.error);if(s.isRemote){const n=()=>U(this,re)[re],o=new AbortController,a=l=>{l.id===s.id&&o.abort()};this.uppy.on("file-removed",a);const u=this.uppy.getRequestClientForFile(s).uploadRemoteFile(s,U(this,er)[er](s),{signal:o.signal,getQueue:n});return U(this,re)[re].wrapSyncFunction(()=>{this.uppy.off("file-removed",a)},{priority:-1})(),u}return U(this,Oe)[Oe].uploadLocalFile(s,t,i)}install(){const{uppy:e}=this;e.addPreProcessor(U(this,nt)[nt]),e.addUploader(U(this,st)[st]);function t(s,n){const o=this;return ri(s,n)?{location:Vs(n.responseURL,lt(s,"Location")),bucket:lt(s,"Bucket"),key:lt(s,"Key"),etag:lt(s,"ETag")}:o.method.toUpperCase()==="POST"?(Fr||(e.log("[AwsS3] No response data found, make sure to set the success_action_status AWS SDK option to 201. See https://uppy.io/docs/aws-s3/#POST-Uploads","warning"),Fr=!0),{location:null}):n.responseURL?{location:n.responseURL.replace(/\?.*$/,"")}:{location:null}}const i={fieldName:"file",responseUrlFieldName:"location",timeout:this.opts.timeout,[Jr]:U(this,re)[re],responseType:"text",getResponseData:this.opts.getResponseData||t,getResponseError:en};i.i18n=this.i18n,U(this,Oe)[Oe]=new Bs(e,i)}uninstall(){this.uppy.removePreProcessor(U(this,nt)[nt]),this.uppy.removeUploader(U(this,st)[st])}}tn.VERSION=Qs.version;export{xr as B,is as E,Hr as N,sn as P,Xr as R,nn as S,Qr as a,xs as b,Jr as c,mi as d,ft as e,Gr as f,on as g,Nr as h,qs as i,ji as j,zr as k,Si as l,_i as n,fi as r,an as t};
