import React, { useState } from "react";
import { GlobalContext, showToast } from "../../globalContext";
import { <PERSON>lipLoader } from "react-spinners";
import { CardElement, useStripe, useElements } from "@stripe/react-stripe-js";
import { InteractiveButton } from "../InteractiveButton";
import MkdSDK from "../../utils/MkdSDK";

// Separate payment form component
const PaymentForm = ({
  onSubmit,
  isLoading,
  couponCode,
  setCouponCode,
  getCurrentPrice,
}) => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const stripe = useStripe();
  const elements = useElements();
  const [cardReady, setCardReady] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!stripe || !elements) {
      showToast(
        globalDispatch,
        "Payment system not ready. Please try again.",
        4000,
        "error"
      );
      return;
    }

    try {
      // Get the card element
      const cardElement = elements.getElement(CardElement);

      if (!cardElement) {
        showToast(
          globalDispatch,
          "Card information not found. Please refresh and try again.",
          4000,
          "error"
        );
        return;
      }

      // Create Stripe token
      const result = await stripe.createToken(cardElement);
      onSubmit(result);
    } catch (error) {
      console.error("Payment form error:", error);
      showToast(
        globalDispatch,
        error.message || "Payment failed. Please try again.",
        4000,
        "error"
      );
    }
  };

  return (
    <div className="mt-8 rounded-lg border border-gray-600 bg-gray-900/20 p-6">
      <h3 className="mb-6 text-lg font-semibold text-white">
        Payment Information
      </h3>

      <form onSubmit={handleSubmit}>
        {/* Stripe Card Element */}
        <div className="mb-6">
          <label className="mb-3 block text-sm font-medium text-white">
            Card Details
          </label>
          <div className="rounded-lg border border-form-strokedark bg-form-input p-4">
            <CardElement
              options={{
                hidePostalCode: true,
                style: {
                  base: {
                    fontSize: "16px",
                    color: "#E5E7EB",
                    fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
                    fontSmoothing: "antialiased",
                    "::placeholder": {
                      color: "#9CA3AF",
                    },
                  },
                  invalid: {
                    color: "#fa755a",
                    iconColor: "#fa755a",
                  },
                },
              }}
              onReady={() => {
                console.log("CardElement is ready");
                setCardReady(true);
              }}
              onChange={(event) => {
                if (event.error) {
                  console.error("CardElement error:", event.error);
                }
              }}
            />
          </div>
        </div>

        {/* Coupon Code */}
        <div className="mb-4">
          <label
            htmlFor="couponCode"
            className="mb-2 block text-sm font-medium text-white"
          >
            Coupon Code (Optional)
          </label>
          <input
            type="text"
            id="couponCode"
            value={couponCode}
            onChange={(e) => setCouponCode(e.target.value)}
            placeholder="Enter coupon code if you have one"
            className="w-full rounded-lg border border-form-strokedark bg-form-input p-3 text-white outline-none focus:border-primary"
          />
        </div>

        {/* Submit Button */}
        <InteractiveButton
          type="submit"
          loading={isLoading}
          disabled={isLoading || !cardReady || !stripe || !elements}
          className="w-full rounded-lg bg-primary px-8 py-3 text-white hover:bg-primary/80"
        >
          {isLoading
            ? "Processing..."
            : !cardReady
            ? "Loading payment form..."
            : `Complete Payment - $${getCurrentPrice()}`}
        </InteractiveButton>

        {/* Security Notice */}
        <div className="mt-4 flex items-center text-xs text-gray-400">
          <svg
            className="mr-2 h-4 w-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
            />
          </svg>
          Your payment information is secure and encrypted
        </div>
      </form>
    </div>
  );
};

const EarlySubscriptionModal = ({ isOpen, onSubscriptionComplete }) => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedProjectRange, setSelectedProjectRange] = useState("1-50");
  const [selectedInterval, setSelectedInterval] = useState("monthly");
  const [selectedPlan, setSelectedPlan] = useState("portal");
  const [couponCode, setCouponCode] = useState("");

  // Project ranges and plan data from MemberSubscriptionPage
  const projectRanges = {
    "1-50": {
      portal: { monthly: 150, annual: 1500 },
      studio: { monthly: 150, annual: 1500 },
      complete: { monthly: 250, annual: 2500 },
    },
    "51-100": {
      portal: { monthly: 175, annual: 1750 },
      studio: { monthly: 175, annual: 1750 },
      complete: { monthly: 275, annual: 2750 },
    },
    "101-150": {
      portal: { monthly: 200, annual: 2000 },
      studio: { monthly: 200, annual: 2000 },
      complete: { monthly: 300, annual: 3000 },
    },
    "151-200": {
      portal: { monthly: 225, annual: 2250 },
      studio: { monthly: 225, annual: 2250 },
      complete: { monthly: 325, annual: 3250 },
    },
    "201+": {
      portal: { monthly: 250, annual: 2500 },
      studio: { monthly: 250, annual: 2500 },
      complete: { monthly: 350, annual: 3500 },
    },
  };

  const planFeatures = {
    "The Portal": [
      "Project Management",
      "Project Calendar",
      "Client Login Portal",
      "Digital 8-count sheets",
      "Automated Music Licenses",
      "Automated Reminder Emails",
      "Automated Music Surveys",
      "Project Edit Management",
      "8-Count Track Management",
      "Custom Email Domain",
    ],
    "The Studio": [
      "Automated Music Surveys",
      "Project Management",
      "Project Calendar",
      "Project Budget Review",
      "Automated Vocal Orders",
      "Excel Style Order View",
      "Automated Reminder Emails",
      "Company Logo Customization",
      "Custom Email Domain",
    ],
    "Complete Suite": [
      "Everything in The Portal",
      "Everything in The Studio",
      "Priority Support",
      "Dedicated Account Manager",
    ],
  };

  const planKeys = {
    "The Portal": "portal",
    "The Studio": "studio",
    "Complete Suite": "complete",
  };

  const plans = Object.keys(planFeatures);

  const getCurrentPrice = () => {
    return projectRanges[selectedProjectRange][selectedPlan][selectedInterval];
  };

  const handleContinueToPayment = () => {
    setPaymentStep(true);
  };

  const handlePaymentSubmit = async (result) => {
    if (result.error) {
      showToast(globalDispatch, result.error.message, 4000, "error");
      return;
    }

    try {
      setIsLoading(true);
      const sdk = new MkdSDK();

      // Create or update Stripe customer with the card token
      const customerParams = {
        cardToken: result.token.id,
      };

      await sdk.createStripeCustomer(customerParams);

      // First, get the products to find the correct product ID
      const productsResult = await sdk.callRawAPI(
        `/v4/api/records/stripe_product?page=${1},${100}`,
        [],
        "GET"
      );

      let productId = null;
      if (!productsResult.error && productsResult.list) {
        // Map plan keys to product names
        const planNameMap = {
          portal: "The Portal",
          studio: "The Studio",
          complete: "Complete Suite",
        };

        // Find the product that matches our selected plan
        const targetProduct = productsResult.list.find(
          (product) => product.name === planNameMap[selectedPlan]
        );

        if (targetProduct) {
          productId = targetProduct.id;
        }
      }

      if (!productId) {
        throw new Error("Could not find the selected product");
      }

      // Get the appropriate price ID based on selected plan, project range, and interval

      // Create subscription with the correct product ID and price ID
      const subscriptionPayload = {
        planId: productId, // Use the actual product ID from stripe_product
      };

      // Add coupon if provided
      if (couponCode && couponCode.trim() !== "") {
        subscriptionPayload.coupon = couponCode.trim();
      }

      const subscriptionResult = await sdk.createStripeSubscription(
        subscriptionPayload
      );

      if (!subscriptionResult.error) {
        showToast(
          globalDispatch,
          "Successfully subscribed! Continuing with setup...",
          4000,
          "success"
        );

        // Update localStorage
        localStorage.setItem("is_plan", true);

        onSubscriptionComplete();
      } else {
        throw new Error(
          subscriptionResult.message || "Failed to create subscription"
        );
      }
    } catch (error) {
      console.error("Subscription error:", error);
      showToast(
        globalDispatch,
        error.message || "Subscription failed. Please try again.",
        4000,
        "error"
      );
    } finally {
      setIsLoading(false);
    }
  };

  const [paymentStep, setPaymentStep] = useState(false);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="fixed  max-h-[90vh] w-full max-w-4xl overflow-y-auto">
        <div className="rounded-lg bg-boxdark shadow-xl">
          {/* Header */}
          <div className="border-b border-strokedark p-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-white">
                Choose Your Plan
              </h2>
              <p className="mt-2 text-gray-400">
                Select a subscription plan to unlock all features and get
                started
              </p>
            </div>
          </div>

          {/* Content */}
          <div className="p-6">
            {isLoading ? (
              <div className="flex h-64 items-center justify-center">
                <ClipLoader color="#3B82F6" size={50} />
              </div>
            ) : (
              <>
                {/* Project Range Selection */}
                <div className="mb-8">
                  <h3 className="mb-4 text-lg font-semibold text-white">
                    How many projects do you expect per month?
                  </h3>
                  <div className="grid grid-cols-5 gap-4">
                    {Object.keys(projectRanges).map((range) => (
                      <button
                        key={range}
                        onClick={() => setSelectedProjectRange(range)}
                        className={`rounded-lg border-2 p-4 text-center transition-all ${
                          selectedProjectRange === range
                            ? "border-primary bg-primary/10"
                            : "border-gray-600 hover:border-gray-500"
                        }`}
                      >
                        <span className="font-medium text-white">{range}</span>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Billing Interval Toggle */}
                <div className="mb-8 flex justify-center">
                  <div className="flex rounded-lg bg-gray-800 p-1">
                    <button
                      onClick={() => setSelectedInterval("monthly")}
                      className={`rounded-md px-4 py-2 text-sm font-medium transition-all ${
                        selectedInterval === "monthly"
                          ? "bg-boxdark text-primary shadow-sm"
                          : "text-gray-400 hover:text-gray-200"
                      }`}
                    >
                      Monthly
                    </button>
                    <button
                      onClick={() => setSelectedInterval("annual")}
                      className={`rounded-md px-4 py-2 text-sm font-medium transition-all ${
                        selectedInterval === "annual"
                          ? "bg-boxdark text-primary shadow-sm"
                          : "text-gray-400 hover:text-gray-200"
                      }`}
                    >
                      Annual
                      <span className="ml-1 rounded-full bg-green-900/20 px-2 py-0.5 text-xs text-green-400">
                        Save 20%
                      </span>
                    </button>
                  </div>
                </div>

                {/* Plans Grid */}
                <div className="grid gap-6 md:grid-cols-3">
                  {plans.map((planName) => {
                    const planKey = planKeys[planName];
                    const price =
                      projectRanges[selectedProjectRange][planKey][
                        selectedInterval
                      ];

                    return (
                      <div
                        key={planName}
                        onClick={() => setSelectedPlan(planKey)}
                        className={`cursor-pointer rounded-lg border-2 p-6 transition-all ${
                          selectedPlan === planKey
                            ? "border-primary bg-primary/10"
                            : "border-gray-600 hover:border-gray-500"
                        }`}
                      >
                        <div className="text-center">
                          <h3 className="text-lg font-semibold text-white">
                            {planName}
                          </h3>
                          <div className="mt-4">
                            <span className="text-3xl font-bold text-white">
                              ${price}
                            </span>
                            <span className="text-gray-400">
                              /
                              {selectedInterval === "monthly"
                                ? "month"
                                : "year"}
                            </span>
                          </div>

                          {/* Features */}
                          <ul className="mt-6 space-y-2 text-left text-sm">
                            {planFeatures[planName]
                              .slice(0, 5)
                              .map((feature, index) => (
                                <li key={index} className="flex items-center">
                                  <svg
                                    className="mr-2 h-4 w-4 text-green-500"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M5 13l4 4L19 7"
                                    />
                                  </svg>
                                  <span className="text-gray-400">
                                    {feature}
                                  </span>
                                </li>
                              ))}
                            {planFeatures[planName].length > 5 && (
                              <li className="text-xs text-gray-500">
                                +{planFeatures[planName].length - 5} more
                                features
                              </li>
                            )}
                          </ul>
                        </div>
                      </div>
                    );
                  })}
                </div>

                {/* Payment Form */}
                {paymentStep ? (
                  <PaymentForm
                    onSubmit={handlePaymentSubmit}
                    isLoading={isLoading}
                    couponCode={couponCode}
                    setCouponCode={setCouponCode}
                    getCurrentPrice={getCurrentPrice}
                  />
                ) : (
                  /* Action Buttons */
                  <div className="mt-8 flex justify-center gap-4">
                    <button
                      onClick={handleContinueToPayment}
                      className="rounded-lg bg-primary px-8 py-3 text-white transition hover:bg-opacity-90"
                    >
                      Continue to Payment
                    </button>
                  </div>
                )}

                {/* Back button when in payment step */}
                {paymentStep && (
                  <div className="mt-4 flex justify-center">
                    <button
                      onClick={() => setPaymentStep(false)}
                      className="rounded-lg border border-gray-600 px-8 py-3 text-gray-300 transition hover:bg-gray-800"
                    >
                      Back to Plans
                    </button>
                  </div>
                )}

                <div className="mt-4 text-center">
                  <p className="text-xs text-gray-500">
                    {paymentStep ? (
                      <>
                        <svg
                          className="mr-1 inline h-3 w-3"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                          />
                        </svg>
                        Secure payment processing powered by Stripe
                      </>
                    ) : (
                      "You can change or cancel your subscription at any time"
                    )}
                  </p>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EarlySubscriptionModal;
