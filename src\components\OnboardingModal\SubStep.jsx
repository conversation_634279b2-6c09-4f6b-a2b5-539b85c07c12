import React, { useState } from "react";

// Figma plan data (static for design, replace with backend data as needed)
const PLAN_DATA = [
  {
    key: "portal",
    label: "The Portal",
    icon: "/figma-icons/icon-sparkles.svg",
    description:
      "Best for teams who want a client portal and project management.",
    features: [
      "Project Management",
      "Project Calendar",
      "Client Login Portal",
      "Digital 8-count sheets",
      "Automated Music Licenses",
      "Automated Reminder Emails",
      "Automated Music Surveys",
      "Project Edit Management",
      "8-Count Track Management",
      "Custom Email Domain",
    ],
    price: { monthly: 225, annual: 2250 },
  },
  {
    key: "studio",
    label: "The Studio",
    icon: "/figma-icons/icon-file-earmark-text.svg",
    description: "For studios needing advanced music survey and order tools.",
    features: [
      "Automated Music Surveys",
      "Project Management",
      "Project Calendar",
      "Project Budget Review",
      "Automated Vocal Orders",
      "Excel Style Order View",
      "Automated Reminder Emails",
      "Company Logo Customization",
      "Custom Email Domain",
    ],
    price: { monthly: 225, annual: 2250 },
  },
  {
    key: "complete",
    label: "Complete Suite",
    icon: "/figma-icons/icon-credit-card.svg",
    description: "All features, priority support, and dedicated manager.",
    features: [
      "Everything In The Portal",
      "Everything In The Studio",
      "Priority Support",
      "Dedicated Account Manager",
    ],
    price: { monthly: 325, annual: 3250 },
  },
];

const SubStep = ({ plans = PLAN_DATA, onSelect }) => {
  const [selectedInterval, setSelectedInterval] = useState("annual");
  const [selectedPlan, setSelectedPlan] = useState(null);

  // Plan List View
  if (!selectedPlan) {
    return (
      <div className="mx-auto w-full max-w-3xl">
        <div className="mb-8 text-center">
          <h2 className="mb-2 text-3xl font-bold text-[#131E2B]">
            Choose Your Plan
          </h2>
          <p className="text-lg text-[#667484]">
            Select the best plan for your business needs.
          </p>
        </div>
        <div className="mb-8 flex justify-center gap-4">
          <div className="flex items-center gap-2">
            <span className="font-medium text-[#4A5B70]">Show pricing for</span>
            <select
              className="rounded-lg border border-[#D5D7DA] bg-white px-4 py-2 text-sm font-semibold text-[#414651] shadow-sm"
              value={selectedInterval}
              onChange={(e) => setSelectedInterval(e.target.value)}
            >
              <option value="annual">Annual</option>
              <option value="monthly">Monthly</option>
            </select>
          </div>
        </div>
        <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
          {plans.map((plan) => (
            <div
              key={plan.key}
              className="flex cursor-pointer flex-col rounded-2xl border border-[#D1D6DE] bg-white p-8 shadow-lg transition hover:border-[#3C50E0] hover:shadow-xl"
            >
              <div className="mb-4 flex items-center justify-between">
                <span className="text-2xl font-bold text-[#3C50E0]">
                  {plan.label}
                </span>
                <img src={plan.icon} alt="icon" className="h-8 w-8" />
              </div>
              <div className="mb-2 min-h-[40px] text-lg text-[#667484]">
                {plan.description}
              </div>
              <div className="mb-4 flex items-end gap-1">
                <span className="text-3xl font-bold text-[#131E2B]">
                  ${plan.price[selectedInterval]}
                </span>
                <span className="text-base text-[#6F6C90]">
                  /{selectedInterval === "annual" ? "year" : "month"}
                </span>
              </div>
              <ul className="mb-6 space-y-2">
                {plan.features.slice(0, 5).map((feature, i) => (
                  <li
                    key={i}
                    className="flex items-center gap-2 text-sm text-[#131E2B]"
                  >
                    <svg
                      className="h-4 w-4 text-[#3C50E0]"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 16 16"
                    >
                      <path
                        d="M4 8l2 2 4-4"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                    {feature}
                  </li>
                ))}
                {plan.features.length > 5 && (
                  <li className="text-xs text-[#667484]">...and more</li>
                )}
              </ul>
              <button
                className="mt-auto w-full rounded-full bg-[#3C50E0] py-3 text-base font-semibold text-white transition hover:bg-[#2B3EB4]"
                onClick={() => setSelectedPlan(plan.key)}
              >
                Subscribe Now
              </button>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Plan Details View
  const plan = plans.find((p) => p.key === selectedPlan);
  return (
    <div className="mx-auto w-full max-w-xl">
      <button
        className="mb-6 flex items-center text-[#3C50E0] hover:underline"
        onClick={() => setSelectedPlan(null)}
      >
        <svg
          className="mr-2 h-5 w-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M15 19l-7-7 7-7"
          />
        </svg>
        Back to Plans
      </button>
      <div className="rounded-2xl border border-[#D1D6DE] bg-white p-10 shadow-xl">
        <div className="mb-4 flex items-center gap-4">
          <img src={plan.icon} alt="icon" className="h-10 w-10" />
          <h2 className="text-2xl font-bold text-[#131E2B]">{plan.label}</h2>
        </div>
        <div className="mb-2 text-lg text-[#667484]">{plan.description}</div>
        <div className="mb-6 flex items-end gap-1">
          <span className="text-4xl font-bold text-[#3C50E0]">
            ${plan.price[selectedInterval]}
          </span>
          <span className="text-base text-[#6F6C90]">
            /{selectedInterval === "annual" ? "year" : "month"}
          </span>
        </div>
        <div className="mb-6">
          <h4 className="mb-2 font-semibold text-[#131E2B]">Features:</h4>
          <ul className="space-y-2">
            {plan.features.map((feature, i) => (
              <li
                key={i}
                className="flex items-center gap-2 text-sm text-[#131E2B]"
              >
                <svg
                  className="h-4 w-4 text-[#3C50E0]"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 16 16"
                >
                  <path
                    d="M4 8l2 2 4-4"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                {feature}
              </li>
            ))}
          </ul>
        </div>
        <button
          className="w-full rounded-full bg-[#3C50E0] py-3 text-base font-semibold text-white transition hover:bg-[#2B3EB4]"
          onClick={() => onSelect && onSelect(plan)}
        >
          Continue
        </button>
      </div>
    </div>
  );
};

export default SubStep;
