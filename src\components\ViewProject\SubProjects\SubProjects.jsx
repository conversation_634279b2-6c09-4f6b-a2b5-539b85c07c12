import React from "react";
import SubProject from "../SubProject/SubProject";
import CompletedSubProject from "../SubProject/CompletedSubProject";
import Tracking from "../Tracking/Tracking";
import Upload from "../SubProject/Upload";

const SubProjects = ({
  theme,
  authState,
  mixSeasons,
  projectId,
  setTempUploadCount,
  tempUploadCount,
  UploadCount,
  expandAll,
  isEdit,
  ideas,
  subProjects,
  writers,
  artists,
  engineers,
  producers,
  programName,
  surveySubmitStatus,
  setLyrics,
  setUpdateSubprojectPayload,
  setEightCountPayload,
  setWriterPayload,
  setWriterCostPayload,
  setArtistPayload,
  setArtistCostPayload,
  setProducerPayload,
  setProducerCostPayload,
  setDeleteIdeaPayload,
  setSelectedSubProjectId,
  setSelectedSubProjectIdForDelete,
  setUnSelectedSubProjectIdForDelete,
  setShowAssignIdeaModal,
  setInCompleteSubProjectId,
  setDeleteFileId,
  setLoadIdeaFromViewProject,
  setResetWriterPayload,
  setResetArtistPayload,
}) => {
  console.log(mixSeasons, "djdhd");
  console.log(subProjects, "subProjects");
  return (
    <>
      {subProjects &&
        subProjects.length > 0 &&
        subProjects.map((subProject, index) => {
          let employees = [subProject.employees];
          let writer = [];
          let artist = [];
          let engineer = [];
          let producer = [];
          if (employees.length > 0) {
            for (let i = 0; i < employees.length; i++) {
              writer = employees[i].filter(
                (employee) =>
                  employee.is_writer && employee.emp_type === "writer"
              );
              artist = employees[i].filter(
                (employee) =>
                  employee.is_artist && employee.emp_type === "artist"
              );
              engineer = employees[i].filter(
                (employee) =>
                  employee.is_engineer && employee.emp_type === "engineer"
              );
              producer = employees[i].filter(
                (employee) =>
                  employee.is_producer && employee.emp_type === "producer"
              );
            }
          }
          //
          //
          //

          // First ensure subProject.type exists and is a string
          let subProjectType = "";
          if (subProject && subProject.type) {
            // remove numbers and trim spaces
            subProjectType = subProject.type.replace(/[0-9]/g, "").trim();

            // Check if it contains "Upload" first
            if (subProjectType.toLowerCase().includes("upload")) {
              subProjectType = "Upload";
            }
            // Only check for Voiceover if it's not an Upload
            else if (
              subProjectType.toLowerCase().startsWith("voiceover") &&
              !subProjectType.toLowerCase().includes("upload")
            ) {
              subProjectType = "Voiceover";
            }

            // Remove any remaining spaces
            subProjectType = subProjectType.replace(/\s/g, "");
          }

          console.log("Processed type:", subProjectType);
          if (subProjectType === "Voiceover") {
            console.log(subProjectType);
            if (Number(subProject.status) === 0) {
              return (
                <SubProject
                  mixSeasons={mixSeasons}
                  key={index}
                  theme={theme}
                  UploadCount={UploadCount}
                  setTempUploadCount={setTempUploadCount}
                  isSong={false}
                  tempUploadCount={tempUploadCount}
                  setUpdateSubprojectPayload={setUpdateSubprojectPayload}
                  expandAll={expandAll}
                  isEdit={isEdit}
                  ideas={ideas}
                  projectId={projectId}
                  writers={writers}
                  artists={artists}
                  engineers={engineers}
                  writer={writer}
                  artist={artist}
                  engineer={engineer}
                  surveySubmitStatus={surveySubmitStatus}
                  setLyrics={setLyrics}
                  setEightCountPayload={setEightCountPayload}
                  setWriterPayload={setWriterPayload}
                  setWriterCostPayload={setWriterCostPayload}
                  setArtistPayload={setArtistPayload}
                  setArtistCostPayload={setArtistCostPayload}
                  setDeleteIdeaPayload={setDeleteIdeaPayload}
                  subProject={subProject}
                  setShowAssignIdeaModal={setShowAssignIdeaModal}
                  setSelectedSubProjectId={setSelectedSubProjectId}
                  setSelectedSubProjectIdForDelete={
                    setSelectedSubProjectIdForDelete
                  }
                  setUnSelectedSubProjectIdForDelete={
                    setUnSelectedSubProjectIdForDelete
                  }
                  setDeleteFileId={setDeleteFileId}
                  setLoadIdeaFromViewProject={setLoadIdeaFromViewProject}
                  setResetWriterPayload={setResetWriterPayload}
                  setResetArtistPayload={setResetArtistPayload}
                />
              );
            } else if (Number(subProject.status) === 1) {
              return (
                <CompletedSubProject
                  key={index}
                  subProjectId={subProject.id}
                  authState={authState}
                  writers={writers}
                  artists={artists}
                  engineers={engineers}
                  writer={writer}
                  artist={artist}
                  engineer={engineer}
                  eightCount={subProject.eight_count}
                  subProject={subProject}
                  surveySubmitStatus={surveySubmitStatus}
                  setInCompleteSubProjectId={setInCompleteSubProjectId}
                />
              );
            }
          }
          if (subProjectType === "Upload") {
            console.log(subProjectType);
            if (Number(subProject.status) === 0) {
              return (
                <Upload
                  key={index}
                  theme={theme}
                  mixSeasons={mixSeasons}
                  UploadCount={UploadCount}
                  setTempUploadCount={setTempUploadCount}
                  isSong={false}
                  tempUploadCount={tempUploadCount}
                  setUpdateSubprojectPayload={setUpdateSubprojectPayload}
                  expandAll={expandAll}
                  isEdit={isEdit}
                  ideas={ideas}
                  projectId={projectId}
                  writers={writers}
                  artists={artists}
                  engineers={engineers}
                  writer={writer}
                  artist={artist}
                  engineer={engineer}
                  surveySubmitStatus={surveySubmitStatus}
                  setLyrics={setLyrics}
                  setEightCountPayload={setEightCountPayload}
                  setWriterPayload={setWriterPayload}
                  setWriterCostPayload={setWriterCostPayload}
                  setArtistPayload={setArtistPayload}
                  setArtistCostPayload={setArtistCostPayload}
                  setDeleteIdeaPayload={setDeleteIdeaPayload}
                  subProject={subProject}
                  setShowAssignIdeaModal={setShowAssignIdeaModal}
                  setSelectedSubProjectId={setSelectedSubProjectId}
                  setSelectedSubProjectIdForDelete={
                    setSelectedSubProjectIdForDelete
                  }
                  setUnSelectedSubProjectIdForDelete={
                    setUnSelectedSubProjectIdForDelete
                  }
                  setDeleteFileId={setDeleteFileId}
                  setLoadIdeaFromViewProject={setLoadIdeaFromViewProject}
                  setResetWriterPayload={setResetWriterPayload}
                  setResetArtistPayload={setResetArtistPayload}
                />
              );
            } else if (Number(subProject.status) === 1) {
              return (
                <CompletedSubProject
                  key={index}
                  subProjectId={subProject.id}
                  authState={authState}
                  writers={writers}
                  artists={artists}
                  engineers={engineers}
                  writer={writer}
                  artist={artist}
                  engineer={engineer}
                  eightCount={subProject.eight_count}
                  subProject={subProject}
                  surveySubmitStatus={surveySubmitStatus}
                  setInCompleteSubProjectId={setInCompleteSubProjectId}
                />
              );
            }
          } else if (subProjectType === "Tracking") {
            return (
              <Tracking
                key={index}
                authState={authState}
                isEdit={isEdit}
                expandAll={expandAll}
                subProject={subProject}
                subProjectId={subProject.id}
                producers={producers}
                producer={producer}
                programName={programName}
                surveySubmitStatus={surveySubmitStatus}
                setProducerPayload={setProducerPayload}
                setProducerCostPayload={setProducerCostPayload}
                setEightCountPayload={setEightCountPayload}
                setInCompleteSubProjectId={setInCompleteSubProjectId}
                setSelectedSubProjectIdForDelete={
                  setSelectedSubProjectIdForDelete
                }
                setUnSelectedSubProjectIdForDelete={
                  setUnSelectedSubProjectIdForDelete
                }
                setDeleteFileId={setDeleteFileId}
              />
            );
          } else {
            if (Number(subProject.status) === 0) {
              return (
                <SubProject
                  mixSeasons={mixSeasons}
                  theme={theme}
                  UploadCount={UploadCount}
                  setTempUploadCount={setTempUploadCount}
                  tempUploadCount={tempUploadCount}
                  setUpdateSubprojectPayload={setUpdateSubprojectPayload}
                  key={index}
                  isSong={true}
                  expandAll={expandAll}
                  isEdit={isEdit}
                  ideas={ideas}
                  projectId={projectId}
                  writers={writers}
                  artists={artists}
                  engineers={engineers}
                  writer={writer}
                  artist={artist}
                  engineer={engineer}
                  surveySubmitStatus={surveySubmitStatus}
                  setLyrics={setLyrics}
                  setEightCountPayload={setEightCountPayload}
                  setWriterPayload={setWriterPayload}
                  setWriterCostPayload={setWriterCostPayload}
                  setArtistPayload={setArtistPayload}
                  setArtistCostPayload={setArtistCostPayload}
                  setDeleteIdeaPayload={setDeleteIdeaPayload}
                  subProject={subProject}
                  setShowAssignIdeaModal={setShowAssignIdeaModal}
                  setSelectedSubProjectId={setSelectedSubProjectId}
                  setSelectedSubProjectIdForDelete={
                    setSelectedSubProjectIdForDelete
                  }
                  setUnSelectedSubProjectIdForDelete={
                    setUnSelectedSubProjectIdForDelete
                  }
                  setDeleteFileId={setDeleteFileId}
                  setLoadIdeaFromViewProject={setLoadIdeaFromViewProject}
                  setResetWriterPayload={setResetWriterPayload}
                  setResetArtistPayload={setResetArtistPayload}
                />
              );
            } else if (Number(subProject.status) === 1) {
              return (
                <CompletedSubProject
                  key={index}
                  authState={authState}
                  writers={writers}
                  artists={artists}
                  engineers={engineers}
                  writer={writer}
                  artist={artist}
                  engineer={engineer}
                  eightCount={subProject.eight_count}
                  subProject={subProject}
                  surveySubmitStatus={surveySubmitStatus}
                  setInCompleteSubProjectId={setInCompleteSubProjectId}
                />
              );
            }
          }
        })}
    </>
  );
};

export default SubProjects;
