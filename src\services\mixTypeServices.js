import MkdSDK from '../utils/MkdSDK';

let sdk = new MkdSDK();

export const getAllMixTypeAPI = async () => {
  try {
    const uri = `/v3/api/custom/equality_record/mix_type/get_all`;
    const res = await sdk.callRawAPI(uri, {}, 'GET');
    return res;
  } catch (error) {
    return error;
  }
};

export const retrieveAllMixTypesAPI = async (page, limit, filter) => {
  try {
    const payload = {
      page: page,
      limit: limit,
      filter: filter,
    };
    const uri = `/v3/api/custom/equality_record/mix_type/retrieve`;
    const res = await sdk.callRawAPI(uri, payload, 'POST');
    return res;
  } catch (error) {
    return error;
  }
};

export const addMixTypeAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/mix_type/add`;
    const res = await sdk.callRawAPI(uri, payload, 'POST');
    return res;
  } catch (error) {
    return error;
  }
};

export const getMixTypeDetailsAPI = async (id) => {
  try {
    const uri = `/v3/api/custom/equality_record/mix_type/${id}`;
    const res = await sdk.callRawAPI(uri, [], 'GET');
    return res;
  } catch (error) {
    return error;
  }
};

export const updateMixTypeAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/mix_type/${payload.id}`;
    const res = await sdk.callRawAPI(uri, payload, 'PUT');
    return res;
  } catch (error) {
    return error;
  }
};

export const deleteMixTypeAPI = async (id) => {
  try {
    const uri = `/v3/api/custom/equality_record/mix_type/${id}`;
    const res = await sdk.callRawAPI(uri, [], 'DELETE');
    return res;
  } catch (error) {
    return error;
  }
};
