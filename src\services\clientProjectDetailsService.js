import { Tab } from "bootstrap";
import MkdSDK from "../utils/MkdSDK";
import { Portal } from "@headlessui/react";
import { removeKeysWhenValueIsNull } from "Utils/utils";

let sdk = new MkdSDK();

export const getProjectDetailsClientAPI = async (id, filters) => {
  const payload = {
    page: 1,
    ...(filters && Object.keys(filters).length > 0 && { filter: filters }),
  };
  try {
    const uri = `/v3/api/custom/equality_record/project/client/view/${id}`;
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const getAllMixTypeClientAPI = async () => {
  try {
    const uri = `/v3/api/custom/equality_record/mix_type/client/get_all`;

    const res = await sdk.callRawAPI(uri, {}, "GET");

    return res;
  } catch (error) {
    return error;
  }
};

export const updateMusicAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/client/${payload.id}`;
    const res = await sdk.callRawAPI(uri, payload, "PUT");
    return res;
  } catch (error) {
    return error;
  }
};

export const addMediaAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/media`;
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};
export const deleteMediaAPI = async (id) => {
  try {
    const uri = `/v3/api/custom/equality_record/media/${id}`;
    const res = await sdk.callRawAPI(uri, [], "DELETE");
    return res;
  } catch (error) {
    return error;
  }
};

export const retrieveAllMediaAPI = async (payload) => {
  try {
    const uri = "/v3/api/custom/equality_record/media/retrieve-all";
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const retrieveAllMusicAPI = async (page, limit, payload) => {
  try {
    const uri = "/v3/api/custom/equality_record/music/retrieve-all";
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};
export const addMusicAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/music`;
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};
export const deleteMusicAPI = async (id) => {
  try {
    const uri = `/v3/api/custom/equality_record/music/${id}`;
    const res = await sdk.callRawAPI(uri, [], "DELETE");
    return res;
  } catch (error) {
    return error;
  }
};

export const getEightCountDetailsAPI = async (id) => {
  try {
    const uri = `v3/api/custom/equality_record/music/view/${id}`;
    const res = await sdk.callRawAPI(uri, [], "GET");
    return res;
  } catch (error) {
    return error;
  }
};

export const deleteEightCountAPI = async (id) => {
  try {
    const uri = `/v3/api/custom/equality_record/eight-count/${id}`;
    const res = await sdk.callRawAPI(uri, [], "DELETE");
    return res;
  } catch (error) {
    return error;
  }
};

export const updateEightCountAPI = async (payload, data) => {
  try {
    const uri = `/v3/api/custom/equality_record/eight-count/${payload.id}`;
    const res = await sdk.callRawAPI(uri, data, "PUT");
    return res;
  } catch (error) {
    return error;
  }
};

export const addEightCountAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/eight-count`;
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const retrieveAllEightCountAPI = async (page, limit, filter) => {
  try {
    const payload = {
      page: page,
      limit: limit,
      filter: filter,
    };
    const uri = "/v3/api/custom/equality_record/eight-count/retrieve-all";
    const res = await sdk.callRawAPI(uri, payload, "POST");
    return res;
  } catch (error) {
    return error;
  }
};

export const getTeamDetailsAPI = async (id) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/client/team_details/view/${id}`;
    const res = await sdk.callRawAPI(uri, [], "GET");
    return res;
  } catch (error) {
    return error;
  }
};

export const editTeamDetailsAPI = async (payload) => {
  try {
    const uri = `/v3/api/custom/equality_record/project/client/team_details`;
    const res = await sdk.callRawAPI(uri, payload, "PUT");
    return res;
  } catch (error) {
    return error;
  }
};
