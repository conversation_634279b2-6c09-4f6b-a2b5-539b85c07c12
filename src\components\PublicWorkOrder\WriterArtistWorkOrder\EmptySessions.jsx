import React from "react";
import FileUpload from "Components/FileUpload/FileUpload";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

const EmptySessions = ({
  canUpload = true,
  setFormData,
  uploadedFilesProgressData = {},
}) => {
  console.log(canUpload);
  return (
    <div className="rounded border border-strokedark bg-boxdark p-6">
      <div className="flex flex-col items-center">
        {canUpload ? (
          <>
            {/* Icon and Title */}
            <div className="mb-6 text-center">
              <div className="mb-4 inline-flex h-16 w-16 items-center justify-center rounded-full bg-boxdark">
                <FontAwesomeIcon
                  icon="fa-solid fa-cloud-arrow-up"
                  className="h-8 w-8 text-primary"
                />
              </div>
              <h3 className="mb-1 text-lg font-semibold text-white">
                Upload Session Files
              </h3>
              <p className="text-sm text-bodydark2">
                Drag and drop your audio files or click to browse
              </p>
            </div>

            {/* Upload Component */}

            <div className="w-full max-w-md">
              <FileUpload
                uploadedFilesProgressData={uploadedFilesProgressData}
                justify="center"
                items="center"
                maxFileSize={2048}
                setFormData={setFormData}
              />
            </div>

            {/* File Size Info */}
            <div className="mt-4 text-center">
              <p className="text-xs text-bodydark2">
                Maximum file size: 2048MB
              </p>
            </div>
          </>
        ) : (
          <div className="flex flex-1 items-center justify-center">
            <div className="text-center">
              <svg
                className="mx-auto mb-4 h-12 w-12 text-boxdark-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                />
              </svg>
              <p className="text-base font-medium text-bodydark">
                No Sessions Uploaded
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EmptySessions;
